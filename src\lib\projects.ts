import { supabase } from './supabase';
import { NotificationIntegration } from './notificationIntegration';

// Database types
export interface Project {
  id: string;
  name: string;
  description: string;
  status: string;
  priority: string;
  start_date: string;
  end_date: string;
  budget: number;
  spent: number;
  progress: number;
  client_name: string;
  client_id?: string; // Foreign key to clients table
  project_manager: string;
  location: string;
  date_added: string;
  last_modified: string;
  created_at: string;
  // Creator information
  created_by_user_id?: string;
  created_by_name?: string;
  created_by_avatar?: string;
}

// Project status options
export const PROJECT_STATUSES = [
  'Planning',
  'In Progress',
  'On Hold',
  'Completed',
  'Cancelled'
];

// Priority options
export const PROJECT_PRIORITIES = [
  'Low',
  'Medium',
  'High',
  'Critical'
];

// Database operations
export class ProjectService {
  // Test database connection
  static async testConnection(): Promise<{ success: boolean; message: string; details?: any }> {
    try {
      console.log('Testing projects table connection...');
      
      const { data, error } = await supabase
        .from('projects')
        .select('count', { count: 'exact', head: true });
      
      if (error) {
        console.error('Projects connection error:', error);
        return {
          success: false,
          message: `Database error: ${error.message}`,
          details: error
        };
      }
      
      console.log('Projects connection test successful');
      return {
        success: true,
        message: 'Successfully connected to projects table',
        details: { count: data }
      };
    } catch (error) {
      console.error('Projects connection test failed:', error);
      return {
        success: false,
        message: error instanceof Error ? error.message : 'Unknown connection error',
        details: error
      };
    }
  }

  // Get projects based on user role and access permissions
  static async getProjects(userId?: string): Promise<Project[]> {
    try {
      console.log('Fetching projects from Supabase with role-based access...');

      // Use RLS (Row Level Security) for automatic filtering
      // The database policies will automatically filter based on the authenticated user
      const { data, error } = await supabase
        .from('projects')
        .select('*')
        .order('date_added', { ascending: false });

      if (error) {
        console.error('Error fetching projects:', error);
        throw new Error(`Database error: ${error.message}`);
      }

      console.log('Successfully fetched role-based projects:', data);
      return data || [];
    } catch (error) {
      console.error('Failed to fetch projects:', error);
      throw error;
    }
  }

  // Get all projects (internal method, no role filtering)
  static async getAllProjects(): Promise<Project[]> {
    try {
      const { data, error } = await supabase
        .from('projects')
        .select('*')
        .order('date_added', { ascending: false });

      if (error) {
        console.error('Error fetching all projects:', error);
        throw new Error(`Database error: ${error.message}`);
      }

      console.log('Successfully fetched all projects:', data);
      return data || [];
    } catch (error) {
      console.error('Failed to fetch all projects:', error);
      throw error;
    }
  }

  // Check if user can access a specific project
  static async canUserAccessProject(projectId: string, userId?: string): Promise<boolean> {
    try {
      if (!userId) {
        console.log('No user ID provided for access check');
        return false;
      }

      const { data, error } = await supabase
        .rpc('can_user_access_project', {
          project_id: projectId,
          target_user_id: userId
        });

      if (error) {
        console.error('Error checking project access:', error);
        return false;
      }

      return data || false;
    } catch (error) {
      console.error('Failed to check project access:', error);
      return false;
    }
  }

  // Get user's client relationships
  static async getUserClientRelationships(userId: string): Promise<any[]> {
    try {
      const { data, error } = await supabase
        .from('client_users')
        .select(`
          *,
          client:clients(*)
        `)
        .eq('user_id', userId);

      if (error) {
        console.error('Error fetching user client relationships:', error);
        return [];
      }

      return data || [];
    } catch (error) {
      console.error('Failed to fetch user client relationships:', error);
      return [];
    }
  }

  // Link user to client (admin only)
  static async linkUserToClient(
    userId: string,
    clientId: string,
    role: string = 'client',
    accessLevel: string = 'read',
    isPrimary: boolean = false
  ): Promise<boolean> {
    try {
      const { data, error } = await supabase
        .rpc('link_user_to_client', {
          target_user_id: userId,
          target_client_id: clientId,
          user_role: role,
          access_level: accessLevel,
          is_primary: isPrimary
        });

      if (error) {
        console.error('Error linking user to client:', error);
        throw new Error(`Failed to link user to client: ${error.message}`);
      }

      return data || false;
    } catch (error) {
      console.error('Failed to link user to client:', error);
      throw error;
    }
  }

  // Add new project with notification integration
  static async addProject(project: Omit<Project, 'id' | 'date_added' | 'last_modified' | 'created_at'>, created_by?: string): Promise<Project> {
    try {
      const { data, error } = await supabase
        .from('projects')
        .insert([{
          name: project.name,
          description: project.description,
          status: project.status,
          priority: project.priority,
          start_date: project.start_date,
          end_date: project.end_date,
          budget: project.budget,
          spent: project.spent,
          progress: project.progress,
          client_name: project.client_name,
          project_manager: project.project_manager,
          location: project.location,
          created_by_user_id: created_by
        }])
        .select()
        .single();

      if (error) {
        console.error('Error adding project:', error);
        throw error;
      }

      // Send notification about project creation
      if (created_by) {
        try {
          await NotificationIntegration.handleProjectCreated(data, created_by);
        } catch (notificationError) {
          console.error('Error sending project creation notification:', notificationError);
          // Don't fail the project creation if notification fails
        }
      }

      return data;
    } catch (error) {
      console.error('Failed to add project:', error);
      throw error;
    }
  }

  // Update project with notification integration
  static async updateProject(id: string, updates: Partial<Omit<Project, 'id' | 'date_added' | 'created_at'>>, updated_by?: string): Promise<Project> {
    try {
      // Get the original project to compare changes
      const originalProject = await this.getProjectById(id);

      const { data, error } = await supabase
        .from('projects')
        .update({
          ...updates,
          last_modified: new Date().toISOString()
        })
        .eq('id', id)
        .select()
        .single();

      if (error) {
        console.error('Error updating project:', error);
        throw error;
      }

      // Send notification about project update
      if (updated_by && originalProject) {
        try {
          const changes: string[] = [];

          // Detect what changed
          if (updates.status && updates.status !== originalProject.status) {
            changes.push(`Status changed from ${originalProject.status} to ${updates.status}`);
          }
          if (updates.priority && updates.priority !== originalProject.priority) {
            changes.push(`Priority changed from ${originalProject.priority} to ${updates.priority}`);
          }
          if (updates.progress !== undefined && updates.progress !== originalProject.progress) {
            changes.push(`Progress updated to ${updates.progress}%`);
          }
          if (updates.budget && updates.budget !== originalProject.budget) {
            changes.push(`Budget updated to $${updates.budget.toLocaleString()}`);
          }
          if (updates.end_date && updates.end_date !== originalProject.end_date) {
            changes.push(`End date changed to ${updates.end_date}`);
          }

          if (changes.length > 0) {
            await NotificationIntegration.handleProjectUpdated(data, updated_by, changes);
          }
        } catch (notificationError) {
          console.error('Error sending project update notification:', notificationError);
          // Don't fail the project update if notification fails
        }
      }

      return data;
    } catch (error) {
      console.error('Failed to update project:', error);
      throw error;
    }
  }

  // Delete project
  static async deleteProject(id: string): Promise<void> {
    try {
      const { error } = await supabase
        .from('projects')
        .delete()
        .eq('id', id);
      
      if (error) {
        console.error('Error deleting project:', error);
        throw error;
      }
    } catch (error) {
      console.error('Failed to delete project:', error);
      throw error;
    }
  }

  // Subscribe to real-time changes
  static subscribeToChanges(callback: (payload: any) => void) {
    return supabase
      .channel('projects')
      .on('postgres_changes', 
        { 
          event: '*', 
          schema: 'public', 
          table: 'projects' 
        }, 
        callback
      )
      .subscribe();
  }

  // Export data to CSV
  static async exportToCSV(): Promise<string> {
    try {
      const projects = await this.getProjects();
      const headers = [
        'Name', 'Description', 'Status', 'Priority', 'Start Date', 'End Date', 
        'Budget', 'Spent', 'Progress', 'Client', 'Project Manager', 'Location', 'Date Added'
      ];
      
      const csvContent = [
        headers.join(','),
        ...projects.map(project => [
          `"${project.name}"`,
          `"${project.description}"`,
          `"${project.status}"`,
          `"${project.priority}"`,
          project.start_date,
          project.end_date,
          project.budget,
          project.spent,
          project.progress,
          `"${project.client_name}"`,
          `"${project.project_manager}"`,
          `"${project.location}"`,
          new Date(project.date_added).toLocaleDateString()
        ].join(','))
      ].join('\n');

      return csvContent;
    } catch (error) {
      console.error('Failed to export projects data:', error);
      throw error;
    }
  }

  // Get summary statistics
  static async getSummaryStats() {
    try {
      const projects = await this.getProjects();
      
      const totalProjects = projects.length;
      const totalBudget = projects.reduce((sum, project) => sum + Number(project.budget), 0);
      const totalSpent = projects.reduce((sum, project) => sum + Number(project.spent), 0);
      const averageProgress = totalProjects > 0 ? projects.reduce((sum, project) => sum + project.progress, 0) / totalProjects : 0;
      
      // Group by status
      const statusStats = projects.reduce((acc, project) => {
        if (!acc[project.status]) {
          acc[project.status] = { count: 0, budget: 0, spent: 0 };
        }
        acc[project.status].count++;
        acc[project.status].budget += Number(project.budget);
        acc[project.status].spent += Number(project.spent);
        return acc;
      }, {} as Record<string, { count: number; budget: number; spent: number }>);

      // Group by priority
      const priorityStats = projects.reduce((acc, project) => {
        if (!acc[project.priority]) {
          acc[project.priority] = { count: 0, budget: 0 };
        }
        acc[project.priority].count++;
        acc[project.priority].budget += Number(project.budget);
        return acc;
      }, {} as Record<string, { count: number; budget: number }>);

      return {
        totalProjects,
        totalBudget,
        totalSpent,
        averageProgress,
        statusStats,
        priorityStats
      };
    } catch (error) {
      console.error('Failed to get projects summary stats:', error);
      throw error;
    }
  }
}
