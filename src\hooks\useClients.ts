import { useState, useCallback, useEffect } from 'react';
import { ClientService } from '@/lib/clients';
import { 
  Client, 
  ClientPayment, 
  ClientInvoice, 
  ClientFinancialSummary,
  ClientFilters,
  ClientAnalytics
} from '@/types/client';

export const useClients = () => {
  const [clients, setClients] = useState<Client[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const loadClients = useCallback(async (filters?: ClientFilters) => {
    try {
      setLoading(true);
      setError(null);
      console.log('Loading clients with filters:', filters);

      const data = await ClientService.getClients(filters);
      setClients(data);
      console.log('Clients loaded successfully:', data.length);

      // Initialize financial data for existing clients (only runs once)
      if (data.length > 0) {
        await ClientService.initializeFinancialDataForExistingClients();
      }
    } catch (err) {
      console.error('Failed to load clients:', err);
      setError(err instanceof Error ? err.message : 'Failed to load clients');
    } finally {
      setLoading(false);
    }
  }, []);

  const createClient = useCallback(async (clientData: Omit<Client, 'id' | 'created_at' | 'updated_at'>) => {
    try {
      setError(null);
      setLoading(true);
      console.log('Creating client:', clientData);

      const newClient = await ClientService.createClient(clientData);

      // Refresh the entire client list to ensure we have the latest data
      await loadClients();

      console.log('Client created successfully:', newClient);
      return newClient;
    } catch (err) {
      console.error('Failed to create client:', err);
      setError(err instanceof Error ? err.message : 'Failed to create client');
      throw err;
    } finally {
      setLoading(false);
    }
  }, [loadClients]);

  const updateClient = useCallback(async (id: string, updates: Partial<Client>) => {
    try {
      setError(null);
      console.log('Updating client:', id, updates);
      
      const updatedClient = await ClientService.updateClient(id, updates);
      setClients(prev => prev.map(client => 
        client.id === id ? updatedClient : client
      ));
      console.log('Client updated successfully:', updatedClient);
      
      return updatedClient;
    } catch (err) {
      console.error('Failed to update client:', err);
      setError(err instanceof Error ? err.message : 'Failed to update client');
      throw err;
    }
  }, []);

  const deleteClient = useCallback(async (id: string) => {
    try {
      setError(null);
      console.log('Deleting client:', id);
      
      await ClientService.deleteClient(id);
      setClients(prev => prev.filter(client => client.id !== id));
      console.log('Client deleted successfully');
    } catch (err) {
      console.error('Failed to delete client:', err);
      setError(err instanceof Error ? err.message : 'Failed to delete client');
      throw err;
    }
  }, []);

  const getClientById = useCallback(async (id: string): Promise<Client | null> => {
    try {
      console.log('Getting client by ID:', id);
      return await ClientService.getClientById(id);
    } catch (err) {
      console.error('Failed to get client:', err);
      return null;
    }
  }, []);

  // Initial load
  useEffect(() => {
    console.log('Clients: Initial load');
    loadClients();
  }, [loadClients]);

  return {
    clients,
    loading,
    error,
    loadClients,
    createClient,
    updateClient,
    deleteClient,
    getClientById
  };
};

export const useClientFinancials = (clientId: string) => {
  const [financialSummary, setFinancialSummary] = useState<ClientFinancialSummary | null>(null);
  const [payments, setPayments] = useState<ClientPayment[]>([]);
  const [invoices, setInvoices] = useState<ClientInvoice[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const loadFinancialData = useCallback(async () => {
    if (!clientId) return;

    try {
      setLoading(true);
      setError(null);
      console.log('Loading financial data for client:', clientId);

      const [summary, paymentsData, invoicesData] = await Promise.all([
        ClientService.getClientFinancialSummary(clientId),
        ClientService.getClientPayments(clientId),
        ClientService.getClientInvoices(clientId)
      ]);

      setFinancialSummary(summary);
      setPayments(paymentsData);
      setInvoices(invoicesData);
      console.log('Financial data loaded successfully');
    } catch (err) {
      console.error('Failed to load financial data:', err);
      setError(err instanceof Error ? err.message : 'Failed to load financial data');
    } finally {
      setLoading(false);
    }
  }, [clientId]);

  useEffect(() => {
    loadFinancialData();
  }, [loadFinancialData]);

  return {
    financialSummary,
    payments,
    invoices,
    loading,
    error,
    refreshFinancialData: loadFinancialData
  };
};

export const useClientAnalytics = () => {
  const [analytics, setAnalytics] = useState<ClientAnalytics | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const loadAnalytics = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      console.log('Loading client analytics');

      const data = await ClientService.getClientAnalytics();
      setAnalytics(data);
      console.log('Analytics loaded successfully');
    } catch (err) {
      console.error('Failed to load analytics:', err);
      setError(err instanceof Error ? err.message : 'Failed to load analytics');
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    loadAnalytics();
  }, [loadAnalytics]);

  return {
    analytics,
    loading,
    error,
    refreshAnalytics: loadAnalytics
  };
};
