
import React, { useState, useEffect, useRef } from 'react';
import Layout from '@/components/layout/Layout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { ScrollArea } from '@/components/ui/scroll-area';
import { useToast } from '@/components/ui/use-toast';
import { MessageSquare, Send, Search, Plus, Phone, Video, Hash, Users, MoreVertical, Pin, Reply, Smile, Trash2, Edit3 } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { MessageService, MessageChannel, Message } from '@/lib/messageService';
import { CreatorInfo } from '@/components/ui/CreatorInfo';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Label } from '@/components/ui/label';
import { useAuth } from '@/contexts/AuthContext';
import { NotificationIntegration } from '@/lib/notificationIntegration';

const Messages = () => {
  const { toast } = useToast();
  const { user, profile } = useAuth();
  const [channels, setChannels] = useState<MessageChannel[]>([]);
  const [selectedChannel, setSelectedChannel] = useState<MessageChannel | null>(null);
  const [messages, setMessages] = useState<Message[]>([]);
  const [newMessage, setNewMessage] = useState('');
  const [loading, setLoading] = useState(true);
  const [sendingMessage, setSendingMessage] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // Channel management state
  const [showCreateChannel, setShowCreateChannel] = useState(false);
  const [newChannelName, setNewChannelName] = useState('');
  const [newChannelDescription, setNewChannelDescription] = useState('');
  const [newChannelType, setNewChannelType] = useState<'general' | 'project' | 'team' | 'announcement'>('general');

  // Message actions state
  const [deletingMessageId, setDeletingMessageId] = useState<string | null>(null);

  // Current user from auth context
  const currentUser = {
    id: user?.id,
    name: profile ? `${profile.first_name} ${profile.last_name}` : 'Unknown User',
    email: user?.email || profile?.email,
    role: profile?.role_name || 'user'
  };

  useEffect(() => {
    loadChannels();
  }, []);

  useEffect(() => {
    if (selectedChannel) {
      loadMessages(selectedChannel.id);
    }
  }, [selectedChannel]);

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const loadChannels = async () => {
    try {
      const channelsData = await MessageService.getChannels();
      setChannels(channelsData);

      // Select first channel by default
      if (channelsData.length > 0 && !selectedChannel) {
        setSelectedChannel(channelsData[0]);
      }
    } catch (error) {
      console.error('Error loading channels:', error);
      toast({
        title: "Error",
        description: "Failed to load channels. Please run the database setup first.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const loadMessages = async (channelId: string) => {
    try {
      const messagesData = await MessageService.getMessages(channelId);
      setMessages(messagesData);
    } catch (error) {
      console.error('Error loading messages:', error);
    }
  };

  const sendMessage = async () => {
    if (!newMessage.trim() || !selectedChannel || sendingMessage) return;

    setSendingMessage(true);
    try {
      const messageData = {
        channel_id: selectedChannel.id,
        sender_name: currentUser.name,
        sender_email: currentUser.email,
        sender_role: currentUser.role,
        message_content: newMessage.trim(),
        message_type: 'text' as const,
        attachments: null,
        reply_to_message_id: null
      };

      const sentMessage = await MessageService.sendMessage(messageData);
      setMessages(prev => [...prev, sentMessage]);
      setNewMessage('');

      // Send notification for new message
      if (user?.id) {
        try {
          await NotificationIntegration.handleNewMessage(sentMessage, selectedChannel, user.id);
        } catch (notificationError) {
          console.error('Error sending message notification:', notificationError);
          // Don't show error to user as message was sent successfully
        }
      }
    } catch (error) {
      console.error('Error sending message:', error);
      toast({
        title: "Error",
        description: "Failed to send message. Please try again.",
        variant: "destructive",
      });
    } finally {
      setSendingMessage(false);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      sendMessage();
    }
  };

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  const formatTime = (timestamp: string) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);

    if (diffInHours < 24) {
      return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    } else {
      return date.toLocaleDateString([], { month: 'short', day: 'numeric', hour: '2-digit', minute: '2-digit' });
    }
  };

  const getChannelIcon = (type: string) => {
    switch (type) {
      case 'general':
        return <Hash className="w-4 h-4" />;
      case 'project':
        return <Hash className="w-4 h-4" />;
      case 'team':
        return <Users className="w-4 h-4" />;
      case 'announcement':
        return <MessageSquare className="w-4 h-4" />;
      default:
        return <Hash className="w-4 h-4" />;
    }
  };

  const getMessageTypeStyle = (type: string) => {
    switch (type) {
      case 'system':
        return 'bg-blue-50 border-blue-200 text-blue-800';
      case 'announcement':
        return 'bg-yellow-50 border-yellow-200 text-yellow-800';
      default:
        return '';
    }
  };

  // Message deletion
  const handleDeleteMessage = async (messageId: string) => {
    try {
      setDeletingMessageId(messageId);
      await MessageService.deleteMessage(messageId);

      setMessages(prev => prev.filter(msg => msg.id !== messageId));

      toast({
        title: "Message deleted",
        description: "The message has been removed.",
      });
    } catch (error: any) {
      console.error('Error deleting message:', error);
      toast({
        title: "Failed to delete message",
        description: error.message || "Please try again.",
        variant: "destructive",
      });
    } finally {
      setDeletingMessageId(null);
    }
  };

  // Channel management
  const handleCreateChannel = async () => {
    if (!newChannelName.trim()) return;

    try {
      const channelData = {
        name: newChannelName.trim(),
        description: newChannelDescription.trim() || undefined,
        channel_type: newChannelType,
        created_by: currentUser.email || 'unknown'
      };

      const newChannel = await MessageService.createChannel(channelData);
      setChannels(prev => [...prev, newChannel]);

      // Send notification for new channel
      if (user?.id) {
        try {
          await NotificationIntegration.handleChannelCreated(newChannel, user.id);
        } catch (notificationError) {
          console.error('Error sending channel creation notification:', notificationError);
          // Don't show error to user as channel was created successfully
        }
      }

      // Reset form
      setNewChannelName('');
      setNewChannelDescription('');
      setNewChannelType('general');
      setShowCreateChannel(false);

      toast({
        title: "Channel created",
        description: `#${newChannel.name} has been created successfully.`,
      });
    } catch (error: any) {
      console.error('Error creating channel:', error);
      toast({
        title: "Failed to create channel",
        description: error.message || "Please try again.",
        variant: "destructive",
      });
    }
  };

  const handleDeleteChannel = async (channelId: string) => {
    if (!confirm('Are you sure you want to delete this channel? This action cannot be undone.')) {
      return;
    }

    try {
      await MessageService.deleteChannel(channelId);
      setChannels(prev => prev.filter(ch => ch.id !== channelId));

      // If deleted channel was selected, clear selection
      if (selectedChannel?.id === channelId) {
        setSelectedChannel(null);
        setMessages([]);
      }

      toast({
        title: "Channel deleted",
        description: "The channel has been removed.",
      });
    } catch (error: any) {
      console.error('Error deleting channel:', error);
      toast({
        title: "Failed to delete channel",
        description: error.message || "Please try again.",
        variant: "destructive",
      });
    }
  };

  if (loading) {
    return (
      <Layout>
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
            <p className="mt-2 text-sm text-gray-600">Loading messages...</p>
          </div>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="w-12 h-12 bg-gradient-to-r from-pink-600 to-rose-600 rounded-xl flex items-center justify-center">
              <MessageSquare className="w-6 h-6 text-white" />
            </div>
            <div>
              <h1 className="text-3xl font-bold text-gray-900 font-heading">Messages</h1>
              <p className="text-gray-600">Team communication and project discussions</p>
            </div>
          </div>
          <Dialog open={showCreateChannel} onOpenChange={setShowCreateChannel}>
            <DialogTrigger asChild>
              <Button>
                <Plus className="w-4 h-4 mr-2" />
                New Channel
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Create New Channel</DialogTitle>
                <DialogDescription>
                  Create a new channel for team communication.
                </DialogDescription>
              </DialogHeader>
              <div className="space-y-4">
                <div>
                  <Label htmlFor="channel-name">Channel Name</Label>
                  <Input
                    id="channel-name"
                    placeholder="e.g., general, project-alpha"
                    value={newChannelName}
                    onChange={(e) => setNewChannelName(e.target.value)}
                  />
                </div>
                <div>
                  <Label htmlFor="channel-description">Description (Optional)</Label>
                  <Input
                    id="channel-description"
                    placeholder="What is this channel for?"
                    value={newChannelDescription}
                    onChange={(e) => setNewChannelDescription(e.target.value)}
                  />
                </div>
                <div>
                  <Label htmlFor="channel-type">Channel Type</Label>
                  <select
                    id="channel-type"
                    className="w-full p-2 border border-gray-300 rounded-md"
                    value={newChannelType}
                    onChange={(e) => setNewChannelType(e.target.value as any)}
                  >
                    <option value="general">General</option>
                    <option value="project">Project</option>
                    <option value="team">Team</option>
                    <option value="announcement">Announcement</option>
                  </select>
                </div>
              </div>
              <DialogFooter>
                <Button variant="outline" onClick={() => setShowCreateChannel(false)}>
                  Cancel
                </Button>
                <Button onClick={handleCreateChannel} disabled={!newChannelName.trim()}>
                  Create Channel
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 h-[600px]">
          {/* Channel List */}
          <div className="lg:col-span-1">
            <Card className="h-full">
              <CardHeader>
                <div className="flex items-center space-x-2">
                  <Search className="w-4 h-4 text-gray-500" />
                  <Input
                    placeholder="Search channels..."
                    className="border-0 focus-visible:ring-0"
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                  />
                </div>
              </CardHeader>
              <CardContent className="p-0">
                <ScrollArea className="h-[500px]">
                  <div className="space-y-1">
                    {channels
                      .filter(channel =>
                        channel.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                        channel.description?.toLowerCase().includes(searchQuery.toLowerCase())
                      )
                      .map((channel) => (
                        <div
                          key={channel.id}
                          className={`group p-4 hover:bg-gray-50 cursor-pointer border-l-4 transition-colors ${
                            selectedChannel?.id === channel.id
                              ? 'border-blue-500 bg-blue-50'
                              : 'border-transparent'
                          }`}
                          onClick={() => setSelectedChannel(channel)}
                        >
                          <div className="flex items-center space-x-3">
                            <div className="w-8 h-8 bg-gray-200 rounded-lg flex items-center justify-center">
                              {getChannelIcon(channel.channel_type)}
                            </div>
                            <div className="flex-1 min-w-0">
                              <div className="flex items-center justify-between">
                                <p className="font-medium truncate">{channel.name}</p>
                                <div className="flex items-center space-x-2">
                                  {channel.channel_type === 'announcement' && (
                                    <Badge variant="secondary" className="text-xs">
                                      📢
                                    </Badge>
                                  )}
                                  {channel.is_private && (
                                    <Badge variant="outline" className="text-xs">
                                      🔒
                                    </Badge>
                                  )}
                                  <DropdownMenu>
                                    <DropdownMenuTrigger asChild>
                                      <Button
                                        variant="ghost"
                                        size="sm"
                                        className="h-6 w-6 p-0 opacity-0 group-hover:opacity-100 transition-opacity"
                                        onClick={(e) => e.stopPropagation()}
                                      >
                                        <MoreVertical className="h-3 w-3" />
                                      </Button>
                                    </DropdownMenuTrigger>
                                    <DropdownMenuContent align="end">
                                      <DropdownMenuItem>
                                        <Edit3 className="h-4 w-4 mr-2" />
                                        Edit Channel
                                      </DropdownMenuItem>
                                      <DropdownMenuItem
                                        onClick={(e) => {
                                          e.stopPropagation();
                                          handleDeleteChannel(channel.id);
                                        }}
                                        className="text-red-600"
                                      >
                                        <Trash2 className="h-4 w-4 mr-2" />
                                        Delete Channel
                                      </DropdownMenuItem>
                                    </DropdownMenuContent>
                                  </DropdownMenu>
                                </div>
                              </div>
                              <p className="text-sm text-gray-500 truncate">
                                {channel.description || `${channel.channel_type} channel`}
                              </p>
                            </div>
                          </div>
                        </div>
                      ))}
                  </div>
                </ScrollArea>
              </CardContent>
            </Card>
          </div>

          {/* Chat Area */}
          <div className="lg:col-span-2">
            {selectedChannel ? (
              <Card className="h-full flex flex-col">
                <CardHeader className="border-b">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <div className="w-10 h-10 bg-gray-200 rounded-lg flex items-center justify-center">
                        {getChannelIcon(selectedChannel.channel_type)}
                      </div>
                      <div>
                        <CardTitle className="text-lg">{selectedChannel.name}</CardTitle>
                        <CardDescription>
                          {selectedChannel.description || `${selectedChannel.channel_type} channel`}
                        </CardDescription>
                      </div>
                    </div>
                    <div className="flex space-x-2">
                      <Button variant="ghost" size="sm">
                        <Phone className="w-4 h-4" />
                      </Button>
                      <Button variant="ghost" size="sm">
                        <Video className="w-4 h-4" />
                      </Button>
                      <Button variant="ghost" size="sm">
                        <MoreVertical className="w-4 h-4" />
                      </Button>
                    </div>
                  </div>
                </CardHeader>
              
                <CardContent className="flex-1 p-0 overflow-hidden">
                  <ScrollArea className="h-[400px] p-4">
                    <div className="space-y-4">
                      {messages.length === 0 ? (
                        <div className="text-center py-8">
                          <MessageSquare className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                          <h3 className="text-lg font-medium text-gray-900 mb-2">No messages yet</h3>
                          <p className="text-gray-600">Start the conversation by sending a message!</p>
                        </div>
                      ) : (
                        messages.map((message) => {
                          // Check if message belongs to current user
                          const isOwn = message.created_by_user_id === currentUser.id ||
                                       message.sender_email === currentUser.email;
                          const messageStyle = getMessageTypeStyle(message.message_type);



                          return (
                            <div key={message.id} className={`flex ${isOwn ? 'justify-end' : 'justify-start'} mb-4`}>
                              {!isOwn && (
                                <Avatar className="h-8 w-8 mr-3 mt-1">
                                  <AvatarImage
                                    src={message.created_by_avatar || undefined}
                                    alt={message.created_by_name || message.sender_name || 'User'}
                                  />
                                  <AvatarFallback className="bg-gradient-to-r from-blue-600 to-purple-600 text-white text-sm">
                                    {(() => {
                                      const name = message.created_by_name || message.sender_name || 'Unknown User';
                                      return name.split(' ').map(n => n[0]).join('').toUpperCase().slice(0, 2);
                                    })()}
                                  </AvatarFallback>
                                </Avatar>
                              )}
                              <div className={`max-w-xs lg:max-w-md px-4 py-2 rounded-lg ${
                                message.message_type === 'system' || message.message_type === 'announcement'
                                  ? `${messageStyle} border mx-auto text-center`
                                  : isOwn
                                    ? 'bg-blue-600 text-white'
                                    : 'bg-gray-100 text-gray-900'
                              }`}>
                                {!isOwn && message.message_type === 'text' && (
                                  <div className="flex items-center justify-between mb-1">
                                    <p className="text-xs font-medium text-gray-600">
                                      {message.created_by_name || message.sender_name || 'Unknown User'}
                                    </p>
                                    {message.sender_role && (
                                      <Badge variant="outline" className="text-xs">
                                        {message.sender_role}
                                      </Badge>
                                    )}
                                  </div>
                                )}

                                <p className="text-sm whitespace-pre-wrap">{message.message_content}</p>

                                <div className="flex items-center justify-between mt-1">
                                  <p className={`text-xs ${
                                    message.message_type === 'system' || message.message_type === 'announcement'
                                      ? 'text-gray-500'
                                      : isOwn ? 'text-blue-100' : 'text-gray-500'
                                  }`}>
                                    {formatTime(message.created_at)}
                                    {message.is_edited && ' (edited)'}
                                  </p>

                                  {message.message_type === 'text' && (
                                    <div className="flex items-center space-x-1 ml-2">
                                      <Button variant="ghost" size="sm" className={`h-6 w-6 p-0 ${isOwn ? 'text-white hover:bg-blue-700' : ''}`}>
                                        <Reply className="w-3 h-3" />
                                      </Button>
                                      <Button variant="ghost" size="sm" className={`h-6 w-6 p-0 ${isOwn ? 'text-white hover:bg-blue-700' : ''}`}>
                                        <Smile className="w-3 h-3" />
                                      </Button>
                                      <DropdownMenu>
                                        <DropdownMenuTrigger asChild>
                                          <Button variant="ghost" size="sm" className={`h-6 w-6 p-0 ${isOwn ? 'text-white hover:bg-blue-700' : ''}`}>
                                            <MoreVertical className="w-3 h-3" />
                                          </Button>
                                        </DropdownMenuTrigger>
                                        <DropdownMenuContent align="end">
                                          <DropdownMenuItem>
                                            <Pin className="h-4 w-4 mr-2" />
                                            Pin Message
                                          </DropdownMenuItem>
                                          <DropdownMenuItem>
                                            <Reply className="h-4 w-4 mr-2" />
                                            Reply
                                          </DropdownMenuItem>
                                          {isOwn && (
                                            <>
                                              <DropdownMenuItem>
                                                <Edit3 className="h-4 w-4 mr-2" />
                                                Edit
                                              </DropdownMenuItem>
                                              <DropdownMenuItem
                                                onClick={() => handleDeleteMessage(message.id)}
                                                className="text-red-600"
                                                disabled={deletingMessageId === message.id}
                                              >
                                                <Trash2 className="h-4 w-4 mr-2" />
                                                {deletingMessageId === message.id ? 'Deleting...' : 'Delete'}
                                              </DropdownMenuItem>
                                            </>
                                          )}
                                        </DropdownMenuContent>
                                      </DropdownMenu>
                                      {message.is_pinned && (
                                        <Pin className="w-3 h-3 text-yellow-500" />
                                      )}
                                    </div>
                                  )}
                                </div>
                              </div>

                              {/* Avatar for own messages (right side) */}
                              {isOwn && (
                                <Avatar className="h-8 w-8 ml-3 mt-1">
                                  <AvatarImage
                                    src={profile?.profile_picture || undefined}
                                    alt={currentUser.name}
                                  />
                                  <AvatarFallback className="bg-gradient-to-r from-green-600 to-blue-600 text-white text-sm">
                                    {currentUser.name.split(' ').map(n => n[0]).join('').toUpperCase().slice(0, 2)}
                                  </AvatarFallback>
                                </Avatar>
                              )}
                            </div>
                          );
                        })
                      )}
                      <div ref={messagesEndRef} />
                    </div>
                  </ScrollArea>
                </CardContent>

                <div className="border-t p-4">
                  <div className="flex space-x-2">
                    <Textarea
                      placeholder="Type your message..."
                      className="flex-1 min-h-[40px] max-h-[120px] resize-none"
                      value={newMessage}
                      onChange={(e) => setNewMessage(e.target.value)}
                      onKeyPress={handleKeyPress}
                      disabled={sendingMessage}
                    />
                    <Button
                      onClick={sendMessage}
                      disabled={!newMessage.trim() || sendingMessage}
                      className="self-end"
                    >
                      {sendingMessage ? (
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                      ) : (
                        <Send className="w-4 h-4" />
                      )}
                    </Button>
                  </div>
                </div>
              </Card>
            ) : (
              <Card className="h-full flex items-center justify-center">
                <div className="text-center">
                  <MessageSquare className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">Select a Channel</h3>
                  <p className="text-gray-600">Choose a channel from the sidebar to start messaging</p>
                </div>
              </Card>
            )}
          </div>
        </div>
      </div>
    </Layout>
  );
};

export default Messages;
