import React, { useState, useEffect } from 'react';
import { 
  MessageSquare, 
  Send, 
  Phone, 
  Video, 
  MoreHorizontal,
  Search,
  Pin,
  Archive,
  Trash2,
  Circle,
  Check<PERSON>he<PERSON>,
  Clock
} from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';
import { Textarea } from '@/components/ui/textarea';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { useNavigate } from 'react-router-dom';

interface Message {
  id: string;
  senderId: string;
  senderName: string;
  senderAvatar?: string;
  content: string;
  timestamp: Date;
  read: boolean;
  type: 'text' | 'image' | 'file';
}

interface Conversation {
  id: string;
  participantId: string;
  participantName: string;
  participantAvatar?: string;
  participantRole: string;
  lastMessage: Message;
  unreadCount: number;
  isOnline: boolean;
  isPinned: boolean;
}

interface MessagesCenterProps {
  className?: string;
}

const MessagesCenter: React.FC<MessagesCenterProps> = ({ className = '' }) => {
  const navigate = useNavigate();
  const [isOpen, setIsOpen] = useState(false);
  const [selectedConversation, setSelectedConversation] = useState<string | null>(null);
  const [newMessage, setNewMessage] = useState('');
  const [searchQuery, setSearchQuery] = useState('');

  // Mock data
  const [conversations, setConversations] = useState<Conversation[]>([
    {
      id: '1',
      participantId: 'sarah-johnson',
      participantName: 'Sarah Johnson',
      participantRole: 'Site Engineer',
      lastMessage: {
        id: 'm1',
        senderId: 'sarah-johnson',
        senderName: 'Sarah Johnson',
        content: 'The bridge renovation is progressing well. We should be on schedule for the milestone review.',
        timestamp: new Date(Date.now() - 30 * 60 * 1000),
        read: false,
        type: 'text'
      },
      unreadCount: 2,
      isOnline: true,
      isPinned: true
    },
    {
      id: '2',
      participantId: 'mike-davis',
      participantName: 'Mike Davis',
      participantRole: 'Project Manager',
      lastMessage: {
        id: 'm2',
        senderId: 'mike-davis',
        senderName: 'Mike Davis',
        content: 'Can you review the updated budget for the residential complex?',
        timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000),
        read: false,
        type: 'text'
      },
      unreadCount: 1,
      isOnline: false,
      isPinned: false
    },
    {
      id: '3',
      participantId: 'lisa-chen',
      participantName: 'Lisa Chen',
      participantRole: 'Architect',
      lastMessage: {
        id: 'm3',
        senderId: 'current-user',
        senderName: 'You',
        content: 'Thanks for the updated blueprints. They look great!',
        timestamp: new Date(Date.now() - 4 * 60 * 60 * 1000),
        read: true,
        type: 'text'
      },
      unreadCount: 0,
      isOnline: true,
      isPinned: false
    }
  ]);

  const [messages, setMessages] = useState<{ [conversationId: string]: Message[] }>({
    '1': [
      {
        id: 'm1-1',
        senderId: 'sarah-johnson',
        senderName: 'Sarah Johnson',
        content: 'Hi! I wanted to update you on the bridge renovation progress.',
        timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000),
        read: true,
        type: 'text'
      },
      {
        id: 'm1-2',
        senderId: 'current-user',
        senderName: 'You',
        content: 'Great! How are we doing with the timeline?',
        timestamp: new Date(Date.now() - 90 * 60 * 1000),
        read: true,
        type: 'text'
      },
      {
        id: 'm1-3',
        senderId: 'sarah-johnson',
        senderName: 'Sarah Johnson',
        content: 'The bridge renovation is progressing well. We should be on schedule for the milestone review.',
        timestamp: new Date(Date.now() - 30 * 60 * 1000),
        read: false,
        type: 'text'
      }
    ]
  });

  const totalUnreadCount = conversations.reduce((sum, conv) => sum + conv.unreadCount, 0);

  const formatTimestamp = (timestamp: Date) => {
    const now = new Date();
    const diff = now.getTime() - timestamp.getTime();
    const minutes = Math.floor(diff / (1000 * 60));
    const hours = Math.floor(diff / (1000 * 60 * 60));
    const days = Math.floor(diff / (1000 * 60 * 60 * 24));

    if (minutes < 60) {
      return `${minutes}m`;
    } else if (hours < 24) {
      return `${hours}h`;
    } else {
      return `${days}d`;
    }
  };

  const getInitials = (name: string) => {
    return name.split(' ').map(n => n[0]).join('').toUpperCase();
  };

  const sendMessage = () => {
    if (!newMessage.trim() || !selectedConversation) return;

    const message: Message = {
      id: `m-${Date.now()}`,
      senderId: 'current-user',
      senderName: 'You',
      content: newMessage.trim(),
      timestamp: new Date(),
      read: true,
      type: 'text'
    };

    setMessages(prev => ({
      ...prev,
      [selectedConversation]: [...(prev[selectedConversation] || []), message]
    }));

    // Update conversation last message
    setConversations(prev => 
      prev.map(conv => 
        conv.id === selectedConversation 
          ? { ...conv, lastMessage: message }
          : conv
      )
    );

    setNewMessage('');
  };

  const markAsRead = (conversationId: string) => {
    setConversations(prev =>
      prev.map(conv =>
        conv.id === conversationId
          ? { ...conv, unreadCount: 0, lastMessage: { ...conv.lastMessage, read: true } }
          : conv
      )
    );
  };

  const selectedConv = conversations.find(c => c.id === selectedConversation);
  const conversationMessages = selectedConversation ? messages[selectedConversation] || [] : [];

  const filteredConversations = conversations.filter(conv =>
    conv.participantName.toLowerCase().includes(searchQuery.toLowerCase()) ||
    conv.lastMessage.content.toLowerCase().includes(searchQuery.toLowerCase())
  );

  return (
    <>
      <DropdownMenu open={isOpen} onOpenChange={setIsOpen}>
        <DropdownMenuTrigger asChild>
          <Button variant="ghost" size="sm" className={`relative hover:bg-gray-100 ${className}`}>
            <MessageSquare className="w-5 h-5" />
            {totalUnreadCount > 0 && (
              <Badge 
                variant="destructive" 
                className="absolute -top-1 -right-1 h-5 w-5 flex items-center justify-center p-0 text-xs"
              >
                {totalUnreadCount > 9 ? '9+' : totalUnreadCount}
              </Badge>
            )}
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end" className="w-80 p-0 h-96">
          <div className="flex flex-col h-full">
            {/* Header */}
            <div className="p-4 border-b">
              <div className="flex items-center justify-between mb-3">
                <h3 className="font-semibold">Messages</h3>
                <Button variant="ghost" size="sm">
                  <MoreHorizontal className="h-4 w-4" />
                </Button>
              </div>
              <div className="relative">
                <Search className="absolute left-2 top-2.5 h-4 w-4 text-gray-400" />
                <Input
                  placeholder="Search conversations..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-8 h-9"
                />
              </div>
            </div>

            {/* Conversations List */}
            {!selectedConversation ? (
              <ScrollArea className="flex-1">
                <div className="p-2">
                  {filteredConversations.length === 0 ? (
                    <div className="text-center py-8 text-gray-500">
                      <MessageSquare className="h-8 w-8 mx-auto mb-2 opacity-50" />
                      <p className="text-sm">No conversations found</p>
                    </div>
                  ) : (
                    filteredConversations.map((conversation) => (
                      <div
                        key={conversation.id}
                        className="flex items-center space-x-3 p-3 rounded-lg hover:bg-gray-50 cursor-pointer transition-colors"
                        onClick={() => {
                          setSelectedConversation(conversation.id);
                          markAsRead(conversation.id);
                        }}
                      >
                        <div className="relative">
                          <Avatar className="h-10 w-10">
                            <AvatarImage src={conversation.participantAvatar} />
                            <AvatarFallback className="bg-gradient-to-r from-blue-600 to-purple-600 text-white">
                              {getInitials(conversation.participantName)}
                            </AvatarFallback>
                          </Avatar>
                          {conversation.isOnline && (
                            <div className="absolute -bottom-1 -right-1 w-3 h-3 bg-green-500 border-2 border-white rounded-full"></div>
                          )}
                        </div>
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center justify-between mb-1">
                            <div className="flex items-center space-x-2">
                              <p className="text-sm font-medium truncate">
                                {conversation.participantName}
                              </p>
                              {conversation.isPinned && (
                                <Pin className="h-3 w-3 text-gray-400" />
                              )}
                            </div>
                            <div className="flex items-center space-x-1">
                              <span className="text-xs text-gray-500">
                                {formatTimestamp(conversation.lastMessage.timestamp)}
                              </span>
                              {conversation.unreadCount > 0 && (
                                <Badge variant="destructive" className="h-5 w-5 flex items-center justify-center p-0 text-xs">
                                  {conversation.unreadCount}
                                </Badge>
                              )}
                            </div>
                          </div>
                          <p className="text-xs text-gray-500 mb-1">{conversation.participantRole}</p>
                          <p className="text-sm text-gray-600 truncate">
                            {conversation.lastMessage.senderId === 'current-user' ? 'You: ' : ''}
                            {conversation.lastMessage.content}
                          </p>
                        </div>
                      </div>
                    ))
                  )}
                </div>
              </ScrollArea>
            ) : (
              /* Chat View */
              <div className="flex flex-col h-full">
                {/* Chat Header */}
                <div className="p-4 border-b flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => setSelectedConversation(null)}
                      className="p-1"
                    >
                      ←
                    </Button>
                    <Avatar className="h-8 w-8">
                      <AvatarImage src={selectedConv?.participantAvatar} />
                      <AvatarFallback className="bg-gradient-to-r from-blue-600 to-purple-600 text-white text-sm">
                        {selectedConv ? getInitials(selectedConv.participantName) : ''}
                      </AvatarFallback>
                    </Avatar>
                    <div>
                      <p className="text-sm font-medium">{selectedConv?.participantName}</p>
                      <p className="text-xs text-gray-500">{selectedConv?.participantRole}</p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-1">
                    <Button variant="ghost" size="sm">
                      <Phone className="h-4 w-4" />
                    </Button>
                    <Button variant="ghost" size="sm">
                      <Video className="h-4 w-4" />
                    </Button>
                  </div>
                </div>

                {/* Messages */}
                <ScrollArea className="flex-1 p-4">
                  <div className="space-y-4">
                    {conversationMessages.map((message) => (
                      <div
                        key={message.id}
                        className={`flex ${message.senderId === 'current-user' ? 'justify-end' : 'justify-start'}`}
                      >
                        <div
                          className={`max-w-xs px-3 py-2 rounded-lg ${
                            message.senderId === 'current-user'
                              ? 'bg-blue-600 text-white'
                              : 'bg-gray-100 text-gray-900'
                          }`}
                        >
                          <p className="text-sm">{message.content}</p>
                          <div className="flex items-center justify-end space-x-1 mt-1">
                            <span className={`text-xs ${
                              message.senderId === 'current-user' ? 'text-blue-100' : 'text-gray-500'
                            }`}>
                              {formatTimestamp(message.timestamp)}
                            </span>
                            {message.senderId === 'current-user' && (
                              <CheckCheck className={`h-3 w-3 ${message.read ? 'text-blue-200' : 'text-blue-300'}`} />
                            )}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </ScrollArea>

                {/* Message Input */}
                <div className="p-4 border-t">
                  <div className="flex items-center space-x-2">
                    <Input
                      placeholder="Type a message..."
                      value={newMessage}
                      onChange={(e) => setNewMessage(e.target.value)}
                      onKeyPress={(e) => e.key === 'Enter' && sendMessage()}
                      className="flex-1"
                    />
                    <Button onClick={sendMessage} size="sm" disabled={!newMessage.trim()}>
                      <Send className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </div>
            )}
          </div>
        </DropdownMenuContent>
      </DropdownMenu>

      {/* Full Messages Dialog */}
      <Dialog>
        <DialogTrigger asChild>
          <Button variant="ghost" className="sr-only">
            Open Messages
          </Button>
        </DialogTrigger>
        <DialogContent className="sm:max-w-[800px] h-[600px]">
          <DialogHeader>
            <DialogTitle>Messages</DialogTitle>
            <DialogDescription>
              Communicate with your team members
            </DialogDescription>
          </DialogHeader>
          {/* Full messages interface would go here */}
        </DialogContent>
      </Dialog>
    </>
  );
};

export default MessagesCenter;
