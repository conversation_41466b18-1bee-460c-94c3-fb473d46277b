import React, { useState, useEffect } from 'react';
import { 
  MessageSquare, 
  Send, 
  Phone, 
  Video, 
  MoreHorizontal,
  Search,
  Pin,
  Archive,
  Trash2,
  Circle,
  CheckCheck,
  Clock
} from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';
import { Textarea } from '@/components/ui/textarea';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import { MessageService, MessageChannel } from '@/lib/messageService';
import { NotificationService } from '@/lib/notificationService';
import { useToast } from '@/components/ui/use-toast';

interface MessageNotification {
  id: string;
  type: 'info' | 'warning' | 'success' | 'error';
  title: string;
  message: string;
  timestamp: Date;
  read: boolean;
  category: string;
  actionUrl?: string;
  action_label?: string;
  priority: 'low' | 'medium' | 'high' | 'urgent';
  related_entity_type?: string;
  related_entity_id?: string;
  channel_name?: string;
  sender_name?: string;
}

interface MessagesCenterProps {
  className?: string;
}

const MessagesCenter: React.FC<MessagesCenterProps> = ({ className = '' }) => {
  const navigate = useNavigate();
  const { user } = useAuth();
  const { toast } = useToast();

  const [isOpen, setIsOpen] = useState(false);
  const [messageNotifications, setMessageNotifications] = useState<MessageNotification[]>([]);
  const [channels, setChannels] = useState<MessageChannel[]>([]);
  const [loading, setLoading] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');

  // Load message notifications from the notification system
  const loadMessageNotifications = async () => {
    if (!user) return;

    try {
      setLoading(true);

      // Get message-related notifications
      const result = await NotificationService.getUserNotifications(user.id, {
        limit: 50,
        category: 'message',
        include_expired: false
      });

      const formattedNotifications: MessageNotification[] = result.notifications.map(n => ({
        id: n.id,
        type: n.type,
        title: n.title,
        message: n.message,
        timestamp: new Date(n.created_at),
        read: n.read,
        category: n.category,
        actionUrl: n.action_url,
        action_label: n.action_label,
        priority: n.priority,
        related_entity_type: n.related_entity_type,
        related_entity_id: n.related_entity_id,
        channel_name: n.metadata?.channel_name,
        sender_name: n.metadata?.sender_name
      }));

      setMessageNotifications(formattedNotifications);

      // Also load available channels
      const channelsData = await MessageService.getChannels();
      setChannels(channelsData);

    } catch (error) {
      console.error('Error loading message notifications:', error);
      toast({
        title: "Error",
        description: "Failed to load message notifications",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  // Load data on component mount and when user changes
  useEffect(() => {
    if (user) {
      loadMessageNotifications();
    }
  }, [user]);

  // Subscribe to real-time message notifications
  useEffect(() => {
    if (!user) return;

    let subscription: any = null;

    try {
      subscription = NotificationService.subscribeToUserNotifications(
        user.id,
        (newNotification) => {
          if (newNotification.category === 'message') {
            const formattedNotification: MessageNotification = {
              id: newNotification.id,
              type: newNotification.type,
              title: newNotification.title,
              message: newNotification.message,
              timestamp: new Date(newNotification.created_at),
              read: newNotification.read,
              category: newNotification.category,
              actionUrl: newNotification.action_url,
              action_label: newNotification.action_label,
              priority: newNotification.priority,
              related_entity_type: newNotification.related_entity_type,
              related_entity_id: newNotification.related_entity_id,
              channel_name: newNotification.metadata?.channel_name,
              sender_name: newNotification.metadata?.sender_name
            };

            setMessageNotifications(prev => [formattedNotification, ...prev]);

            // Show toast for new message notifications
            if (!newNotification.read) {
              toast({
                title: newNotification.title,
                description: newNotification.message,
              });
            }
          }
        }
      );
    } catch (error) {
      console.error('Error subscribing to message notifications:', error);
    }

    return () => {
      if (subscription) {
        try {
          NotificationService.unsubscribeFromNotifications(subscription);
        } catch (error) {
          console.error('Error unsubscribing from message notifications:', error);
        }
      }
    };
  }, [user, toast]);

  const totalUnreadCount = messageNotifications.filter(n => !n.read).length;

  const formatTimestamp = (timestamp: Date) => {
    const now = new Date();
    const diff = now.getTime() - timestamp.getTime();
    const minutes = Math.floor(diff / (1000 * 60));
    const hours = Math.floor(diff / (1000 * 60 * 60));
    const days = Math.floor(diff / (1000 * 60 * 60 * 24));

    if (minutes < 60) {
      return `${minutes}m`;
    } else if (hours < 24) {
      return `${hours}h`;
    } else {
      return `${days}d`;
    }
  };

  const getInitials = (name: string) => {
    return name.split(' ').map(n => n[0]).join('').toUpperCase();
  };

  const markNotificationAsRead = async (notificationId: string) => {
    if (!user) return;

    try {
      await NotificationService.markAsRead(user.id, [notificationId]);
      setMessageNotifications(prev =>
        prev.map(n => n.id === notificationId ? { ...n, read: true } : n)
      );
    } catch (error) {
      console.error('Error marking notification as read:', error);
    }
  };

  const handleNotificationClick = async (notification: MessageNotification) => {
    // Mark as read
    if (!notification.read) {
      await markNotificationAsRead(notification.id);
    }

    // Navigate to the relevant page
    if (notification.actionUrl) {
      navigate(notification.actionUrl);
    } else if (notification.related_entity_type === 'channel' && notification.related_entity_id) {
      navigate(`/messages?channel=${notification.related_entity_id}`);
    } else {
      navigate('/messages');
    }

    setIsOpen(false);
  };

  const getNotificationIcon = (type: string, priority: string) => {
    if (priority === 'urgent') return '🔴';
    if (priority === 'high') return '🟠';
    if (type === 'success') return '✅';
    if (type === 'warning') return '⚠️';
    if (type === 'error') return '❌';
    return '💬';
  };

  const filteredNotifications = messageNotifications.filter(notification =>
    notification.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
    notification.message.toLowerCase().includes(searchQuery.toLowerCase()) ||
    (notification.sender_name && notification.sender_name.toLowerCase().includes(searchQuery.toLowerCase())) ||
    (notification.channel_name && notification.channel_name.toLowerCase().includes(searchQuery.toLowerCase()))
  );

  return (
    <>
      <DropdownMenu open={isOpen} onOpenChange={setIsOpen}>
        <DropdownMenuTrigger asChild>
          <Button variant="ghost" size="sm" className={`relative hover:bg-gray-100 ${className}`}>
            <MessageSquare className="w-5 h-5" />
            {totalUnreadCount > 0 && (
              <Badge 
                variant="destructive" 
                className="absolute -top-1 -right-1 h-5 w-5 flex items-center justify-center p-0 text-xs"
              >
                {totalUnreadCount > 9 ? '9+' : totalUnreadCount}
              </Badge>
            )}
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end" className="w-80 p-0 h-96">
          <div className="flex flex-col h-full">
            {/* Header */}
            <div className="p-4 border-b">
              <div className="flex items-center justify-between mb-3">
                <h3 className="font-semibold">Message Notifications</h3>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => navigate('/messages')}
                  title="Open Messages"
                >
                  <MessageSquare className="h-4 w-4" />
                </Button>
              </div>
              <div className="relative">
                <Search className="absolute left-2 top-2.5 h-4 w-4 text-gray-400" />
                <Input
                  placeholder="Search notifications..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-8 h-9"
                />
              </div>
            </div>

            {/* Message Notifications List */}
            <ScrollArea className="flex-1">
              <div className="p-2">
                {loading ? (
                  <div className="text-center py-8 text-gray-500">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-2"></div>
                    <p className="text-sm">Loading notifications...</p>
                  </div>
                ) : filteredNotifications.length === 0 ? (
                  <div className="text-center py-8 text-gray-500">
                    <MessageSquare className="h-8 w-8 mx-auto mb-2 opacity-50" />
                    <p className="text-sm">No message notifications</p>
                    <Button
                      variant="outline"
                      size="sm"
                      className="mt-2"
                      onClick={() => navigate('/messages')}
                    >
                      Go to Messages
                    </Button>
                  </div>
                ) : (
                  filteredNotifications.map((notification) => (
                    <div
                      key={notification.id}
                      className={`flex items-start space-x-3 p-3 rounded-lg hover:bg-gray-50 cursor-pointer transition-colors border-l-4 ${
                        notification.read
                          ? 'border-l-gray-200 bg-white'
                          : 'border-l-blue-500 bg-blue-50'
                      }`}
                      onClick={() => handleNotificationClick(notification)}
                    >
                      <div className="flex-shrink-0 mt-1">
                        <span className="text-lg">
                          {getNotificationIcon(notification.type, notification.priority)}
                        </span>
                      </div>
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center justify-between mb-1">
                          <p className={`text-sm font-medium truncate ${
                            notification.read ? 'text-gray-900' : 'text-blue-900'
                          }`}>
                            {notification.title}
                          </p>
                          <span className="text-xs text-gray-500 ml-2">
                            {formatTimestamp(notification.timestamp)}
                          </span>
                        </div>
                        <p className={`text-sm truncate ${
                          notification.read ? 'text-gray-600' : 'text-blue-800'
                        }`}>
                          {notification.message}
                        </p>
                        {(notification.channel_name || notification.sender_name) && (
                          <div className="flex items-center mt-1 space-x-2">
                            {notification.channel_name && (
                              <Badge variant="outline" className="text-xs">
                                #{notification.channel_name}
                              </Badge>
                            )}
                            {notification.sender_name && (
                              <Badge variant="secondary" className="text-xs">
                                {notification.sender_name}
                              </Badge>
                            )}
                          </div>
                        )}
                      </div>
                      {!notification.read && (
                        <div className="flex-shrink-0">
                          <div className="w-2 h-2 bg-blue-600 rounded-full"></div>
                        </div>
                          )}
                    </div>
                  ))
                )}
              </div>
            </ScrollArea>

            {/* Footer */}
            <div className="p-4 border-t">
              <div className="flex items-center justify-between">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => navigate('/messages')}
                  className="flex-1"
                >
                  <MessageSquare className="h-4 w-4 mr-2" />
                  Open Messages
                </Button>
              </div>
            </div>
          </div>
        </DropdownMenuContent>
      </DropdownMenu>
    </>
  );
};

export default MessagesCenter;
