import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Switch } from '@/components/ui/switch';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useToast } from '@/components/ui/use-toast';
import { useAuth } from '@/contexts/AuthContext';
import { UserService, UserRole } from '@/lib/userService';
import { PERMISSIONS, hasPermission } from '@/lib/permissions';
import {
  Shield, Plus, Edit, Trash2, Users, Settings, Lock, Unlock,
  CheckCircle, XCircle, AlertTriangle, Eye, Key, Crown
} from 'lucide-react';

const CompanyPermissions: React.FC = () => {
  const { toast } = useToast();
  const { user, profile } = useAuth();
  const [loading, setLoading] = useState(false);
  const [roles, setRoles] = useState<UserRole[]>([]);
  const [selectedRole, setSelectedRole] = useState<UserRole | null>(null);
  const [isCreateRoleDialogOpen, setIsCreateRoleDialogOpen] = useState(false);
  const [isEditRoleDialogOpen, setIsEditRoleDialogOpen] = useState(false);

  // Form state for role creation/editing
  const [roleForm, setRoleForm] = useState({
    role_name: '',
    role_display_name: '',
    description: '',
    permissions: {} as Record<string, boolean>
  });

  useEffect(() => {
    loadRoles();
  }, []);

  const loadRoles = async () => {
    try {
      setLoading(true);
      const rolesData = await UserService.getAllRoles();
      setRoles(rolesData);
    } catch (error) {
      console.error('Error loading roles:', error);
      toast({
        title: "Error",
        description: "Failed to load roles",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const resetRoleForm = () => {
    setRoleForm({
      role_name: '',
      role_display_name: '',
      description: '',
      permissions: {}
    });
  };

  const handleCreateRole = async () => {
    try {
      // This would be implemented with actual role creation logic
      toast({
        title: "Success",
        description: "Role created successfully",
      });
      setIsCreateRoleDialogOpen(false);
      resetRoleForm();
      loadRoles();
    } catch (error) {
      console.error('Error creating role:', error);
      toast({
        title: "Error",
        description: "Failed to create role",
        variant: "destructive",
      });
    }
  };

  const handleEditRole = (role: UserRole) => {
    setSelectedRole(role);
    setRoleForm({
      role_name: role.role_name,
      role_display_name: role.role_display_name,
      description: role.description || '',
      permissions: role.permissions || {}
    });
    setIsEditRoleDialogOpen(true);
  };

  const handleUpdateRole = async () => {
    if (!selectedRole) return;

    try {
      // This would be implemented with actual role update logic
      toast({
        title: "Success",
        description: "Role updated successfully",
      });
      setIsEditRoleDialogOpen(false);
      setSelectedRole(null);
      resetRoleForm();
      loadRoles();
    } catch (error) {
      console.error('Error updating role:', error);
      toast({
        title: "Error",
        description: "Failed to update role",
        variant: "destructive",
      });
    }
  };

  const handleDeleteRole = async (roleId: string, roleName: string) => {
    if (!confirm(`Are you sure you want to delete the role "${roleName}"? This action cannot be undone.`)) {
      return;
    }

    try {
      // This would be implemented with actual role deletion logic
      toast({
        title: "Success",
        description: "Role deleted successfully",
      });
      loadRoles();
    } catch (error) {
      console.error('Error deleting role:', error);
      toast({
        title: "Error",
        description: "Failed to delete role",
        variant: "destructive",
      });
    }
  };

  const getRoleIcon = (roleName: string) => {
    switch (roleName.toLowerCase()) {
      case 'admin':
        return <Crown className="w-4 h-4 text-red-500" />;
      case 'management':
        return <Shield className="w-4 h-4 text-purple-500" />;
      case 'accountant':
        return <Key className="w-4 h-4 text-blue-500" />;
      case 'qs':
        return <Settings className="w-4 h-4 text-green-500" />;
      case 'client':
        return <Users className="w-4 h-4 text-gray-500" />;
      default:
        return <Shield className="w-4 h-4 text-gray-500" />;
    }
  };

  const getRoleBadge = (role: UserRole) => {
    const roleColors: Record<string, string> = {
      'admin': 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300',
      'management': 'bg-purple-100 text-purple-800 dark:bg-purple-900/30 dark:text-purple-300',
      'accountant': 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300',
      'qs': 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300',
      'client': 'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300'
    };

    const colorClass = roleColors[role.role_name] || 'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300';

    return (
      <Badge className={colorClass}>
        {getRoleIcon(role.role_name)}
        <span className="ml-1">{role.role_display_name}</span>
      </Badge>
    );
  };

  const getPermissionGroups = () => {
    const groups: Record<string, string[]> = {
      'User Management': [],
      'Financial Management': [],
      'Project Management': [],
      'QS Functions': [],
      'Reports & Analytics': [],
      'System Settings': [],
      'Basic Access': []
    };

    Object.keys(PERMISSIONS).forEach(permission => {
      if (permission.includes('USER') || permission.includes('USERS')) {
        groups['User Management'].push(permission);
      } else if (permission.includes('FINANCIAL') || permission.includes('REVENUE') || permission.includes('INVOICE') || permission.includes('CASH') || permission.includes('ASSET')) {
        groups['Financial Management'].push(permission);
      } else if (permission.includes('PROJECT')) {
        groups['Project Management'].push(permission);
      } else if (permission.includes('QUANTITIES') || permission.includes('ESTIMATES')) {
        groups['QS Functions'].push(permission);
      } else if (permission.includes('REPORT') || permission.includes('EXPORT')) {
        groups['Reports & Analytics'].push(permission);
      } else if (permission.includes('SYSTEM') || permission.includes('AUDIT') || permission.includes('ROLES')) {
        groups['System Settings'].push(permission);
      } else {
        groups['Basic Access'].push(permission);
      }
    });

    return groups;
  };

  const permissionGroups = getPermissionGroups();

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white">Permissions & Roles</h2>
          <p className="text-gray-600 dark:text-gray-400">Manage user roles and their permissions</p>
        </div>
        
        <Dialog open={isCreateRoleDialogOpen} onOpenChange={setIsCreateRoleDialogOpen}>
          <DialogTrigger asChild>
            <Button>
              <Plus className="w-4 h-4 mr-2" />
              Create Role
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-2xl">
            <DialogHeader>
              <DialogTitle>Create New Role</DialogTitle>
              <DialogDescription>
                Define a new role with specific permissions
              </DialogDescription>
            </DialogHeader>
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="role_name">Role Name</Label>
                  <Input
                    id="role_name"
                    value={roleForm.role_name}
                    onChange={(e) => setRoleForm(prev => ({ ...prev, role_name: e.target.value }))}
                    placeholder="e.g., supervisor"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="role_display_name">Display Name</Label>
                  <Input
                    id="role_display_name"
                    value={roleForm.role_display_name}
                    onChange={(e) => setRoleForm(prev => ({ ...prev, role_display_name: e.target.value }))}
                    placeholder="e.g., Site Supervisor"
                  />
                </div>
              </div>
              <div className="space-y-2">
                <Label htmlFor="description">Description</Label>
                <Textarea
                  id="description"
                  value={roleForm.description}
                  onChange={(e) => setRoleForm(prev => ({ ...prev, description: e.target.value }))}
                  placeholder="Describe the role's responsibilities..."
                />
              </div>
              <div className="flex justify-end space-x-2">
                <Button variant="outline" onClick={() => setIsCreateRoleDialogOpen(false)}>
                  Cancel
                </Button>
                <Button onClick={handleCreateRole}>
                  Create Role
                </Button>
              </div>
            </div>
          </DialogContent>
        </Dialog>
      </div>

      <Tabs defaultValue="roles" className="space-y-6">
        <TabsList>
          <TabsTrigger value="roles">Roles</TabsTrigger>
          <TabsTrigger value="permissions">Permissions Matrix</TabsTrigger>
        </TabsList>

        <TabsContent value="roles" className="space-y-6">
          {/* Roles Table */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Shield className="w-5 h-5 mr-2" />
                System Roles ({roles.length})
              </CardTitle>
            </CardHeader>
            <CardContent>
              {loading ? (
                <div className="space-y-4">
                  {[...Array(5)].map((_, i) => (
                    <div key={i} className="flex items-center space-x-4 p-4 animate-pulse">
                      <div className="w-8 h-8 bg-gray-200 rounded"></div>
                      <div className="flex-1 space-y-2">
                        <div className="h-4 bg-gray-200 rounded w-1/4"></div>
                        <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Role</TableHead>
                      <TableHead>Description</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Created</TableHead>
                      <TableHead>Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {roles.map((role) => (
                      <TableRow key={role.id}>
                        <TableCell>
                          <div className="flex items-center space-x-3">
                            {getRoleIcon(role.role_name)}
                            <div>
                              <p className="font-medium">{role.role_display_name}</p>
                              <p className="text-sm text-gray-500">{role.role_name}</p>
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <p className="text-sm">{role.description || 'No description provided'}</p>
                        </TableCell>
                        <TableCell>
                          {role.is_active ? (
                            <Badge className="bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300">
                              <CheckCircle className="w-3 h-3 mr-1" />
                              Active
                            </Badge>
                          ) : (
                            <Badge variant="secondary">
                              <XCircle className="w-3 h-3 mr-1" />
                              Inactive
                            </Badge>
                          )}
                        </TableCell>
                        <TableCell>
                          {new Date(role.created_at).toLocaleDateString()}
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center space-x-2">
                            <Button variant="ghost" size="sm" onClick={() => handleEditRole(role)}>
                              <Edit className="w-4 h-4" />
                            </Button>
                            <Button 
                              variant="ghost" 
                              size="sm" 
                              onClick={() => handleDeleteRole(role.id, role.role_display_name)}
                              className="text-red-600 hover:text-red-700"
                            >
                              <Trash2 className="w-4 h-4" />
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="permissions" className="space-y-6">
          {/* Permissions Matrix */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Key className="w-5 h-5 mr-2" />
                Permissions Matrix
              </CardTitle>
              <CardDescription>
                View and manage permissions for each role
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                {Object.entries(permissionGroups).map(([groupName, permissions]) => (
                  <div key={groupName} className="space-y-3">
                    <h3 className="text-lg font-semibold text-gray-900 dark:text-white">{groupName}</h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
                      {permissions.map((permission) => (
                        <div key={permission} className="p-3 border rounded-lg bg-gray-50 dark:bg-gray-800">
                          <div className="flex items-center justify-between">
                            <span className="text-sm font-medium">{permission.replace(/_/g, ' ')}</span>
                            <Badge variant="outline" className="text-xs">
                              {PERMISSIONS[permission as keyof typeof PERMISSIONS]}
                            </Badge>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Edit Role Dialog */}
      <Dialog open={isEditRoleDialogOpen} onOpenChange={setIsEditRoleDialogOpen}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Edit Role</DialogTitle>
            <DialogDescription>
              Modify role details and permissions
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="edit_role_name">Role Name</Label>
                <Input
                  id="edit_role_name"
                  value={roleForm.role_name}
                  onChange={(e) => setRoleForm(prev => ({ ...prev, role_name: e.target.value }))}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="edit_role_display_name">Display Name</Label>
                <Input
                  id="edit_role_display_name"
                  value={roleForm.role_display_name}
                  onChange={(e) => setRoleForm(prev => ({ ...prev, role_display_name: e.target.value }))}
                />
              </div>
            </div>
            <div className="space-y-2">
              <Label htmlFor="edit_description">Description</Label>
              <Textarea
                id="edit_description"
                value={roleForm.description}
                onChange={(e) => setRoleForm(prev => ({ ...prev, description: e.target.value }))}
              />
            </div>
            <div className="flex justify-end space-x-2">
              <Button variant="outline" onClick={() => setIsEditRoleDialogOpen(false)}>
                Cancel
              </Button>
              <Button onClick={handleUpdateRole}>
                Update Role
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default CompanyPermissions;
