import React, { useState } from 'react';
import {
  BookOpen,
  Search,
  ChevronRight,
  Clock,
  Star,
  Download,
  ExternalLink,
  Building2,
  Users,
  CreditCard,
  Settings,
  Shield,
  BarChart3,
  FileText,
  MessageSquare,
  Zap,
  PlayCircle
} from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';

interface DocumentationSection {
  id: string;
  title: string;
  description: string;
  icon: React.ReactNode;
  articles: Array<{
    title: string;
    description: string;
    readTime: string;
    difficulty: 'Beginner' | 'Intermediate' | 'Advanced';
    popular?: boolean;
  }>;
}

const Documentation: React.FC = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const [activeSection, setActiveSection] = useState('getting-started');

  const documentationSections: DocumentationSection[] = [
    {
      id: 'getting-started',
      title: 'Getting Started',
      description: 'Learn the basics of Martcosy Construction Management',
      icon: <PlayCircle className="w-5 h-5" />,
      articles: [
        {
          title: 'Quick Start Guide',
          description: 'Get up and running with Martcosy in 5 minutes',
          readTime: '5 min',
          difficulty: 'Beginner',
          popular: true
        },
        {
          title: 'Setting Up Your First Project',
          description: 'Create and configure your first construction project',
          readTime: '8 min',
          difficulty: 'Beginner',
          popular: true
        },
        {
          title: 'Understanding the Dashboard',
          description: 'Navigate and customize your main dashboard',
          readTime: '6 min',
          difficulty: 'Beginner'
        },
        {
          title: 'User Interface Overview',
          description: 'Learn about the main interface elements and navigation',
          readTime: '10 min',
          difficulty: 'Beginner'
        }
      ]
    },
    {
      id: 'project-management',
      title: 'Project Management',
      description: 'Manage projects, tasks, and timelines effectively',
      icon: <Building2 className="w-5 h-5" />,
      articles: [
        {
          title: 'Creating and Managing Projects',
          description: 'Complete guide to project creation and management',
          readTime: '12 min',
          difficulty: 'Intermediate',
          popular: true
        },
        {
          title: 'Gantt Charts and Timeline Management',
          description: 'Use Gantt charts for project scheduling and tracking',
          readTime: '15 min',
          difficulty: 'Intermediate',
          popular: true
        },
        {
          title: 'Task Management and Assignment',
          description: 'Create, assign, and track project tasks',
          readTime: '10 min',
          difficulty: 'Beginner'
        },
        {
          title: 'Project Templates and Workflows',
          description: 'Create reusable project templates and workflows',
          readTime: '18 min',
          difficulty: 'Advanced'
        }
      ]
    },
    {
      id: 'financial-management',
      title: 'Financial Management',
      description: 'Handle invoices, payments, and financial reporting',
      icon: <CreditCard className="w-5 h-5" />,
      articles: [
        {
          title: 'Creating and Managing Invoices',
          description: 'Generate professional invoices and track payments',
          readTime: '12 min',
          difficulty: 'Beginner',
          popular: true
        },
        {
          title: 'Client Financial Management',
          description: 'Track client payments and outstanding amounts',
          readTime: '10 min',
          difficulty: 'Intermediate'
        },
        {
          title: 'Financial Reports and Analytics',
          description: 'Generate comprehensive financial reports',
          readTime: '15 min',
          difficulty: 'Intermediate'
        },
        {
          title: 'Budget Management and Tracking',
          description: 'Set up and monitor project budgets',
          readTime: '14 min',
          difficulty: 'Advanced'
        }
      ]
    },
    {
      id: 'user-management',
      title: 'User Management',
      description: 'Manage team members, roles, and permissions',
      icon: <Users className="w-5 h-5" />,
      articles: [
        {
          title: 'User Roles and Permissions',
          description: 'Set up user roles and configure permissions',
          readTime: '10 min',
          difficulty: 'Intermediate',
          popular: true
        },
        {
          title: 'Adding and Managing Team Members',
          description: 'Invite and manage your team members',
          readTime: '8 min',
          difficulty: 'Beginner'
        },
        {
          title: 'Client Access Management',
          description: 'Configure client access and project visibility',
          readTime: '12 min',
          difficulty: 'Intermediate'
        },
        {
          title: 'Advanced Permission Settings',
          description: 'Configure complex permission scenarios',
          readTime: '20 min',
          difficulty: 'Advanced'
        }
      ]
    },
    {
      id: 'reports-analytics',
      title: 'Reports & Analytics',
      description: 'Generate insights and comprehensive reports',
      icon: <BarChart3 className="w-5 h-5" />,
      articles: [
        {
          title: 'Dashboard Analytics Overview',
          description: 'Understand your dashboard metrics and KPIs',
          readTime: '8 min',
          difficulty: 'Beginner'
        },
        {
          title: 'Custom Report Generation',
          description: 'Create custom reports for your specific needs',
          readTime: '15 min',
          difficulty: 'Intermediate',
          popular: true
        },
        {
          title: 'Exporting and Sharing Reports',
          description: 'Export reports in various formats and share them',
          readTime: '6 min',
          difficulty: 'Beginner'
        },
        {
          title: 'Advanced Analytics and Forecasting',
          description: 'Use advanced analytics for business insights',
          readTime: '25 min',
          difficulty: 'Advanced'
        }
      ]
    },
    {
      id: 'settings-configuration',
      title: 'Settings & Configuration',
      description: 'Customize your workspace and preferences',
      icon: <Settings className="w-5 h-5" />,
      articles: [
        {
          title: 'Company Settings Configuration',
          description: 'Set up your company information and preferences',
          readTime: '10 min',
          difficulty: 'Beginner'
        },
        {
          title: 'Notification Settings',
          description: 'Configure email and system notifications',
          readTime: '8 min',
          difficulty: 'Beginner'
        },
        {
          title: 'System Integration Setup',
          description: 'Integrate with external tools and services',
          readTime: '20 min',
          difficulty: 'Advanced'
        },
        {
          title: 'Backup and Data Management',
          description: 'Configure data backup and management settings',
          readTime: '15 min',
          difficulty: 'Intermediate'
        }
      ]
    }
  ];

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'Beginner':
        return 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400';
      case 'Intermediate':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400';
      case 'Advanced':
        return 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400';
    }
  };

  const filteredSections = documentationSections.filter(section =>
    section.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
    section.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
    section.articles.some(article =>
      article.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      article.description.toLowerCase().includes(searchQuery.toLowerCase())
    )
  );

  return (
    <div className="space-y-8 p-6 max-w-7xl mx-auto">
      {/* Header */}
      <div className="text-center space-y-4">
        <div className="flex items-center justify-center space-x-3">
          <div className="w-12 h-12 rounded-xl bg-blue-50 dark:bg-blue-900/20 flex items-center justify-center">
            <BookOpen className="w-6 h-6 text-blue-600 dark:text-blue-400" />
          </div>
          <div>
            <h1 className="text-3xl font-bold text-gray-900 dark:text-white">Documentation</h1>
            <p className="text-gray-600 dark:text-gray-400">Complete guides and references for Martcosy</p>
          </div>
        </div>

        {/* Search */}
        <div className="max-w-2xl mx-auto">
          <div className="relative">
            <Search className="absolute left-3 top-3 h-5 w-5 text-gray-400" />
            <Input
              placeholder="Search documentation..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10 py-3"
            />
          </div>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="flex flex-wrap gap-4 justify-center">
        <Button variant="outline" onClick={() => window.open('https://help.martcosy.com/api-docs', '_blank')}>
          <FileText className="w-4 h-4 mr-2" />
          API Reference
          <ExternalLink className="w-3 h-3 ml-2" />
        </Button>
        <Button variant="outline" onClick={() => window.open('https://help.martcosy.com/downloads', '_blank')}>
          <Download className="w-4 h-4 mr-2" />
          Downloads
          <ExternalLink className="w-3 h-3 ml-2" />
        </Button>
        <Button variant="outline" onClick={() => window.open('https://help.martcosy.com/changelog', '_blank')}>
          <Zap className="w-4 h-4 mr-2" />
          Changelog
          <ExternalLink className="w-3 h-3 ml-2" />
        </Button>
      </div>

      {/* Documentation Sections */}
      <Tabs value={activeSection} onValueChange={setActiveSection} className="w-full">
        <TabsList className="grid w-full grid-cols-3 lg:grid-cols-6">
          {documentationSections.map((section) => (
            <TabsTrigger key={section.id} value={section.id} className="text-xs">
              {section.icon}
              <span className="ml-1 hidden sm:inline">{section.title.split(' ')[0]}</span>
            </TabsTrigger>
          ))}
        </TabsList>

        {documentationSections.map((section) => (
          <TabsContent key={section.id} value={section.id} className="space-y-6">
            <div className="text-center space-y-2">
              <h2 className="text-2xl font-bold text-gray-900 dark:text-white">{section.title}</h2>
              <p className="text-gray-600 dark:text-gray-400">{section.description}</p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {section.articles.map((article, index) => (
                <Card key={index} className="cursor-pointer hover:shadow-lg transition-shadow">
                  <CardContent className="p-6">
                    <div className="space-y-4">
                      <div className="flex items-start justify-between">
                        <div className="space-y-2 flex-1">
                          <div className="flex items-center gap-2">
                            <h3 className="font-semibold text-gray-900 dark:text-white">{article.title}</h3>
                            {article.popular && (
                              <Badge variant="secondary" className="text-xs">
                                <Star className="w-3 h-3 mr-1" />
                                Popular
                              </Badge>
                            )}
                          </div>
                          <p className="text-sm text-gray-600 dark:text-gray-400">{article.description}</p>
                        </div>
                        <ChevronRight className="w-5 h-5 text-gray-400 flex-shrink-0 ml-2" />
                      </div>
                      
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-3">
                          <div className="flex items-center gap-1 text-xs text-gray-500">
                            <Clock className="w-3 h-3" />
                            {article.readTime}
                          </div>
                          <Badge className={`text-xs ${getDifficultyColor(article.difficulty)}`}>
                            {article.difficulty}
                          </Badge>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>
        ))}
      </Tabs>

      {/* Additional Resources */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <MessageSquare className="w-5 h-5" />
            Need More Help?
          </CardTitle>
          <CardDescription>
            Can't find what you're looking for? We're here to help!
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Button variant="outline" className="h-auto p-4 flex-col space-y-2">
              <MessageSquare className="w-6 h-6" />
              <span className="font-medium">Live Chat</span>
              <span className="text-xs text-gray-500">Get instant help</span>
            </Button>
            <Button variant="outline" className="h-auto p-4 flex-col space-y-2">
              <FileText className="w-6 h-6" />
              <span className="font-medium">Submit Ticket</span>
              <span className="text-xs text-gray-500">Detailed support</span>
            </Button>
            <Button variant="outline" className="h-auto p-4 flex-col space-y-2">
              <Users className="w-6 h-6" />
              <span className="font-medium">Community</span>
              <span className="text-xs text-gray-500">Ask the community</span>
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default Documentation;
