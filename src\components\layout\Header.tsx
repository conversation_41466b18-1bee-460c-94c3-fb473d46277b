
import React, { useState } from 'react';
import {
  Bell,
  Search,
  Settings,
  User,
  MessageSquare,
  HelpCircle,
  Zap,
  Sun,
  Moon,
  Menu,
  X
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import NotificationCenter from './NotificationCenter';
import AccountMenu from './AccountMenu';
import GlobalSearch from './GlobalSearch';
import MessagesCenter from './MessagesCenter';
import QuickActions from './QuickActions';
import { useToast } from '@/components/ui/use-toast';
import { useTheme } from '@/contexts/ThemeContext';

interface HeaderProps {
  onMenuToggle?: () => void;
  isMobileMenuOpen?: boolean;
}

const Header: React.FC<HeaderProps> = ({ onMenuToggle, isMobileMenuOpen = false }) => {
  const { toast } = useToast();
  const { theme, toggleTheme } = useTheme();

  const handleToggleTheme = () => {
    toggleTheme();
    toast({
      title: theme === 'light' ? "Dark Mode Enabled" : "Light Mode Enabled",
      description: `Switched to ${theme === 'light' ? 'dark' : 'light'} mode.`,
    });
  };

  const handleQuickAction = (action: string) => {
    toast({
      title: "Quick Action",
      description: `${action} feature coming soon!`,
    });
  };

  return (
    <header className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 px-4 lg:px-6 py-4 sticky top-0 z-50 backdrop-blur-sm bg-white/95 dark:bg-gray-800/95">
      <div className="flex items-center justify-between">
        {/* Left Section */}
        <div className="flex items-center space-x-4">
          {/* Mobile Menu Toggle */}
          <Button
            variant="ghost"
            size="sm"
            onClick={onMenuToggle}
            className="lg:hidden"
          >
            {isMobileMenuOpen ? <X className="w-5 h-5" /> : <Menu className="w-5 h-5" />}
          </Button>

          {/* Logo/Title */}
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-gradient-to-r from-blue-600 to-orange-500 rounded-xl flex items-center justify-center shadow-lg">
              <span className="text-white font-bold text-lg">MC</span>
            </div>
            <div className="hidden sm:block">
              <h1 className="text-xl font-bold text-gray-900 dark:text-white">Martcosy</h1>
              <p className="text-xs text-gray-500 dark:text-gray-400">Construction Management</p>
            </div>
          </div>
        </div>

        {/* Center Section - Search */}
        <div className="hidden md:flex flex-1 justify-center max-w-2xl mx-8">
          <GlobalSearch className="w-full max-w-md" />
        </div>

        {/* Right Section */}
        <div className="flex items-center space-x-2">
          {/* Mobile Search */}
          <Button
            variant="ghost"
            size="sm"
            className="md:hidden"
            onClick={() => handleQuickAction('Mobile Search')}
          >
            <Search className="w-5 h-5" />
          </Button>

          {/* Quick Actions */}
          <div className="hidden lg:flex items-center space-x-1">
            <QuickActions />

            <Button
              variant="ghost"
              size="sm"
              onClick={handleToggleTheme}
              className="text-gray-600 hover:text-gray-900 dark:text-gray-300 dark:hover:text-white"
            >
              {theme === 'dark' ? <Sun className="w-4 h-4" /> : <Moon className="w-4 h-4" />}
            </Button>
          </div>

          <Separator orientation="vertical" className="h-6 hidden lg:block" />

          {/* Messages */}
          <MessagesCenter />

          {/* Notifications */}
          <NotificationCenter />

          {/* Help */}
          <Button
            variant="ghost"
            size="sm"
            onClick={() => handleQuickAction('Help & Support')}
            className="text-gray-600 hover:text-gray-900 hidden sm:flex"
          >
            <HelpCircle className="w-5 h-5" />
          </Button>

          <Separator orientation="vertical" className="h-6 hidden sm:block" />

          {/* Account Menu */}
          <AccountMenu />
        </div>
      </div>

      {/* Mobile Search Bar */}
      <div className="md:hidden mt-4">
        <GlobalSearch className="w-full" />
      </div>
    </header>
  );
};

export default Header;
