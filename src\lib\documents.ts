import { supabase } from './supabase';

// Database types
export interface DocumentItem {
  id: string;
  description: string;
  quantity: number;
  unit: string;
  unit_price: number;
  total: number;
}

export interface Document {
  id: string;
  type: 'quotation' | 'invoice';
  document_number: string;
  title: string;
  client_name: string;
  client_email: string;
  client_address: string;
  client_phone: string;
  project_name: string;
  issue_date: string;
  due_date: string;
  status: string;
  items: DocumentItem[];
  subtotal: number;
  tax_rate: number;
  tax_amount: number;
  discount_rate: number;
  discount_amount: number;
  total_amount: number;
  notes: string;
  terms_conditions: string;
  company_info: CompanyInfo;
  date_added: string;
  last_modified: string;
  created_at: string;
  // Creator information
  created_by_user_id?: string;
  created_by_name?: string;
  created_by_avatar?: string;
}

export interface CompanyInfo {
  name: string;
  logo_url: string;
  address: string;
  phone: string;
  email: string;
  website: string;
  tax_number: string;
  registration_number: string;
}

// Document status options
export const DOCUMENT_STATUSES = {
  quotation: ['Draft', 'Sent', 'Accepted', 'Rejected', 'Expired'],
  invoice: ['Draft', 'Sent', 'Paid', 'Overdue', 'Cancelled']
};

// Common units
export const COMMON_UNITS = [
  'pcs', 'kg', 'm', 'm²', 'm³', 'hrs', 'days', 'weeks', 'months',
  'tons', 'bags', 'boxes', 'rolls', 'sheets', 'litres', 'gallons'
];

// Default company info
export const DEFAULT_COMPANY_INFO: CompanyInfo = {
  name: 'Your Construction Company',
  logo_url: '',
  address: '123 Construction Street\nHarare, Zimbabwe',
  phone: '+263 4 123 4567',
  email: '<EMAIL>',
  website: 'www.yourcompany.com',
  tax_number: 'TAX123456789',
  registration_number: 'REG987654321'
};

// Database operations
export class DocumentService {
  // Test database connection
  static async testConnection(): Promise<{ success: boolean; message: string; details?: any }> {
    try {
      console.log('Testing documents table connection...');
      
      const { data, error } = await supabase
        .from('documents')
        .select('count', { count: 'exact', head: true });
      
      if (error) {
        console.error('Documents connection error:', error);
        return {
          success: false,
          message: `Database error: ${error.message}`,
          details: error
        };
      }
      
      console.log('Documents connection test successful');
      return {
        success: true,
        message: 'Successfully connected to documents table',
        details: { count: data }
      };
    } catch (error) {
      console.error('Documents connection test failed:', error);
      return {
        success: false,
        message: error instanceof Error ? error.message : 'Unknown connection error',
        details: error
      };
    }
  }

  // Get all documents
  static async getDocuments(): Promise<Document[]> {
    try {
      console.log('Fetching documents from Supabase...');
      
      const { data, error } = await supabase
        .from('documents')
        .select('*')
        .order('date_added', { ascending: false });
      
      if (error) {
        console.error('Error fetching documents:', error);
        throw new Error(`Database error: ${error.message}`);
      }
      
      console.log('Successfully fetched documents:', data);
      return data || [];
    } catch (error) {
      console.error('Failed to fetch documents:', error);
      throw error;
    }
  }

  // Add new document
  static async addDocument(document: Omit<Document, 'id' | 'date_added' | 'last_modified' | 'created_at'>): Promise<Document> {
    try {
      const { data, error } = await supabase
        .from('documents')
        .insert([{
          type: document.type,
          document_number: document.document_number,
          title: document.title,
          client_name: document.client_name,
          client_email: document.client_email,
          client_address: document.client_address,
          client_phone: document.client_phone,
          project_name: document.project_name,
          issue_date: document.issue_date,
          due_date: document.due_date,
          status: document.status,
          items: document.items,
          subtotal: document.subtotal,
          tax_rate: document.tax_rate,
          tax_amount: document.tax_amount,
          discount_rate: document.discount_rate,
          discount_amount: document.discount_amount,
          total_amount: document.total_amount,
          notes: document.notes,
          terms_conditions: document.terms_conditions,
          company_info: document.company_info
        }])
        .select()
        .single();
      
      if (error) {
        console.error('Error adding document:', error);
        throw error;
      }
      
      return data;
    } catch (error) {
      console.error('Failed to add document:', error);
      throw error;
    }
  }

  // Update document
  static async updateDocument(id: string, updates: Partial<Omit<Document, 'id' | 'date_added' | 'created_at'>>): Promise<Document> {
    try {
      const { data, error } = await supabase
        .from('documents')
        .update({
          ...updates,
          last_modified: new Date().toISOString()
        })
        .eq('id', id)
        .select()
        .single();
      
      if (error) {
        console.error('Error updating document:', error);
        throw error;
      }
      
      return data;
    } catch (error) {
      console.error('Failed to update document:', error);
      throw error;
    }
  }

  // Delete document
  static async deleteDocument(id: string): Promise<void> {
    try {
      const { error } = await supabase
        .from('documents')
        .delete()
        .eq('id', id);
      
      if (error) {
        console.error('Error deleting document:', error);
        throw error;
      }
    } catch (error) {
      console.error('Failed to delete document:', error);
      throw error;
    }
  }

  // Generate document number
  static generateDocumentNumber(type: 'quotation' | 'invoice'): string {
    const prefix = type === 'quotation' ? 'QUO' : 'INV';
    const date = new Date();
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const timestamp = Date.now().toString().slice(-4);
    
    return `${prefix}-${year}${month}-${timestamp}`;
  }

  // Calculate document totals
  static calculateTotals(items: DocumentItem[], taxRate: number, discountRate: number) {
    const subtotal = items.reduce((sum, item) => sum + item.total, 0);
    const discountAmount = (subtotal * discountRate) / 100;
    const taxableAmount = subtotal - discountAmount;
    const taxAmount = (taxableAmount * taxRate) / 100;
    const totalAmount = taxableAmount + taxAmount;

    return {
      subtotal,
      discountAmount,
      taxAmount,
      totalAmount
    };
  }

  // Subscribe to real-time changes
  static subscribeToChanges(callback: (payload: any) => void) {
    return supabase
      .channel('documents')
      .on('postgres_changes', 
        { 
          event: '*', 
          schema: 'public', 
          table: 'documents' 
        }, 
        callback
      )
      .subscribe();
  }

  // Export data to CSV
  static async exportToCSV(): Promise<string> {
    try {
      const documents = await this.getDocuments();
      const headers = [
        'Type', 'Document Number', 'Title', 'Client', 'Project', 'Issue Date', 
        'Due Date', 'Status', 'Subtotal', 'Tax', 'Total', 'Date Added'
      ];
      
      const csvContent = [
        headers.join(','),
        ...documents.map(doc => [
          doc.type,
          `"${doc.document_number}"`,
          `"${doc.title}"`,
          `"${doc.client_name}"`,
          `"${doc.project_name}"`,
          doc.issue_date,
          doc.due_date,
          `"${doc.status}"`,
          doc.subtotal,
          doc.tax_amount,
          doc.total_amount,
          new Date(doc.date_added).toLocaleDateString()
        ].join(','))
      ].join('\n');

      return csvContent;
    } catch (error) {
      console.error('Failed to export documents data:', error);
      throw error;
    }
  }

  // Get summary statistics
  static async getSummaryStats() {
    try {
      const documents = await this.getDocuments();
      
      const totalDocuments = documents.length;
      const quotations = documents.filter(d => d.type === 'quotation');
      const invoices = documents.filter(d => d.type === 'invoice');
      
      const totalQuotationValue = quotations.reduce((sum, doc) => sum + doc.total_amount, 0);
      const totalInvoiceValue = invoices.reduce((sum, doc) => sum + doc.total_amount, 0);
      
      // Group by status
      const statusStats = documents.reduce((acc, doc) => {
        const key = `${doc.type}_${doc.status}`;
        if (!acc[key]) {
          acc[key] = { count: 0, value: 0 };
        }
        acc[key].count++;
        acc[key].value += doc.total_amount;
        return acc;
      }, {} as Record<string, { count: number; value: number }>);

      return {
        totalDocuments,
        totalQuotations: quotations.length,
        totalInvoices: invoices.length,
        totalQuotationValue,
        totalInvoiceValue,
        statusStats
      };
    } catch (error) {
      console.error('Failed to get documents summary stats:', error);
      throw error;
    }
  }
}
