-- Simple Settings System Database Schema
-- This version removes complex views and session tracking to avoid column errors

-- Create user notification preferences table
CREATE TABLE IF NOT EXISTS public.user_notification_preferences (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    email_notifications <PERSON><PERSON><PERSON><PERSON><PERSON> DEFAULT true,
    push_notifications B<PERSON><PERSON><PERSON><PERSON> DEFAULT true,
    sms_notifications B<PERSON><PERSON>EAN DEFAULT false,
    project_updates BOOLEAN DEFAULT true,
    financial_alerts BOOLEAN DEFAULT true,
    time_tracking_reminders BOOLEAN DEFAULT true,
    team_messages B<PERSON>OLEAN DEFAULT true,
    system_announcements BO<PERSON><PERSON><PERSON> DEFAULT true,
    invoice_notifications B<PERSON>OL<PERSON>N DEFAULT true,
    deadline_reminders BO<PERSON><PERSON>N DEFAULT true,
    notification_frequency VARCHAR(20) DEFAULT 'immediate' CHECK (notification_frequency IN ('immediate', 'daily', 'weekly')),
    quiet_hours_enabled BOOLEAN DEFAULT false,
    quiet_hours_start TIME DEFAULT '22:00',
    quiet_hours_end TIME DEFAULT '08:00',
    two_factor_enabled BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(user_id)
);

-- Create system configuration table
CREATE TABLE IF NOT EXISTS public.system_config (
    id INTEGER PRIMARY KEY DEFAULT 1,
    company_name VARCHAR(255) DEFAULT 'Construction Management System',
    company_email VARCHAR(255) DEFAULT '<EMAIL>',
    company_phone VARCHAR(50) DEFAULT '+****************',
    company_address TEXT DEFAULT '123 Business Ave, City, State 12345',
    timezone VARCHAR(50) DEFAULT 'America/New_York',
    date_format VARCHAR(20) DEFAULT 'MM/DD/YYYY',
    currency VARCHAR(10) DEFAULT 'USD',
    session_timeout INTEGER DEFAULT 30,
    max_file_size INTEGER DEFAULT 10,
    backup_frequency VARCHAR(20) DEFAULT 'daily' CHECK (backup_frequency IN ('hourly', 'daily', 'weekly', 'monthly')),
    maintenance_mode BOOLEAN DEFAULT false,
    registration_enabled BOOLEAN DEFAULT true,
    email_verification_required BOOLEAN DEFAULT true,
    two_factor_required BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_by UUID REFERENCES auth.users(id),
    CHECK (id = 1) -- Ensure only one row
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_user_notification_preferences_user_id ON public.user_notification_preferences(user_id);

-- Create function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Apply triggers to tables
DROP TRIGGER IF EXISTS trigger_update_notification_preferences_updated_at ON public.user_notification_preferences;
CREATE TRIGGER trigger_update_notification_preferences_updated_at
    BEFORE UPDATE ON public.user_notification_preferences
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS trigger_update_system_config_updated_at ON public.system_config;
CREATE TRIGGER trigger_update_system_config_updated_at
    BEFORE UPDATE ON public.system_config
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Grant permissions
GRANT ALL ON public.user_notification_preferences TO authenticated;
GRANT ALL ON public.system_config TO authenticated;

-- Insert default system configuration
INSERT INTO public.system_config (
    id,
    company_name,
    company_email,
    company_phone,
    company_address,
    timezone,
    date_format,
    currency,
    session_timeout,
    max_file_size,
    backup_frequency,
    maintenance_mode,
    registration_enabled,
    email_verification_required,
    two_factor_required
) VALUES (
    1,
    'Construction Management System',
    '<EMAIL>',
    '+****************',
    '123 Business Ave, City, State 12345',
    'America/New_York',
    'MM/DD/YYYY',
    'USD',
    30,
    10,
    'daily',
    false,
    true,
    true,
    false
) ON CONFLICT (id) DO NOTHING;

-- Create default notification preferences for existing users
INSERT INTO public.user_notification_preferences (
    user_id,
    email_notifications,
    push_notifications,
    sms_notifications,
    project_updates,
    financial_alerts,
    time_tracking_reminders,
    team_messages,
    system_announcements,
    invoice_notifications,
    deadline_reminders,
    notification_frequency,
    quiet_hours_enabled,
    quiet_hours_start,
    quiet_hours_end,
    two_factor_enabled
)
SELECT 
    u.id,
    true,
    true,
    false,
    true,
    true,
    true,
    true,
    true,
    true,
    true,
    'immediate',
    false,
    '22:00',
    '08:00',
    false
FROM auth.users u
WHERE NOT EXISTS (
    SELECT 1 FROM public.user_notification_preferences unp 
    WHERE unp.user_id = u.id
)
ON CONFLICT (user_id) DO NOTHING;

-- Simple function to get system configuration
CREATE OR REPLACE FUNCTION get_system_config()
RETURNS TABLE (
    company_name VARCHAR(255),
    company_email VARCHAR(255),
    company_phone VARCHAR(50),
    company_address TEXT,
    timezone VARCHAR(50),
    date_format VARCHAR(20),
    currency VARCHAR(10),
    session_timeout INTEGER,
    max_file_size INTEGER,
    backup_frequency VARCHAR(20),
    maintenance_mode BOOLEAN,
    registration_enabled BOOLEAN,
    email_verification_required BOOLEAN,
    two_factor_required BOOLEAN
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        sc.company_name,
        sc.company_email,
        sc.company_phone,
        sc.company_address,
        sc.timezone,
        sc.date_format,
        sc.currency,
        sc.session_timeout,
        sc.max_file_size,
        sc.backup_frequency,
        sc.maintenance_mode,
        sc.registration_enabled,
        sc.email_verification_required,
        sc.two_factor_required
    FROM public.system_config sc
    WHERE sc.id = 1;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

GRANT EXECUTE ON FUNCTION get_system_config() TO authenticated;

-- Verify setup
DO $$
DECLARE
    user_count INTEGER;
    pref_count INTEGER;
    config_count INTEGER;
BEGIN
    SELECT COUNT(*) INTO user_count FROM auth.users;
    SELECT COUNT(*) INTO pref_count FROM public.user_notification_preferences;
    SELECT COUNT(*) INTO config_count FROM public.system_config;
    
    RAISE NOTICE '=== Settings System Setup Complete ===';
    RAISE NOTICE 'Users in system: %', user_count;
    RAISE NOTICE 'Notification preferences: %', pref_count;
    RAISE NOTICE 'System configurations: %', config_count;
    
    IF config_count > 0 THEN
        RAISE NOTICE 'Settings system is ready to use!';
    ELSE
        RAISE WARNING 'System configuration not found - check permissions';
    END IF;
END $$;
