-- Test Deletion Fix
-- Run this to create a test user and verify deletion works

-- 1. Create a test user for deletion testing
DO $$
DECLARE
    test_user_id UUID;
    test_email VARCHAR(255) := 'test-deletion-' || EXTRACT(EPOCH FROM NOW()) || '@example.com';
    user_count_before INTEGER;
    user_count_after INTEGER;
BEGIN
    -- Count users before
    SELECT COUNT(*) INTO user_count_before FROM public.user_profiles;
    RAISE NOTICE '=== DELETION TEST ===';
    RAISE NOTICE 'Users before test: %', user_count_before;
    
    -- Create test user
    INSERT INTO public.user_profiles (
        email, 
        first_name, 
        last_name, 
        role_id,
        is_active
    ) VALUES (
        test_email,
        'Test',
        'Deletion',
        (SELECT id FROM public.user_roles WHERE role_name = 'client' LIMIT 1),
        true
    ) RETURNING id INTO test_user_id;
    
    RAISE NOTICE 'Created test user: % (%)', test_email, test_user_id;
    
    -- Verify user exists
    IF EXISTS (SELECT 1 FROM public.user_profiles WHERE id = test_user_id) THEN
        RAISE NOTICE '✓ Test user created successfully';
    ELSE
        RAISE NOTICE '✗ Failed to create test user';
        RETURN;
    END IF;
    
    -- Delete the test user
    DELETE FROM public.user_profiles WHERE id = test_user_id;
    
    -- Verify deletion
    IF NOT EXISTS (SELECT 1 FROM public.user_profiles WHERE id = test_user_id) THEN
        RAISE NOTICE '✓ Test user deleted successfully';
    ELSE
        RAISE NOTICE '✗ Test user still exists after deletion';
        RETURN;
    END IF;
    
    -- Count users after
    SELECT COUNT(*) INTO user_count_after FROM public.user_profiles;
    RAISE NOTICE 'Users after test: %', user_count_after;
    
    IF user_count_after = user_count_before THEN
        RAISE NOTICE '✓ User count returned to original (deletion successful)';
    ELSE
        RAISE NOTICE '✗ User count mismatch (before: %, after: %)', user_count_before, user_count_after;
    END IF;
    
    RAISE NOTICE '';
    RAISE NOTICE 'Test completed. Database deletion is working properly.';
    RAISE NOTICE 'Now test the frontend deletion with enhanced logging.';
    
EXCEPTION
    WHEN OTHERS THEN
        RAISE NOTICE '✗ Test failed with error: %', SQLERRM;
END $$;

-- 2. Show current users for reference
DO $$
DECLARE
    user_record RECORD;
BEGIN
    RAISE NOTICE '';
    RAISE NOTICE '=== CURRENT USERS IN DATABASE ===';
    
    FOR user_record IN 
        SELECT id, email, first_name, last_name, is_active
        FROM public.user_profiles 
        ORDER BY created_at DESC
        LIMIT 10
    LOOP
        RAISE NOTICE '- %: % % (%)', 
            SUBSTRING(user_record.id::TEXT, 1, 8),
            user_record.first_name, 
            user_record.last_name, 
            user_record.email;
    END LOOP;
    
    RAISE NOTICE '';
    RAISE NOTICE 'Try deleting one of these users through the frontend.';
    RAISE NOTICE 'Watch the browser console for detailed logs.';
END $$;
