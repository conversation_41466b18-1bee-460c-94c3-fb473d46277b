import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { useToast } from '@/components/ui/use-toast';
import { useAuth } from '@/contexts/AuthContext';
import { hasPermission } from '@/lib/permissions';
import {
  Building2, Users, Activity, Shield, Settings, Plus, Edit, Trash2,
  UserCheck, UserX, Clock, Eye, AlertTriangle, CheckCircle, XCircle
} from 'lucide-react';

// Import sub-components
import TeamMembers from './TeamMembers';
import ActivityLog from './ActivityLog';
import CompanyPermissions from './CompanyPermissions';
import CompanyOverview from './CompanyOverview';

interface CompanyManagementProps {
  className?: string;
}

const CompanyManagement: React.FC<CompanyManagementProps> = ({ className = '' }) => {
  const { toast } = useToast();
  const { user, profile } = useAuth();
  const [activeTab, setActiveTab] = useState('overview');
  const [loading, setLoading] = useState(false);

  // Check permissions
  const canManageUsers = hasPermission(profile?.role?.role_name || '', 'CREATE_USERS');
  const canViewAuditLogs = hasPermission(profile?.role?.role_name || '', 'VIEW_AUDIT_LOGS');
  const canManagePermissions = hasPermission(profile?.role?.role_name || '', 'MANAGE_ROLES');

  // Quick stats for the header
  const [stats, setStats] = useState({
    totalMembers: 0,
    activeMembers: 0,
    pendingInvites: 0,
    recentActivity: 0
  });

  useEffect(() => {
    loadCompanyStats();
  }, []);

  const loadCompanyStats = async () => {
    try {
      setLoading(true);
      // This will be implemented with actual data fetching
      // For now, using placeholder data
      setStats({
        totalMembers: 12,
        activeMembers: 10,
        pendingInvites: 2,
        recentActivity: 24
      });
    } catch (error) {
      console.error('Error loading company stats:', error);
      toast({
        title: "Error",
        description: "Failed to load company statistics",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const getTabIcon = (tabName: string) => {
    switch (tabName) {
      case 'overview':
        return <Building2 className="w-4 h-4" />;
      case 'team':
        return <Users className="w-4 h-4" />;
      case 'activity':
        return <Activity className="w-4 h-4" />;
      case 'permissions':
        return <Shield className="w-4 h-4" />;
      default:
        return <Settings className="w-4 h-4" />;
    }
  };

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header Section */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white font-heading">
            Company Management
          </h1>
          <p className="text-gray-600 dark:text-gray-400 mt-1">
            Manage your team, permissions, and company settings
          </p>
        </div>
        
        {/* Quick Stats */}
        <div className="flex items-center space-x-4">
          <div className="text-center">
            <div className="text-2xl font-bold text-blue-600">{stats.totalMembers}</div>
            <div className="text-xs text-gray-500">Total Members</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-green-600">{stats.activeMembers}</div>
            <div className="text-xs text-gray-500">Active</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-orange-600">{stats.pendingInvites}</div>
            <div className="text-xs text-gray-500">Pending</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-purple-600">{stats.recentActivity}</div>
            <div className="text-xs text-gray-500">Recent Activity</div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview" className="flex items-center space-x-2">
            {getTabIcon('overview')}
            <span>Overview</span>
          </TabsTrigger>
          <TabsTrigger value="team" className="flex items-center space-x-2">
            {getTabIcon('team')}
            <span>Team Members</span>
          </TabsTrigger>
          <TabsTrigger value="activity" className="flex items-center space-x-2" disabled={!canViewAuditLogs}>
            {getTabIcon('activity')}
            <span>Activity Log</span>
          </TabsTrigger>
          <TabsTrigger value="permissions" className="flex items-center space-x-2" disabled={!canManagePermissions}>
            {getTabIcon('permissions')}
            <span>Permissions</span>
          </TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          <CompanyOverview onStatsUpdate={setStats} />
        </TabsContent>

        <TabsContent value="team" className="space-y-6">
          <TeamMembers onMemberUpdate={loadCompanyStats} />
        </TabsContent>

        <TabsContent value="activity" className="space-y-6">
          <ActivityLog />
        </TabsContent>

        <TabsContent value="permissions" className="space-y-6">
          <CompanyPermissions />
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default CompanyManagement;
