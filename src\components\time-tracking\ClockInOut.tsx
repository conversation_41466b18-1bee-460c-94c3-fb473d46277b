import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useToast } from '@/components/ui/use-toast';
import { useAuth } from '@/contexts/AuthContext';
import { TimeTrackingService, TimeEntry, Site } from '@/lib/timeTrackingService';
import {
  Clock,
  MapPin,
  PlayCircle,
  StopCircle,
  PauseCircle,
  Loader2,
  AlertCircle,
  CheckCircle,
  Navigation
} from 'lucide-react';

interface ClockInOutProps {
  activeTimeEntry: TimeEntry | null;
  sites: Site[];
  onClockAction: () => void;
}

const ClockInOut: React.FC<ClockInOutProps> = ({ activeTimeEntry, sites, onClockAction }) => {
  const { toast } = useToast();
  const { user } = useAuth();
  const [loading, setLoading] = useState(false);
  const [selectedSite, setSelectedSite] = useState('');
  const [workDescription, setWorkDescription] = useState('');
  const [location, setLocation] = useState<{ lat: number; lng: number } | null>(null);
  const [locationLoading, setLocationLoading] = useState(false);

  // Get current location
  const getCurrentLocation = () => {
    setLocationLoading(true);
    if (navigator.geolocation) {
      navigator.geolocation.getCurrentPosition(
        (position) => {
          setLocation({
            lat: position.coords.latitude,
            lng: position.coords.longitude
          });
          setLocationLoading(false);
          toast({
            title: "Location Captured",
            description: "Your current location has been recorded.",
          });
        },
        (error) => {
          console.error('Error getting location:', error);
          setLocationLoading(false);
          toast({
            title: "Location Error",
            description: "Could not get your current location. You can still clock in without it.",
            variant: "destructive",
          });
        }
      );
    } else {
      setLocationLoading(false);
      toast({
        title: "Location Not Supported",
        description: "Geolocation is not supported by this browser.",
        variant: "destructive",
      });
    }
  };

  const handleClockIn = async () => {
    if (!selectedSite) {
      toast({
        title: "Site Required",
        description: "Please select a site before clocking in.",
        variant: "destructive",
      });
      return;
    }

    if (!user) return;

    setLoading(true);
    try {
      const selectedSiteData = sites.find(site => site.id === selectedSite);
      
      const result = await TimeTrackingService.clockIn(user.id, {
        site_id: selectedSite,
        project_id: selectedSiteData?.project_id,
        work_description: workDescription || undefined,
        location_lat: location?.lat,
        location_lng: location?.lng
      });

      if (result.success) {
        toast({
          title: "Clocked In Successfully",
          description: `Started work at ${selectedSiteData?.name}`,
        });
        setWorkDescription('');
        onClockAction();
      } else {
        throw new Error(result.error);
      }
    } catch (error: any) {
      console.error('Clock in error:', error);
      toast({
        title: "Clock In Failed",
        description: error.message || "Failed to clock in",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const handleClockOut = async () => {
    if (!user) return;

    setLoading(true);
    try {
      const result = await TimeTrackingService.clockOut(user.id, {
        work_description: workDescription || undefined
      });

      if (result.success) {
        toast({
          title: "Clocked Out Successfully",
          description: `Total hours: ${result.total_hours?.toFixed(2) || 'N/A'}`,
        });
        setWorkDescription('');
        onClockAction();
      } else {
        throw new Error(result.error);
      }
    } catch (error: any) {
      console.error('Clock out error:', error);
      toast({
        title: "Clock Out Failed",
        description: error.message || "Failed to clock out",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const formatDuration = (startTime: string) => {
    const start = new Date(startTime);
    const now = new Date();
    const diffMs = now.getTime() - start.getTime();
    const hours = Math.floor(diffMs / (1000 * 60 * 60));
    const minutes = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60));
    return `${hours}h ${minutes}m`;
  };

  return (
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
      {/* Clock In/Out Card */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Clock className="h-5 w-5" />
            {activeTimeEntry ? 'Clock Out' : 'Clock In'}
          </CardTitle>
          <CardDescription>
            {activeTimeEntry 
              ? 'End your current work session'
              : 'Start tracking your work time'
            }
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {!activeTimeEntry ? (
            // Clock In Form
            <>
              <div className="space-y-2">
                <Label htmlFor="site">Work Site *</Label>
                <Select value={selectedSite} onValueChange={setSelectedSite}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select a work site" />
                  </SelectTrigger>
                  <SelectContent>
                    {sites.map((site) => (
                      <SelectItem key={site.id} value={site.id}>
                        <div className="flex items-center gap-2">
                          <MapPin className="h-4 w-4" />
                          <div>
                            <div className="font-medium">{site.name}</div>
                            <div className="text-xs text-gray-500">{site.site_code}</div>
                          </div>
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="description">Work Description (Optional)</Label>
                <Textarea
                  id="description"
                  placeholder="Describe what you'll be working on..."
                  value={workDescription}
                  onChange={(e) => setWorkDescription(e.target.value)}
                  rows={3}
                />
              </div>

              <div className="flex items-center justify-between p-3 border rounded-lg">
                <div className="flex items-center gap-2">
                  <Navigation className="h-4 w-4 text-gray-500" />
                  <span className="text-sm">
                    {location ? 'Location captured' : 'Location not captured'}
                  </span>
                </div>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={getCurrentLocation}
                  disabled={locationLoading}
                >
                  {locationLoading ? (
                    <Loader2 className="h-4 w-4 animate-spin" />
                  ) : (
                    <Navigation className="h-4 w-4" />
                  )}
                  {location ? 'Update' : 'Get Location'}
                </Button>
              </div>

              <Button
                onClick={handleClockIn}
                disabled={loading || !selectedSite}
                className="w-full"
                size="lg"
              >
                {loading ? (
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                ) : (
                  <PlayCircle className="h-4 w-4 mr-2" />
                )}
                Clock In
              </Button>
            </>
          ) : (
            // Clock Out Form
            <>
              <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
                <div className="flex items-center gap-2 text-green-800 mb-2">
                  <CheckCircle className="h-4 w-4" />
                  <span className="font-medium">Currently Working</span>
                </div>
                <div className="space-y-1 text-sm">
                  <div><strong>Site:</strong> {activeTimeEntry.site?.name}</div>
                  <div><strong>Started:</strong> {new Date(activeTimeEntry.clock_in_time).toLocaleString()}</div>
                  <div><strong>Duration:</strong> {formatDuration(activeTimeEntry.clock_in_time)}</div>
                  {activeTimeEntry.work_description && (
                    <div><strong>Description:</strong> {activeTimeEntry.work_description}</div>
                  )}
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="clockOutDescription">Final Work Description (Optional)</Label>
                <Textarea
                  id="clockOutDescription"
                  placeholder="Describe what you accomplished..."
                  value={workDescription}
                  onChange={(e) => setWorkDescription(e.target.value)}
                  rows={3}
                />
              </div>

              <Button
                onClick={handleClockOut}
                disabled={loading}
                className="w-full"
                size="lg"
                variant="destructive"
              >
                {loading ? (
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                ) : (
                  <StopCircle className="h-4 w-4 mr-2" />
                )}
                Clock Out
              </Button>
            </>
          )}
        </CardContent>
      </Card>

      {/* Current Status Card */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Clock className="h-5 w-5" />
            Current Status
          </CardTitle>
          <CardDescription>Your work session information</CardDescription>
        </CardHeader>
        <CardContent>
          {activeTimeEntry ? (
            <div className="space-y-4">
              <div className="text-center">
                <div className="text-3xl font-bold text-green-600 mb-2">
                  {formatDuration(activeTimeEntry.clock_in_time)}
                </div>
                <p className="text-gray-600">Time worked today</p>
              </div>

              <div className="grid grid-cols-2 gap-4 text-sm">
                <div className="text-center p-3 bg-blue-50 rounded-lg">
                  <div className="font-semibold text-blue-800">Site</div>
                  <div className="text-blue-600">{activeTimeEntry.site?.name}</div>
                </div>
                <div className="text-center p-3 bg-purple-50 rounded-lg">
                  <div className="font-semibold text-purple-800">Status</div>
                  <div className="text-purple-600 capitalize">{activeTimeEntry.status}</div>
                </div>
              </div>

              {activeTimeEntry.status === 'break' && (
                <div className="p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                  <div className="flex items-center gap-2 text-yellow-800">
                    <PauseCircle className="h-4 w-4" />
                    <span className="font-medium">On Break</span>
                  </div>
                  <p className="text-yellow-700 text-sm mt-1">
                    You're currently on a break. Resume work when ready.
                  </p>
                </div>
              )}
            </div>
          ) : (
            <div className="text-center py-8">
              <Clock className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="font-semibold text-gray-600 mb-2">Not Currently Working</h3>
              <p className="text-gray-500 text-sm">
                Clock in to start tracking your work time
              </p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default ClockInOut;
