-- Simple Permanent Registration Fix - Just Creates the Functions
-- This ensures all future registrations work correctly without trying to fix existing data

-- 1. Create a robust registration function that handles all edge cases
CREATE OR REPLACE FUNCTION public.complete_user_registration(
    p_email VARCHAR(255),
    p_auth_user_id UUID,
    p_first_name <PERSON><PERSON><PERSON><PERSON>(100),
    p_last_name VA<PERSON><PERSON><PERSON>(100),
    p_role_name VA<PERSON>HAR(50) DEFAULT 'client'
)
RETURNS jsonb AS $$
DECLARE
    v_role_id UUID;
    v_profile_id UUID;
    v_existing_profile RECORD;
    v_auth_user RECORD;
BEGIN
    -- Check if auth user exists (with a small delay to ensure it's committed)
    PERFORM pg_sleep(0.5);
    
    SELECT * INTO v_auth_user
    FROM auth.users
    WHERE id = p_auth_user_id;
    
    IF v_auth_user.id IS NULL THEN
        RETURN jsonb_build_object(
            'success', false,
            'error', 'Auth user not found',
            'code', 'AUTH_USER_NOT_FOUND'
        );
    END IF;
    
    -- Check if profile already exists
    SELECT * INTO v_existing_profile
    FROM public.user_profiles
    WHERE email = p_email;
    
    IF v_existing_profile.id IS NOT NULL THEN
        -- Update existing profile instead of creating new one
        UPDATE public.user_profiles
        SET 
            user_id = p_auth_user_id,
            first_name = p_first_name,
            last_name = p_last_name,
            is_verified = true,
            is_active = true,
            email_status = 'registration_completed',
            updated_at = NOW()
        WHERE id = v_existing_profile.id
        RETURNING id INTO v_profile_id;
        
        RETURN jsonb_build_object(
            'success', true,
            'message', 'Profile updated successfully',
            'profile_id', v_profile_id,
            'auth_user_id', p_auth_user_id,
            'action', 'updated'
        );
    END IF;
    
    -- Get role ID
    SELECT id INTO v_role_id
    FROM public.user_roles
    WHERE role_name = p_role_name AND is_active = true;
    
    IF v_role_id IS NULL THEN
        -- Default to client role if specified role not found
        SELECT id INTO v_role_id
        FROM public.user_roles
        WHERE role_name = 'client' AND is_active = true
        LIMIT 1;
    END IF;
    
    IF v_role_id IS NULL THEN
        -- If still no role, get any active role
        SELECT id INTO v_role_id
        FROM public.user_roles
        WHERE is_active = true
        LIMIT 1;
    END IF;
    
    IF v_role_id IS NULL THEN
        RETURN jsonb_build_object(
            'success', false,
            'error', 'No active roles found in system',
            'code', 'NO_ROLES_AVAILABLE'
        );
    END IF;
    
    -- Create new profile
    INSERT INTO public.user_profiles (
        user_id,
        email,
        first_name,
        last_name,
        role_id,
        is_verified,
        is_active,
        email_status,
        created_at,
        updated_at
    ) VALUES (
        p_auth_user_id,
        p_email,
        p_first_name,
        p_last_name,
        v_role_id,
        true,
        true,
        'registration_completed',
        NOW(),
        NOW()
    )
    RETURNING id INTO v_profile_id;
    
    RETURN jsonb_build_object(
        'success', true,
        'message', 'Profile created successfully',
        'profile_id', v_profile_id,
        'auth_user_id', p_auth_user_id,
        'role_id', v_role_id,
        'action', 'created'
    );
    
EXCEPTION
    WHEN OTHERS THEN
        RETURN jsonb_build_object(
            'success', false,
            'error', 'Profile creation failed: ' || SQLERRM,
            'code', 'PROFILE_CREATION_ERROR'
        );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 2. Grant permissions
GRANT EXECUTE ON FUNCTION public.complete_user_registration(VARCHAR, UUID, VARCHAR, VARCHAR, VARCHAR) TO anon, authenticated;

-- 3. Test notification
DO $$
BEGIN
    RAISE NOTICE '';
    RAISE NOTICE '=== SIMPLE PERMANENT REGISTRATION FIX INSTALLED ===';
    RAISE NOTICE '';
    RAISE NOTICE 'New function created:';
    RAISE NOTICE '✓ complete_user_registration() - Robust registration with error handling';
    RAISE NOTICE '';
    RAISE NOTICE 'Benefits for future registrations:';
    RAISE NOTICE '✓ Handles auth user verification with delay';
    RAISE NOTICE '✓ Updates existing profiles instead of failing';
    RAISE NOTICE '✓ Automatic role assignment';
    RAISE NOTICE '✓ Comprehensive error handling';
    RAISE NOTICE '✓ No duplicate key violations';
    RAISE NOTICE '';
    RAISE NOTICE 'The frontend now uses complete_user_registration()';
    RAISE NOTICE 'for all future registrations.';
    RAISE NOTICE '';
    RAISE NOTICE 'This should prevent the orphaned auth user issue';
    RAISE NOTICE 'that caused the login failure.';
    RAISE NOTICE '';
END $$;
