# 🔧 Fix Supabase Dashboard Settings for Port 8080

## ❌ **The Problem**
Password reset emails are redirecting to `localhost:3000` instead of `http://************:8080`

## ✅ **The Solution**

### **STEP 1: Update Supabase Dashboard Settings**

1. **Go to Supabase Dashboard** → Your Project
2. **Navigate to Authentication** → Settings → URL Configuration
3. **Update these settings:**

#### **Site URL:**
```
http://************:8080
```

#### **Redirect URLs (Add all of these):**
```
http://************:8080/**
http://localhost:8080/**
http://127.0.0.1:8080/**
http://************:8080/login
http://************:8080/register
http://************:8080/setup-password
```

### **STEP 2: Update Environment Variables**

Check your `.env.local` file and ensure:

```env
VITE_SUPABASE_URL=https://your-project.supabase.co
VITE_SUPABASE_ANON_KEY=your-anon-key
VITE_SUPABASE_SERVICE_ROLE_KEY=your-service-role-key

# Make sure no localhost:3000 references exist
```

### **STEP 3: Alternative - Use Custom Email Templates**

If dashboard settings don't work, we can override the email templates:

1. **Go to Supabase Dashboard** → Authentication → Email Templates
2. **Edit "Reset Password" template**
3. **Replace the action URL** with:
   ```
   http://************:8080/setup-password
   ```

### **STEP 4: Test the Fix**

1. **Save all dashboard settings**
2. **Try "Resend Setup Email"** from your admin panel
3. **Check the email** - should now have correct URLs

## 🎯 **Expected Results**

### **Before Fix:**
- ❌ Email links go to `localhost:3000`
- ❌ Links don't work from other devices

### **After Fix:**
- ✅ Email links go to `http://************:8080`
- ✅ Links work from any device on network
- ✅ Registration and password reset work properly

## 📋 **Quick Checklist**

- [ ] Update Site URL in Supabase Dashboard
- [ ] Add all redirect URLs in Dashboard
- [ ] Check .env.local for localhost:3000 references
- [ ] Test email links after changes
- [ ] Verify links work from other devices

## 🚨 **If Still Not Working**

If the dashboard settings don't fix it, the issue might be:

1. **Cached settings** - Wait 5-10 minutes for changes to propagate
2. **Email template override** - Check Authentication → Email Templates
3. **Environment variables** - Check for localhost:3000 in .env files
4. **Browser cache** - Clear browser cache and try again

## 🎯 **Final Test**

After making these changes:

1. **Go to admin panel**
2. **Click "Resend Setup Email"**
3. **Check email** - should show `http://************:8080` URLs
4. **Click the link** - should work from any device

The dashboard settings are the most likely cause of the localhost:3000 issue!
