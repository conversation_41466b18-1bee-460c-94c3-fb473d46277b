import React from 'react';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { User } from 'lucide-react';

interface CreatorInfoProps {
  createdByName?: string;
  createdByAvatar?: string;
  createdAt?: string;
  variant?: 'default' | 'compact' | 'minimal';
  showDate?: boolean;
  showLabel?: boolean;
  className?: string;
}

export const CreatorInfo: React.FC<CreatorInfoProps> = ({
  createdByName,
  createdByAvatar,
  createdAt,
  variant = 'default',
  showDate = true,
  showLabel = true,
  className = ''
}) => {
  if (!createdByName && !createdAt) {
    return null;
  }

  const formatDate = (dateString: string) => {
    try {
      return new Date(dateString).toLocaleDateString('en-US', {
        month: 'short',
        day: 'numeric',
        year: 'numeric'
      });
    } catch {
      return dateString;
    }
  };

  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(word => word[0])
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  if (variant === 'minimal') {
    return (
      <div className={`flex items-center space-x-1 text-xs text-gray-500 ${className}`}>
        <User className="h-3 w-3" />
        <span>{createdByName || 'Unknown'}</span>
        {showDate && createdAt && (
          <>
            <span>•</span>
            <span>{formatDate(createdAt)}</span>
          </>
        )}
      </div>
    );
  }

  if (variant === 'compact') {
    return (
      <div className={`flex items-center space-x-2 ${className}`}>
        <Avatar className="h-6 w-6">
          <AvatarImage src={createdByAvatar} alt={createdByName} />
          <AvatarFallback className="bg-gradient-to-r from-blue-600 to-purple-600 text-white text-xs">
            {createdByName ? getInitials(createdByName) : 'U'}
          </AvatarFallback>
        </Avatar>
        <div className="flex flex-col">
          <span className="text-xs font-medium text-gray-700">
            {createdByName || 'Unknown User'}
          </span>
          {showDate && createdAt && (
            <span className="text-xs text-gray-500">
              {formatDate(createdAt)}
            </span>
          )}
        </div>
      </div>
    );
  }

  // Default variant
  return (
    <div className={`flex items-center space-x-3 ${className}`}>
      <Avatar className="h-8 w-8">
        <AvatarImage src={createdByAvatar} alt={createdByName} />
        <AvatarFallback className="bg-gradient-to-r from-blue-600 to-purple-600 text-white text-sm">
          {createdByName ? getInitials(createdByName) : 'U'}
        </AvatarFallback>
      </Avatar>
      <div className="flex flex-col">
        {showLabel && (
          <span className="text-xs text-gray-500 uppercase tracking-wide">
            Created by
          </span>
        )}
        <span className="text-sm font-medium text-gray-700">
          {createdByName || 'Unknown User'}
        </span>
        {showDate && createdAt && (
          <span className="text-xs text-gray-500">
            {formatDate(createdAt)}
          </span>
        )}
      </div>
    </div>
  );
};

// Badge variant for showing creator in a compact badge format
export const CreatorBadge: React.FC<CreatorInfoProps> = ({
  createdByName,
  createdByAvatar,
  className = ''
}) => {
  if (!createdByName) {
    return null;
  }

  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(word => word[0])
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  return (
    <Badge variant="outline" className={`flex items-center space-x-1 ${className}`}>
      <Avatar className="h-4 w-4">
        <AvatarImage src={createdByAvatar} alt={createdByName} />
        <AvatarFallback className="bg-gradient-to-r from-blue-600 to-purple-600 text-white text-xs">
          {getInitials(createdByName)}
        </AvatarFallback>
      </Avatar>
      <span className="text-xs">{createdByName}</span>
    </Badge>
  );
};

export default CreatorInfo;
