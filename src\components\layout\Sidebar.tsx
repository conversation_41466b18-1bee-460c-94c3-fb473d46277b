
import React from 'react';
import { NavLink } from 'react-router-dom';
import {
  LayoutDashboard,
  Calendar,
  Users,
  Building,
  FileText,
  Settings,
  DollarSign,
  BarChart3,
  MessageSquare,
  Clock,
  BookOpen,
  Truck,
  Building2
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { useRoleAccess } from '@/hooks/useRoleAccess';

const Sidebar = () => {
  const { canAccess, userRole } = useRoleAccess();

  const allNavigation = [
    { name: 'Dashboard', href: '/', icon: LayoutDashboard, resource: 'dashboard' },
    { name: 'Projects', href: '/projects', icon: Calendar, resource: 'projects' },
    { name: 'Clients', href: '/clients', icon: Building, resource: 'clients' },
    { name: 'Workforce', href: '/workforce', icon: Users, resource: 'users' },
    { name: 'Documents', href: '/documents', icon: FileText, resource: 'projects' },
    { name: 'Financials', href: '/financials', icon: DollarSign, resource: 'financials' },
    { name: 'Assets', href: '/assets', icon: Truck, resource: 'assets' },
    { name: 'Company', href: '/company', icon: Building2, resource: 'users' },
    { name: 'Register', href: '/register', icon: BookOpen, resource: 'users' },
    { name: 'Reports', href: '/reports', icon: BarChart3, resource: 'reports' },
    { name: 'Messages', href: '/messages', icon: MessageSquare, resource: 'messages' },
    { name: 'Time Tracking', href: '/time-tracking', icon: Clock, resource: 'projects' },
    { name: 'Settings', href: '/settings', icon: Settings, resource: 'dashboard' },
  ];

  // Filter navigation based on user role permissions
  const navigation = allNavigation.filter(item => canAccess(item.resource));

  return (
    <div className="bg-gradient-to-b from-slate-900 to-slate-800 text-white w-72 min-h-screen shadow-2xl">
      <div className="p-6">
        <div className="mb-8">
          <div className="mb-4">
            <h2 className="text-lg font-semibold text-gray-300 mb-2 font-heading">Navigation</h2>
            <div className="text-xs text-gray-400 bg-slate-800 px-2 py-1 rounded">
              Role: {userRole.charAt(0).toUpperCase() + userRole.slice(1)}
            </div>
          </div>
          <nav className="space-y-1">
            {navigation.map((item) => {
              const Icon = item.icon;
              return (
                <NavLink
                  key={item.name}
                  to={item.href}
                  className={({ isActive }) =>
                    cn(
                      'flex items-center px-4 py-3 rounded-xl text-sm font-medium transition-all duration-200 group',
                      isActive
                        ? 'bg-gradient-to-r from-blue-600 to-blue-700 text-white shadow-lg transform scale-105'
                        : 'text-gray-300 hover:bg-slate-700/50 hover:text-white hover:transform hover:scale-105'
                    )
                  }
                >
                  <Icon className="w-5 h-5 mr-3 transition-transform group-hover:scale-110" />
                  {item.name}
                </NavLink>
              );
            })}
          </nav>
        </div>
        
        <div className="mt-auto pt-8 border-t border-slate-700">
          <div className="bg-gradient-to-r from-blue-600/20 to-orange-600/20 rounded-xl p-4 border border-blue-500/30">
            <h3 className="font-semibold text-sm text-white mb-2 font-heading">System Status</h3>
            <div className="space-y-2 text-xs">
              <div className="flex justify-between">
                <span className="text-gray-300">Server Status</span>
                <span className="text-green-400 font-medium">Online</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-300">Last Backup</span>
                <span className="text-blue-400 font-medium">2h ago</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Sidebar;
