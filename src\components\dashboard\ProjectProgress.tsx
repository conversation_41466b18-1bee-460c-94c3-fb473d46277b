
import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';

interface Project {
  id: string;
  name: string;
  progress: number;
  deadline: string;
  status: 'on-track' | 'at-risk' | 'delayed';
}

const ProjectProgress = () => {
  const projects: Project[] = [
    { id: '1', name: 'Harare CBD Office Complex', progress: 75, deadline: '2024-08-15', status: 'on-track' },
    { id: '2', name: 'Bulawayo Shopping Mall', progress: 45, deadline: '2024-09-30', status: 'at-risk' },
    { id: '3', name: 'Mutare Residential Estate', progress: 90, deadline: '2024-07-20', status: 'on-track' },
    { id: '4', name: 'Gweru Industrial Park', progress: 25, deadline: '2024-12-01', status: 'delayed' },
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'on-track':
        return 'text-green-600';
      case 'at-risk':
        return 'text-yellow-600';
      case 'delayed':
        return 'text-red-600';
      default:
        return 'text-gray-600';
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Active Projects</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {projects.map((project) => (
          <div key={project.id} className="space-y-2">
            <div className="flex justify-between items-center">
              <h4 className="font-medium text-gray-900">{project.name}</h4>
              <span className={`text-sm font-medium ${getStatusColor(project.status)}`}>
                {project.status.replace('-', ' ').toUpperCase()}
              </span>
            </div>
            <Progress value={project.progress} className="h-2" />
            <div className="flex justify-between text-sm text-gray-600">
              <span>{project.progress}% complete</span>
              <span>Due: {new Date(project.deadline).toLocaleDateString()}</span>
            </div>
          </div>
        ))}
      </CardContent>
    </Card>
  );
};

export default ProjectProgress;
