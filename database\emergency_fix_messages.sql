-- EMERGENCY FIX: Remove problematic trigger to allow messages to work
-- Run this immediately to fix the "created_by" error

-- 1. Drop the problematic trigger that's causing the error
DROP TRIGGER IF EXISTS trigger_create_message_notifications ON public.messages;

-- 2. Drop the problematic function
DROP FUNCTION IF EXISTS create_message_notifications();

-- 3. Verify the trigger is removed
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.triggers 
        WHERE trigger_name = 'trigger_create_message_notifications'
    ) THEN
        RAISE NOTICE '✓ Problematic trigger removed successfully';
        RAISE NOTICE '✓ Messages should now work without the "created_by" error';
        RAISE NOTICE '⚠ Note: Message notifications are temporarily disabled';
        RAISE NOTICE '⚠ Run the full fix script next to re-enable notifications';
    ELSE
        RAISE NOTICE '⚠ Trigger still exists - please check manually';
    END IF;
END $$;
