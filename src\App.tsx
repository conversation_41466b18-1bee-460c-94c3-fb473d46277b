
import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route, Navigate } from "react-router-dom";
import { AuthProvider } from "@/contexts/AuthContext";
import { ThemeProvider } from "@/contexts/ThemeContext";
import ProtectedRoute from "@/components/auth/ProtectedRoute";
import AdminRoute from "./components/auth/AdminRoute";
import { initBrowserFixes } from "@/utils/browserFixes";
import Dashboard from "./pages/Dashboard";
import TestDashboard from "./pages/TestDashboard";
import Projects from "./pages/Projects";
import ProjectDetail from "./pages/ProjectDetail";
import { Clients } from "./pages/Clients";
import Workforce from "./pages/Workforce";
import Documents from "./pages/Documents";
import Financials from "./pages/Financials";
import Assets from "./pages/Assets";
import Reports from "./pages/Reports";
import Messages from "./pages/Messages";
import TimeTracking from "./pages/TimeTracking";
import Settings from "./pages/Settings";
import Login from "./pages/Login";
import Register from "./pages/Register";
import UserRegister from "./pages/UserRegister";
import AdminRegister from "./pages/AdminRegister";
import AdminCreateUser from "./pages/AdminCreateUser";
import RegisterDisabled from "./pages/RegisterDisabled";
import InvitedRegister from "./pages/InvitedRegister";
import SetupPassword from "./pages/SetupPassword";
import Profile from "./pages/Profile";
import FixOrphanedUsers from "./pages/FixOrphanedUsers";
import TestAuth from "./pages/TestAuth";
import DirectAuthTest from "./pages/DirectAuthTest";
import StandaloneRegister from "./pages/StandaloneRegister";
import Company from "./pages/Company";
import HelpSupport from "./pages/HelpSupport";
import Documentation from "./pages/Documentation";
import VideoTutorials from "./pages/VideoTutorials";
import FAQ from "./pages/FAQ";
import ContactSupport from "./pages/ContactSupport";

import NotFound from "./pages/NotFound";

const queryClient = new QueryClient();

// Initialize browser fixes for local development
if (typeof window !== 'undefined') {
  initBrowserFixes();
}

const App = () => (
  <QueryClientProvider client={queryClient}>
    <ThemeProvider>
      <TooltipProvider>
        <Toaster />
        <Sonner />
        <BrowserRouter>
          <AuthProvider>
          <Routes>
            {/* Public Routes */}
            <Route path="/login" element={<Login />} />
            <Route path="/user-register" element={<UserRegister />} />
            <Route path="/invited-register" element={<InvitedRegister />} />
            <Route path="/register-disabled" element={<RegisterDisabled />} />
            <Route path="/admin-register" element={<AdminRegister />} />
            <Route path="/setup-password" element={<SetupPassword />} />

            {/* Protected Routes */}
            <Route path="/register" element={<ProtectedRoute><Register /></ProtectedRoute>} />

            {/* Protected Routes */}
            <Route path="/" element={<Navigate to="/dashboard" replace />} />
            <Route path="/dashboard" element={
              <ProtectedRoute>
                <Dashboard />
              </ProtectedRoute>
            } />
            <Route path="/test-dashboard" element={
              <ProtectedRoute>
                <TestDashboard />
              </ProtectedRoute>
            } />
            <Route path="/projects" element={
              <ProtectedRoute>
                <Projects />
              </ProtectedRoute>
            } />
            <Route path="/projects/:id" element={
              <ProtectedRoute>
                <ProjectDetail />
              </ProtectedRoute>
            } />
            <Route path="/clients" element={
              <ProtectedRoute>
                <Clients />
              </ProtectedRoute>
            } />
            <Route path="/workforce" element={
              <ProtectedRoute>
                <Workforce />
              </ProtectedRoute>
            } />
            <Route path="/documents" element={
              <ProtectedRoute>
                <Documents />
              </ProtectedRoute>
            } />
            <Route path="/financials" element={
              <ProtectedRoute>
                <Financials />
              </ProtectedRoute>
            } />
            <Route path="/assets" element={
              <ProtectedRoute>
                <Assets />
              </ProtectedRoute>
            } />
            <Route path="/reports" element={
              <ProtectedRoute>
                <Reports />
              </ProtectedRoute>
            } />
            <Route path="/messages" element={
              <ProtectedRoute>
                <Messages />
              </ProtectedRoute>
            } />
            <Route path="/time-tracking" element={
              <ProtectedRoute>
                <TimeTracking />
              </ProtectedRoute>
            } />
            <Route path="/profile" element={
              <ProtectedRoute>
                <Profile />
              </ProtectedRoute>
            } />
            <Route path="/settings" element={
              <ProtectedRoute>
                <Settings />
              </ProtectedRoute>
            } />
            <Route path="/company" element={
              <ProtectedRoute>
                <Company />
              </ProtectedRoute>
            } />
            <Route path="/help" element={
              <ProtectedRoute>
                <HelpSupport />
              </ProtectedRoute>
            } />
            <Route path="/documentation" element={
              <ProtectedRoute>
                <Documentation />
              </ProtectedRoute>
            } />
            <Route path="/video-tutorials" element={
              <ProtectedRoute>
                <VideoTutorials />
              </ProtectedRoute>
            } />
            <Route path="/faq" element={
              <ProtectedRoute>
                <FAQ />
              </ProtectedRoute>
            } />
            <Route path="/contact-support" element={
              <ProtectedRoute>
                <ContactSupport />
              </ProtectedRoute>
            } />
            <Route path="/fix-orphaned-users" element={
              <ProtectedRoute>
                <FixOrphanedUsers />
              </ProtectedRoute>
            } />
            <Route path="/test-auth" element={
              <ProtectedRoute>
                <TestAuth />
              </ProtectedRoute>
            } />
            <Route path="/direct-auth-test" element={<DirectAuthTest />} />
            <Route path="/standalone-register" element={<StandaloneRegister />} />
            <Route path="/admin-create-user" element={
              <ProtectedRoute>
                <AdminRoute>
                  <AdminCreateUser />
                </AdminRoute>
              </ProtectedRoute>
            } />

            <Route path="*" element={<NotFound />} />
          </Routes>
          </AuthProvider>
        </BrowserRouter>
      </TooltipProvider>
    </ThemeProvider>
  </QueryClientProvider>
);

export default App;
