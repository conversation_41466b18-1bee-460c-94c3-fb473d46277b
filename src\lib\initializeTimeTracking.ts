import { supabase } from './supabase';

export const initializeTimeTracking = async () => {
  try {
    console.log('🔍 Checking time tracking database setup...');
    
    // Check if tables exist
    const { data: sites, error: sitesError } = await supabase
      .from('sites')
      .select('count')
      .limit(1);
    
    if (sitesError) {
      console.error('❌ Sites table not found:', sitesError.message);
      return {
        success: false,
        error: 'Time tracking tables not found. Please run the SQL schema first.',
        details: sitesError
      };
    }
    
    const { data: timeEntries, error: timeEntriesError } = await supabase
      .from('time_entries')
      .select('count')
      .limit(1);
    
    if (timeEntriesError) {
      console.error('❌ Time entries table not found:', timeEntriesError.message);
      return {
        success: false,
        error: 'Time entries table not found. Please run the SQL schema first.',
        details: timeEntriesError
      };
    }
    
    // Check if functions exist by trying to call one
    const { data: functionTest, error: functionError } = await supabase.rpc('get_active_time_entry', {
      p_user_id: '00000000-0000-0000-0000-000000000000' // Test UUID
    });
    
    if (functionError && functionError.message.includes('function') && functionError.message.includes('does not exist')) {
      console.error('❌ Time tracking functions not found:', functionError.message);
      return {
        success: false,
        error: 'Time tracking functions not found. Please run the SQL schema first.',
        details: functionError
      };
    }
    
    console.log('✅ Time tracking database setup verified');
    return {
      success: true,
      message: 'Time tracking system is ready',
      tablesFound: true,
      functionsFound: true
    };
    
  } catch (error) {
    console.error('❌ Error checking time tracking setup:', error);
    return {
      success: false,
      error: 'Failed to verify time tracking setup',
      details: error
    };
  }
};

// Helper function to create sample sites if none exist
export const createSampleSites = async () => {
  try {
    // Check if any sites exist
    const { data: existingSites, error: checkError } = await supabase
      .from('sites')
      .select('id')
      .limit(1);
    
    if (checkError) {
      throw checkError;
    }
    
    if (existingSites && existingSites.length > 0) {
      console.log('✅ Sites already exist, skipping sample creation');
      return { success: true, message: 'Sites already exist' };
    }
    
    // Get a project to associate with sites
    const { data: projects, error: projectError } = await supabase
      .from('projects')
      .select('id, name')
      .limit(1);
    
    const projectId = projects && projects.length > 0 ? projects[0].id : null;
    
    // Create sample sites
    const sampleSites = [
      {
        name: 'Main Construction Site',
        address: '123 Building Ave, Construction City',
        project_id: projectId,
        site_code: 'MAIN-001',
        description: 'Primary construction location',
        is_active: true
      },
      {
        name: 'Storage Facility',
        address: '456 Storage St, Construction City',
        project_id: projectId,
        site_code: 'STOR-001',
        description: 'Equipment and materials storage',
        is_active: true
      },
      {
        name: 'Office Trailer',
        address: '789 Office Rd, Construction City',
        project_id: projectId,
        site_code: 'OFF-001',
        description: 'On-site office and meeting space',
        is_active: true
      }
    ];
    
    const { data, error } = await supabase
      .from('sites')
      .insert(sampleSites)
      .select();
    
    if (error) {
      throw error;
    }
    
    console.log('✅ Sample sites created successfully:', data?.length);
    return {
      success: true,
      message: `Created ${data?.length} sample sites`,
      sites: data
    };
    
  } catch (error) {
    console.error('❌ Error creating sample sites:', error);
    return {
      success: false,
      error: 'Failed to create sample sites',
      details: error
    };
  }
};
