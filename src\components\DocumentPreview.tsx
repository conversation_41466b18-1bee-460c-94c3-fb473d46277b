import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Eye, Download } from 'lucide-react';
import { Document } from '@/lib/documents';
import { PDFGenerator } from '@/lib/pdfGenerator';
import { useToast } from '@/components/ui/use-toast';

interface DocumentPreviewProps {
  document: Document;
}

const DocumentPreview: React.FC<DocumentPreviewProps> = ({ document }) => {
  const { toast } = useToast();
  const isQuotation = document.type === 'quotation';

  // Get current company info from localStorage or use document's company info as fallback
  const getCurrentCompanyInfo = () => {
    try {
      const saved = localStorage.getItem('company_info');
      return saved ? JSON.parse(saved) : document.company_info;
    } catch {
      return document.company_info;
    }
  };

  const company = getCurrentCompanyInfo();

  const handleDownloadPDF = async () => {
    try {
      // Use current company info for PDF generation
      const documentWithCurrentCompany = {
        ...document,
        company_info: company
      };

      await PDFGenerator.generateDocumentPDF(documentWithCurrentCompany);
      toast({
        title: "Success",
        description: "PDF downloaded successfully",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to generate PDF",
        variant: "destructive",
      });
    }
  };

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'paid':
      case 'accepted':
        return 'text-green-600 bg-green-100';
      case 'sent':
        return 'text-blue-600 bg-blue-100';
      case 'overdue':
      case 'rejected':
        return 'text-red-600 bg-red-100';
      case 'draft':
        return 'text-yellow-600 bg-yellow-100';
      default:
        return 'text-gray-600 bg-gray-100';
    }
  };

  return (
    <Dialog>
      <DialogTrigger asChild>
        <Button variant="outline" size="sm" title="Preview">
          <Eye className="w-4 h-4" />
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[900px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <div className="flex items-center justify-between">
            <DialogTitle>
              {isQuotation ? 'Quotation' : 'Invoice'} Preview
            </DialogTitle>
            <Button onClick={handleDownloadPDF} size="sm">
              <Download className="w-4 h-4 mr-2" />
              Download PDF
            </Button>
          </div>
        </DialogHeader>
        
        <Card className="mt-4">
          <CardContent className="p-8">
            {/* Header Section */}
            <div className="flex justify-between items-start mb-8 pb-6 border-b-2 border-gray-200 print:border-black">
              <div className="flex-1 max-w-md">
                {company.logo_url && (
                  <div className="mb-4">
                    <img
                      src={company.logo_url}
                      alt="Company Logo"
                      className="max-h-20 max-w-48 object-contain"
                      onError={(e) => {
                        console.warn('Logo failed to load:', company.logo_url);
                        e.currentTarget.style.display = 'none';
                      }}
                    />
                  </div>
                )}
                <h1 className="text-2xl font-bold text-gray-900 mb-3 print:text-black">{company.name || 'Company Name'}</h1>
                <div className="text-gray-600 text-sm leading-relaxed print:text-black">
                  {company.address ? company.address.split('\n').map((line, i) => (
                    <div key={i}>{line}</div>
                  )) : <div>Company Address</div>}
                </div>
                <div className="mt-3 text-gray-600 text-sm print:text-black space-y-1">
                  <div>Phone: {company.phone || 'N/A'}</div>
                  <div>Email: {company.email || 'N/A'}</div>
                  {company.website && <div>Website: {company.website}</div>}
                </div>
              </div>
              <div className="text-right max-w-xs">
                <h2 className={`text-3xl font-bold mb-4 print:text-black ${isQuotation ? 'text-purple-600' : 'text-orange-600'}`}>
                  {isQuotation ? 'QUOTATION' : 'INVOICE'}
                </h2>
                <div className="text-sm text-gray-600 print:text-black space-y-2">
                  <div><strong>Document #:</strong> {document.document_number}</div>
                  <div><strong>Issue Date:</strong> {new Date(document.issue_date).toLocaleDateString()}</div>
                  <div><strong>Due Date:</strong> {new Date(document.due_date).toLocaleDateString()}</div>
                  <div className="flex items-center justify-end">
                    <strong className="mr-2">Status:</strong>
                    <span className={`px-2 py-1 rounded-full text-xs font-medium print:border print:border-black ${getStatusColor(document.status)}`}>
                      {document.status}
                    </span>
                  </div>
                </div>
              </div>
            </div>

            {/* Title */}
            <h3 className="text-xl font-semibold text-gray-900 mb-6">{document.title}</h3>

            {/* Client and Project Information */}
            <div className="grid grid-cols-2 gap-8 mb-8">
              <div>
                <h4 className="text-sm font-bold text-gray-700 uppercase tracking-wide border-b border-gray-300 pb-2 mb-3">
                  Bill To
                </h4>
                <div className="text-gray-900 leading-relaxed">
                  <div className="font-semibold text-lg mb-2">{document.client_name}</div>
                  {document.client_address && (
                    <div className="mb-2">
                      {document.client_address.split('\n').map((line, i) => (
                        <div key={i}>{line}</div>
                      ))}
                    </div>
                  )}
                  {document.client_phone && <div>Phone: {document.client_phone}</div>}
                  <div>Email: {document.client_email}</div>
                </div>
              </div>
              <div>
                <h4 className="text-sm font-bold text-gray-700 uppercase tracking-wide border-b border-gray-300 pb-2 mb-3">
                  Project
                </h4>
                <div className="text-gray-900 leading-relaxed">
                  <div className="font-semibold text-lg">{document.project_name}</div>
                </div>
              </div>
            </div>

            {/* Items Table */}
            <div className="mb-8">
              <table className="w-full border-collapse">
                <thead>
                  <tr className="bg-gray-50 border-b-2 border-gray-300">
                    <th className="text-left py-3 px-4 font-semibold text-gray-700">Description</th>
                    <th className="text-center py-3 px-4 font-semibold text-gray-700 w-20">Qty</th>
                    <th className="text-center py-3 px-4 font-semibold text-gray-700 w-16">Unit</th>
                    <th className="text-right py-3 px-4 font-semibold text-gray-700 w-24">Unit Price</th>
                    <th className="text-right py-3 px-4 font-semibold text-gray-700 w-24">Total</th>
                  </tr>
                </thead>
                <tbody>
                  {document.items.map((item, index) => (
                    <tr key={item.id} className={`border-b border-gray-200 ${index % 2 === 1 ? 'bg-gray-25' : ''}`}>
                      <td className="py-3 px-4 text-gray-900">{item.description}</td>
                      <td className="py-3 px-4 text-center text-gray-900">{item.quantity}</td>
                      <td className="py-3 px-4 text-center text-gray-600">{item.unit}</td>
                      <td className="py-3 px-4 text-right text-gray-900">${item.unit_price.toFixed(2)}</td>
                      <td className="py-3 px-4 text-right font-semibold text-gray-900">${item.total.toFixed(2)}</td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>

            {/* Totals Section */}
            <div className="flex justify-end mb-8">
              <div className="w-80">
                <div className="bg-gray-50 border border-gray-200 rounded-lg p-6">
                  <div className="space-y-3">
                    <div className="flex justify-between text-gray-600">
                      <span>Subtotal:</span>
                      <span>${document.subtotal.toFixed(2)}</span>
                    </div>
                    {document.discount_amount > 0 && (
                      <div className="flex justify-between text-red-600">
                        <span>Discount ({document.discount_rate}%):</span>
                        <span>-${document.discount_amount.toFixed(2)}</span>
                      </div>
                    )}
                    <div className="flex justify-between text-gray-600">
                      <span>Tax ({document.tax_rate}%):</span>
                      <span>${document.tax_amount.toFixed(2)}</span>
                    </div>
                    <div className="border-t-2 border-gray-400 pt-3 mt-3">
                      <div className="flex justify-between text-xl font-bold text-gray-900">
                        <span>Total:</span>
                        <span>${document.total_amount.toFixed(2)}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Notes and Terms */}
            {document.notes && (
              <div className="mb-6">
                <h4 className="text-sm font-bold text-gray-700 mb-2">Notes</h4>
                <div className="text-gray-600 bg-gray-50 p-4 rounded-lg leading-relaxed">
                  {document.notes.split('\n').map((line, i) => (
                    <div key={i}>{line}</div>
                  ))}
                </div>
              </div>
            )}

            {document.terms_conditions && (
              <div className="mb-6">
                <h4 className="text-sm font-bold text-gray-700 mb-2">Terms & Conditions</h4>
                <div className="text-gray-600 text-sm leading-relaxed">
                  {document.terms_conditions.split('\n').map((line, i) => (
                    <div key={i}>{line}</div>
                  ))}
                </div>
              </div>
            )}

            {/* Footer */}
            <div className="mt-8 pt-6 border-t border-gray-300 text-center text-gray-500 text-sm">
              {company.tax_number && <div>Tax Number: {company.tax_number}</div>}
              {company.registration_number && <div>Registration Number: {company.registration_number}</div>}
              <div className="mt-2 font-medium">
                Thank you for your business!
              </div>
            </div>
          </CardContent>
        </Card>
      </DialogContent>
    </Dialog>
  );
};

export default DocumentPreview;
