// Browser compatibility fixes for local development

export const suppressCloudflareWarnings = () => {
  // Suppress Cloudflare cookie warnings in development
  if (typeof window !== 'undefined' && window.location.hostname.includes('192.168')) {
    const originalConsoleWarn = console.warn;
    console.warn = (...args) => {
      const message = args.join(' ');
      
      // Suppress specific Cloudflare cookie warnings
      if (message.includes('__cf_bm') || 
          message.includes('invalid domain') ||
          message.includes('websocket')) {
        return; // Don't log these warnings
      }
      
      // Log other warnings normally
      originalConsoleWarn.apply(console, args);
    };
  }
};

export const initBrowserFixes = () => {
  suppressCloudflareWarnings();
  
  // Add other browser fixes as needed
  if (typeof window !== 'undefined') {
    // Handle WebSocket connection issues gracefully
    window.addEventListener('error', (event) => {
      if (event.message && event.message.includes('WebSocket')) {
        console.log('WebSocket connection issue detected - continuing with HTTP-only mode');
        event.preventDefault();
      }
    });
  }
};
