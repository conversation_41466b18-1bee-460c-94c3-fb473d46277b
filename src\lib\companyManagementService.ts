import { supabase } from './supabase';

export interface CompanyStats {
  totalMembers: number;
  activeMembers: number;
  pendingInvites: number;
  recentActivity: number;
  totalProjects?: number;
  activeProjects?: number;
  totalRevenue?: number;
  monthlyRevenue?: number;
}

export interface RecentActivity {
  id: string;
  action: string;
  user: string;
  target: string;
  timestamp: Date;
  type: string;
  user_id?: string;
  resource_type?: string;
  resource_id?: string;
}

export interface DepartmentStats {
  id: string;
  name: string;
  description?: string;
  member_count: number;
  active_members: number;
  created_at: string;
}

export class CompanyManagementService {
  // Get comprehensive company statistics
  static async getCompanyStats(): Promise<CompanyStats> {
    try {
      // Get user statistics directly from database
      const { data: users, error: usersError } = await supabase
        .from('user_profiles')
        .select('id, is_active, requires_password_setup');

      if (usersError) throw usersError;

      const allUsers = users || [];
      const activeUsers = allUsers.filter(u => u.is_active);
      const pendingUsers = allUsers.filter(u => u.requires_password_setup);

      // Get recent activity count (last 24 hours)
      const { data: recentLogs, error: logsError } = await supabase
        .from('audit_logs')
        .select('created_at')
        .gte('created_at', new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString())
        .limit(100);

      if (logsError) console.warn('Could not fetch audit logs:', logsError);
      const recentActivity = recentLogs?.length || 0;

      // Get project statistics if projects table exists
      let totalProjects = 0;
      let activeProjects = 0;
      try {
        const { data: projects, error } = await supabase
          .from('projects')
          .select('id, status');
        
        if (!error && projects) {
          totalProjects = projects.length;
          activeProjects = projects.filter(p => 
            p.status === 'active' || p.status === 'in_progress'
          ).length;
        }
      } catch (error) {
        console.log('Projects table not available:', error);
      }

      // Get financial statistics if invoices table exists
      let totalRevenue = 0;
      let monthlyRevenue = 0;
      try {
        const { data: invoices, error } = await supabase
          .from('invoices')
          .select('amount, status, created_at');
        
        if (!error && invoices) {
          totalRevenue = invoices
            .filter(inv => inv.status === 'paid')
            .reduce((sum, inv) => sum + (inv.amount || 0), 0);
          
          const currentMonth = new Date();
          currentMonth.setDate(1);
          monthlyRevenue = invoices
            .filter(inv => 
              inv.status === 'paid' && 
              new Date(inv.created_at) >= currentMonth
            )
            .reduce((sum, inv) => sum + (inv.amount || 0), 0);
        }
      } catch (error) {
        console.log('Invoices table not available:', error);
      }

      return {
        totalMembers: allUsers.length,
        activeMembers: activeUsers.length,
        pendingInvites: pendingUsers.length,
        recentActivity,
        totalProjects,
        activeProjects,
        totalRevenue,
        monthlyRevenue
      };
    } catch (error) {
      console.error('Error getting company stats:', error);
      throw error;
    }
  }

  // Get recent activity with user names
  static async getRecentActivity(limit: number = 10): Promise<RecentActivity[]> {
    try {
      // Get audit logs directly from database
      const { data: auditLogs, error: logsError } = await supabase
        .from('audit_logs')
        .select('*')
        .order('created_at', { ascending: false })
        .limit(limit);

      if (logsError) throw logsError;

      // Get all users for name lookup
      const { data: allUsers, error: usersError } = await supabase
        .from('user_profiles')
        .select('id, first_name, last_name');

      if (usersError) console.warn('Could not fetch users for activity:', usersError);

      return (auditLogs || []).map(log => {
        const logUser = log.user_id && allUsers ? allUsers.find(u => u.id === log.user_id) : null;
        const userName = logUser ? `${logUser.first_name} ${logUser.last_name}` : 'System';

        return {
          id: log.id,
          action: log.action,
          user: userName,
          target: log.resource_id || log.resource_type || 'System',
          timestamp: new Date(log.created_at),
          type: log.resource_type || 'system',
          user_id: log.user_id,
          resource_type: log.resource_type,
          resource_id: log.resource_id
        };
      });
    } catch (error) {
      console.error('Error getting recent activity:', error);
      throw error;
    }
  }

  // Get department statistics
  static async getDepartmentStats(): Promise<DepartmentStats[]> {
    try {
      const { data: departments, error } = await supabase
        .from('departments')
        .select('*');

      if (error) {
        console.warn('Could not fetch departments:', error);
        return [];
      }

      if (!departments) return [];

      // Get user counts for each department
      const { data: users, error: usersError } = await supabase
        .from('user_profiles')
        .select('id, department, is_active');

      if (usersError) {
        console.warn('Could not fetch users for department stats:', usersError);
        return departments.map(dept => ({
          id: dept.id,
          name: dept.name,
          description: dept.description,
          member_count: 0,
          active_members: 0,
          created_at: dept.created_at
        }));
      }
      
      return departments.map(dept => {
        const deptUsers = (users || []).filter(u => u.department === dept.name);
        const activeDeptUsers = deptUsers.filter(u => u.is_active);

        return {
          id: dept.id,
          name: dept.name,
          description: dept.description,
          member_count: deptUsers.length,
          active_members: activeDeptUsers.length,
          created_at: dept.created_at
        };
      });
    } catch (error) {
      console.error('Error getting department stats:', error);
      return [];
    }
  }

  // Get company settings
  static async getCompanySettings() {
    try {
      const { data, error } = await supabase
        .from('company_settings')
        .select('*')
        .order('updated_at', { ascending: false })
        .limit(1)
        .single();

      if (error) {
        console.warn('Could not fetch company settings:', error);
        return null;
      }

      return data;
    } catch (error) {
      console.error('Error getting company settings:', error);
      return null;
    }
  }

  // Update company settings
  static async updateCompanySettings(settings: any) {
    try {
      // Check if we have existing settings
      const { data: existing } = await supabase
        .from('company_settings')
        .select('id')
        .order('updated_at', { ascending: false })
        .limit(1)
        .single();

      if (existing?.id) {
        // Update existing settings
        const { data, error } = await supabase
          .from('company_settings')
          .update(settings)
          .eq('id', existing.id)
          .select()
          .single();

        if (error) throw error;
        return data;
      } else {
        // Insert new settings
        const { data, error } = await supabase
          .from('company_settings')
          .insert([settings])
          .select()
          .single();

        if (error) throw error;
        return data;
      }
    } catch (error) {
      console.error('Error updating company settings:', error);
      throw error;
    }
  }

  // Log company management actions
  static async logAction(
    action: string,
    resourceType?: string,
    resourceId?: string,
    details?: any
  ): Promise<void> {
    try {
      const { error } = await supabase.rpc('log_user_action', {
        action_name: action,
        resource_type: resourceType,
        resource_id: resourceId,
        old_values: null,
        new_values: details
      });

      if (error) console.error('Failed to log action:', error);
    } catch (error) {
      console.error('Error logging company management action:', error);
    }
  }
}
