import { NotificationService } from './notificationService';
import { supabase } from './supabase';

export interface TriggerContext {
  user_id?: string;
  entity_id: string;
  entity_type: string;
  action: 'created' | 'updated' | 'deleted' | 'status_changed';
  data: any;
  metadata?: Record<string, any>;
}

export class NotificationTriggers {
  // Project-related notification triggers
  static async onProjectCreated(project: any, created_by: string): Promise<void> {
    try {
      // Get all users who should be notified about project updates
      const recipients = await this.getProjectRecipients(project.id, created_by);
      
      await NotificationService.createProjectNotification(
        recipients,
        'project_created',
        {
          project_name: project.name,
          project_description: project.description,
          project_id: project.id,
          start_date: project.start_date,
          end_date: project.end_date,
          created_by_name: await this.getUserName(created_by)
        },
        {
          priority: 'medium',
          related_entity_type: 'project',
          related_entity_id: project.id
        }
      );

      console.log(`Project creation notifications sent for project: ${project.name}`);
    } catch (error) {
      console.error('Error sending project creation notifications:', error);
    }
  }

  static async onProjectUpdated(project: any, updated_by: string, changes: string[]): Promise<void> {
    try {
      const recipients = await this.getProjectRecipients(project.id, updated_by);
      
      await NotificationService.createProjectNotification(
        recipients,
        'project_updated',
        {
          project_name: project.name,
          project_id: project.id,
          changes: changes.join(', '),
          updated_by_name: await this.getUserName(updated_by)
        },
        {
          priority: 'medium',
          related_entity_type: 'project',
          related_entity_id: project.id
        }
      );

      console.log(`Project update notifications sent for project: ${project.name}`);
    } catch (error) {
      console.error('Error sending project update notifications:', error);
    }
  }

  static async onProjectDeadlineApproaching(project: any, days_remaining: number): Promise<void> {
    try {
      const recipients = await this.getProjectRecipients(project.id);
      
      await NotificationService.createProjectNotification(
        recipients,
        'project_deadline_approaching',
        {
          project_name: project.name,
          project_id: project.id,
          days_remaining: days_remaining.toString(),
          deadline_date: project.end_date
        },
        {
          priority: days_remaining <= 3 ? 'urgent' : days_remaining <= 7 ? 'high' : 'medium',
          related_entity_type: 'project',
          related_entity_id: project.id
        }
      );

      console.log(`Deadline notifications sent for project: ${project.name} (${days_remaining} days remaining)`);
    } catch (error) {
      console.error('Error sending project deadline notifications:', error);
    }
  }

  // Financial notification triggers
  static async onInvoiceCreated(invoice: any, created_by: string): Promise<void> {
    try {
      const recipients = await this.getFinancialRecipients(created_by);
      
      await NotificationService.createFinancialNotification(
        recipients,
        'invoice_created',
        {
          invoice_number: invoice.invoice_number,
          client_name: invoice.client_name,
          amount: invoice.total_amount,
          due_date: invoice.due_date,
          invoice_id: invoice.id,
          created_by_name: await this.getUserName(created_by)
        },
        {
          priority: 'medium',
          related_entity_type: 'invoice',
          related_entity_id: invoice.id
        }
      );

      console.log(`Invoice creation notifications sent for invoice: ${invoice.invoice_number}`);
    } catch (error) {
      console.error('Error sending invoice creation notifications:', error);
    }
  }

  static async onQuotationCreated(quotation: any, created_by: string): Promise<void> {
    try {
      const recipients = await this.getFinancialRecipients(created_by);

      await NotificationService.createFinancialNotification(
        recipients,
        'quotation_created',
        {
          quotation_number: quotation.document_number,
          client_name: quotation.client_name,
          amount: quotation.total_amount,
          quotation_id: quotation.id,
          created_by_name: await this.getUserName(created_by)
        },
        {
          priority: 'medium',
          related_entity_type: 'quotation',
          related_entity_id: quotation.id
        }
      );

      console.log(`Quotation creation notifications sent to ${recipients.length} recipients`);
    } catch (error) {
      console.error('Error sending quotation creation notifications:', error);
    }
  }

  static async onProjectUpdated(project: any, updated_by: string, changes: string[]): Promise<void> {
    try {
      const recipients = await this.getProjectRecipients(project.id, updated_by);

      await NotificationService.createProjectNotification(
        recipients,
        'project_updated',
        {
          project_name: project.name,
          project_id: project.id,
          changes: changes.join(', '),
          updated_by_name: await this.getUserName(updated_by)
        },
        {
          priority: 'medium',
          related_entity_type: 'project',
          related_entity_id: project.id
        }
      );

      console.log(`Project update notifications sent to ${recipients.length} recipients`);
    } catch (error) {
      console.error('Error sending project update notifications:', error);
    }
  }

  static async onPaymentReceived(payment: any, invoice: any): Promise<void> {
    try {
      const recipients = await this.getFinancialRecipients();

      await NotificationService.createFinancialNotification(
        recipients,
        'payment_received',
        {
          amount: payment.amount,
          payment_date: payment.payment_date,
          payment_method: payment.payment_method,
          reference_number: payment.reference_number,
          invoice_number: invoice.document_number,
          client_name: invoice.client_name
        },
        {
          priority: 'high',
          related_entity_type: 'payment',
          related_entity_id: payment.id || invoice.id
        }
      );

      console.log(`Payment received notifications sent to ${recipients.length} recipients`);
    } catch (error) {
      console.error('Error sending payment received notifications:', error);
    }
  }

  static async onPaymentReceived(payment: any, invoice: any): Promise<void> {
    try {
      const recipients = await this.getFinancialRecipients();
      
      await NotificationService.createFinancialNotification(
        recipients,
        'payment_received',
        {
          amount: payment.amount,
          invoice_number: invoice.invoice_number,
          client_name: invoice.client_name,
          payment_date: payment.payment_date,
          invoice_id: invoice.id
        },
        {
          priority: 'high',
          related_entity_type: 'payment',
          related_entity_id: payment.id
        }
      );

      console.log(`Payment received notifications sent for invoice: ${invoice.invoice_number}`);
    } catch (error) {
      console.error('Error sending payment received notifications:', error);
    }
  }

  // Message notification triggers
  static async onNewMessage(message: any, channel: any, sender_id: string): Promise<void> {
    try {
      // Get channel members excluding the sender
      const recipients = await this.getChannelRecipients(channel.id, sender_id);
      
      if (recipients.length === 0) {
        return; // No one to notify
      }

      const sender_name = await this.getUserName(sender_id);
      const messageContent = message.message_content || message.content || '';
      const message_preview = messageContent.length > 100
        ? messageContent.substring(0, 100) + '...'
        : messageContent;

      await NotificationService.createMessageNotification(
        recipients,
        'new_message',
        {
          sender_name,
          channel_name: channel.name,
          message_content: message_preview,
          channel_id: channel.id,
          message_id: message.id
        },
        {
          priority: 'medium',
          related_entity_type: 'message',
          related_entity_id: message.id,
          send_email: false // Usually don't send emails for every message
        }
      );

      console.log(`Message notifications sent for channel: ${channel.name}`);
    } catch (error) {
      console.error('Error sending message notifications:', error);
    }
  }

  // User management notification triggers
  static async onUserCreated(user: any, created_by: string): Promise<void> {
    try {
      // Notify the new user
      await NotificationService.createNotification({
        user_id: user.id,
        template_name: 'user_created',
        category: 'user',
        variables: {
          user_name: `${user.first_name} ${user.last_name}`.trim() || user.email
        },
        priority: 'medium',
        send_email: true
      });

      // Notify admins
      const adminRecipients = await this.getAdminRecipients(created_by);
      if (adminRecipients.length > 0) {
        await NotificationService.createBulkNotifications(adminRecipients, {
          category: 'user',
          type: 'info',
          title: 'New User Account Created',
          message: `A new user account has been created for ${user.first_name} ${user.last_name} (${user.email}).`,
          priority: 'low',
          related_entity_type: 'user',
          related_entity_id: user.id
        });
      }

      console.log(`User creation notifications sent for: ${user.email}`);
    } catch (error) {
      console.error('Error sending user creation notifications:', error);
    }
  }

  // System notification triggers
  static async onSystemMaintenance(maintenance_info: any): Promise<void> {
    try {
      const allUsers = await this.getAllActiveUsers();
      
      await NotificationService.createSystemNotification(
        allUsers,
        'system_maintenance',
        {
          maintenance_date: maintenance_info.date,
          start_time: maintenance_info.start_time,
          end_time: maintenance_info.end_time,
          duration: maintenance_info.duration
        },
        {
          priority: 'high',
          send_email: true
        }
      );

      console.log(`System maintenance notifications sent to ${allUsers.length} users`);
    } catch (error) {
      console.error('Error sending system maintenance notifications:', error);
    }
  }

  // Asset management notification triggers
  static async onAssetMaintenanceDue(asset: any): Promise<void> {
    try {
      const recipients = await this.getAssetManagerRecipients();
      
      await NotificationService.createBulkNotifications(recipients, {
        category: 'asset',
        type: 'warning',
        title: 'Asset Maintenance Due',
        message: `Asset "${asset.name}" (${asset.asset_id}) is due for maintenance on ${asset.next_maintenance_date}.`,
        action_url: `/assets/${asset.id}`,
        action_label: 'View Asset',
        priority: 'high',
        related_entity_type: 'asset',
        related_entity_id: asset.id,
        send_email: true
      });

      console.log(`Asset maintenance notifications sent for: ${asset.name}`);
    } catch (error) {
      console.error('Error sending asset maintenance notifications:', error);
    }
  }

  // Time tracking notification triggers
  static async onTimeTrackingReminder(user_id: string, site_name: string): Promise<void> {
    try {
      await NotificationService.createNotification({
        user_id,
        category: 'time_tracking',
        type: 'reminder',
        title: 'Time Tracking Reminder',
        message: `Don't forget to clock out from ${site_name}. You've been clocked in for an extended period.`,
        action_url: '/time-tracking',
        action_label: 'Clock Out',
        priority: 'medium',
        send_email: false // Usually just in-app for time tracking
      });

      console.log(`Time tracking reminder sent to user: ${user_id}`);
    } catch (error) {
      console.error('Error sending time tracking reminder:', error);
    }
  }

  // Helper methods to get recipients
  static async getProjectRecipients(project_id: string, exclude_user?: string): Promise<string[]> {
    try {
      // Get users with project management roles or assigned to the project
      const { data: users, error } = await supabase
        .from('user_profiles')
        .select('user_id')
        .in('role_name', ['admin', 'management', 'qs'])
        .neq('user_id', exclude_user || '');

      if (error) {
        throw error;
      }

      return users?.map(u => u.user_id) || [];
    } catch (error) {
      console.error('Error getting project recipients:', error);
      return [];
    }
  }

  static async getFinancialRecipients(exclude_user?: string): Promise<string[]> {
    try {
      const { data: users, error } = await supabase
        .from('user_profiles')
        .select('user_id')
        .in('role_name', ['admin', 'management', 'accountant'])
        .neq('user_id', exclude_user || '');

      if (error) {
        throw error;
      }

      return users?.map(u => u.user_id) || [];
    } catch (error) {
      console.error('Error getting financial recipients:', error);
      return [];
    }
  }

  static async getChannelRecipients(channel_id: string, exclude_user?: string): Promise<string[]> {
    try {
      // Get all users who have access to the channel (simplified - you might have a channel_members table)
      const { data: users, error } = await supabase
        .from('user_profiles')
        .select('user_id')
        .eq('is_active', true)
        .neq('user_id', exclude_user || '');

      if (error) {
        throw error;
      }

      return users?.map(u => u.user_id) || [];
    } catch (error) {
      console.error('Error getting channel recipients:', error);
      return [];
    }
  }

  static async getAdminRecipients(exclude_user?: string): Promise<string[]> {
    try {
      const { data: users, error } = await supabase
        .from('user_profiles')
        .select('user_id')
        .eq('role_name', 'admin')
        .neq('user_id', exclude_user || '');

      if (error) {
        throw error;
      }

      return users?.map(u => u.user_id) || [];
    } catch (error) {
      console.error('Error getting admin recipients:', error);
      return [];
    }
  }

  static async getAssetManagerRecipients(): Promise<string[]> {
    try {
      const { data: users, error } = await supabase
        .from('user_profiles')
        .select('user_id')
        .in('role_name', ['admin', 'management'])
        .eq('is_active', true);

      if (error) {
        throw error;
      }

      return users?.map(u => u.user_id) || [];
    } catch (error) {
      console.error('Error getting asset manager recipients:', error);
      return [];
    }
  }

  static async getAllActiveUsers(): Promise<string[]> {
    try {
      const { data: users, error } = await supabase
        .from('user_profiles')
        .select('user_id')
        .eq('is_active', true);

      if (error) {
        throw error;
      }

      return users?.map(u => u.user_id) || [];
    } catch (error) {
      console.error('Error getting all active users:', error);
      return [];
    }
  }

  static async getUserName(user_id: string): Promise<string> {
    try {
      const { data: profile, error } = await supabase
        .from('user_profiles')
        .select('first_name, last_name, email')
        .eq('user_id', user_id)
        .single();

      if (error || !profile) {
        return 'Unknown User';
      }

      return `${profile.first_name} ${profile.last_name}`.trim() || profile.email || 'Unknown User';
    } catch (error) {
      console.error('Error getting user name:', error);
      return 'Unknown User';
    }
  }

  // Batch notification processing for scheduled tasks
  static async processScheduledNotifications(): Promise<void> {
    try {
      console.log('Processing scheduled notifications...');

      // Check for project deadlines
      await this.checkProjectDeadlines();

      // Check for asset maintenance due dates
      await this.checkAssetMaintenance();

      // Check for overdue invoices
      await this.checkOverdueInvoices();

      console.log('Scheduled notification processing complete');
    } catch (error) {
      console.error('Error processing scheduled notifications:', error);
    }
  }

  static async checkProjectDeadlines(): Promise<void> {
    try {
      const { data: projects, error } = await supabase
        .from('projects')
        .select('*')
        .not('end_date', 'is', null)
        .eq('status', 'active');

      if (error || !projects) {
        return;
      }

      const now = new Date();
      
      for (const project of projects) {
        const endDate = new Date(project.end_date);
        const daysRemaining = Math.ceil((endDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24));

        // Send notifications for 1, 3, 7, and 14 days before deadline
        if ([1, 3, 7, 14].includes(daysRemaining)) {
          await this.onProjectDeadlineApproaching(project, daysRemaining);
        }
      }
    } catch (error) {
      console.error('Error checking project deadlines:', error);
    }
  }

  static async checkAssetMaintenance(): Promise<void> {
    try {
      const { data: assets, error } = await supabase
        .from('company_assets')
        .select('*')
        .not('next_maintenance_date', 'is', null)
        .eq('status', 'active');

      if (error || !assets) {
        return;
      }

      const now = new Date();
      const threeDaysFromNow = new Date(now.getTime() + 3 * 24 * 60 * 60 * 1000);

      for (const asset of assets) {
        const maintenanceDate = new Date(asset.next_maintenance_date);
        
        // Send notification if maintenance is due within 3 days
        if (maintenanceDate <= threeDaysFromNow) {
          await this.onAssetMaintenanceDue(asset);
        }
      }
    } catch (error) {
      console.error('Error checking asset maintenance:', error);
    }
  }

  static async checkOverdueInvoices(): Promise<void> {
    try {
      const { data: invoices, error } = await supabase
        .from('invoices')
        .select('*')
        .eq('status', 'sent')
        .lt('due_date', new Date().toISOString());

      if (error || !invoices) {
        return;
      }

      for (const invoice of invoices) {
        const recipients = await this.getFinancialRecipients();
        
        await NotificationService.createFinancialNotification(
          recipients,
          'invoice_overdue',
          {
            invoice_number: invoice.invoice_number,
            client_name: invoice.client_name,
            amount: invoice.total_amount,
            due_date: invoice.due_date,
            days_overdue: Math.ceil((new Date().getTime() - new Date(invoice.due_date).getTime()) / (1000 * 60 * 60 * 24)),
            invoice_id: invoice.id
          },
          {
            priority: 'high',
            related_entity_type: 'invoice',
            related_entity_id: invoice.id,
            send_email: true
          }
        );
      }
    } catch (error) {
      console.error('Error checking overdue invoices:', error);
    }
  }
}
