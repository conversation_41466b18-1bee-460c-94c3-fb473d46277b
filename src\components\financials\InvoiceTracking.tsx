import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Receipt, 
  Plus, 
  Search, 
  Filter, 
  Download, 
  Eye, 
  Edit, 
  DollarSign, 
  Calendar, 
  Clock, 
  CheckCircle, 
  AlertCircle,
  TrendingUp,
  FileText,
  CreditCard,
  RefreshCw
} from 'lucide-react';
import { useToast } from '@/components/ui/use-toast';
import { useDocuments } from '@/hooks/useDocuments';
import { ClientService } from '@/lib/clients';
import { FinancialService } from '@/lib/financials';

interface PaymentRecord {
  id: string;
  invoice_id: string;
  payment_amount: number;
  payment_date: string;
  payment_method: string;
  reference_number?: string;
  notes?: string;
  status: 'pending' | 'completed' | 'failed';
  created_at: string;
}

interface InvoiceTrackingProps {
  paymentRecords: PaymentRecord[];
  onPaymentRecorded?: () => void; // Callback to refresh financial data
}

const InvoiceTracking: React.FC<InvoiceTrackingProps> = ({ paymentRecords, onPaymentRecorded }) => {
  const { toast } = useToast();
  const { documents, loading: documentsLoading, updateDocument } = useDocuments();
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [isPaymentDialogOpen, setIsPaymentDialogOpen] = useState(false);
  const [selectedInvoice, setSelectedInvoice] = useState<any>(null);
  const [paymentForm, setPaymentForm] = useState({
    payment_amount: '',
    payment_date: new Date().toISOString().split('T')[0],
    payment_method: '',
    reference_number: '',
    notes: ''
  });

  // Filter invoices from documents
  const invoices = documents.filter(doc => doc.type === 'invoice');



  // Use only real invoices from documents - no sample data
  const allInvoices = invoices;

  // Calculate invoice statistics
  const invoiceStats = {
    total: allInvoices.length,
    paid: allInvoices.filter(inv => inv.status === 'paid').length,
    pending: allInvoices.filter(inv => inv.status === 'sent' || inv.status === 'draft').length,
    overdue: allInvoices.filter(inv => {
      const dueDate = new Date(inv.due_date);
      const today = new Date();
      return dueDate < today && inv.status !== 'paid';
    }).length,
    totalValue: allInvoices.reduce((sum, inv) => sum + inv.total_amount, 0),
    paidValue: allInvoices.filter(inv => inv.status === 'paid').reduce((sum, inv) => sum + inv.total_amount, 0),
    outstandingValue: allInvoices.filter(inv => inv.status !== 'paid').reduce((sum, inv) => sum + inv.total_amount, 0)
  };

  // Filter invoices based on search and status
  const filteredInvoices = allInvoices.filter(invoice => {
    const matchesSearch = invoice.client_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         invoice.document_number.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         invoice.project_name.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesStatus = statusFilter === 'all' || invoice.status === statusFilter;
    
    return matchesSearch && matchesStatus;
  });

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'paid':
        return 'bg-green-100 text-green-800';
      case 'sent':
        return 'bg-blue-100 text-blue-800';
      case 'overdue':
        return 'bg-red-100 text-red-800';
      case 'draft':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'paid':
        return <CheckCircle className="w-4 h-4" />;
      case 'sent':
        return <Clock className="w-4 h-4" />;
      case 'overdue':
        return <AlertCircle className="w-4 h-4" />;
      case 'draft':
        return <FileText className="w-4 h-4" />;
      default:
        return <FileText className="w-4 h-4" />;
    }
  };

  const handleRecordPayment = (invoice: any) => {
    setSelectedInvoice(invoice);
    setPaymentForm({
      payment_amount: invoice.total_amount.toString(),
      payment_date: new Date().toISOString().split('T')[0],
      payment_method: '',
      reference_number: '',
      notes: ''
    });
    setIsPaymentDialogOpen(true);
  };

  const handleSyncPayment = async (invoice: any) => {
    try {
      console.log('🔄 Syncing payment for paid invoice:', invoice);

      // Find the client for this invoice
      const clients = await ClientService.getClients();
      console.log('📋 Available clients:', clients.map(c => ({ id: c.id, name: c.name })));
      const client = clients.find(c => c.name === invoice.client_name);
      console.log('🎯 Found client:', client);

      if (!client) {
        toast({
          title: "Error",
          description: `Client "${invoice.client_name}" not found. Please check client name matches exactly.`,
          variant: "destructive",
        });
        return;
      }

      // Create payment record for the full invoice amount
      const paymentData = {
        client_id: client.id,
        invoice_id: invoice.document_number,
        project_id: invoice.project_name,
        amount: invoice.total_amount,
        payment_date: new Date().toISOString().split('T')[0], // Today's date
        payment_method: 'Bank Transfer' as any, // Default method
        reference_number: `SYNC-${invoice.document_number}`,
        description: `Synced payment for invoice ${invoice.document_number}`,
        status: 'Completed' as const,
        processed_by: 'System Sync'
      };

      console.log('💰 Creating synced payment with data:', paymentData);
      const payment = await ClientService.recordPayment(paymentData);
      console.log('✅ Synced payment created:', payment);

      if (!payment) {
        throw new Error('Failed to create synced payment record');
      }

      toast({
        title: "Payment Synced",
        description: `Payment record created for invoice ${invoice.document_number}. Financial data will update shortly.`,
      });

      // Trigger refresh of financial data
      if (onPaymentRecorded) {
        console.log('🔄 Triggering financial data refresh after sync...');
        onPaymentRecorded();
      }

    } catch (error) {
      console.error('Sync payment error:', error);
      toast({
        title: "Sync Error",
        description: "Failed to sync payment record. Please try again.",
        variant: "destructive",
      });
    }
  };

  const handlePaymentSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!paymentForm.payment_amount || !paymentForm.payment_method) {
      toast({
        title: "Validation Error",
        description: "Please fill in payment amount and method",
        variant: "destructive",
      });
      return;
    }

    try {
      const paymentAmount = parseFloat(paymentForm.payment_amount);
      console.log('🔄 Recording payment:', { paymentAmount, invoice: selectedInvoice });

      // Find the client for this invoice
      const clients = await ClientService.getClients();
      console.log('📋 Available clients:', clients.map(c => ({ id: c.id, name: c.name })));
      const client = clients.find(c => c.name === selectedInvoice.client_name);
      console.log('🎯 Found client:', client);

      if (!client) {
        console.error('❌ Client not found for invoice:', selectedInvoice.client_name);
        toast({
          title: "Error",
          description: "Client not found for this invoice",
          variant: "destructive",
        });
        return;
      }

      // Create payment record in client system
      const paymentData = {
        client_id: client.id,
        invoice_id: selectedInvoice.document_number,
        project_id: selectedInvoice.project_name,
        amount: paymentAmount,
        payment_date: paymentForm.payment_date,
        payment_method: paymentForm.payment_method as any,
        reference_number: paymentForm.reference_number,
        description: `Payment for invoice ${selectedInvoice.document_number}`,
        status: 'Completed' as const,
        processed_by: 'System'
      };

      // Record payment in client financial system
      console.log('💰 Recording payment with data:', paymentData);
      const payment = await ClientService.recordPayment(paymentData);
      console.log('✅ Payment recorded:', payment);

      if (!payment) {
        throw new Error('Failed to record payment in client system');
      }

      // Update invoice status to paid if full payment and automatically create revenue
      const newStatus = paymentAmount >= selectedInvoice.total_amount ? 'paid' : 'partial';
      console.log('📄 Updating invoice status to:', newStatus);

      if (newStatus === 'paid') {
        // Use the new automatic revenue creation function
        const result = await FinancialService.updateInvoiceStatus(selectedInvoice.id, newStatus, {
          payment_date: paymentForm.payment_date,
          payment_method: paymentForm.payment_method,
          reference_number: paymentForm.reference_number
        });
        console.log('✅ Invoice status updated and revenue created:', result);
      } else {
        // Just update status for partial payments
        await updateDocument(selectedInvoice.id, {
          status: newStatus,
          last_modified: new Date().toISOString()
        });
        console.log('✅ Invoice status updated to partial');
      }

      if (paymentAmount >= selectedInvoice.total_amount) {
        toast({
          title: "Payment Recorded",
          description: `Payment of $${paymentForm.payment_amount} recorded for invoice ${selectedInvoice.document_number}. Invoice marked as paid.`,
        });
      } else {
        toast({
          title: "Partial Payment Recorded",
          description: `Partial payment of $${paymentForm.payment_amount} recorded for invoice ${selectedInvoice.document_number}`,
        });
      }

      setIsPaymentDialogOpen(false);
      setSelectedInvoice(null);

      // Reset form
      setPaymentForm({
        payment_amount: '',
        payment_date: new Date().toISOString().split('T')[0],
        payment_method: '',
        reference_number: '',
        notes: ''
      });

      // Trigger refresh of financial data
      if (onPaymentRecorded) {
        console.log('🔄 Triggering financial data refresh...');
        onPaymentRecorded();
      }

    } catch (error) {
      console.error('Payment recording error:', error);
      toast({
        title: "Error",
        description: "Failed to record payment. Please try again.",
        variant: "destructive",
      });
    }
  };

  if (documentsLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading invoices...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold">Invoice & Billing Management</h2>
          <p className="text-gray-600">Track invoices and payment status</p>
        </div>
        <Button onClick={() => window.location.href = '/documents'}>
          <Plus className="w-4 h-4 mr-2" />
          Create Invoice
        </Button>
      </div>

      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Invoices</CardTitle>
            <Receipt className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{invoiceStats.total}</div>
            <p className="text-xs text-muted-foreground">
              Total value: ${invoiceStats.totalValue.toLocaleString()}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Paid Invoices</CardTitle>
            <CheckCircle className="h-4 w-4 text-green-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">{invoiceStats.paid}</div>
            <p className="text-xs text-muted-foreground">
              Value: ${invoiceStats.paidValue.toLocaleString()}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Outstanding</CardTitle>
            <Clock className="h-4 w-4 text-blue-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-600">{invoiceStats.pending}</div>
            <p className="text-xs text-muted-foreground">
              Value: ${invoiceStats.outstandingValue.toLocaleString()}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Overdue</CardTitle>
            <AlertCircle className="h-4 w-4 text-red-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-600">{invoiceStats.overdue}</div>
            <p className="text-xs text-muted-foreground">
              Requires attention
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Filters and Search */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="flex-1">
          <div className="relative">
            <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
            <Input
              placeholder="Search invoices by client, number, or project..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>
        </div>
        <Select value={statusFilter} onValueChange={setStatusFilter}>
          <SelectTrigger className="w-48">
            <SelectValue placeholder="Filter by status" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Statuses</SelectItem>
            <SelectItem value="draft">Draft</SelectItem>
            <SelectItem value="sent">Sent</SelectItem>
            <SelectItem value="paid">Paid</SelectItem>
            <SelectItem value="overdue">Overdue</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {/* Invoices Table */}
      <Card>
        <CardHeader>
          <CardTitle>Invoice List</CardTitle>
          <CardDescription>
            Manage and track all your invoices
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Invoice #</TableHead>
                <TableHead>Client</TableHead>
                <TableHead>Project</TableHead>
                <TableHead>Amount</TableHead>
                <TableHead>Issue Date</TableHead>
                <TableHead>Due Date</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredInvoices.map((invoice) => (
                <TableRow key={invoice.id}>
                  <TableCell className="font-medium">{invoice.document_number}</TableCell>
                  <TableCell>{invoice.client_name}</TableCell>
                  <TableCell>{invoice.project_name}</TableCell>
                  <TableCell className="font-semibold">${invoice.total_amount.toLocaleString()}</TableCell>
                  <TableCell>{new Date(invoice.issue_date).toLocaleDateString()}</TableCell>
                  <TableCell>{new Date(invoice.due_date).toLocaleDateString()}</TableCell>
                  <TableCell>
                    <Badge className={getStatusColor(invoice.status)}>
                      <span className="flex items-center space-x-1">
                        {getStatusIcon(invoice.status)}
                        <span className="capitalize">{invoice.status}</span>
                      </span>
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center space-x-2">
                      <Button variant="outline" size="sm">
                        <Eye className="w-4 h-4" />
                      </Button>
                      {invoice.status !== 'paid' ? (
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleRecordPayment(invoice)}
                          title="Record Payment"
                        >
                          <CreditCard className="w-4 h-4" />
                        </Button>
                      ) : (
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleSyncPayment(invoice)}
                          title="Sync Payment Record"
                          className="text-orange-600 border-orange-600 hover:bg-orange-50"
                        >
                          <RefreshCw className="w-4 h-4" />
                        </Button>
                      )}
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
          
          {filteredInvoices.length === 0 && (
            <div className="text-center py-8">
              <Receipt className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-500 mb-4">No invoices found</p>
              <Button onClick={() => window.location.href = '/documents'}>
                <Plus className="w-4 h-4 mr-2" />
                Create Your First Invoice
              </Button>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Payment Recording Dialog */}
      <Dialog open={isPaymentDialogOpen} onOpenChange={setIsPaymentDialogOpen}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle className="flex items-center space-x-2">
              <CreditCard className="w-5 h-5" />
              <span>Record Payment</span>
            </DialogTitle>
            <DialogDescription>
              Record a payment for invoice {selectedInvoice?.document_number}
            </DialogDescription>
          </DialogHeader>
          
          <form onSubmit={handlePaymentSubmit} className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="payment_amount">Payment Amount *</Label>
                <div className="relative">
                  <DollarSign className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                  <Input
                    id="payment_amount"
                    type="number"
                    step="0.01"
                    min="0"
                    value={paymentForm.payment_amount}
                    onChange={(e) => setPaymentForm({...paymentForm, payment_amount: e.target.value})}
                    placeholder="0.00"
                    className="pl-10"
                    required
                  />
                </div>
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="payment_date">Payment Date *</Label>
                <div className="relative">
                  <Calendar className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                  <Input
                    id="payment_date"
                    type="date"
                    value={paymentForm.payment_date}
                    onChange={(e) => setPaymentForm({...paymentForm, payment_date: e.target.value})}
                    className="pl-10"
                    required
                  />
                </div>
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="payment_method">Payment Method *</Label>
              <Select value={paymentForm.payment_method} onValueChange={(value) => setPaymentForm({...paymentForm, payment_method: value})}>
                <SelectTrigger>
                  <SelectValue placeholder="Select payment method" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="bank_transfer">Bank Transfer</SelectItem>
                  <SelectItem value="cash">Cash</SelectItem>
                  <SelectItem value="check">Check</SelectItem>
                  <SelectItem value="credit_card">Credit Card</SelectItem>
                  <SelectItem value="online_payment">Online Payment</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="reference_number">Reference Number</Label>
              <Input
                id="reference_number"
                value={paymentForm.reference_number}
                onChange={(e) => setPaymentForm({...paymentForm, reference_number: e.target.value})}
                placeholder="Transaction ID, check number, etc."
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="notes">Notes</Label>
              <Input
                id="notes"
                value={paymentForm.notes}
                onChange={(e) => setPaymentForm({...paymentForm, notes: e.target.value})}
                placeholder="Additional payment notes..."
              />
            </div>

            <DialogFooter>
              <Button type="button" variant="outline" onClick={() => setIsPaymentDialogOpen(false)}>
                Cancel
              </Button>
              <Button type="submit">
                Record Payment
              </Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default InvoiceTracking;
