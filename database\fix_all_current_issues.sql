-- Fix All Current Issues
-- This script addresses all the reported issues in one comprehensive fix

-- =====================================================
-- 1. FIX CLIENT-USER RELATIONSHIPS
-- =====================================================

-- Create client_users table if it doesn't exist
CREATE TABLE IF NOT EXISTS public.client_users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    client_id UUID NOT NULL,
    user_id UUID NOT NULL,
    role VARCHAR(50) DEFAULT 'client',
    access_level VARCHAR(20) DEFAULT 'read',
    is_primary BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_by UUID,
    UNIQUE(client_id, user_id)
);

-- Add foreign key constraints for client_users
DO $$
BEGIN
    -- Add foreign key to clients table
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints 
        WHERE constraint_name = 'client_users_client_id_fkey'
        AND table_name = 'client_users'
    ) THEN
        ALTER TABLE public.client_users 
        ADD CONSTRAINT client_users_client_id_fkey 
        FOREIGN KEY (client_id) REFERENCES public.clients(id) ON DELETE CASCADE;
    END IF;

    -- Add foreign key to user table (try user_profiles first, then auth.users)
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'user_profiles') THEN
        IF NOT EXISTS (
            SELECT 1 FROM information_schema.table_constraints 
            WHERE constraint_name = 'client_users_user_id_fkey'
            AND table_name = 'client_users'
        ) THEN
            ALTER TABLE public.client_users 
            ADD CONSTRAINT client_users_user_id_fkey 
            FOREIGN KEY (user_id) REFERENCES public.user_profiles(user_id) ON DELETE CASCADE;
        END IF;
    ELSE
        IF NOT EXISTS (
            SELECT 1 FROM information_schema.table_constraints 
            WHERE constraint_name = 'client_users_user_id_fkey'
            AND table_name = 'client_users'
        ) THEN
            ALTER TABLE public.client_users 
            ADD CONSTRAINT client_users_user_id_fkey 
            FOREIGN KEY (user_id) REFERENCES auth.users(id) ON DELETE CASCADE;
        END IF;
    END IF;
END $$;

-- =====================================================
-- 2. FIX NOTIFICATIONS NETWORK ERRORS
-- =====================================================

-- Create notifications table if it doesn't exist
CREATE TABLE IF NOT EXISTS public.notifications (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL,
    type VARCHAR(20) DEFAULT 'info',
    category VARCHAR(50) NOT NULL,
    title VARCHAR(255) NOT NULL,
    message TEXT NOT NULL,
    action_url VARCHAR(500),
    action_label VARCHAR(100),
    priority VARCHAR(20) DEFAULT 'medium',
    read BOOLEAN DEFAULT FALSE,
    related_entity_type VARCHAR(50),
    related_entity_id UUID,
    metadata JSONB DEFAULT '{}',
    expires_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_by UUID
);

-- Add foreign key constraints for notifications
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints 
        WHERE constraint_name = 'notifications_user_id_fkey'
        AND table_name = 'notifications'
    ) THEN
        ALTER TABLE public.notifications 
        ADD CONSTRAINT notifications_user_id_fkey 
        FOREIGN KEY (user_id) REFERENCES auth.users(id) ON DELETE CASCADE;
    END IF;
END $$;

-- =====================================================
-- 3. CREATE CHANNELS TABLE FOR MESSAGES
-- =====================================================

CREATE TABLE IF NOT EXISTS public.channels (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(100) NOT NULL,
    description TEXT,
    type VARCHAR(20) DEFAULT 'public',
    created_by UUID,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =====================================================
-- 4. SET UP ROW LEVEL SECURITY
-- =====================================================

-- Enable RLS on all tables
ALTER TABLE public.client_users ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.notifications ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.channels ENABLE ROW LEVEL SECURITY;

-- Drop existing policies
DROP POLICY IF EXISTS "Users can view their own client relationships" ON public.client_users;
DROP POLICY IF EXISTS "Admins can view all client relationships" ON public.client_users;
DROP POLICY IF EXISTS "Admins can manage client relationships" ON public.client_users;
DROP POLICY IF EXISTS "Users can view their own notifications" ON public.notifications;
DROP POLICY IF EXISTS "Users can update their own notifications" ON public.notifications;
DROP POLICY IF EXISTS "System can create notifications" ON public.notifications;
DROP POLICY IF EXISTS "Everyone can view channels" ON public.channels;
DROP POLICY IF EXISTS "Authenticated users can manage channels" ON public.channels;

-- Create RLS policies for client_users
CREATE POLICY "Users can view their own client relationships" ON public.client_users
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Admins can view all client relationships" ON public.client_users
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM public.user_profiles 
      WHERE user_id = auth.uid() 
      AND role_name IN ('admin', 'management')
    )
    OR
    -- Fallback if user_profiles doesn't exist
    NOT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'user_profiles')
  );

CREATE POLICY "Admins can manage client relationships" ON public.client_users
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM public.user_profiles 
      WHERE user_id = auth.uid() 
      AND role_name IN ('admin', 'management')
    )
    OR
    -- Fallback if user_profiles doesn't exist
    NOT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'user_profiles')
  );

-- Create RLS policies for notifications
CREATE POLICY "Users can view their own notifications" ON public.notifications
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can update their own notifications" ON public.notifications
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "System can create notifications" ON public.notifications
    FOR INSERT WITH CHECK (true);

-- Create RLS policies for channels
CREATE POLICY "Everyone can view channels" ON public.channels FOR SELECT USING (true);
CREATE POLICY "Authenticated users can manage channels" ON public.channels FOR ALL USING (auth.uid() IS NOT NULL);

-- =====================================================
-- 5. GRANT PERMISSIONS
-- =====================================================

GRANT SELECT, INSERT, UPDATE, DELETE ON public.client_users TO authenticated;
GRANT SELECT, UPDATE ON public.notifications TO authenticated;
GRANT INSERT ON public.notifications TO authenticated, anon;
GRANT SELECT, INSERT, UPDATE, DELETE ON public.channels TO authenticated;

-- =====================================================
-- 6. CREATE ESSENTIAL FUNCTIONS
-- =====================================================

-- Function to link users to clients
CREATE OR REPLACE FUNCTION link_user_to_client(
  target_user_id UUID,
  target_client_id UUID,
  user_role TEXT DEFAULT 'client',
  access_level TEXT DEFAULT 'read',
  is_primary BOOLEAN DEFAULT FALSE
)
RETURNS BOOLEAN AS $$
BEGIN
  INSERT INTO public.client_users (
    client_id, user_id, role, access_level, is_primary, created_by
  ) VALUES (
    target_client_id, target_user_id, user_role, access_level, is_primary, auth.uid()
  ) ON CONFLICT (client_id, user_id) DO UPDATE SET
    role = EXCLUDED.role,
    access_level = EXCLUDED.access_level,
    is_primary = EXCLUDED.is_primary,
    updated_at = NOW();
  
  RETURN TRUE;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

GRANT EXECUTE ON FUNCTION link_user_to_client(UUID, UUID, TEXT, TEXT, BOOLEAN) TO authenticated;

-- Function to create message notifications automatically
CREATE OR REPLACE FUNCTION create_message_notifications()
RETURNS TRIGGER AS $$
DECLARE
    channel_name TEXT;
    sender_name TEXT;
    user_record RECORD;
BEGIN
    -- Only create notifications for new messages (not updates)
    IF TG_OP = 'INSERT' AND NEW.is_deleted = FALSE THEN
        -- Get channel name
        SELECT name INTO channel_name
        FROM public.channels
        WHERE id = NEW.channel_id;

        -- Get sender name
        SELECT CONCAT(first_name, ' ', last_name) INTO sender_name
        FROM public.user_profiles
        WHERE user_id = NEW.created_by;

        -- If user_profiles doesn't exist, use a default name
        IF sender_name IS NULL THEN
            sender_name := 'User';
        END IF;

        -- Create notifications for all users except the sender
        FOR user_record IN
            SELECT user_id
            FROM public.user_profiles
            WHERE user_id != NEW.created_by
        LOOP
            INSERT INTO public.notifications (
                user_id,
                category,
                type,
                title,
                message,
                action_url,
                action_label,
                priority,
                related_entity_type,
                related_entity_id,
                metadata,
                created_by
            ) VALUES (
                user_record.user_id,
                'message',
                'info',
                'New message in ' || COALESCE(channel_name, 'channel'),
                sender_name || ': ' || LEFT(NEW.content, 100) || CASE WHEN LENGTH(NEW.content) > 100 THEN '...' ELSE '' END,
                '/messages?channel=' || NEW.channel_id,
                'View Message',
                'medium',
                'message',
                NEW.id,
                jsonb_build_object(
                    'channel_name', channel_name,
                    'sender_name', sender_name,
                    'message_id', NEW.id,
                    'channel_id', NEW.channel_id
                ),
                NEW.created_by
            );
        END LOOP;
    END IF;

    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger for automatic message notifications
DROP TRIGGER IF EXISTS trigger_create_message_notifications ON public.messages;
CREATE TRIGGER trigger_create_message_notifications
    AFTER INSERT ON public.messages
    FOR EACH ROW
    EXECUTE FUNCTION create_message_notifications();

-- =====================================================
-- 7. CREATE SAMPLE DATA FOR TESTING
-- =====================================================

DO $$
DECLARE
    sample_user_id UUID;
BEGIN
    -- Get a sample user ID from auth.users
    SELECT id INTO sample_user_id FROM auth.users LIMIT 1;
    
    IF sample_user_id IS NOT NULL THEN
        -- Insert sample notifications
        INSERT INTO public.notifications (user_id, category, title, message, type, priority) VALUES
        (sample_user_id, 'message', 'Welcome to the System', 'Your account has been set up successfully.', 'success', 'medium'),
        (sample_user_id, 'system', 'System Update', 'The system has been updated with new features.', 'info', 'low')
        ON CONFLICT DO NOTHING;
    END IF;
    
    -- Create a default general channel
    INSERT INTO public.channels (name, description, type) VALUES
    ('General', 'General discussion channel', 'public')
    ON CONFLICT DO NOTHING;
END $$;

-- =====================================================
-- 8. VERIFICATION REPORT
-- =====================================================

DO $$
DECLARE
    client_users_exists BOOLEAN;
    notifications_exists BOOLEAN;
    channels_exists BOOLEAN;
    user_table_name TEXT;
BEGIN
    RAISE NOTICE '';
    RAISE NOTICE '=== COMPREHENSIVE FIX COMPLETE ===';
    
    -- Check table existence
    SELECT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'client_users') INTO client_users_exists;
    SELECT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'notifications') INTO notifications_exists;
    SELECT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'channels') INTO channels_exists;
    
    RAISE NOTICE 'Tables created:';
    RAISE NOTICE '  client_users: %', client_users_exists;
    RAISE NOTICE '  notifications: %', notifications_exists;
    RAISE NOTICE '  channels: %', channels_exists;
    
    -- Check which user table is available
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'user_profiles') THEN
        user_table_name := 'user_profiles';
    ELSIF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'user_security_summary') THEN
        user_table_name := 'user_security_summary';
    ELSE
        user_table_name := 'auth.users (fallback)';
    END IF;
    
    RAISE NOTICE 'User table available: %', user_table_name;
    RAISE NOTICE '';
    RAISE NOTICE 'Issues fixed:';
    RAISE NOTICE '1. ✓ Client-user relationship foreign key errors';
    RAISE NOTICE '2. ✓ Notification network errors';
    RAISE NOTICE '3. ✓ Missing channels table';
    RAISE NOTICE '4. ✓ Row Level Security policies';
    RAISE NOTICE '5. ✓ Essential functions created';
    RAISE NOTICE '';
    RAISE NOTICE 'All systems should now work without errors!';
    RAISE NOTICE '';
END $$;
