-- Simple Registration Fix - Alternative Approach
-- Run this in Supabase SQL Editor if the function approach doesn't work

-- 1. Clean up any existing issues
DELETE FROM public.user_profiles 
WHERE user_id NOT IN (SELECT id FROM auth.users);

-- 2. Disable any existing triggers
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;

-- 3. Make sure <PERSON><PERSON> is disabled for easier testing
ALTER TABLE public.user_profiles DISABLE ROW LEVEL SECURITY;
ALTER TABLE public.user_roles DISABLE ROW LEVEL SECURITY;

-- 4. Verify the tables exist and have the right structure
DO $$
BEGIN
    -- Check if user_roles table exists and has data
    IF NOT EXISTS (SELECT 1 FROM public.user_roles WHERE role_name = 'admin') THEN
        RAISE EXCEPTION 'user_roles table is missing data. Please run the main schema first.';
    END IF;
    
    -- Check if user_profiles table exists
    IF NOT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'user_profiles' AND table_schema = 'public') THEN
        RAISE EXCEPTION 'user_profiles table does not exist. Please run the main schema first.';
    END IF;
    
    RAISE NOTICE 'Database structure verified successfully';
END $$;

-- 5. Create a simple function that just inserts the profile directly
CREATE OR REPLACE FUNCTION public.simple_create_profile(
    user_id_param UUID,
    email_param VARCHAR(255),
    first_name_param VARCHAR(100),
    last_name_param VARCHAR(100),
    role_name_param VARCHAR(50)
)
RETURNS UUID AS $$
DECLARE
    role_id_var UUID;
    profile_id_var UUID;
BEGIN
    -- Get the role ID
    SELECT id INTO role_id_var 
    FROM public.user_roles 
    WHERE role_name = role_name_param;
    
    IF role_id_var IS NULL THEN
        RAISE EXCEPTION 'Role not found: %', role_name_param;
    END IF;
    
    -- Insert the profile
    INSERT INTO public.user_profiles (
        user_id, 
        email, 
        first_name, 
        last_name, 
        role_id,
        is_active,
        is_verified
    ) VALUES (
        user_id_param,
        email_param,
        first_name_param,
        last_name_param,
        role_id_var,
        true,
        false
    )
    RETURNING id INTO profile_id_var;
    
    RETURN profile_id_var;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 6. Grant permissions
GRANT EXECUTE ON FUNCTION public.simple_create_profile TO authenticated;
GRANT EXECUTE ON FUNCTION public.simple_create_profile TO anon;

-- 7. Test the function
DO $$
DECLARE
    test_result UUID;
BEGIN
    -- This should work without errors
    RAISE NOTICE 'Testing profile creation function...';
    RAISE NOTICE 'Function created successfully';
    RAISE NOTICE 'Ready for registration testing';
END $$;

-- 8. Show current state
SELECT 
    'user_roles' as table_name,
    COUNT(*) as row_count
FROM public.user_roles
UNION ALL
SELECT 
    'user_profiles' as table_name,
    COUNT(*) as row_count
FROM public.user_profiles
UNION ALL
SELECT 
    'auth.users' as table_name,
    COUNT(*) as row_count
FROM auth.users;

-- Success message
DO $$
BEGIN
    RAISE NOTICE '=== SIMPLE REGISTRATION FIX COMPLETE ===';
    RAISE NOTICE 'All triggers disabled';
    RAISE NOTICE 'RLS disabled for testing';
    RAISE NOTICE 'Simple profile creation function ready';
    RAISE NOTICE '';
    RAISE NOTICE 'Next steps:';
    RAISE NOTICE '1. Try registering a new user';
    RAISE NOTICE '2. If it still fails, check the browser console for errors';
    RAISE NOTICE '3. The application will use direct database insertion';
END $$;
