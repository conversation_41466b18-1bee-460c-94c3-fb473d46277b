import { supabase } from './supabase'

// Task interface
export interface Task {
  id: string
  name: string
  description?: string
  status: string
  priority: string
  start_date: string
  end_date: string
  progress: number
  assignee?: string
  project_id: string
  estimated_hours?: number
  actual_hours?: number
  dependencies?: string[]
  tags?: string[]
  parent_task_id?: string
  created_at?: string
  updated_at?: string
}

// Task statuses
export const TASK_STATUSES = [
  'Not Started',
  'In Progress',
  'Completed',
  'On Hold',
  'Cancelled'
] as const

// Task priorities
export const TASK_PRIORITIES = [
  'Low',
  'Medium',
  'High',
  'Critical'
] as const

export class TaskService {
  // Test database connection
  static async testConnection(): Promise<{ success: boolean; message: string; details?: any }> {
    try {
      console.log('Testing tasks table connection...');

      const { data, error } = await supabase
        .from('tasks')
        .select('count', { count: 'exact', head: true })

      if (error) {
        console.error('Tasks table connection error:', error);
        return {
          success: false,
          message: `Database error: ${error.message}`,
          details: error
        };
      }

      console.log('Tasks table connection test successful');
      return {
        success: true,
        message: 'Successfully connected to tasks table',
        details: { count: data }
      };
    } catch (error) {
      console.error('Tasks connection test failed:', error);
      return {
        success: false,
        message: error instanceof Error ? error.message : 'Unknown connection error',
        details: error
      };
    }
  }

  // Get all tasks
  static async getTasks(): Promise<Task[]> {
    try {
      console.log('Fetching tasks from Supabase...');

      const { data, error } = await supabase
        .from('tasks')
        .select('*')
        .order('created_at', { ascending: false })

      if (error) {
        console.error('Error fetching tasks:', error)
        throw new Error(`Database error: ${error.message}`)
      }

      console.log('Successfully fetched tasks:', data);
      return data || []
    } catch (error) {
      console.error('Failed to fetch tasks:', error)
      throw error
    }
  }

  // Get tasks by project ID
  static async getTasksByProject(projectId: string): Promise<Task[]> {
    try {
      console.log('Fetching tasks for project:', projectId);

      const { data, error } = await supabase
        .from('tasks')
        .select('*')
        .eq('project_id', projectId)
        .order('created_at', { ascending: false })

      if (error) {
        console.error('Error fetching project tasks:', error)
        throw new Error(`Database error: ${error.message}`)
      }

      console.log('Successfully fetched project tasks:', data);
      return data || []
    } catch (error) {
      console.error('Failed to fetch project tasks:', error)
      throw error
    }
  }

  // Add new task
  static async addTask(task: Omit<Task, 'id' | 'created_at' | 'updated_at'>): Promise<Task> {
    try {
      console.log('Adding task to Supabase:', task);

      const { data, error } = await supabase
        .from('tasks')
        .insert([{
          name: task.name,
          description: task.description,
          status: task.status,
          priority: task.priority,
          start_date: task.start_date,
          end_date: task.end_date,
          progress: task.progress,
          assignee: task.assignee,
          project_id: task.project_id,
          estimated_hours: task.estimated_hours,
          actual_hours: task.actual_hours,
          dependencies: task.dependencies,
          tags: task.tags,
          parent_task_id: task.parent_task_id
        }])
        .select()
        .single()
      
      if (error) {
        console.error('Error adding task:', error)
        throw error
      }
      
      console.log('Successfully added task:', data);
      return data
    } catch (error) {
      console.error('Failed to add task:', error)
      throw error
    }
  }

  // Update task
  static async updateTask(id: string, updates: Partial<Omit<Task, 'id' | 'created_at'>>): Promise<Task> {
    try {
      console.log('Updating task:', id, updates);

      const { data, error } = await supabase
        .from('tasks')
        .update({
          ...updates,
          updated_at: new Date().toISOString()
        })
        .eq('id', id)
        .select()
        .single()
      
      if (error) {
        console.error('Error updating task:', error)
        throw error
      }
      
      console.log('Successfully updated task:', data);
      return data
    } catch (error) {
      console.error('Failed to update task:', error)
      throw error
    }
  }

  // Delete task
  static async deleteTask(id: string): Promise<void> {
    try {
      console.log('Deleting task:', id);

      const { error } = await supabase
        .from('tasks')
        .delete()
        .eq('id', id)
      
      if (error) {
        console.error('Error deleting task:', error)
        throw error
      }

      console.log('Successfully deleted task:', id);
    } catch (error) {
      console.error('Failed to delete task:', error)
      throw error
    }
  }

  // Subscribe to real-time changes
  static subscribeToChanges(callback: (payload: any) => void) {
    return supabase
      .channel('tasks')
      .on('postgres_changes', 
        { 
          event: '*', 
          schema: 'public', 
          table: 'tasks' 
        }, 
        callback
      )
      .subscribe()
  }

  // Get task statistics
  static async getTaskStats(projectId?: string) {
    try {
      let query = supabase.from('tasks').select('*')
      
      if (projectId) {
        query = query.eq('project_id', projectId)
      }

      const { data: tasks, error } = await query

      if (error) {
        console.error('Error fetching task stats:', error)
        throw error
      }

      const totalTasks = tasks?.length || 0
      const completedTasks = tasks?.filter(task => task.status === 'Completed').length || 0
      const inProgressTasks = tasks?.filter(task => task.status === 'In Progress').length || 0
      const overdueTasks = tasks?.filter(task => 
        new Date(task.end_date) < new Date() && task.status !== 'Completed'
      ).length || 0

      const averageProgress = totalTasks > 0 
        ? tasks.reduce((sum, task) => sum + task.progress, 0) / totalTasks 
        : 0

      return {
        totalTasks,
        completedTasks,
        inProgressTasks,
        overdueTasks,
        averageProgress,
        completionRate: totalTasks > 0 ? (completedTasks / totalTasks) * 100 : 0
      }
    } catch (error) {
      console.error('Failed to get task stats:', error)
      throw error
    }
  }

  // Export tasks to CSV
  static async exportToCSV(projectId?: string): Promise<string> {
    try {
      const tasks = projectId 
        ? await this.getTasksByProject(projectId)
        : await this.getTasks()
      
      const headers = [
        'Name', 'Description', 'Status', 'Priority', 'Start Date', 'End Date', 
        'Progress', 'Assignee', 'Project ID', 'Estimated Hours', 'Actual Hours', 'Created At'
      ]
      
      const csvContent = [
        headers.join(','),
        ...tasks.map(task => [
          `"${task.name}"`,
          `"${task.description || ''}"`,
          `"${task.status}"`,
          `"${task.priority}"`,
          task.start_date,
          task.end_date,
          task.progress,
          `"${task.assignee || ''}"`,
          task.project_id,
          task.estimated_hours || 0,
          task.actual_hours || 0,
          task.created_at ? new Date(task.created_at).toLocaleDateString() : ''
        ].join(','))
      ].join('\n')

      return csvContent
    } catch (error) {
      console.error('Failed to export tasks:', error)
      throw error
    }
  }
}
