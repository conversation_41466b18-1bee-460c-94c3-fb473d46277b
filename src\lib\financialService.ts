import { ClientService } from './clients';
import { Client, ClientPayment, ClientInvoice, ClientFinancialSummary } from '@/types/client';

export class FinancialService {
  // Get comprehensive financial overview for all clients
  static async getFinancialOverview() {
    try {
      const clients = await ClientService.getClients();
      const allInvoices = JSON.parse(localStorage.getItem('client_invoices') || '[]');
      const allPayments = JSON.parse(localStorage.getItem('client_payments') || '[]');
      
      // Calculate totals - Revenue should be PAID invoices only
      const totalInvoiced = allInvoices.reduce((sum: number, inv: any) => sum + inv.total_amount, 0);
      const totalPaid = allPayments.reduce((sum: number, pay: any) => sum + pay.amount, 0);
      const totalRevenue = totalPaid; // Revenue = actual payments received
      const outstandingAmount = totalInvoiced - totalPaid;
      const overdueAmount = allInvoices
        .filter((inv: any) => inv.status === 'Overdue')
        .reduce((sum: number, inv: any) => sum + inv.total_amount, 0);
      
      // Get this month's payments
      const thisMonth = new Date();
      const thisMonthKey = `${thisMonth.getFullYear()}-${String(thisMonth.getMonth() + 1).padStart(2, '0')}`;
      const paidThisMonth = allPayments
        .filter((pay: any) => pay.payment_date.startsWith(thisMonthKey))
        .reduce((sum: number, pay: any) => sum + pay.amount, 0);
      
      // Calculate collection metrics
      const invoicesSent = allInvoices.length;
      const invoicesPaid = allInvoices.filter((inv: any) => inv.status === 'Paid').length;
      const collectionRate = invoicesSent > 0 ? (invoicesPaid / invoicesSent) * 100 : 0;
      
      // Calculate average payment time
      const paidInvoices = allInvoices.filter((inv: any) => inv.paid_date);
      const averagePaymentTime = paidInvoices.length > 0 
        ? Math.round(paidInvoices.reduce((sum: number, inv: any) => {
            const issueDate = new Date(inv.issue_date);
            const paidDate = new Date(inv.paid_date);
            const daysDiff = Math.floor((paidDate.getTime() - issueDate.getTime()) / (1000 * 60 * 60 * 24));
            return sum + daysDiff;
          }, 0) / paidInvoices.length)
        : 30;
      
      return {
        totalClients: clients.length,
        activeClients: clients.filter(c => c.status === 'Active').length,
        totalInvoiced,
        totalRevenue, // This is the actual revenue (paid invoices)
        totalPaid,
        outstandingAmount,
        overdueAmount,
        paidThisMonth,
        invoicesSent,
        invoicesPaid,
        collectionRate,
        averagePaymentTime,
        clients,
        invoices: allInvoices,
        payments: allPayments
      };
    } catch (error) {
      console.error('Error getting financial overview:', error);
      throw error;
    }
  }
  
  // Get recent financial activity across all clients
  static async getRecentActivity(limit: number = 10) {
    try {
      const allInvoices = JSON.parse(localStorage.getItem('client_invoices') || '[]');
      const allPayments = JSON.parse(localStorage.getItem('client_payments') || '[]');
      const clients = await ClientService.getClients();
      
      // Combine and sort recent activity
      const activity = [];
      
      // Add recent payments
      allPayments.forEach((payment: any) => {
        const client = clients.find(c => c.id === payment.client_id);
        activity.push({
          type: 'payment',
          date: payment.payment_date,
          client: client?.name || 'Unknown Client',
          amount: payment.amount,
          description: `Payment received via ${payment.payment_method}`,
          status: payment.status,
          id: payment.id
        });
      });
      
      // Add recent invoices
      allInvoices.forEach((invoice: any) => {
        const client = clients.find(c => c.id === invoice.client_id);
        activity.push({
          type: 'invoice',
          date: invoice.issue_date,
          client: client?.name || 'Unknown Client',
          amount: invoice.total_amount,
          description: `Invoice ${invoice.invoice_number} issued`,
          status: invoice.status,
          id: invoice.id
        });
      });
      
      // Sort by date (most recent first) and limit
      return activity
        .sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime())
        .slice(0, limit);
        
    } catch (error) {
      console.error('Error getting recent activity:', error);
      return [];
    }
  }
  
  // Get financial summary for a specific client
  static async getClientFinancialSummary(clientId: string): Promise<ClientFinancialSummary> {
    return ClientService.getClientFinancialSummary(clientId);
  }
  
  // Get overdue invoices across all clients
  static async getOverdueInvoices() {
    try {
      const allInvoices = JSON.parse(localStorage.getItem('client_invoices') || '[]');
      const clients = await ClientService.getClients();
      
      const today = new Date();
      
      return allInvoices
        .filter((inv: any) => {
          const dueDate = new Date(inv.due_date);
          return dueDate < today && inv.status !== 'Paid' && inv.status !== 'Cancelled';
        })
        .map((invoice: any) => {
          const client = clients.find(c => c.id === invoice.client_id);
          const dueDate = new Date(invoice.due_date);
          const daysOverdue = Math.floor((today.getTime() - dueDate.getTime()) / (1000 * 60 * 60 * 24));
          
          return {
            ...invoice,
            client_name: client?.name || 'Unknown Client',
            days_overdue: daysOverdue
          };
        })
        .sort((a: any, b: any) => b.days_overdue - a.days_overdue);
        
    } catch (error) {
      console.error('Error getting overdue invoices:', error);
      return [];
    }
  }
  
  // Get payment trends for dashboard charts
  static async getPaymentTrends(months: number = 6) {
    try {
      const allPayments = JSON.parse(localStorage.getItem('client_payments') || '[]');
      const allInvoices = JSON.parse(localStorage.getItem('client_invoices') || '[]');
      
      const trends = [];
      
      for (let i = months - 1; i >= 0; i--) {
        const date = new Date();
        date.setMonth(date.getMonth() - i);
        const monthKey = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;
        
        const monthPayments = allPayments.filter((pay: any) => pay.payment_date.startsWith(monthKey));
        const monthInvoices = allInvoices.filter((inv: any) => inv.issue_date.startsWith(monthKey));
        
        const paymentsReceived = monthPayments.reduce((sum: number, pay: any) => sum + pay.amount, 0);
        const invoicesSent = monthInvoices.reduce((sum: number, inv: any) => sum + inv.total_amount, 0);
        
        trends.push({
          month: date.toLocaleDateString('en-US', { month: 'short', year: 'numeric' }),
          monthKey,
          payments_received: paymentsReceived,
          invoices_sent: invoicesSent,
          outstanding: Math.max(0, invoicesSent - paymentsReceived)
        });
      }
      
      return trends;
    } catch (error) {
      console.error('Error getting payment trends:', error);
      return [];
    }
  }
  
  // Get top performing clients by revenue (actual payments received)
  static async getTopClients(limit: number = 5) {
    try {
      const clients = await ClientService.getClients();
      const allInvoices = JSON.parse(localStorage.getItem('client_invoices') || '[]');
      const allPayments = JSON.parse(localStorage.getItem('client_payments') || '[]');

      const clientRevenue = new Map();

      // Calculate actual revenue from payments, not invoices
      allPayments.forEach((payment: any) => {
        const current = clientRevenue.get(payment.client_id) || 0;
        clientRevenue.set(payment.client_id, current + payment.amount);
      });
      
      return Array.from(clientRevenue.entries())
        .map(([clientId, revenue]) => {
          const client = clients.find(c => c.id === clientId);
          const invoiceCount = allInvoices.filter((inv: any) => inv.client_id === clientId).length;
          const paymentCount = allPayments.filter((pay: any) => pay.client_id === clientId).length;
          const projectCount = new Set(allInvoices.filter((inv: any) => inv.client_id === clientId).map((inv: any) => inv.project_id)).size;

          return {
            client_id: clientId,
            client_name: client?.name || 'Unknown Client',
            total_revenue: revenue as number, // This is now actual revenue from payments
            invoice_count: invoiceCount,
            payment_count: paymentCount,
            project_count: projectCount,
            industry: client?.industry || 'Unknown'
          };
        })
        .sort((a, b) => b.total_revenue - a.total_revenue)
        .slice(0, limit);
        
    } catch (error) {
      console.error('Error getting top clients:', error);
      return [];
    }
  }
  
  // Refresh all financial data (useful after adding new clients)
  static async refreshFinancialData() {
    try {
      // Initialize financial data for any clients that don't have it
      await ClientService.initializeFinancialDataForExistingClients();
      
      // Return fresh overview
      return await this.getFinancialOverview();
    } catch (error) {
      console.error('Error refreshing financial data:', error);
      throw error;
    }
  }
  
  // Get industry performance breakdown (based on actual revenue from payments)
  static async getIndustryBreakdown() {
    try {
      const clients = await ClientService.getClients();
      const allInvoices = JSON.parse(localStorage.getItem('client_invoices') || '[]');
      const allPayments = JSON.parse(localStorage.getItem('client_payments') || '[]');

      const industryMap = new Map();

      clients.forEach(client => {
        const industry = client.industry || 'Other';
        const current = industryMap.get(industry) || {
          client_count: 0,
          revenue: 0,
          invoices: 0,
          payments: 0,
          avg_payment_terms: 0
        };

        // Calculate actual revenue from payments, not invoices
        const clientRevenue = allPayments
          .filter((pay: any) => pay.client_id === client.id)
          .reduce((sum: number, pay: any) => sum + pay.amount, 0);

        const clientInvoices = allInvoices.filter((inv: any) => inv.client_id === client.id).length;
        const clientPayments = allPayments.filter((pay: any) => pay.client_id === client.id).length;
        
        industryMap.set(industry, {
          client_count: current.client_count + 1,
          revenue: current.revenue + clientRevenue, // Now actual revenue from payments
          invoices: current.invoices + clientInvoices,
          payments: current.payments + clientPayments,
          avg_payment_terms: current.avg_payment_terms + (client.payment_terms || 30)
        });
      });
      
      return Array.from(industryMap.entries())
        .map(([industry, data]) => ({
          industry,
          client_count: data.client_count,
          revenue: data.revenue, // Actual revenue from payments
          invoice_count: data.invoices,
          payment_count: data.payments,
          avg_payment_terms: Math.round(data.avg_payment_terms / data.client_count),
          avg_revenue_per_client: Math.round(data.revenue / data.client_count)
        }))
        .sort((a, b) => b.revenue - a.revenue);
        
    } catch (error) {
      console.error('Error getting industry breakdown:', error);
      return [];
    }
  }
}
