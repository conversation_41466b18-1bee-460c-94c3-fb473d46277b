import { AnalyticsService, AnalyticsData } from './analytics';

export interface RealTimeMetrics {
  revenue: number;
  expenses: number;
  profit: number;
  cashFlow: number;
  activeProjects: number;
  completedProjects: number;
  activeClients: number;
  pendingInvoices: number;
  timestamp: Date;
}

export interface StreamingData {
  id: string;
  timestamp: Date;
  value: number;
  category: string;
  type: 'revenue' | 'expense' | 'cashflow' | 'project';
}

export interface DashboardData {
  metrics: RealTimeMetrics;
  revenueChart: any[];
  expenseChart: any[];
  cashFlowChart: any[];
  projectChart: any[];
  clientChart: any[];
  trends: any[];
  alerts: any[];
}

class RealTimeDataService {
  private subscribers: Map<string, (data: any) => void> = new Map();
  private intervals: Map<string, NodeJS.Timeout> = new Map();
  private isRunning = false;

  // Simulate real-time data updates
  startRealTimeUpdates() {
    if (this.isRunning) return;
    
    this.isRunning = true;
    console.log('🔄 Starting real-time data updates...');

    // Update metrics every 5 seconds
    const metricsInterval = setInterval(() => {
      this.updateMetrics();
    }, 5000);

    // Update charts every 10 seconds
    const chartsInterval = setInterval(() => {
      this.updateCharts();
    }, 10000);

    // Generate streaming data every 2 seconds
    const streamInterval = setInterval(() => {
      this.generateStreamingData();
    }, 2000);

    this.intervals.set('metrics', metricsInterval);
    this.intervals.set('charts', chartsInterval);
    this.intervals.set('stream', streamInterval);
  }

  stopRealTimeUpdates() {
    this.isRunning = false;
    console.log('⏹️ Stopping real-time data updates...');
    
    this.intervals.forEach((interval) => {
      clearInterval(interval);
    });
    this.intervals.clear();
  }

  subscribe(id: string, callback: (data: any) => void) {
    this.subscribers.set(id, callback);
    console.log(`📡 Subscribed to real-time updates: ${id}`);
  }

  unsubscribe(id: string) {
    this.subscribers.delete(id);
    console.log(`📡 Unsubscribed from real-time updates: ${id}`);
  }

  private notify(type: string, data: any) {
    this.subscribers.forEach((callback, id) => {
      if (id.includes(type) || id === 'all') {
        callback({ type, data, timestamp: new Date() });
      }
    });
  }

  private async updateMetrics() {
    try {
      // Get current analytics data
      const analyticsData = await AnalyticsService.getAnalyticsData();
      
      // Add some realistic variations
      const variation = () => (Math.random() - 0.5) * 0.1; // ±5% variation
      
      const metrics: RealTimeMetrics = {
        revenue: analyticsData.financialSummary.totalRevenue * (1 + variation()),
        expenses: analyticsData.financialSummary.totalExpenses * (1 + variation()),
        profit: analyticsData.financialSummary.netProfit * (1 + variation()),
        cashFlow: analyticsData.cashFlowAnalytics.netCashFlow * (1 + variation()),
        activeProjects: analyticsData.projectAnalytics.activeProjects + Math.floor(Math.random() * 3),
        completedProjects: analyticsData.projectAnalytics.completedProjects,
        activeClients: analyticsData.clientAnalytics.activeClients + Math.floor(Math.random() * 2),
        pendingInvoices: Math.floor(Math.random() * 10) + 5,
        timestamp: new Date()
      };

      this.notify('metrics', metrics);
    } catch (error) {
      console.error('Error updating real-time metrics:', error);
    }
  }

  private async updateCharts() {
    try {
      // Generate new chart data points
      const now = new Date();
      const timeLabel = now.toLocaleTimeString();

      // Revenue chart update
      const revenueData = {
        name: timeLabel,
        revenue: Math.floor(Math.random() * 50000) + 100000,
        target: 120000
      };

      // Expense chart update
      const expenseData = {
        name: timeLabel,
        expenses: Math.floor(Math.random() * 30000) + 50000,
        budget: 70000
      };

      // Cash flow chart update
      const cashFlowData = {
        name: timeLabel,
        inflow: Math.floor(Math.random() * 40000) + 80000,
        outflow: Math.floor(Math.random() * 35000) + 60000
      };

      this.notify('charts', {
        revenue: revenueData,
        expense: expenseData,
        cashFlow: cashFlowData
      });
    } catch (error) {
      console.error('Error updating chart data:', error);
    }
  }

  private generateStreamingData() {
    const types: Array<'revenue' | 'expense' | 'cashflow' | 'project'> = ['revenue', 'expense', 'cashflow', 'project'];
    const categories = {
      revenue: ['Construction', 'Consulting', 'Materials', 'Equipment'],
      expense: ['Labor', 'Materials', 'Equipment', 'Overhead'],
      cashflow: ['Inflow', 'Outflow'],
      project: ['Started', 'Completed', 'On Hold']
    };

    const type = types[Math.floor(Math.random() * types.length)];
    const categoryList = categories[type];
    const category = categoryList[Math.floor(Math.random() * categoryList.length)];

    const streamData: StreamingData = {
      id: `${type}-${Date.now()}`,
      timestamp: new Date(),
      value: Math.floor(Math.random() * 10000) + 1000,
      category,
      type
    };

    this.notify('stream', streamData);
  }

  // Generate comprehensive dashboard data
  async getDashboardData(): Promise<DashboardData> {
    try {
      console.log('📊 Generating comprehensive dashboard data...');
      
      const analyticsData = await AnalyticsService.getAnalyticsData();
      
      // Generate real-time metrics
      const metrics: RealTimeMetrics = {
        revenue: analyticsData.financialSummary.totalRevenue,
        expenses: analyticsData.financialSummary.totalExpenses,
        profit: analyticsData.financialSummary.netProfit,
        cashFlow: analyticsData.cashFlowAnalytics.netCashFlow,
        activeProjects: analyticsData.projectAnalytics.activeProjects,
        completedProjects: analyticsData.projectAnalytics.completedProjects,
        activeClients: analyticsData.clientAnalytics.activeClients,
        pendingInvoices: Math.floor(Math.random() * 15) + 5,
        timestamp: new Date()
      };

      // Generate enhanced chart data
      const revenueChart = this.generateTimeSeriesData('Revenue', 30, 50000, 150000);
      const expenseChart = this.generateTimeSeriesData('Expenses', 30, 30000, 100000);
      const cashFlowChart = this.generateCashFlowData(30);
      const projectChart = await this.generateProjectData();
      const clientChart = analyticsData.clientAnalytics.topClients.slice(0, 10);

      // Generate trend data
      const trends = this.generateTrendData();

      // Generate alerts
      const alerts = this.generateAlerts(metrics);

      return {
        metrics,
        revenueChart,
        expenseChart,
        cashFlowChart,
        projectChart,
        clientChart,
        trends,
        alerts
      };
    } catch (error) {
      console.error('Error generating dashboard data:', error);
      throw error;
    }
  }

  private generateTimeSeriesData(name: string, days: number, min: number, max: number) {
    const data = [];
    const now = new Date();
    
    for (let i = days; i >= 0; i--) {
      const date = new Date(now.getTime() - i * 24 * 60 * 60 * 1000);
      const value = Math.floor(Math.random() * (max - min)) + min;
      
      data.push({
        name: date.toLocaleDateString(),
        value,
        date: date.toISOString()
      });
    }
    
    return data;
  }

  private generateCashFlowData(days: number) {
    // No demo data - return empty array for real data only
    return [];
  }

  private async generateProjectData() {
    try {
      // Get real project analytics data
      const analyticsData = await AnalyticsService.getAnalyticsData();
      const projectAnalytics = analyticsData.projectAnalytics;

      // Convert project status data to chart format
      const data = projectAnalytics.projectsByStatus.map(status => ({
        name: status.status,
        value: status.count,
        percentage: status.percentage
      }));

      // If no real data, return default structure
      if (data.length === 0) {
        const defaultStatuses = ['Planning', 'In Progress', 'On Hold', 'Completed'];
        return defaultStatuses.map(status => ({
          name: status,
          value: 0,
          percentage: 0
        }));
      }

      return data;
    } catch (error) {
      console.error('Error generating project data:', error);
      // Fallback to mock data if there's an error
      const statuses = ['Planning', 'In Progress', 'On Hold', 'Completed'];
      const data = statuses.map(status => ({
        name: status,
        value: Math.floor(Math.random() * 5) + 1,
        percentage: 0
      }));

      const total = data.reduce((sum, item) => sum + item.value, 0);
      data.forEach(item => {
        item.percentage = total > 0 ? (item.value / total) * 100 : 0;
      });

      return data;
    }
  }

  private generateTrendData() {
    return [
      {
        metric: 'Revenue Growth',
        value: (Math.random() * 20 + 5).toFixed(1) + '%',
        trend: 'up',
        period: 'Month over Month'
      },
      {
        metric: 'Project Completion Rate',
        value: (Math.random() * 15 + 80).toFixed(1) + '%',
        trend: 'up',
        period: 'This Quarter'
      },
      {
        metric: 'Client Satisfaction',
        value: (Math.random() * 10 + 85).toFixed(1) + '%',
        trend: 'neutral',
        period: 'Last 30 Days'
      },
      {
        metric: 'Cost Efficiency',
        value: (Math.random() * 8 + 12).toFixed(1) + '%',
        trend: 'down',
        period: 'Year over Year'
      }
    ];
  }

  private generateAlerts(metrics: RealTimeMetrics) {
    const alerts = [];

    if (metrics.cashFlow < 0) {
      alerts.push({
        id: 'cashflow-negative',
        type: 'warning',
        title: 'Negative Cash Flow',
        message: 'Current cash flow is negative. Monitor closely.',
        timestamp: new Date()
      });
    }

    if (metrics.pendingInvoices > 10) {
      alerts.push({
        id: 'invoices-pending',
        type: 'info',
        title: 'Pending Invoices',
        message: `${metrics.pendingInvoices} invoices are pending payment.`,
        timestamp: new Date()
      });
    }

    if (metrics.profit < metrics.revenue * 0.1) {
      alerts.push({
        id: 'low-profit-margin',
        type: 'warning',
        title: 'Low Profit Margin',
        message: 'Profit margin is below 10%. Review expenses.',
        timestamp: new Date()
      });
    }

    return alerts;
  }
}

export const realTimeDataService = new RealTimeDataService();
export default realTimeDataService;
