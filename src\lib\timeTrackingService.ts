import { supabase } from './supabase';

export interface Site {
  id: string;
  name: string;
  address?: string;
  project_id?: string;
  site_code: string;
  description?: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
  project?: {
    id: string;
    name: string;
  };
}

export interface TimeEntry {
  id: string;
  user_id: string;
  site_id: string;
  project_id?: string;
  clock_in_time: string;
  clock_out_time?: string;
  break_duration_minutes: number;
  total_hours?: number;
  overtime_hours: number;
  work_description?: string;
  status: 'active' | 'completed' | 'break';
  location_lat?: number;
  location_lng?: number;
  created_at: string;
  updated_at: string;
  site?: Site;
  project?: {
    id: string;
    name: string;
  };
  user_profile?: {
    first_name: string;
    last_name: string;
    role_name: string;
  };
}

export interface SiteAttendance {
  user_id: string;
  user_name: string;
  user_role: string;
  site_id: string;
  site_name: string;
  clock_in_time: string;
  clock_out_time?: string;
  total_hours?: number;
  status: string;
  work_description?: string;
}

export interface ClockInData {
  site_id: string;
  project_id?: string;
  work_description?: string;
  location_lat?: number;
  location_lng?: number;
}

export interface ClockOutData {
  work_description?: string;
}

export class TimeTrackingService {
  // Get all sites
  static async getSites(): Promise<Site[]> {
    try {
      const { data, error } = await supabase
        .from('sites')
        .select(`
          *,
          project:projects(id, name)
        `)
        .eq('is_active', true)
        .order('name');

      if (error) throw error;
      return data || [];
    } catch (error) {
      console.error('Error fetching sites:', error);
      throw error;
    }
  }

  // Get sites for a specific project
  static async getSitesByProject(projectId: string): Promise<Site[]> {
    try {
      const { data, error } = await supabase
        .from('sites')
        .select(`
          *,
          project:projects(id, name)
        `)
        .eq('project_id', projectId)
        .eq('is_active', true)
        .order('name');

      if (error) throw error;
      return data || [];
    } catch (error) {
      console.error('Error fetching sites by project:', error);
      throw error;
    }
  }

  // Create a new site
  static async createSite(siteData: Omit<Site, 'id' | 'created_at' | 'updated_at'>): Promise<Site> {
    try {
      const { data, error } = await supabase
        .from('sites')
        .insert([siteData])
        .select(`
          *,
          project:projects(id, name)
        `)
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      console.error('Error creating site:', error);
      throw error;
    }
  }

  // Get active time entry for current user
  static async getActiveTimeEntry(userId: string): Promise<TimeEntry | null> {
    try {
      const { data, error } = await supabase.rpc('get_active_time_entry', {
        p_user_id: userId
      });

      if (error) throw error;
      return data && data.length > 0 ? data[0] : null;
    } catch (error) {
      console.error('Error fetching active time entry:', error);
      throw error;
    }
  }

  // Clock in
  static async clockIn(userId: string, clockInData: ClockInData): Promise<any> {
    try {
      const { data, error } = await supabase.rpc('clock_in', {
        p_user_id: userId,
        p_site_id: clockInData.site_id,
        p_project_id: clockInData.project_id || null,
        p_work_description: clockInData.work_description || null,
        p_location_lat: clockInData.location_lat || null,
        p_location_lng: clockInData.location_lng || null
      });

      if (error) throw error;
      return data;
    } catch (error) {
      console.error('Error clocking in:', error);
      throw error;
    }
  }

  // Clock out
  static async clockOut(userId: string, clockOutData?: ClockOutData): Promise<any> {
    try {
      const { data, error } = await supabase.rpc('clock_out', {
        p_user_id: userId,
        p_work_description: clockOutData?.work_description || null
      });

      if (error) throw error;
      return data;
    } catch (error) {
      console.error('Error clocking out:', error);
      throw error;
    }
  }

  // Get time entries for a user (with date range)
  static async getUserTimeEntries(
    userId: string, 
    startDate?: string, 
    endDate?: string
  ): Promise<TimeEntry[]> {
    try {
      let query = supabase
        .from('time_entries')
        .select(`
          *,
          site:sites(id, name, site_code),
          project:projects(id, name)
        `)
        .eq('user_id', userId)
        .order('clock_in_time', { ascending: false });

      if (startDate) {
        query = query.gte('clock_in_time', startDate);
      }
      if (endDate) {
        query = query.lte('clock_in_time', endDate);
      }

      const { data, error } = await query;

      if (error) throw error;
      return data || [];
    } catch (error) {
      console.error('Error fetching user time entries:', error);
      throw error;
    }
  }

  // Get site attendance (for management/admin only)
  static async getSiteAttendance(
    siteId?: string, 
    date: string = new Date().toISOString().split('T')[0]
  ): Promise<SiteAttendance[]> {
    try {
      const { data, error } = await supabase.rpc('get_site_attendance', {
        p_site_id: siteId || null,
        p_date: date
      });

      if (error) throw error;
      return data || [];
    } catch (error) {
      console.error('Error fetching site attendance:', error);
      throw error;
    }
  }

  // Get all time entries for management view
  static async getAllTimeEntries(
    startDate?: string,
    endDate?: string,
    siteId?: string,
    userId?: string
  ): Promise<TimeEntry[]> {
    try {
      let query = supabase
        .from('time_entries')
        .select(`
          *,
          site:sites(id, name, site_code),
          project:projects(id, name),
          user_profile:user_profiles(first_name, last_name, role_name)
        `)
        .order('clock_in_time', { ascending: false });

      if (startDate) {
        query = query.gte('clock_in_time', startDate);
      }
      if (endDate) {
        query = query.lte('clock_in_time', endDate);
      }
      if (siteId) {
        query = query.eq('site_id', siteId);
      }
      if (userId) {
        query = query.eq('user_id', userId);
      }

      const { data, error } = await query;

      if (error) throw error;
      return data || [];
    } catch (error) {
      console.error('Error fetching all time entries:', error);
      throw error;
    }
  }

  // Get time tracking statistics
  static async getTimeTrackingStats(
    startDate?: string,
    endDate?: string
  ): Promise<{
    totalHours: number;
    totalOvertimeHours: number;
    totalEntries: number;
    activeEntries: number;
    averageHoursPerDay: number;
  }> {
    try {
      let query = supabase
        .from('time_entries')
        .select('total_hours, overtime_hours, status');

      if (startDate) {
        query = query.gte('clock_in_time', startDate);
      }
      if (endDate) {
        query = query.lte('clock_in_time', endDate);
      }

      const { data, error } = await query;

      if (error) throw error;

      const stats = {
        totalHours: 0,
        totalOvertimeHours: 0,
        totalEntries: data?.length || 0,
        activeEntries: 0,
        averageHoursPerDay: 0
      };

      if (data) {
        stats.totalHours = data.reduce((sum, entry) => sum + (entry.total_hours || 0), 0);
        stats.totalOvertimeHours = data.reduce((sum, entry) => sum + (entry.overtime_hours || 0), 0);
        stats.activeEntries = data.filter(entry => entry.status === 'active').length;
        
        // Calculate average hours per day (assuming work days)
        const days = startDate && endDate 
          ? Math.ceil((new Date(endDate).getTime() - new Date(startDate).getTime()) / (1000 * 60 * 60 * 24))
          : 1;
        stats.averageHoursPerDay = stats.totalHours / Math.max(days, 1);
      }

      return stats;
    } catch (error) {
      console.error('Error fetching time tracking stats:', error);
      throw error;
    }
  }

  // Start break
  static async startBreak(timeEntryId: string, breakType: 'regular' | 'lunch' | 'emergency' = 'regular'): Promise<any> {
    try {
      const { data, error } = await supabase
        .from('time_breaks')
        .insert([{
          time_entry_id: timeEntryId,
          break_start: new Date().toISOString(),
          break_type: breakType
        }])
        .select()
        .single();

      if (error) throw error;

      // Update time entry status to break
      await supabase
        .from('time_entries')
        .update({ status: 'break' })
        .eq('id', timeEntryId);

      return data;
    } catch (error) {
      console.error('Error starting break:', error);
      throw error;
    }
  }

  // End break
  static async endBreak(breakId: string, timeEntryId: string): Promise<any> {
    try {
      const { data, error } = await supabase
        .from('time_breaks')
        .update({ break_end: new Date().toISOString() })
        .eq('id', breakId)
        .select()
        .single();

      if (error) throw error;

      // Calculate total break duration and update time entry
      const breakDuration = Math.round(
        (new Date(data.break_end).getTime() - new Date(data.break_start).getTime()) / (1000 * 60)
      );

      await supabase
        .from('time_entries')
        .update({ 
          status: 'active',
          break_duration_minutes: breakDuration
        })
        .eq('id', timeEntryId);

      return data;
    } catch (error) {
      console.error('Error ending break:', error);
      throw error;
    }
  }
}
