-- Reset password for martin<PERSON><PERSON><PERSON>@gmail.com to a known value
-- Since the account exists but password is unknown

DO $$
DECLARE
    v_new_password VARCHAR(255) := 'TempPass123!';
    v_auth_user_id UUID;
BEGIN
    RAISE NOTICE '';
    RAISE NOTICE '=== RESETTING <NAME_EMAIL> ===';
    
    -- Get the auth user ID
    SELECT id INTO v_auth_user_id
    FROM auth.users
    WHERE email = '<EMAIL>';
    
    IF v_auth_user_id IS NULL THEN
        RAISE NOTICE '❌ Auth user not <NAME_EMAIL>';
        RETURN;
    END IF;
    
    RAISE NOTICE '✅ Found auth user: %', v_auth_user_id;
    
    -- Update the password
    UPDATE auth.users
    SET 
        encrypted_password = crypt(v_new_password, gen_salt('bf')),
        updated_at = NOW()
    WHERE id = v_auth_user_id;
    
    RAISE NOTICE '✅ Password updated successfully';
    RAISE NOTICE '';
    RAISE NOTICE '🎉 LOGIN CREDENTIALS:';
    RAISE NOTICE '   Email: <EMAIL>';
    RAISE NOTICE '   Password: %', v_new_password;
    RAISE NOTICE '';
    RAISE NOTICE 'You can now login with these credentials!';
    RAISE NOTICE '';
    
EXCEPTION
    WHEN OTHERS THEN
        RAISE NOTICE '❌ Failed to reset password: %', SQLERRM;
END $$;

-- Also reset password for any other users that might have the same issue
DO $$
DECLARE
    user_record RECORD;
    v_password VARCHAR(255) := 'TempPass123!';
    reset_count INTEGER := 0;
BEGIN
    RAISE NOTICE '';
    RAISE NOTICE '=== RESETTING PASSWORDS FOR ALL USERS ===';
    RAISE NOTICE 'Setting all user passwords to: %', v_password;
    RAISE NOTICE '';
    
    -- Reset password for all auth users
    FOR user_record IN 
        SELECT au.id, au.email
        FROM auth.users au
        ORDER BY au.email
    LOOP
        BEGIN
            UPDATE auth.users
            SET 
                encrypted_password = crypt(v_password, gen_salt('bf')),
                updated_at = NOW()
            WHERE id = user_record.id;
            
            RAISE NOTICE '✅ Reset password for: %', user_record.email;
            reset_count := reset_count + 1;
            
        EXCEPTION
            WHEN OTHERS THEN
                RAISE NOTICE '❌ Failed to reset password for: % - %', user_record.email, SQLERRM;
        END;
    END LOOP;
    
    RAISE NOTICE '';
    RAISE NOTICE '=== SUMMARY ===';
    RAISE NOTICE '✅ Reset passwords for % users', reset_count;
    RAISE NOTICE '🔑 All users can now login with password: %', v_password;
    RAISE NOTICE '';
    RAISE NOTICE 'Test these logins:';
    
    -- Show all users
    FOR user_record IN 
        SELECT au.email
        FROM auth.users au
        ORDER BY au.email
    LOOP
        RAISE NOTICE '   % / %', user_record.email, v_password;
    END LOOP;
    
    RAISE NOTICE '';
END $$;
