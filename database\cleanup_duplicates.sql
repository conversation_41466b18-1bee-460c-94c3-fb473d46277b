-- Cleanup Script for Duplicate Email Issues
-- Run this in Supabase SQL Editor if you're having duplicate email problems

-- 1. First, let's see what duplicates exist
DO $$
BEGIN
    RAISE NOTICE '=== CHECKING FOR DUPLICATE EMAILS ===';
END $$;

-- Check for duplicate emails in user_profiles
SELECT 
    email, 
    COUNT(*) as count,
    STRING_AGG(id::text, ', ') as profile_ids
FROM public.user_profiles 
GROUP BY email 
HAVING COUNT(*) > 1;

-- Check for orphaned auth users (users without profiles)
SELECT 
    au.id as auth_user_id,
    au.email,
    au.created_at
FROM auth.users au
LEFT JOIN public.user_profiles up ON au.id = up.user_id
WHERE up.user_id IS NULL;

-- Check for orphaned profiles (profiles without auth users)
SELECT 
    up.id as profile_id,
    up.email,
    up.user_id,
    up.created_at
FROM public.user_profiles up
LEFT JOIN auth.users au ON up.user_id = au.id
WHERE au.id IS NULL;

-- 2. Clean up orphaned profiles (profiles without corresponding auth users)
DO $$
DECLARE
    orphaned_count INTEGER;
BEGIN
    -- Delete profiles that don't have corresponding auth users
    DELETE FROM public.user_profiles 
    WHERE user_id NOT IN (SELECT id FROM auth.users);
    
    GET DIAGNOSTICS orphaned_count = ROW_COUNT;
    RAISE NOTICE 'Deleted % orphaned user profiles', orphaned_count;
END $$;

-- 3. Clean up duplicate profiles (keep the oldest one for each email)
DO $$
DECLARE
    duplicate_count INTEGER;
BEGIN
    -- Delete duplicate profiles, keeping only the oldest one for each email
    DELETE FROM public.user_profiles 
    WHERE id NOT IN (
        SELECT DISTINCT ON (email) id 
        FROM public.user_profiles 
        ORDER BY email, created_at ASC
    );
    
    GET DIAGNOSTICS duplicate_count = ROW_COUNT;
    RAISE NOTICE 'Deleted % duplicate user profiles', duplicate_count;
END $$;

-- 4. Update the trigger function to be more robust
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
    -- Only create profile if one doesn't already exist (check both user_id and email)
    IF NOT EXISTS (
        SELECT 1 FROM public.user_profiles 
        WHERE user_id = NEW.id OR email = NEW.email
    ) THEN
        INSERT INTO public.user_profiles (user_id, email, first_name, last_name, role_id)
        VALUES (
            NEW.id,
            NEW.email,
            COALESCE(NEW.raw_user_meta_data->>'first_name', 'User'),
            COALESCE(NEW.raw_user_meta_data->>'last_name', ''),
            (SELECT id FROM public.user_roles WHERE role_name = 'client' LIMIT 1)
        )
        ON CONFLICT (email) DO NOTHING;
    END IF;
    RETURN NEW;
EXCEPTION
    WHEN unique_violation THEN
        -- Handle unique constraint violations gracefully
        RAISE WARNING 'Profile already exists for email: %', NEW.email;
        RETURN NEW;
    WHEN OTHERS THEN
        -- Log error but don't fail the user creation
        RAISE WARNING 'Failed to create user profile for %: %', NEW.email, SQLERRM;
        RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 5. Recreate the trigger
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
CREATE TRIGGER on_auth_user_created
    AFTER INSERT ON auth.users
    FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

-- 6. Add a function to manually create missing profiles
CREATE OR REPLACE FUNCTION public.create_missing_profiles()
RETURNS INTEGER AS $$
DECLARE
    missing_count INTEGER := 0;
    auth_user RECORD;
BEGIN
    -- Create profiles for auth users that don't have them
    FOR auth_user IN 
        SELECT au.id, au.email, au.raw_user_meta_data, au.created_at
        FROM auth.users au
        LEFT JOIN public.user_profiles up ON au.id = up.user_id
        WHERE up.user_id IS NULL
    LOOP
        BEGIN
            INSERT INTO public.user_profiles (user_id, email, first_name, last_name, role_id)
            VALUES (
                auth_user.id,
                auth_user.email,
                COALESCE(auth_user.raw_user_meta_data->>'first_name', 'User'),
                COALESCE(auth_user.raw_user_meta_data->>'last_name', ''),
                (SELECT id FROM public.user_roles WHERE role_name = 'client' LIMIT 1)
            )
            ON CONFLICT (email) DO NOTHING;
            
            missing_count := missing_count + 1;
        EXCEPTION
            WHEN OTHERS THEN
                RAISE WARNING 'Failed to create profile for user %: %', auth_user.email, SQLERRM;
        END;
    END LOOP;
    
    RETURN missing_count;
END;
$$ LANGUAGE plpgsql;

-- 7. Run the missing profiles function
DO $$
DECLARE
    created_count INTEGER;
BEGIN
    SELECT public.create_missing_profiles() INTO created_count;
    RAISE NOTICE 'Created % missing user profiles', created_count;
END $$;

-- 8. Final verification
DO $$
DECLARE
    total_auth_users INTEGER;
    total_profiles INTEGER;
    orphaned_profiles INTEGER;
    orphaned_auth_users INTEGER;
BEGIN
    SELECT COUNT(*) INTO total_auth_users FROM auth.users;
    SELECT COUNT(*) INTO total_profiles FROM public.user_profiles;
    
    SELECT COUNT(*) INTO orphaned_profiles 
    FROM public.user_profiles up
    LEFT JOIN auth.users au ON up.user_id = au.id
    WHERE au.id IS NULL;
    
    SELECT COUNT(*) INTO orphaned_auth_users
    FROM auth.users au
    LEFT JOIN public.user_profiles up ON au.id = up.user_id
    WHERE up.user_id IS NULL;
    
    RAISE NOTICE '=== CLEANUP COMPLETE ===';
    RAISE NOTICE 'Total auth users: %', total_auth_users;
    RAISE NOTICE 'Total user profiles: %', total_profiles;
    RAISE NOTICE 'Orphaned profiles: %', orphaned_profiles;
    RAISE NOTICE 'Orphaned auth users: %', orphaned_auth_users;
    
    IF orphaned_profiles = 0 AND orphaned_auth_users = 0 THEN
        RAISE NOTICE 'SUCCESS: All users have matching profiles!';
    ELSE
        RAISE NOTICE 'WARNING: There are still orphaned records that need attention.';
    END IF;
END $$;
