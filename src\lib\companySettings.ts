import { supabase } from './supabase';
import { CompanyInfo, DEFAULT_COMPANY_INFO } from './documents';

export interface CompanySettings extends CompanyInfo {
  id?: string;
  created_at?: string;
  updated_at?: string;
}

export class CompanySettingsService {
  // Get company settings (with localStorage fallback)
  static async getCompanySettings(): Promise<CompanyInfo> {
    try {
      // Try to get from database first
      const { data, error } = await supabase
        .from('company_settings')
        .select('*')
        .order('updated_at', { ascending: false })
        .limit(1)
        .single();

      if (error) {
        console.warn('Could not fetch company settings from database:', error);
        // Fallback to localStorage
        return this.getFromLocalStorage();
      }

      if (data) {
        // Save to localStorage as backup
        const companyInfo: CompanyInfo = {
          name: data.name,
          logo_url: data.logo_url || '',
          address: data.address || '',
          phone: data.phone || '',
          email: data.email,
          website: data.website || '',
          tax_number: data.tax_number || '',
          registration_number: data.registration_number || ''
        };
        
        localStorage.setItem('company_info', JSON.stringify(companyInfo));
        return companyInfo;
      }

      return this.getFromLocalStorage();
    } catch (error) {
      console.error('Error fetching company settings:', error);
      return this.getFromLocalStorage();
    }
  }

  // Save company settings to database and localStorage
  static async saveCompanySettings(companyInfo: CompanyInfo): Promise<boolean> {
    try {
      // Save to localStorage first (immediate backup)
      localStorage.setItem('company_info', JSON.stringify(companyInfo));

      // Check if we have existing settings
      const { data: existing } = await supabase
        .from('company_settings')
        .select('id')
        .order('updated_at', { ascending: false })
        .limit(1)
        .single();

      const settingsData = {
        name: companyInfo.name,
        logo_url: companyInfo.logo_url,
        address: companyInfo.address,
        phone: companyInfo.phone,
        email: companyInfo.email,
        website: companyInfo.website,
        tax_number: companyInfo.tax_number,
        registration_number: companyInfo.registration_number
      };

      if (existing?.id) {
        // Update existing settings
        const { error } = await supabase
          .from('company_settings')
          .update(settingsData)
          .eq('id', existing.id);

        if (error) {
          console.error('Error updating company settings:', error);
          return false;
        }
      } else {
        // Insert new settings
        const { error } = await supabase
          .from('company_settings')
          .insert([settingsData]);

        if (error) {
          console.error('Error inserting company settings:', error);
          return false;
        }
      }

      console.log('Company settings saved to database successfully');
      return true;
    } catch (error) {
      console.error('Error saving company settings:', error);
      return false;
    }
  }

  // Get from localStorage (fallback)
  private static getFromLocalStorage(): CompanyInfo {
    try {
      const saved = localStorage.getItem('company_info');
      return saved ? JSON.parse(saved) : DEFAULT_COMPANY_INFO;
    } catch {
      return DEFAULT_COMPANY_INFO;
    }
  }

  // Sync localStorage to database (useful for migration)
  static async syncLocalStorageToDatabase(): Promise<boolean> {
    try {
      const localData = this.getFromLocalStorage();
      if (localData.name !== DEFAULT_COMPANY_INFO.name) {
        return await this.saveCompanySettings(localData);
      }
      return true;
    } catch (error) {
      console.error('Error syncing localStorage to database:', error);
      return false;
    }
  }
}
