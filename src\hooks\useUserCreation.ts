// Reusable user creation hook based on the working standalone register logic
import { useState } from 'react';
import { supabase } from '@/lib/supabase';

export interface CreateUserData {
  email: string;
  firstName: string;
  lastName: string;
  role: string;
  password?: string;
  phone?: string;
  department?: string;
  jobTitle?: string;
}

export interface CreateUserResult {
  success: boolean;
  step?: string;
  method?: string;
  error?: string;
  details?: any;
  authUser?: any;
  profile?: any;
  result?: any;
  credentials?: {
    email: string;
    password: string;
  };
}

export const useUserCreation = () => {
  const [loading, setLoading] = useState(false);
  const [roles, setRoles] = useState<any[]>([]);

  // Load available roles
  const loadRoles = async () => {
    try {
      const { data, error } = await supabase
        .from('user_roles')
        .select('*')
        .order('role_name');
      
      if (error) throw error;
      setRoles(data || []);
      return data || [];
    } catch (error) {
      console.error('Failed to load roles:', error);
      return [];
    }
  };

  // Create user using Supabase Auth (the working method from standalone register)
  const createUserWithAuth = async (userData: CreateUserData): Promise<CreateUserResult> => {
    setLoading(true);

    try {
      console.log('🔍 Creating user with Supabase Auth...');

      // Validate required fields
      if (!userData.email || !userData.firstName || !userData.lastName || !userData.role) {
        return {
          success: false,
          error: 'Please fill in all required fields'
        };
      }

      const password = userData.password || 'TempPass123!';

      // Step 1: Ensure roles are loaded
      let currentRoles = roles;
      if (!currentRoles || currentRoles.length === 0) {
        console.log('Loading roles for user creation...');
        currentRoles = await loadRoles();
      }

      // Step 2: Create auth user using the working method
      console.log('Creating auth user...');
      const { data: authData, error: authError } = await supabase.auth.signUp({
        email: userData.email,
        password: password,
        options: {
          data: {
            first_name: userData.firstName,
            last_name: userData.lastName
          }
        }
      });

      if (authError) {
        console.error('Auth creation failed:', authError);
        return {
          success: false,
          step: 'Auth User Creation',
          error: authError.message,
          details: authError
        };
      }

      console.log('✅ Auth user created:', authData.user?.id);

      // Step 3: Get role information
      const selectedRole = currentRoles.find(r => r.role_name === userData.role);
      if (!selectedRole) {
        console.error('Available roles:', currentRoles.map(r => r.role_name));
        return {
          success: false,
          step: 'Role Validation',
          error: `Role '${userData.role}' not found. Available roles: ${currentRoles.map(r => r.role_name).join(', ')}`
        };
      }

      // Step 3: Create user profile using the working method
      console.log('Creating user profile...');
      const { data: profileData, error: profileError } = await supabase
        .from('user_profiles')
        .insert({
          user_id: authData.user?.id,
          email: userData.email,
          first_name: userData.firstName,
          last_name: userData.lastName,
          role_id: selectedRole.id,
          role_name: selectedRole.role_name,
          phone: userData.phone || null,
          department: userData.department || null,
          job_title: userData.jobTitle || null,
          is_active: true
        })
        .select()
        .single();

      if (profileError) {
        console.error('Profile creation failed:', profileError);
        return {
          success: false,
          step: 'User Profile Creation',
          error: profileError.message,
          details: profileError
        };
      }

      console.log('✅ User profile created:', profileData.id);

      // Success!
      return {
        success: true,
        method: 'Supabase Auth',
        authUser: authData.user,
        profile: profileData,
        credentials: {
          email: userData.email,
          password: password
        }
      };

    } catch (error: any) {
      console.error('User creation failed:', error);
      return {
        success: false,
        step: 'General Error',
        error: error.message,
        details: error
      };
    } finally {
      setLoading(false);
    }
  };

  // Create user directly in database (bypasses Supabase Auth issues)
  const createUserDirectly = async (userData: CreateUserData): Promise<CreateUserResult> => {
    setLoading(true);
    
    try {
      console.log('🔍 Creating user directly in database...');
      
      // Validate required fields
      if (!userData.email || !userData.firstName || !userData.lastName || !userData.role) {
        return {
          success: false,
          error: 'Please fill in all required fields'
        };
      }

      // Ensure roles are loaded
      let currentRoles = roles;
      if (!currentRoles || currentRoles.length === 0) {
        console.log('Loading roles for direct user creation...');
        currentRoles = await loadRoles();
      }

      // Get role information
      const selectedRole = currentRoles.find(r => r.role_name === userData.role);
      if (!selectedRole) {
        console.error('Available roles:', currentRoles.map(r => r.role_name));
        return {
          success: false,
          error: `Role '${userData.role}' not found. Available roles: ${currentRoles.map(r => r.role_name).join(', ')}`
        };
      }

      const password = userData.password || 'TempPass123!';

      // Create using SQL function (bypasses Supabase Auth)
      const { data, error } = await supabase.rpc('create_user_directly', {
        p_email: userData.email,
        p_first_name: userData.firstName,
        p_last_name: userData.lastName,
        p_role_id: selectedRole.id,
        p_role_name: selectedRole.role_name,
        p_password: password
      });

      if (error) {
        console.error('Direct creation failed:', error);
        return {
          success: false,
          step: 'Direct Database Creation',
          error: error.message,
          details: error
        };
      }

      console.log('✅ User created directly:', data);

      return {
        success: true,
        method: 'Direct Database Creation',
        result: data,
        credentials: {
          email: userData.email,
          password: password
        }
      };

    } catch (error: any) {
      console.error('Direct user creation failed:', error);
      return {
        success: false,
        step: 'Direct Creation Error',
        error: error.message,
        details: error
      };
    } finally {
      setLoading(false);
    }
  };

  // Smart create user - tries Auth first, falls back to direct if needed
  const createUser = async (userData: CreateUserData): Promise<CreateUserResult> => {
    console.log('🎯 Smart user creation starting...');
    
    // Try Supabase Auth first (the working method)
    const authResult = await createUserWithAuth(userData);
    
    if (authResult.success) {
      console.log('✅ User created successfully with Supabase Auth');
      return authResult;
    }
    
    console.log('⚠️ Supabase Auth failed, trying direct creation...');
    
    // If Auth fails, try direct creation
    const directResult = await createUserDirectly(userData);
    
    if (directResult.success) {
      console.log('✅ User created successfully with direct method');
      return directResult;
    }
    
    console.log('❌ Both methods failed');
    
    // Return the Auth error as primary, with direct error as fallback info
    return {
      ...authResult,
      details: {
        authError: authResult.details,
        directError: directResult.details
      }
    };
  };

  return {
    loading,
    roles,
    loadRoles,
    createUser,
    createUserWithAuth,
    createUserDirectly
  };
};
