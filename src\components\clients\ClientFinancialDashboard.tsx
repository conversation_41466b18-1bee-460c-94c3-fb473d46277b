import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { 
  DollarSign, 
  TrendingUp, 
  TrendingDown, 
  AlertTriangle, 
  Clock,
  CreditCard,
  FileText,
  Calendar,
  ArrowUpRight,
  ArrowDownRight
} from 'lucide-react';
import { Client } from '@/types/client';
import { FinancialService } from '@/lib/financialService';

interface ClientFinancialDashboardProps {
  clients: Client[];
}

export const ClientFinancialDashboard: React.FC<ClientFinancialDashboardProps> = ({
  clients
}) => {
  const [selectedPeriod, setSelectedPeriod] = useState('30');
  const [selectedClient, setSelectedClient] = useState<string>('all');
  const [financialData, setFinancialData] = useState<any>(null);
  const [loading, setLoading] = useState(true);

  // Load real financial data using the comprehensive financial service
  useEffect(() => {
    const loadFinancialData = async () => {
      try {
        setLoading(true);

        // Use the comprehensive financial service
        const overview = await FinancialService.getFinancialOverview();
        const recentActivity = await FinancialService.getRecentActivity(5);
        const overdueInvoices = await FinancialService.getOverdueInvoices();

        // Format recent payments from activity
        const recentPayments = recentActivity
          .filter(activity => activity.type === 'payment')
          .slice(0, 5)
          .map(activity => ({
            id: activity.id,
            client: activity.client,
            amount: activity.amount,
            date: activity.date,
            method: activity.description.includes('Bank Transfer') ? 'Bank Transfer' :
                   activity.description.includes('Check') ? 'Check' :
                   activity.description.includes('Wire Transfer') ? 'Wire Transfer' : 'Credit Card',
            status: activity.status
          }));

        // Format outstanding invoices
        const outstandingInvoices = overdueInvoices
          .slice(0, 5)
          .map((invoice: any) => ({
            id: invoice.invoice_number,
            client: invoice.client_name,
            amount: invoice.total_amount,
            dueDate: invoice.due_date,
            daysOverdue: invoice.days_overdue,
            status: invoice.days_overdue > 0 ? 'Overdue' : invoice.status
          }));

        setFinancialData({
          totalRevenue: overview.totalRevenue, // Now using actual revenue from payments
          totalPaid: overview.totalPaid,
          outstandingAmount: overview.outstandingAmount,
          overdueAmount: overview.overdueAmount,
          paidThisMonth: overview.paidThisMonth,
          invoicesSent: overview.invoicesSent,
          invoicesPaid: overview.invoicesPaid,
          averagePaymentTime: overview.averagePaymentTime,
          creditUtilization: clients.length > 0 ? Math.floor((clients.filter(c => c.credit_limit && c.credit_limit > 0).length / clients.length) * 100) : 0,
          recentPayments,
          outstandingInvoices
        });

      } catch (error) {
        console.error('Error loading financial data:', error);
        // Fallback to basic calculations if no data
        setFinancialData({
          totalRevenue: 0,
          totalPaid: 0,
          outstandingAmount: 0,
          overdueAmount: 0,
          paidThisMonth: 0,
          invoicesSent: 0,
          invoicesPaid: 0,
          averagePaymentTime: 30,
          creditUtilization: 0,
          recentPayments: [],
          outstandingInvoices: []
        });
      } finally {
        setLoading(false);
      }
    };

    if (clients.length > 0) {
      loadFinancialData();
    }
  }, [clients]);

  if (loading || !financialData) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="text-center">
          <div className="w-8 h-8 border-4 border-blue-600 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-gray-600">Loading financial data...</p>
        </div>
      </div>
    );
  }

  const getPaymentMethodIcon = (method: string) => {
    switch (method) {
      case 'Bank Transfer':
      case 'Wire Transfer':
        return <CreditCard className="h-4 w-4" />;
      case 'Check':
        return <FileText className="h-4 w-4" />;
      default:
        return <DollarSign className="h-4 w-4" />;
    }
  };

  const getStatusColor = (status: string, daysOverdue?: number) => {
    if (status === 'Overdue' || (daysOverdue && daysOverdue > 0)) {
      return 'bg-red-100 text-red-800';
    }
    if (status === 'Pending') {
      return 'bg-yellow-100 text-yellow-800';
    }
    return 'bg-green-100 text-green-800';
  };

  return (
    <div className="space-y-6">
      {/* Header Controls */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              <DollarSign className="h-5 w-5" />
              Financial Dashboard
            </CardTitle>
            <div className="flex items-center gap-4">
              <Select value={selectedPeriod} onValueChange={setSelectedPeriod}>
                <SelectTrigger className="w-32">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="7">Last 7 days</SelectItem>
                  <SelectItem value="30">Last 30 days</SelectItem>
                  <SelectItem value="90">Last 90 days</SelectItem>
                  <SelectItem value="365">Last year</SelectItem>
                </SelectContent>
              </Select>
              <Select value={selectedClient} onValueChange={setSelectedClient}>
                <SelectTrigger className="w-48">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Clients</SelectItem>
                  {clients.map(client => (
                    <SelectItem key={client.id} value={client.id}>
                      {client.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardHeader>
      </Card>

      {/* Financial Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Revenue</p>
                <p className="text-2xl font-bold text-green-600">
                  ${financialData.totalRevenue.toLocaleString()}
                </p>
              </div>
              <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                <TrendingUp className="h-6 w-6 text-green-600" />
              </div>
            </div>
            <div className="flex items-center mt-2 text-sm">
              <ArrowUpRight className="h-4 w-4 text-green-600 mr-1" />
              <span className="text-green-600">+12.5%</span>
              <span className="text-gray-500 ml-1">vs last month</span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Outstanding</p>
                <p className="text-2xl font-bold text-orange-600">
                  ${financialData.outstandingAmount.toLocaleString()}
                </p>
              </div>
              <div className="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center">
                <Clock className="h-6 w-6 text-orange-600" />
              </div>
            </div>
            <div className="flex items-center mt-2 text-sm">
              <span className="text-gray-600">{financialData.invoicesSent - financialData.invoicesPaid} pending invoices</span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Overdue</p>
                <p className="text-2xl font-bold text-red-600">
                  ${financialData.overdueAmount.toLocaleString()}
                </p>
              </div>
              <div className="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center">
                <AlertTriangle className="h-6 w-6 text-red-600" />
              </div>
            </div>
            <div className="flex items-center mt-2 text-sm">
              <ArrowDownRight className="h-4 w-4 text-red-600 mr-1" />
              <span className="text-red-600">Requires attention</span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Avg Payment Time</p>
                <p className="text-2xl font-bold text-blue-600">
                  {financialData.averagePaymentTime} days
                </p>
              </div>
              <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                <Calendar className="h-6 w-6 text-blue-600" />
              </div>
            </div>
            <div className="flex items-center mt-2 text-sm">
              <span className="text-gray-600">Industry avg: 35 days</span>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Payment Collection Rate */}
      <Card>
        <CardHeader>
          <CardTitle>Payment Collection Rate</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">Invoices Paid</span>
              <span className="text-sm text-gray-600">
                {financialData.invoicesPaid} of {financialData.invoicesSent}
              </span>
            </div>
            <Progress 
              value={(financialData.invoicesPaid / financialData.invoicesSent) * 100} 
              className="h-2"
            />
            <div className="flex items-center justify-between text-sm text-gray-600">
              <span>Collection Rate: {Math.round((financialData.invoicesPaid / financialData.invoicesSent) * 100)}%</span>
              <span>Target: 95%</span>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Recent Activity */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Recent Payments */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <CreditCard className="h-5 w-5" />
              Recent Payments
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {financialData.recentPayments.map((payment) => (
                <div key={payment.id} className="flex items-center justify-between p-3 border rounded-lg">
                  <div className="flex items-center space-x-3">
                    <div className="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
                      {getPaymentMethodIcon(payment.method)}
                    </div>
                    <div>
                      <p className="font-medium text-gray-900">{payment.client}</p>
                      <p className="text-sm text-gray-500">{payment.method} • {payment.date}</p>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className="font-medium text-green-600">
                      +${payment.amount.toLocaleString()}
                    </p>
                    <Badge className="bg-green-100 text-green-800">
                      {payment.status}
                    </Badge>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Outstanding Invoices */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <FileText className="h-5 w-5" />
              Outstanding Invoices
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {financialData.outstandingInvoices.map((invoice) => (
                <div key={invoice.id} className="flex items-center justify-between p-3 border rounded-lg">
                  <div>
                    <p className="font-medium text-gray-900">{invoice.id}</p>
                    <p className="text-sm text-gray-600">{invoice.client}</p>
                    <p className="text-sm text-gray-500">Due: {invoice.dueDate}</p>
                  </div>
                  <div className="text-right">
                    <p className="font-medium text-gray-900">
                      ${invoice.amount.toLocaleString()}
                    </p>
                    <Badge className={getStatusColor(invoice.status, invoice.daysOverdue)}>
                      {invoice.daysOverdue > 0 ? `${invoice.daysOverdue}d overdue` : invoice.status}
                    </Badge>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};
