-- CREATE EMERGENCY ADMIN USER
-- This will create a fresh admin user to get you back into the system
-- Run this AFTER the profile unlinking script

-- STEP 1: First run the profile unlinking script if you haven't already
-- (Copy and paste the fix_profile_unlinking.sql script first)

-- STEP 2: Create emergency admin auth user
DO $$
DECLARE
    v_admin_auth_id UUID;
    v_admin_profile_id UUID;
    v_admin_email VARCHAR(255) := '<EMAIL>';
    v_admin_password VARCHAR(255) := 'EmergencyAdmin123!';
    v_admin_role_id UUID;
BEGIN
    RAISE NOTICE '';
    RAISE NOTICE '🚨 === CREATING EMERGENCY ADMIN USER === 🚨';
    RAISE NOTICE '';
    RAISE NOTICE 'This will create a fresh admin user to get you back into the system.';
    RAISE NOTICE '';
    
    -- Get admin role ID
    SELECT id INTO v_admin_role_id 
    FROM public.user_roles 
    WHERE role_name = 'admin' 
    LIMIT 1;
    
    IF v_admin_role_id IS NULL THEN
        RAISE NOTICE '❌ Admin role not found in user_roles table';
        RETURN;
    END IF;
    
    RAISE NOTICE '✅ Found admin role ID: %', v_admin_role_id;
    
    -- Generate UUIDs
    v_admin_auth_id := gen_random_uuid();
    v_admin_profile_id := gen_random_uuid();
    
    RAISE NOTICE 'Generated auth ID: %', v_admin_auth_id;
    RAISE NOTICE 'Generated profile ID: %', v_admin_profile_id;
    
    -- Create auth user first
    INSERT INTO auth.users (
        id,
        instance_id,
        email,
        encrypted_password,
        email_confirmed_at,
        created_at,
        updated_at,
        raw_user_meta_data,
        raw_app_meta_data,
        aud,
        role
    ) VALUES (
        v_admin_auth_id,
        '00000000-0000-0000-0000-000000000000',
        v_admin_email,
        crypt(v_admin_password, gen_salt('bf')),
        NOW(),
        NOW(),
        NOW(),
        jsonb_build_object(
            'first_name', 'Emergency',
            'last_name', 'Admin',
            'email', v_admin_email
        ),
        jsonb_build_object(
            'provider', 'email',
            'providers', ARRAY['email']
        ),
        'authenticated',
        'authenticated'
    );
    
    RAISE NOTICE '✅ Created auth user for emergency admin';
    
    -- Create user profile
    INSERT INTO public.user_profiles (
        id,
        user_id,
        email,
        first_name,
        last_name,
        role_id,
        role_name,
        is_active,
        created_at,
        updated_at
    ) VALUES (
        v_admin_profile_id,
        v_admin_auth_id,
        v_admin_email,
        'Emergency',
        'Admin',
        v_admin_role_id,
        'admin',
        true,
        NOW(),
        NOW()
    );
    
    RAISE NOTICE '✅ Created user profile for emergency admin';
    RAISE NOTICE '';
    
END $$;

-- STEP 3: Create alternative admin using existing profile
DO $$
DECLARE
    v_existing_admin RECORD;
    v_new_auth_id UUID;
    v_password VARCHAR(255) := 'NewAdmin123!';
BEGIN
    RAISE NOTICE '=== CREATING AUTH FOR EXISTING ADMIN PROFILE ===';
    
    -- Find an existing admin profile that's unlinked
    SELECT id, email, first_name, last_name, role_name
    INTO v_existing_admin
    FROM public.user_profiles
    WHERE role_name = 'admin' 
    AND user_id IS NULL
    LIMIT 1;
    
    IF v_existing_admin.id IS NULL THEN
        RAISE NOTICE '⚠️ No unlinked admin profile found';
    ELSE
        RAISE NOTICE 'Found existing admin profile: % (% %)', 
            v_existing_admin.email,
            v_existing_admin.first_name,
            v_existing_admin.last_name;
        
        -- Generate new auth ID
        v_new_auth_id := gen_random_uuid();
        
        -- Create auth user for existing admin profile
        INSERT INTO auth.users (
            id,
            instance_id,
            email,
            encrypted_password,
            email_confirmed_at,
            created_at,
            updated_at,
            raw_user_meta_data,
            raw_app_meta_data,
            aud,
            role
        ) VALUES (
            v_new_auth_id,
            '00000000-0000-0000-0000-000000000000',
            v_existing_admin.email,
            crypt(v_password, gen_salt('bf')),
            NOW(),
            NOW(),
            NOW(),
            jsonb_build_object(
                'first_name', v_existing_admin.first_name,
                'last_name', v_existing_admin.last_name,
                'email', v_existing_admin.email
            ),
            jsonb_build_object(
                'provider', 'email',
                'providers', ARRAY['email']
            ),
            'authenticated',
            'authenticated'
        );
        
        -- Link the existing profile to new auth user
        UPDATE public.user_profiles
        SET user_id = v_new_auth_id,
            updated_at = NOW()
        WHERE id = v_existing_admin.id;
        
        RAISE NOTICE '✅ Created auth user and linked to existing admin profile';
        RAISE NOTICE '📧 Email: %', v_existing_admin.email;
        RAISE NOTICE '🔑 Password: %', v_password;
    END IF;
    
    RAISE NOTICE '';
END $$;

-- STEP 4: Verification and login credentials
DO $$
DECLARE
    admin_record RECORD;
    admin_count INTEGER := 0;
BEGIN
    RAISE NOTICE '=== ADMIN USERS READY FOR LOGIN ===';
    
    -- Show all admin users that can now login
    FOR admin_record IN 
        SELECT up.email, up.first_name, up.last_name, up.role_name, au.id as auth_id
        FROM public.user_profiles up
        JOIN auth.users au ON up.user_id = au.id
        WHERE up.role_name = 'admin'
        ORDER BY up.email
    LOOP
        admin_count := admin_count + 1;
        RAISE NOTICE 'Admin %: % (% %) | Auth ID: %', 
            admin_count,
            admin_record.email,
            admin_record.first_name,
            admin_record.last_name,
            admin_record.auth_id;
    END LOOP;
    
    IF admin_count = 0 THEN
        RAISE NOTICE '❌ No admin users found - something went wrong';
    ELSE
        RAISE NOTICE '';
        RAISE NOTICE '✅ Found % admin user(s) ready for login', admin_count;
    END IF;
    
    RAISE NOTICE '';
END $$;

-- STEP 5: Final login instructions
DO $$
BEGIN
    RAISE NOTICE '🎉 === EMERGENCY ADMIN CREATION COMPLETE === 🎉';
    RAISE NOTICE '';
    RAISE NOTICE '🔑 LOGIN CREDENTIALS:';
    RAISE NOTICE '';
    RAISE NOTICE '1. EMERGENCY ADMIN (if created):';
    RAISE NOTICE '   Email: <EMAIL>';
    RAISE NOTICE '   Password: EmergencyAdmin123!';
    RAISE NOTICE '';
    RAISE NOTICE '2. EXISTING ADMIN (if found and linked):';
    RAISE NOTICE '   Email: [check output above]';
    RAISE NOTICE '   Password: NewAdmin123!';
    RAISE NOTICE '';
    RAISE NOTICE '📋 NEXT STEPS:';
    RAISE NOTICE '1. Try logging in with one of the admin accounts above';
    RAISE NOTICE '2. Once logged in, you can create other users';
    RAISE NOTICE '3. You can change passwords after logging in';
    RAISE NOTICE '4. Delete the emergency admin if you created one';
    RAISE NOTICE '';
    RAISE NOTICE '🚀 You should now be able to access your system!';
    RAISE NOTICE '';
END $$;
