import { supabase } from './supabase';

export interface Notification {
  id: string;
  user_id: string;
  type: 'info' | 'warning' | 'success' | 'error' | 'reminder';
  category: 'project' | 'financial' | 'system' | 'message' | 'user' | 'invoice' | 'time_tracking' | 'asset';
  priority: 'low' | 'medium' | 'high' | 'urgent';
  title: string;
  message: string;
  action_url?: string;
  action_label?: string;
  read: boolean;
  email_sent: boolean;
  email_sent_at?: string;
  related_entity_type?: string;
  related_entity_id?: string;
  metadata?: Record<string, any>;
  created_at: string;
  updated_at: string;
  expires_at?: string;
  created_by?: string;
}

export interface NotificationTemplate {
  id: string;
  name: string;
  category: string;
  type: string;
  priority: string;
  title_template: string;
  message_template: string;
  email_subject_template?: string;
  email_body_template?: string;
  action_url_template?: string;
  action_label?: string;
  variables: string[];
  is_active: boolean;
}

export interface EmailQueueItem {
  id: string;
  notification_id: string;
  user_id: string;
  to_email: string;
  subject: string;
  html_content: string;
  text_content: string;
  status: 'pending' | 'sending' | 'sent' | 'failed' | 'cancelled';
  attempts: number;
  max_attempts: number;
  scheduled_for: string;
  sent_at?: string;
  error_message?: string;
}

export interface NotificationPreferences {
  email_notifications: boolean;
  push_notifications: boolean;
  sms_notifications: boolean;
  project_updates: boolean;
  financial_alerts: boolean;
  time_tracking_reminders: boolean;
  team_messages: boolean;
  system_announcements: boolean;
  invoice_notifications: boolean;
  deadline_reminders: boolean;
  asset_notifications: boolean;
  user_management_notifications: boolean;
  email_project_updates: boolean;
  email_financial_alerts: boolean;
  email_time_tracking_reminders: boolean;
  email_team_messages: boolean;
  email_system_announcements: boolean;
  email_invoice_notifications: boolean;
  email_deadline_reminders: boolean;
  email_asset_notifications: boolean;
  email_user_management_notifications: boolean;
  notification_frequency: 'immediate' | 'hourly' | 'daily' | 'weekly';
  email_digest_frequency: 'immediate' | 'hourly' | 'daily' | 'weekly' | 'never';
  quiet_hours_enabled: boolean;
  quiet_hours_start: string;
  quiet_hours_end: string;
  timezone: string;
}

export interface CreateNotificationParams {
  user_id: string;
  template_name?: string;
  type?: 'info' | 'warning' | 'success' | 'error' | 'reminder';
  category: 'project' | 'financial' | 'system' | 'message' | 'user' | 'invoice' | 'time_tracking' | 'asset';
  priority?: 'low' | 'medium' | 'high' | 'urgent';
  title?: string;
  message?: string;
  action_url?: string;
  action_label?: string;
  related_entity_type?: string;
  related_entity_id?: string;
  metadata?: Record<string, any>;
  variables?: Record<string, any>;
  expires_at?: string;
  send_email?: boolean;
  schedule_email_for?: string;
}

export class NotificationService {
  // Create a new notification
  static async createNotification(params: CreateNotificationParams): Promise<Notification> {
    try {
      let notificationData: any = {
        user_id: params.user_id,
        type: params.type || 'info',
        category: params.category,
        priority: params.priority || 'medium',
        related_entity_type: params.related_entity_type,
        related_entity_id: params.related_entity_id,
        metadata: params.metadata || {},
        expires_at: params.expires_at,
        created_by: (await supabase.auth.getUser()).data.user?.id
      };

      // If template is provided, use it to generate content
      if (params.template_name) {
        const template = await this.getTemplate(params.template_name);
        if (template) {
          notificationData.title = this.processTemplate(template.title_template, params.variables || {});
          notificationData.message = this.processTemplate(template.message_template, params.variables || {});
          notificationData.action_url = template.action_url_template 
            ? this.processTemplate(template.action_url_template, params.variables || {})
            : params.action_url;
          notificationData.action_label = template.action_label || params.action_label;
          notificationData.type = template.type;
          notificationData.priority = template.priority;
        }
      } else {
        // Use provided content directly
        notificationData.title = params.title;
        notificationData.message = params.message;
        notificationData.action_url = params.action_url;
        notificationData.action_label = params.action_label;
      }

      // Check user preferences before creating notification
      const preferences = await this.getUserPreferences(params.user_id);
      const shouldCreateNotification = this.shouldCreateNotification(params.category, preferences);

      if (!shouldCreateNotification) {
        console.log(`Notification skipped for user ${params.user_id} due to preferences`);
        return null;
      }

      // Create the notification
      const { data: notification, error } = await supabase
        .from('notifications')
        .insert(notificationData)
        .select()
        .single();

      if (error) {
        throw error;
      }

      // Handle email notification if requested and user has email notifications enabled
      if ((params.send_email || params.send_email !== false) && preferences?.email_notifications) {
        const shouldSendEmail = this.shouldSendEmail(params.category, preferences);
        
        if (shouldSendEmail) {
          await this.queueEmailNotification(
            notification,
            params.template_name,
            params.variables || {},
            params.schedule_email_for
          );
        }
      }

      return notification;
    } catch (error) {
      console.error('Error creating notification:', error);
      throw error;
    }
  }

  // Create multiple notifications for multiple users
  static async createBulkNotifications(
    user_ids: string[],
    params: Omit<CreateNotificationParams, 'user_id'>
  ): Promise<Notification[]> {
    const notifications = [];
    
    for (const user_id of user_ids) {
      try {
        const notification = await this.createNotification({
          ...params,
          user_id
        });
        if (notification) {
          notifications.push(notification);
        }
      } catch (error) {
        console.error(`Error creating notification for user ${user_id}:`, error);
      }
    }

    return notifications;
  }

  // Get notifications for a user
  static async getUserNotifications(
    user_id: string,
    options: {
      limit?: number;
      offset?: number;
      unread_only?: boolean;
      category?: string;
      include_expired?: boolean;
    } = {}
  ): Promise<{ notifications: Notification[]; total_count: number }> {
    try {
      let query = supabase
        .from('notifications')
        .select('*', { count: 'exact' })
        .eq('user_id', user_id)
        .order('created_at', { ascending: false });

      if (options.unread_only) {
        query = query.eq('read', false);
      }

      if (options.category) {
        query = query.eq('category', options.category);
      }

      if (!options.include_expired) {
        query = query.or('expires_at.is.null,expires_at.gt.now()');
      }

      if (options.limit) {
        query = query.limit(options.limit);
      }

      if (options.offset) {
        query = query.range(options.offset, options.offset + (options.limit || 10) - 1);
      }

      const { data: notifications, error, count } = await query;

      if (error) {
        throw error;
      }

      return {
        notifications: notifications || [],
        total_count: count || 0
      };
    } catch (error) {
      console.error('Error fetching notifications:', error);
      throw error;
    }
  }

  // Mark notification as read
  static async markAsRead(notification_id: string): Promise<void> {
    try {
      const { error } = await supabase
        .from('notifications')
        .update({ read: true, updated_at: new Date().toISOString() })
        .eq('id', notification_id);

      if (error) {
        throw error;
      }
    } catch (error) {
      console.error('Error marking notification as read:', error);
      throw error;
    }
  }

  // Mark all notifications as read for a user
  static async markAllAsRead(user_id: string): Promise<void> {
    try {
      const { error } = await supabase
        .from('notifications')
        .update({ read: true, updated_at: new Date().toISOString() })
        .eq('user_id', user_id)
        .eq('read', false);

      if (error) {
        throw error;
      }
    } catch (error) {
      console.error('Error marking all notifications as read:', error);
      throw error;
    }
  }

  // Delete a notification
  static async deleteNotification(notification_id: string): Promise<void> {
    try {
      const { error } = await supabase
        .from('notifications')
        .delete()
        .eq('id', notification_id);

      if (error) {
        throw error;
      }
    } catch (error) {
      console.error('Error deleting notification:', error);
      throw error;
    }
  }

  // Get notification template
  static async getTemplate(template_name: string): Promise<NotificationTemplate | null> {
    try {
      const { data: template, error } = await supabase
        .from('notification_templates')
        .select('*')
        .eq('name', template_name)
        .eq('is_active', true)
        .single();

      if (error && error.code !== 'PGRST116') {
        throw error;
      }

      return template;
    } catch (error) {
      console.error('Error fetching notification template:', error);
      return null;
    }
  }

  // Process template with variables
  static processTemplate(template: string, variables: Record<string, any>): string {
    let processed = template;
    
    Object.entries(variables).forEach(([key, value]) => {
      const regex = new RegExp(`{{${key}}}`, 'g');
      processed = processed.replace(regex, String(value));
    });

    return processed;
  }

  // Get user notification preferences
  static async getUserPreferences(user_id: string): Promise<NotificationPreferences | null> {
    try {
      const { data: preferences, error } = await supabase
        .from('user_notification_preferences')
        .select('*')
        .eq('user_id', user_id)
        .single();

      if (error && error.code !== 'PGRST116') {
        throw error;
      }

      return preferences;
    } catch (error) {
      console.error('Error fetching user preferences:', error);
      return null;
    }
  }

  // Check if notification should be created based on user preferences
  static shouldCreateNotification(category: string, preferences: NotificationPreferences | null): boolean {
    if (!preferences) return true; // Default to creating notifications

    const categoryMap: Record<string, keyof NotificationPreferences> = {
      'project': 'project_updates',
      'financial': 'financial_alerts',
      'time_tracking': 'time_tracking_reminders',
      'message': 'team_messages',
      'system': 'system_announcements',
      'invoice': 'invoice_notifications',
      'asset': 'asset_notifications',
      'user': 'user_management_notifications'
    };

    const preferenceKey = categoryMap[category];
    return preferenceKey ? preferences[preferenceKey] : true;
  }

  // Check if email should be sent based on user preferences
  static shouldSendEmail(category: string, preferences: NotificationPreferences | null): boolean {
    if (!preferences || !preferences.email_notifications) return false;

    const emailCategoryMap: Record<string, keyof NotificationPreferences> = {
      'project': 'email_project_updates',
      'financial': 'email_financial_alerts',
      'time_tracking': 'email_time_tracking_reminders',
      'message': 'email_team_messages',
      'system': 'email_system_announcements',
      'invoice': 'email_invoice_notifications',
      'asset': 'email_asset_notifications',
      'user': 'email_user_management_notifications'
    };

    const preferenceKey = emailCategoryMap[category];
    return preferenceKey ? preferences[preferenceKey] : false;
  }

  // Queue email notification
  static async queueEmailNotification(
    notification: Notification,
    template_name?: string,
    variables: Record<string, any> = {},
    schedule_for?: string
  ): Promise<void> {
    try {
      // Get user email
      const { data: user, error: userError } = await supabase.auth.admin.getUserById(notification.user_id);
      if (userError || !user?.user?.email) {
        console.error('Cannot send email - user email not found');
        return;
      }

      let subject = notification.title;
      let htmlContent = notification.message;
      let textContent = notification.message;

      // If template is provided, use email templates
      if (template_name) {
        const template = await this.getTemplate(template_name);
        if (template?.email_subject_template && template?.email_body_template) {
          subject = this.processTemplate(template.email_subject_template, variables);
          htmlContent = this.processTemplate(template.email_body_template, variables);
          textContent = htmlContent.replace(/<[^>]*>/g, ''); // Strip HTML for text version
        }
      }

      // Create email queue entry
      const { error } = await supabase
        .from('email_queue')
        .insert({
          notification_id: notification.id,
          user_id: notification.user_id,
          to_email: user.user.email,
          subject,
          html_content: htmlContent,
          text_content: textContent,
          scheduled_for: schedule_for || new Date().toISOString()
        });

      if (error) {
        throw error;
      }

      console.log(`Email queued for notification ${notification.id}`);
    } catch (error) {
      console.error('Error queueing email notification:', error);
    }
  }

  // Process email queue (send pending emails)
  static async processEmailQueue(limit: number = 10): Promise<void> {
    try {
      // Get pending emails
      const { data: emails, error } = await supabase
        .from('email_queue')
        .select('*')
        .eq('status', 'pending')
        .lte('scheduled_for', new Date().toISOString())
        .lt('attempts', 3) // Don't retry failed emails more than 3 times
        .order('scheduled_for', { ascending: true })
        .limit(limit);

      if (error) {
        throw error;
      }

      if (!emails || emails.length === 0) {
        return;
      }

      console.log(`Processing ${emails.length} emails from queue`);

      for (const email of emails) {
        await this.sendQueuedEmail(email);
      }
    } catch (error) {
      console.error('Error processing email queue:', error);
    }
  }

  // Send a single queued email
  static async sendQueuedEmail(email: EmailQueueItem): Promise<void> {
    try {
      // Mark as sending
      await supabase
        .from('email_queue')
        .update({
          status: 'sending',
          attempts: email.attempts + 1,
          updated_at: new Date().toISOString()
        })
        .eq('id', email.id);

      // Try to send email using Supabase Auth (if SMTP is configured)
      try {
        const { error: emailError } = await supabase.auth.resetPasswordForEmail(
          email.to_email,
          {
            redirectTo: `${window.location.origin}/notifications`
          }
        );

        if (emailError) {
          throw emailError;
        }

        // Mark as sent
        await supabase
          .from('email_queue')
          .update({
            status: 'sent',
            sent_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          })
          .eq('id', email.id);

        // Update notification as email sent
        await supabase
          .from('notifications')
          .update({
            email_sent: true,
            email_sent_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          })
          .eq('id', email.notification_id);

        console.log(`Email sent successfully to ${email.to_email}`);

      } catch (sendError) {
        console.error(`Failed to send email to ${email.to_email}:`, sendError);

        // Mark as failed if max attempts reached
        const status = email.attempts + 1 >= email.max_attempts ? 'failed' : 'pending';

        await supabase
          .from('email_queue')
          .update({
            status,
            error_message: sendError.message,
            updated_at: new Date().toISOString()
          })
          .eq('id', email.id);
      }
    } catch (error) {
      console.error('Error sending queued email:', error);
    }
  }

  // Get unread count for a user
  static async getUnreadCount(user_id: string): Promise<number> {
    try {
      const { count, error } = await supabase
        .from('notifications')
        .select('*', { count: 'exact', head: true })
        .eq('user_id', user_id)
        .eq('read', false)
        .or('expires_at.is.null,expires_at.gt.now()');

      if (error) {
        throw error;
      }

      return count || 0;
    } catch (error) {
      console.error('Error getting unread count:', error);
      return 0;
    }
  }

  // Clean up expired notifications
  static async cleanupExpiredNotifications(): Promise<number> {
    try {
      const { data, error } = await supabase.rpc('cleanup_expired_notifications');

      if (error) {
        throw error;
      }

      console.log(`Cleaned up ${data} expired notifications`);
      return data || 0;
    } catch (error) {
      console.error('Error cleaning up expired notifications:', error);
      return 0;
    }
  }

  // Subscribe to real-time notifications for a user
  static subscribeToUserNotifications(
    user_id: string,
    callback: (notification: Notification) => void
  ) {
    // Create a unique channel name per user to avoid subscription conflicts
    const channelName = `user-notifications-${user_id}`;

    const subscription = supabase
      .channel(channelName)
      .on(
        'postgres_changes',
        {
          event: 'INSERT',
          schema: 'public',
          table: 'notifications',
          filter: `user_id=eq.${user_id}`
        },
        (payload) => {
          callback(payload.new as Notification);
        }
      )
      .subscribe();

    return subscription;
  }

  // Unsubscribe from real-time notifications
  static unsubscribeFromNotifications(subscription: any) {
    if (subscription) {
      supabase.removeChannel(subscription);
    }
  }

  // Update notification preferences
  static async updateUserPreferences(
    user_id: string,
    preferences: Partial<NotificationPreferences>
  ): Promise<void> {
    try {
      const { error } = await supabase
        .from('user_notification_preferences')
        .upsert({
          user_id,
          ...preferences,
          updated_at: new Date().toISOString()
        });

      if (error) {
        throw error;
      }
    } catch (error) {
      console.error('Error updating notification preferences:', error);
      throw error;
    }
  }

  // Helper method to create common notification types
  static async createProjectNotification(
    user_ids: string[],
    template_name: string,
    project_data: any,
    options: Partial<CreateNotificationParams> = {}
  ): Promise<Notification[]> {
    return this.createBulkNotifications(user_ids, {
      template_name,
      category: 'project',
      variables: project_data,
      related_entity_type: 'project',
      related_entity_id: project_data.project_id,
      ...options
    });
  }

  static async createFinancialNotification(
    user_ids: string[],
    template_name: string,
    financial_data: any,
    options: Partial<CreateNotificationParams> = {}
  ): Promise<Notification[]> {
    return this.createBulkNotifications(user_ids, {
      template_name,
      category: 'financial',
      variables: financial_data,
      related_entity_type: 'invoice',
      related_entity_id: financial_data.invoice_id,
      ...options
    });
  }

  static async createMessageNotification(
    user_ids: string[],
    template_name: string,
    message_data: any,
    options: Partial<CreateNotificationParams> = {}
  ): Promise<Notification[]> {
    return this.createBulkNotifications(user_ids, {
      template_name,
      category: 'message',
      variables: message_data,
      related_entity_type: 'message',
      related_entity_id: message_data.message_id,
      ...options
    });
  }

  static async createSystemNotification(
    user_ids: string[],
    template_name: string,
    system_data: any,
    options: Partial<CreateNotificationParams> = {}
  ): Promise<Notification[]> {
    return this.createBulkNotifications(user_ids, {
      template_name,
      category: 'system',
      variables: system_data,
      ...options
    });
  }

  // Test notification system
  static async testNotificationSystem(user_id: string): Promise<void> {
    try {
      await this.createNotification({
        user_id,
        type: 'info',
        category: 'system',
        title: 'Test Notification',
        message: 'This is a test notification to verify the system is working correctly.',
        action_url: '/settings/notifications',
        action_label: 'View Settings',
        send_email: true
      });

      console.log('Test notification created successfully');
    } catch (error) {
      console.error('Error creating test notification:', error);
      throw error;
    }
  }
}
