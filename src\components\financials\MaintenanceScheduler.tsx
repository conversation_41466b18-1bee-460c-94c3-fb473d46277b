import React, { useState } from 'react';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useToast } from '@/components/ui/use-toast';
import { AssetService, CompanyAsset } from '@/lib/assetService';
import { Calendar, Clock, Wrench, Save, X, Loader2 } from 'lucide-react';

interface MaintenanceSchedulerProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  asset: CompanyAsset | null;
  onMaintenanceScheduled: () => void;
}

const MaintenanceScheduler: React.FC<MaintenanceSchedulerProps> = ({
  open,
  onOpenChange,
  asset,
  onMaintenanceScheduled
}) => {
  const { toast } = useToast();
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState({
    maintenance_type: 'Preventive',
    description: '',
    scheduled_date: '',
    next_due_date: '',
    cost: '',
    labor_hours: '',
    parts_cost: '',
    external_service_cost: '',
    service_provider: '',
    technician: '',
    priority: 'Medium',
    recommendations: ''
  });

  React.useEffect(() => {
    if (open && asset) {
      // Reset form with default values
      const today = new Date().toISOString().split('T')[0];
      const nextMonth = new Date();
      nextMonth.setMonth(nextMonth.getMonth() + (asset.maintenance_interval_months || 12));
      
      setFormData({
        maintenance_type: 'Preventive',
        description: `Scheduled maintenance for ${asset.name}`,
        scheduled_date: today,
        next_due_date: nextMonth.toISOString().split('T')[0],
        cost: '',
        labor_hours: '',
        parts_cost: '',
        external_service_cost: '',
        service_provider: '',
        technician: '',
        priority: 'Medium',
        recommendations: ''
      });
    }
  }, [open, asset]);

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!asset || !formData.description || !formData.scheduled_date) {
      toast({
        title: "Validation Error",
        description: "Please fill in all required fields.",
        variant: "destructive",
      });
      return;
    }

    setLoading(true);
    try {
      const maintenanceData = {
        asset_id: asset.id,
        maintenance_type: formData.maintenance_type as any,
        description: formData.description,
        scheduled_date: formData.scheduled_date,
        next_due_date: formData.next_due_date || null,
        cost: parseFloat(formData.cost) || 0,
        labor_hours: parseFloat(formData.labor_hours) || 0,
        parts_cost: parseFloat(formData.parts_cost) || 0,
        external_service_cost: parseFloat(formData.external_service_cost) || 0,
        service_provider: formData.service_provider || null,
        technician: formData.technician || null,
        status: 'Scheduled' as any,
        priority: formData.priority as any,
        recommendations: formData.recommendations || null,
        created_by: 'Current User' // Replace with actual user
      };

      console.log('Creating maintenance record:', maintenanceData);
      const createdRecord = await AssetService.createMaintenanceRecord(maintenanceData);
      console.log('Maintenance record created:', createdRecord);

      // Update asset's next maintenance date
      if (formData.next_due_date) {
        console.log('Updating asset next maintenance date:', formData.next_due_date);
        await AssetService.updateAsset(asset.id, {
          next_maintenance_date: formData.next_due_date,
          last_maintenance_date: formData.scheduled_date // Also update last maintenance if it's being performed
        });
        console.log('Asset updated with new maintenance dates');
      }

      toast({
        title: "Maintenance Scheduled",
        description: `Maintenance for ${asset.name} has been scheduled successfully.`,
      });

      onMaintenanceScheduled();
      onOpenChange(false);
    } catch (error) {
      console.error('Error scheduling maintenance:', error);
      toast({
        title: "Error",
        description: "Failed to schedule maintenance. Please try again.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  if (!asset) return null;

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center">
            <Calendar className="w-5 h-5 mr-2" />
            Schedule Maintenance
          </DialogTitle>
          <DialogDescription>
            Schedule maintenance for {asset.name} (#{asset.asset_number})
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-4">
          {/* Maintenance Type and Priority */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label>Maintenance Type *</Label>
              <Select value={formData.maintenance_type} onValueChange={(value) => handleInputChange('maintenance_type', value)}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="Preventive">Preventive</SelectItem>
                  <SelectItem value="Corrective">Corrective</SelectItem>
                  <SelectItem value="Emergency">Emergency</SelectItem>
                  <SelectItem value="Inspection">Inspection</SelectItem>
                  <SelectItem value="Upgrade">Upgrade</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <Label>Priority</Label>
              <Select value={formData.priority} onValueChange={(value) => handleInputChange('priority', value)}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="Low">Low</SelectItem>
                  <SelectItem value="Medium">Medium</SelectItem>
                  <SelectItem value="High">High</SelectItem>
                  <SelectItem value="Critical">Critical</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Description */}
          <div className="space-y-2">
            <Label htmlFor="description">Description *</Label>
            <Textarea
              id="description"
              value={formData.description}
              onChange={(e) => handleInputChange('description', e.target.value)}
              placeholder="Describe the maintenance work to be performed"
              rows={3}
              required
            />
          </div>

          {/* Dates */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="scheduled_date">Scheduled Date *</Label>
              <Input
                id="scheduled_date"
                type="date"
                value={formData.scheduled_date}
                onChange={(e) => handleInputChange('scheduled_date', e.target.value)}
                required
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="next_due_date">Next Due Date</Label>
              <Input
                id="next_due_date"
                type="date"
                value={formData.next_due_date}
                onChange={(e) => handleInputChange('next_due_date', e.target.value)}
              />
            </div>
          </div>

          {/* Cost Information */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="cost">Estimated Total Cost</Label>
              <Input
                id="cost"
                type="number"
                step="0.01"
                value={formData.cost}
                onChange={(e) => handleInputChange('cost', e.target.value)}
                placeholder="0.00"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="labor_hours">Labor Hours</Label>
              <Input
                id="labor_hours"
                type="number"
                step="0.5"
                value={formData.labor_hours}
                onChange={(e) => handleInputChange('labor_hours', e.target.value)}
                placeholder="0.0"
              />
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="parts_cost">Parts Cost</Label>
              <Input
                id="parts_cost"
                type="number"
                step="0.01"
                value={formData.parts_cost}
                onChange={(e) => handleInputChange('parts_cost', e.target.value)}
                placeholder="0.00"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="external_service_cost">External Service Cost</Label>
              <Input
                id="external_service_cost"
                type="number"
                step="0.01"
                value={formData.external_service_cost}
                onChange={(e) => handleInputChange('external_service_cost', e.target.value)}
                placeholder="0.00"
              />
            </div>
          </div>

          {/* Service Provider and Technician */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="service_provider">Service Provider</Label>
              <Input
                id="service_provider"
                value={formData.service_provider}
                onChange={(e) => handleInputChange('service_provider', e.target.value)}
                placeholder="Company or provider name"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="technician">Technician</Label>
              <Input
                id="technician"
                value={formData.technician}
                onChange={(e) => handleInputChange('technician', e.target.value)}
                placeholder="Technician name"
              />
            </div>
          </div>

          {/* Recommendations */}
          <div className="space-y-2">
            <Label htmlFor="recommendations">Recommendations</Label>
            <Textarea
              id="recommendations"
              value={formData.recommendations}
              onChange={(e) => handleInputChange('recommendations', e.target.value)}
              placeholder="Any special instructions or recommendations"
              rows={2}
            />
          </div>

          <DialogFooter>
            <Button type="button" variant="outline" onClick={() => onOpenChange(false)}>
              <X className="w-4 h-4 mr-2" />
              Cancel
            </Button>
            <Button type="submit" disabled={loading}>
              {loading ? (
                <Loader2 className="w-4 h-4 mr-2 animate-spin" />
              ) : (
                <Save className="w-4 h-4 mr-2" />
              )}
              Schedule Maintenance
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
};

export default MaintenanceScheduler;
