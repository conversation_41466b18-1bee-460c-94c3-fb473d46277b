-- Minimal Profile Picture Setup
-- This script only adds what's needed for profile picture upload
-- Compatible with your existing database structure and functions

-- 1. Create storage bucket for profile pictures
INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
VALUES (
  'profile-pictures',
  'profile-pictures',
  true,
  5242880, -- 5MB limit
  ARRAY['image/jpeg', 'image/png', 'image/webp', 'image/gif']
) ON CONFLICT (id) DO UPDATE SET
  public = true,
  file_size_limit = 5242880,
  allowed_mime_types = ARRAY['image/jpeg', 'image/png', 'image/webp', 'image/gif'];

-- 2. Create storage policies for profile pictures
DROP POLICY IF EXISTS "Users can view all profile pictures" ON storage.objects;
DROP POLICY IF EXISTS "Users can upload their own profile picture" ON storage.objects;
DROP POLICY IF EXISTS "Users can update their own profile picture" ON storage.objects;
DROP POLICY IF EXISTS "Users can delete their own profile picture" ON storage.objects;

CREATE POLICY "Users can view all profile pictures" ON storage.objects
FOR SELECT USING (bucket_id = 'profile-pictures');

CREATE POLICY "Users can upload their own profile picture" ON storage.objects
FOR INSERT WITH CHECK (
  bucket_id = 'profile-pictures' 
  AND auth.uid()::text = (storage.foldername(name))[1]
);

CREATE POLICY "Users can update their own profile picture" ON storage.objects
FOR UPDATE USING (
  bucket_id = 'profile-pictures' 
  AND auth.uid()::text = (storage.foldername(name))[1]
);

CREATE POLICY "Users can delete their own profile picture" ON storage.objects
FOR DELETE USING (
  bucket_id = 'profile-pictures' 
  AND auth.uid()::text = (storage.foldername(name))[1]
);

-- 3. Add only the essential columns to user_profiles table
DO $$
BEGIN
  -- Check if user_profiles table exists
  IF EXISTS (SELECT FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'user_profiles') THEN
    
    -- Add profile picture URL column
    IF NOT EXISTS (SELECT FROM information_schema.columns WHERE table_schema = 'public' AND table_name = 'user_profiles' AND column_name = 'profile_picture_url') THEN
      ALTER TABLE public.user_profiles ADD COLUMN profile_picture_url TEXT;
      RAISE NOTICE '✓ Added profile_picture_url column';
    ELSE
      RAISE NOTICE '✓ profile_picture_url column already exists';
    END IF;
    
    -- Add bio column
    IF NOT EXISTS (SELECT FROM information_schema.columns WHERE table_schema = 'public' AND table_name = 'user_profiles' AND column_name = 'bio') THEN
      ALTER TABLE public.user_profiles ADD COLUMN bio TEXT;
      RAISE NOTICE '✓ Added bio column';
    ELSE
      RAISE NOTICE '✓ bio column already exists';
    END IF;
    
    -- Add location column
    IF NOT EXISTS (SELECT FROM information_schema.columns WHERE table_schema = 'public' AND table_name = 'user_profiles' AND column_name = 'location') THEN
      ALTER TABLE public.user_profiles ADD COLUMN location TEXT;
      RAISE NOTICE '✓ Added location column';
    ELSE
      RAISE NOTICE '✓ location column already exists';
    END IF;
    
    -- Add date of birth column
    IF NOT EXISTS (SELECT FROM information_schema.columns WHERE table_schema = 'public' AND table_name = 'user_profiles' AND column_name = 'date_of_birth') THEN
      ALTER TABLE public.user_profiles ADD COLUMN date_of_birth DATE;
      RAISE NOTICE '✓ Added date_of_birth column';
    ELSE
      RAISE NOTICE '✓ date_of_birth column already exists';
    END IF;
    
    -- Add emergency contact columns
    IF NOT EXISTS (SELECT FROM information_schema.columns WHERE table_schema = 'public' AND table_name = 'user_profiles' AND column_name = 'emergency_contact_name') THEN
      ALTER TABLE public.user_profiles ADD COLUMN emergency_contact_name TEXT;
      RAISE NOTICE '✓ Added emergency_contact_name column';
    ELSE
      RAISE NOTICE '✓ emergency_contact_name column already exists';
    END IF;
    
    IF NOT EXISTS (SELECT FROM information_schema.columns WHERE table_schema = 'public' AND table_name = 'user_profiles' AND column_name = 'emergency_contact_phone') THEN
      ALTER TABLE public.user_profiles ADD COLUMN emergency_contact_phone TEXT;
      RAISE NOTICE '✓ Added emergency_contact_phone column';
    ELSE
      RAISE NOTICE '✓ emergency_contact_phone column already exists';
    END IF;
    
    IF NOT EXISTS (SELECT FROM information_schema.columns WHERE table_schema = 'public' AND table_name = 'user_profiles' AND column_name = 'emergency_contact_relationship') THEN
      ALTER TABLE public.user_profiles ADD COLUMN emergency_contact_relationship TEXT;
      RAISE NOTICE '✓ Added emergency_contact_relationship column';
    ELSE
      RAISE NOTICE '✓ emergency_contact_relationship column already exists';
    END IF;
    
    RAISE NOTICE '✅ user_profiles table updated successfully';
  ELSE
    RAISE NOTICE '❌ user_profiles table does not exist';
  END IF;
END $$;

-- 4. Create simple profile picture upload function (compatible with existing functions)
CREATE OR REPLACE FUNCTION handle_profile_picture_upload(
  p_user_id UUID,
  p_file_path TEXT
)
RETURNS TEXT AS $$
DECLARE
  new_picture_url TEXT;
  supabase_url TEXT := 'https://ygdaucsngasdutbvmevs.supabase.co/storage/v1/object/public/profile-pictures/';
BEGIN
  -- Generate the new picture URL
  new_picture_url := supabase_url || p_file_path;

  -- Check if user_profiles table exists and has the column
  IF EXISTS (
    SELECT FROM information_schema.columns
    WHERE table_schema = 'public'
    AND table_name = 'user_profiles'
    AND column_name = 'profile_picture_url'
  ) THEN
    -- Update the user profile with new picture URL
    -- Try both user_id and id columns to be compatible with different schemas
    UPDATE public.user_profiles
    SET
      profile_picture_url = new_picture_url,
      updated_at = COALESCE(updated_at, NOW()) -- Only update if column exists
    WHERE (user_profiles.user_id = p_user_id OR user_profiles.id = p_user_id);

    -- Check if update was successful
    IF FOUND THEN
      RAISE NOTICE 'Profile picture updated successfully for user %', p_user_id;
    ELSE
      RAISE NOTICE 'No user found with ID %', p_user_id;
    END IF;
  ELSE
    RAISE NOTICE 'profile_picture_url column does not exist in user_profiles table';
  END IF;

  RETURN new_picture_url;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 5. Grant permissions
GRANT EXECUTE ON FUNCTION handle_profile_picture_upload(UUID, TEXT) TO authenticated;

-- 6. Test the setup
DO $$
DECLARE
  test_user_id UUID;
  test_result TEXT;
  user_count INTEGER;
BEGIN
  -- Count total users
  SELECT COUNT(*) INTO user_count FROM auth.users;
  RAISE NOTICE 'Total users in system: %', user_count;
  
  -- Get a test user ID (first user in auth.users)
  SELECT id INTO test_user_id FROM auth.users LIMIT 1;
  
  IF test_user_id IS NOT NULL THEN
    -- Test the profile picture function
    SELECT handle_profile_picture_upload(test_user_id, 'test/test-image.jpg') INTO test_result;
    RAISE NOTICE 'Profile picture function test result: %', test_result;
  ELSE
    RAISE NOTICE 'No users found for testing';
  END IF;
  
  RAISE NOTICE '=== SETUP COMPLETE ===';
  RAISE NOTICE '✅ Storage bucket created: profile-pictures';
  RAISE NOTICE '✅ Storage policies configured';
  RAISE NOTICE '✅ Profile columns added to user_profiles table';
  RAISE NOTICE '✅ Profile picture upload function created';
  RAISE NOTICE '✅ Compatible with existing database functions';
  RAISE NOTICE '';
  RAISE NOTICE 'You can now:';
  RAISE NOTICE '- Upload profile pictures up to 5MB';
  RAISE NOTICE '- Store bio, location, date of birth';
  RAISE NOTICE '- Add emergency contact information';
  RAISE NOTICE '- Use existing functions like simple_update_profile';
END $$;
