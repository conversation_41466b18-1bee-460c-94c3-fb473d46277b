import React, { useState, useEffect } from 'react';
import Layout from '@/components/layout/Layout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  BarChart3, 
  TrendingUp, 
  DollarSign, 
  Users, 
  Building, 
  Zap,
  RefreshCw,
  Settings,
  Grid3X3,
  Bell,
  Calendar
} from 'lucide-react';
import AdvancedChart, { ChartConfig } from '@/components/dashboard/AdvancedChart';
import KPIWidget, { createRevenueKPI, createProfitKPI, createProjectsKPI, KPIData } from '@/components/dashboard/KPIWidget';
import { useToast } from '@/components/ui/use-toast';

const TestDashboard: React.FC = () => {
  const { toast } = useToast();
  const [loading, setLoading] = useState(false);
  const [realTimeEnabled, setRealTimeEnabled] = useState(false);
  const [lastUpdate, setLastUpdate] = useState<Date>(new Date());

  // Sample data for testing
  const sampleMetrics = {
    revenue: 150000,
    profit: 45000,
    activeProjects: 8,
    activeClients: 12,
    cashFlow: 25000,
    pendingInvoices: 7
  };

  const sampleChartData = [
    { name: 'Jan', value: 120000 },
    { name: 'Feb', value: 135000 },
    { name: 'Mar', value: 148000 },
    { name: 'Apr', value: 162000 },
    { name: 'May', value: 150000 }
  ];

  // Create KPI data
  const kpiData: KPIData[] = [
    createRevenueKPI(sampleMetrics.revenue),
    createProfitKPI(sampleMetrics.profit),
    createProjectsKPI(sampleMetrics.activeProjects, 15),
    {
      id: 'clients',
      title: 'Active Clients',
      value: sampleMetrics.activeClients,
      format: 'number',
      status: 'info',
      icon: <Users className="h-4 w-4 text-blue-500" />,
      color: '#3b82f6',
      animated: true
    },
    {
      id: 'cashflow',
      title: 'Cash Flow',
      value: sampleMetrics.cashFlow,
      format: 'currency',
      trend: 'up',
      status: 'success',
      icon: <TrendingUp className="h-4 w-4 text-green-500" />,
      color: '#10b981',
      animated: true
    },
    {
      id: 'invoices',
      title: 'Pending Invoices',
      value: sampleMetrics.pendingInvoices,
      format: 'number',
      status: 'success',
      icon: <Building className="h-4 w-4 text-purple-500" />,
      color: '#8b5cf6',
      animated: true
    }
  ];

  // Create chart configuration
  const revenueChartConfig: ChartConfig = {
    type: 'area',
    title: 'Revenue Trend',
    description: 'Monthly revenue over time',
    data: sampleChartData,
    dataKeys: ['value'],
    colors: ['#10b981'],
    height: 300,
    animated: true,
    gradient: true
  };

  const toggleRealTime = () => {
    setRealTimeEnabled(!realTimeEnabled);
    toast({
      title: realTimeEnabled ? "Real-time Disabled" : "Real-time Enabled",
      description: realTimeEnabled ? "Dashboard updates stopped" : "Dashboard will update automatically",
    });
  };

  const refreshData = () => {
    setLoading(true);
    setLastUpdate(new Date());
    setTimeout(() => setLoading(false), 1000);
    toast({
      title: "Data Refreshed",
      description: "Dashboard data has been updated",
    });
  };

  return (
    <Layout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Advanced Dashboard</h1>
            <p className="text-gray-600 mt-1">
              Real-time insights and comprehensive analytics for your construction business
            </p>
          </div>
          <div className="flex items-center space-x-3">
            <Badge variant={realTimeEnabled ? "default" : "secondary"} className="flex items-center space-x-1">
              <div className={`w-2 h-2 rounded-full ${realTimeEnabled ? 'bg-green-500 animate-pulse' : 'bg-gray-400'}`} />
              <span>{realTimeEnabled ? 'Live' : 'Static'}</span>
            </Badge>
            <Button variant="outline" onClick={toggleRealTime}>
              <Zap className={`w-4 h-4 mr-2 ${realTimeEnabled ? 'text-green-500' : ''}`} />
              {realTimeEnabled ? 'Disable' : 'Enable'} Real-time
            </Button>
            <Button variant="outline" onClick={refreshData} disabled={loading}>
              <RefreshCw className={`w-4 h-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
              Refresh
            </Button>
            <Button variant="outline">
              <Settings className="w-4 h-4 mr-2" />
              Customize
            </Button>
          </div>
        </div>

        {/* Last Update Info */}
        <div className="flex items-center justify-between bg-blue-50 border border-blue-200 rounded-lg p-3">
          <div className="flex items-center space-x-2">
            <Calendar className="h-4 w-4 text-blue-500" />
            <span className="text-sm text-blue-700">
              Last updated: {lastUpdate.toLocaleTimeString()}
            </span>
          </div>
          <div className="flex items-center space-x-2">
            <Bell className="h-4 w-4 text-green-500" />
            <span className="text-sm text-green-700">
              All systems operational
            </span>
          </div>
        </div>

        {/* KPI Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-6">
          {kpiData.map((kpi) => (
            <KPIWidget
              key={kpi.id}
              data={kpi}
              variant="modern"
              size="md"
              loading={loading}
            />
          ))}
        </div>

        {/* Sample Alert */}
        <Card className="border-l-4 border-green-500 bg-green-50">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-semibold text-green-800">System Status</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-green-700">All dashboard components are working correctly!</p>
            <p className="text-xs text-green-600 mt-2">
              {new Date().toLocaleTimeString()}
            </p>
          </CardContent>
        </Card>

        {/* Sample Chart */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <AdvancedChart
            config={revenueChartConfig}
            loading={loading}
            onRefresh={refreshData}
          />
          
          <Card>
            <CardHeader>
              <CardTitle>Dashboard Features</CardTitle>
              <CardDescription>Advanced dashboard capabilities</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                  <div>
                    <p className="font-medium">Real-time Updates</p>
                    <p className="text-sm text-gray-500">Live data streaming</p>
                  </div>
                  <Badge variant="outline" className="text-green-600">Active</Badge>
                </div>
                <div className="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                  <div>
                    <p className="font-medium">Interactive Charts</p>
                    <p className="text-sm text-gray-500">Multiple chart types</p>
                  </div>
                  <Badge variant="outline" className="text-blue-600">Ready</Badge>
                </div>
                <div className="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                  <div>
                    <p className="font-medium">KPI Widgets</p>
                    <p className="text-sm text-gray-500">Animated metrics</p>
                  </div>
                  <Badge variant="outline" className="text-purple-600">Working</Badge>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </Layout>
  );
};

export default TestDashboard;
