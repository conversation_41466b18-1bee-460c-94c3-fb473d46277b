import { supabase } from './supabase';

// Types for Messages System
export interface MessageChannel {
  id: string;
  name: string;
  description?: string;
  channel_type: 'general' | 'project' | 'team' | 'direct' | 'announcement';
  project_id?: string;
  is_private: boolean;
  created_by?: string;
  created_at: string;
  updated_at: string;
  unread_count?: number;
  last_message?: Message;
}

export interface Message {
  id: string;
  channel_id: string;
  sender_name: string;
  sender_email?: string;
  sender_role?: string;
  message_content: string;
  message_type: 'text' | 'file' | 'image' | 'system' | 'announcement';
  is_edited: boolean;
  edited_at?: string;
  reply_to_message_id?: string;
  attachments?: any;
  is_pinned: boolean;
  is_deleted: boolean;
  created_at: string;
  updated_at: string;
  reactions?: MessageReaction[];
  reply_to?: Message;
  // Creator information
  created_by_user_id?: string;
  created_by_avatar?: string;
}

export interface MessageReaction {
  id: string;
  message_id: string;
  user_name: string;
  user_email?: string;
  reaction_emoji: string;
  created_at: string;
}

export interface ChannelMember {
  id: string;
  channel_id: string;
  user_name: string;
  user_email: string;
  role: 'admin' | 'moderator' | 'member';
  joined_at: string;
}

export interface MessageReadStatus {
  id: string;
  message_id: string;
  user_email: string;
  read_at: string;
}

export class MessageService {
  // Channel Management
  static async getChannels(): Promise<MessageChannel[]> {
    const { data, error } = await supabase
      .from('message_channels')
      .select('*')
      .order('created_at', { ascending: true });

    if (error) throw error;
    return data || [];
  }

  static async getChannelById(id: string): Promise<MessageChannel | null> {
    const { data, error } = await supabase
      .from('message_channels')
      .select('*')
      .eq('id', id)
      .single();

    if (error) {
      if (error.code === 'PGRST116') return null;
      throw error;
    }
    return data;
  }

  static async createChannel(channel: Omit<MessageChannel, 'id' | 'created_at' | 'updated_at'>): Promise<MessageChannel> {
    const { data, error } = await supabase
      .from('message_channels')
      .insert([channel])
      .select()
      .single();

    if (error) throw error;
    return data;
  }

  static async updateChannel(id: string, updates: Partial<MessageChannel>): Promise<MessageChannel> {
    const { data, error } = await supabase
      .from('message_channels')
      .update(updates)
      .eq('id', id)
      .select()
      .single();

    if (error) throw error;
    return data;
  }

  static async deleteChannel(id: string): Promise<void> {
    const { error } = await supabase
      .from('message_channels')
      .delete()
      .eq('id', id);

    if (error) throw error;
  }

  // Message Management
  static async getMessages(channelId: string, limit: number = 50, offset: number = 0): Promise<Message[]> {
    const { data, error } = await supabase
      .from('messages')
      .select(`
        *,
        reply_to:messages!reply_to_message_id(*)
      `)
      .eq('channel_id', channelId)
      .eq('is_deleted', false)
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1);

    if (error) throw error;
    
    // Reverse to show oldest first
    return (data || []).reverse();
  }

  static async getMessageById(id: string): Promise<Message | null> {
    const { data, error } = await supabase
      .from('messages')
      .select(`
        *,
        reply_to:messages!reply_to_message_id(*)
      `)
      .eq('id', id)
      .single();

    if (error) {
      if (error.code === 'PGRST116') return null;
      throw error;
    }
    return data;
  }

  static async sendMessage(message: Omit<Message, 'id' | 'created_at' | 'updated_at' | 'is_edited' | 'edited_at' | 'is_pinned' | 'is_deleted' | 'reactions' | 'reply_to'>): Promise<Message> {
    const { data, error } = await supabase
      .from('messages')
      .insert([{
        ...message,
        is_edited: false,
        is_pinned: false,
        is_deleted: false
      }])
      .select(`
        *,
        reply_to:messages!reply_to_message_id(*)
      `)
      .single();

    if (error) throw error;
    return data;
  }

  static async updateMessage(id: string, updates: Partial<Message>): Promise<Message> {
    const { data, error } = await supabase
      .from('messages')
      .update({
        ...updates,
        is_edited: true,
        edited_at: new Date().toISOString()
      })
      .eq('id', id)
      .select(`
        *,
        reply_to:messages!reply_to_message_id(*)
      `)
      .single();

    if (error) throw error;
    return data;
  }

  static async deleteMessage(id: string): Promise<void> {
    const { error } = await supabase
      .from('messages')
      .update({ is_deleted: true })
      .eq('id', id);

    if (error) throw error;
  }

  static async pinMessage(id: string, pinned: boolean = true): Promise<Message> {
    const { data, error } = await supabase
      .from('messages')
      .update({ is_pinned: pinned })
      .eq('id', id)
      .select(`
        *,
        reply_to:messages!reply_to_message_id(*)
      `)
      .single();

    if (error) throw error;
    return data;
  }

  // Message Reactions
  static async getMessageReactions(messageId: string): Promise<MessageReaction[]> {
    const { data, error } = await supabase
      .from('message_reactions')
      .select('*')
      .eq('message_id', messageId)
      .order('created_at', { ascending: true });

    if (error) throw error;
    return data || [];
  }

  static async addReaction(messageId: string, userEmail: string, userName: string, emoji: string): Promise<MessageReaction> {
    const { data, error } = await supabase
      .from('message_reactions')
      .insert([{
        message_id: messageId,
        user_email: userEmail,
        user_name: userName,
        reaction_emoji: emoji
      }])
      .select()
      .single();

    if (error) throw error;
    return data;
  }

  static async removeReaction(messageId: string, userEmail: string, emoji: string): Promise<void> {
    const { error } = await supabase
      .from('message_reactions')
      .delete()
      .eq('message_id', messageId)
      .eq('user_email', userEmail)
      .eq('reaction_emoji', emoji);

    if (error) throw error;
  }

  // Channel Members
  static async getChannelMembers(channelId: string): Promise<ChannelMember[]> {
    const { data, error } = await supabase
      .from('channel_members')
      .select('*')
      .eq('channel_id', channelId)
      .order('joined_at', { ascending: true });

    if (error) throw error;
    return data || [];
  }

  static async addChannelMember(channelId: string, userEmail: string, userName: string, role: 'admin' | 'moderator' | 'member' = 'member'): Promise<ChannelMember> {
    const { data, error } = await supabase
      .from('channel_members')
      .insert([{
        channel_id: channelId,
        user_email: userEmail,
        user_name: userName,
        role: role
      }])
      .select()
      .single();

    if (error) throw error;
    return data;
  }

  static async removeChannelMember(channelId: string, userEmail: string): Promise<void> {
    const { error } = await supabase
      .from('channel_members')
      .delete()
      .eq('channel_id', channelId)
      .eq('user_email', userEmail);

    if (error) throw error;
  }

  // Search Messages
  static async searchMessages(query: string, channelId?: string): Promise<Message[]> {
    let queryBuilder = supabase
      .from('messages')
      .select(`
        *,
        reply_to:messages!reply_to_message_id(*)
      `)
      .ilike('message_content', `%${query}%`)
      .eq('is_deleted', false)
      .order('created_at', { ascending: false })
      .limit(20);

    if (channelId) {
      queryBuilder = queryBuilder.eq('channel_id', channelId);
    }

    const { data, error } = await queryBuilder;
    if (error) throw error;
    return data || [];
  }

  // Get Recent Activity
  static async getRecentActivity(limit: number = 10): Promise<Message[]> {
    const { data, error } = await supabase
      .from('messages')
      .select(`
        *,
        reply_to:messages!reply_to_message_id(*)
      `)
      .eq('is_deleted', false)
      .order('created_at', { ascending: false })
      .limit(limit);

    if (error) throw error;
    return data || [];
  }

  // Mark messages as read
  static async markMessageAsRead(messageId: string, userEmail: string): Promise<void> {
    const { error } = await supabase
      .from('message_read_status')
      .upsert([{
        message_id: messageId,
        user_email: userEmail
      }]);

    if (error) throw error;
  }

  // Get unread message count for a channel
  static async getUnreadCount(channelId: string, userEmail: string): Promise<number> {
    const { data, error } = await supabase
      .from('messages')
      .select('id')
      .eq('channel_id', channelId)
      .eq('is_deleted', false)
      .not('id', 'in', `(
        SELECT message_id FROM message_read_status 
        WHERE user_email = '${userEmail}'
      )`);

    if (error) throw error;
    return data?.length || 0;
  }
}
