import { supabase } from './supabase';
import { 
  Client, 
  ClientPayment, 
  ClientInvoice, 
  ClientFinancialSummary, 
  ClientContact,
  ClientDocument,
  ClientActivity,
  ClientFilters,
  ClientAnalytics
} from '@/types/client';

export class ClientService {
  // Client CRUD operations
  static async getClients(filters?: ClientFilters): Promise<Client[]> {
    try {
      console.log('Fetching clients from Supabase...');
      
      let query = supabase.from('clients').select('*');
      
      if (filters?.status) {
        query = query.eq('status', filters.status);
      }
      
      if (filters?.client_type) {
        query = query.eq('client_type', filters.client_type);
      }
      
      if (filters?.industry) {
        query = query.eq('industry', filters.industry);
      }
      
      if (filters?.search) {
        query = query.or(`name.ilike.%${filters.search}%,company_name.ilike.%${filters.search}%,email.ilike.%${filters.search}%`);
      }
      
      const { data, error } = await query.order('created_at', { ascending: false });
      
      if (error) {
        console.error('Error fetching clients:', error);
        // Return mock data for development
        return this.getMockClients();
      }
      
      console.log('Successfully fetched clients:', data?.length || 0);
      return data || [];
    } catch (error) {
      console.error('Error in getClients:', error);
      return this.getMockClients();
    }
  }

  static async getClientById(id: string): Promise<Client | null> {
    try {
      const { data, error } = await supabase
        .from('clients')
        .select('*')
        .eq('id', id)
        .single();
      
      if (error) {
        console.error('Error fetching client:', error);
        return this.getMockClients().find(c => c.id === id) || null;
      }
      
      return data;
    } catch (error) {
      console.error('Error in getClientById:', error);
      return this.getMockClients().find(c => c.id === id) || null;
    }
  }

  static async createClient(client: Omit<Client, 'id' | 'created_at' | 'updated_at'>): Promise<Client> {
    try {
      console.log('Creating client:', client);

      const { data, error } = await supabase
        .from('clients')
        .insert([client])
        .select()
        .single();

      if (error) {
        console.error('Error creating client:', error);
        // Create a fallback client for development
        const fallbackClient = {
          ...client,
          id: crypto.randomUUID(),
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        };
        console.log('Using fallback client:', fallbackClient);

        return fallbackClient as Client;
      }

      console.log('Client created successfully:', data);

      return data;
    } catch (error) {
      console.error('Error in createClient:', error);
      // Create a fallback client for development
      const fallbackClient = {
        ...client,
        id: crypto.randomUUID(),
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };
      console.log('Using fallback client due to error:', fallbackClient);

      return fallbackClient as Client;
    }
  }

  static async updateClient(id: string, updates: Partial<Client>): Promise<Client> {
    try {
      const updatedClient = {
        ...updates,
        updated_at: new Date().toISOString()
      };

      const { data, error } = await supabase
        .from('clients')
        .update(updatedClient)
        .eq('id', id)
        .select()
        .single();

      if (error) {
        console.error('Error updating client:', error);
        throw error;
      }

      console.log('Client updated successfully:', data);
      return data;
    } catch (error) {
      console.error('Error in updateClient:', error);
      throw error;
    }
  }

  static async deleteClient(id: string): Promise<void> {
    try {
      const { error } = await supabase
        .from('clients')
        .delete()
        .eq('id', id);

      if (error) {
        console.error('Error deleting client:', error);
        throw error;
      }

      console.log('Client deleted successfully');
    } catch (error) {
      console.error('Error in deleteClient:', error);
      throw error;
    }
  }

  // Financial operations
  static async getClientFinancialSummary(clientId: string): Promise<ClientFinancialSummary> {
    try {
      // Get the client and their actual financial data
      const client = await this.getClientById(clientId);
      if (!client) {
        throw new Error('Client not found');
      }

      // Get real invoices and payments
      const invoices = await this.getClientInvoices(clientId);
      const payments = await this.getClientPayments(clientId);

      // Calculate real financial metrics
      const totalInvoiced = invoices.reduce((sum, inv) => sum + inv.total_amount, 0);
      const totalPaid = payments.reduce((sum, pay) => sum + pay.amount, 0);
      const outstandingAmount = totalInvoiced - totalPaid;
      const overdueAmount = invoices
        .filter(inv => inv.status === 'Overdue')
        .reduce((sum, inv) => sum + inv.total_amount, 0);

      // Get last payment info
      const lastPayment = payments.sort((a, b) =>
        new Date(b.payment_date).getTime() - new Date(a.payment_date).getTime()
      )[0];

      // Calculate average payment days
      const paidInvoices = invoices.filter(inv => inv.paid_date);
      const averagePaymentDays = paidInvoices.length > 0
        ? Math.round(paidInvoices.reduce((sum, inv) => {
            const issueDate = new Date(inv.issue_date);
            const paidDate = new Date(inv.paid_date!);
            const daysDiff = Math.floor((paidDate.getTime() - issueDate.getTime()) / (1000 * 60 * 60 * 24));
            return sum + daysDiff;
          }, 0) / paidInvoices.length)
        : client.payment_terms || 30;

      const creditLimit = client.credit_limit || 0;
      const creditUsed = Math.min(outstandingAmount, creditLimit);

      return {
        client_id: clientId,
        total_invoiced: totalInvoiced,
        total_paid: totalPaid,
        outstanding_amount: outstandingAmount,
        overdue_amount: overdueAmount,
        credit_used: creditUsed,
        credit_available: Math.max(0, creditLimit - creditUsed),
        last_payment_date: lastPayment?.payment_date || null,
        last_payment_amount: lastPayment?.amount || 0,
        average_payment_days: averagePaymentDays,
        payment_history_months: 12,
        total_projects: new Set(invoices.map(inv => inv.project_id).filter(Boolean)).size,
        active_projects: invoices.filter(inv => inv.status !== 'Paid' && inv.status !== 'Cancelled').length
      };
    } catch (error) {
      console.error('Error getting client financial summary:', error);
      throw error;
    }
  }

  static async recordPayment(paymentData: Omit<ClientPayment, 'id' | 'created_at' | 'updated_at' | 'currency'>): Promise<ClientPayment | null> {
    try {
      console.log('Recording payment:', paymentData);

      const payment: ClientPayment = {
        ...paymentData,
        id: crypto.randomUUID(),
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        currency: 'USD'
      };

      // Try to save to Supabase first
      const { data, error } = await supabase
        .from('client_payments')
        .insert([payment])
        .select()
        .single();

      if (error) {
        console.error('Error saving payment to Supabase:', error);
        // Fallback to localStorage
        const localPayments = JSON.parse(localStorage.getItem('client_payments') || '[]');
        localPayments.push(payment);
        localStorage.setItem('client_payments', JSON.stringify(localPayments));
        console.log('Payment saved to localStorage:', payment);
        return payment;
      }

      console.log('Payment saved to Supabase:', data);
      return data;
    } catch (error) {
      console.error('Error recording payment:', error);
      return null;
    }
  }

  static async getClientPayments(clientId: string): Promise<ClientPayment[]> {
    try {
      const { data, error } = await supabase
        .from('client_payments')
        .select('*')
        .eq('client_id', clientId)
        .order('payment_date', { ascending: false });

      if (error) {
        console.error('Error fetching client payments:', error);
        // Try localStorage first, then fallback to mock data
        const localPayments = JSON.parse(localStorage.getItem('client_payments') || '[]');
        const clientPayments = localPayments.filter((p: ClientPayment) => p.client_id === clientId);
        return clientPayments.length > 0 ? clientPayments : this.getMockPayments(clientId);
      }

      return data || [];
    } catch (error) {
      console.error('Error in getClientPayments:', error);
      // Try localStorage first, then fallback to mock data
      const localPayments = JSON.parse(localStorage.getItem('client_payments') || '[]');
      const clientPayments = localPayments.filter((p: ClientPayment) => p.client_id === clientId);
      return clientPayments.length > 0 ? clientPayments : this.getMockPayments(clientId);
    }
  }

  static async getClientInvoices(clientId: string): Promise<ClientInvoice[]> {
    try {
      const { data, error } = await supabase
        .from('client_invoices')
        .select('*')
        .eq('client_id', clientId)
        .order('issue_date', { ascending: false });

      if (error) {
        console.error('Error fetching client invoices:', error);
        // Try localStorage first, then fallback to mock data
        const localInvoices = JSON.parse(localStorage.getItem('client_invoices') || '[]');
        const clientInvoices = localInvoices.filter((i: ClientInvoice) => i.client_id === clientId);
        return clientInvoices.length > 0 ? clientInvoices : this.getMockInvoices(clientId);
      }

      return data || [];
    } catch (error) {
      console.error('Error in getClientInvoices:', error);
      // Try localStorage first, then fallback to mock data
      const localInvoices = JSON.parse(localStorage.getItem('client_invoices') || '[]');
      const clientInvoices = localInvoices.filter((i: ClientInvoice) => i.client_id === clientId);
      return clientInvoices.length > 0 ? clientInvoices : this.getMockInvoices(clientId);
    }
  }

  // Analytics
  static async getClientAnalytics(): Promise<ClientAnalytics> {
    try {
      // Get real data from clients, invoices, and payments
      const clients = await this.getClients();
      const allInvoices = JSON.parse(localStorage.getItem('client_invoices') || '[]');
      const allPayments = JSON.parse(localStorage.getItem('client_payments') || '[]');

      // Calculate real analytics
      const totalRevenue = allInvoices.reduce((sum: number, inv: any) => sum + inv.total_amount, 0);
      const totalPaid = allPayments.reduce((sum: number, pay: any) => sum + pay.amount, 0);
      const outstandingRevenue = totalRevenue - totalPaid;
      const overdueRevenue = allInvoices
        .filter((inv: any) => inv.status === 'Overdue')
        .reduce((sum: number, inv: any) => sum + inv.total_amount, 0);

      // Calculate average payment time
      const paidInvoices = allInvoices.filter((inv: any) => inv.paid_date);
      const averagePaymentTime = paidInvoices.length > 0
        ? Math.round(paidInvoices.reduce((sum: number, inv: any) => {
            const issueDate = new Date(inv.issue_date);
            const paidDate = new Date(inv.paid_date);
            const daysDiff = Math.floor((paidDate.getTime() - issueDate.getTime()) / (1000 * 60 * 60 * 24));
            return sum + daysDiff;
          }, 0) / paidInvoices.length)
        : 30;

      // Calculate top clients by revenue
      const clientRevenue = new Map();
      allInvoices.forEach((inv: any) => {
        const current = clientRevenue.get(inv.client_id) || 0;
        clientRevenue.set(inv.client_id, current + inv.total_amount);
      });

      const topClients = Array.from(clientRevenue.entries())
        .map(([clientId, revenue]) => {
          const client = clients.find(c => c.id === clientId);
          const projectCount = new Set(allInvoices.filter((inv: any) => inv.client_id === clientId).map((inv: any) => inv.project_id)).size;
          return {
            client_id: clientId,
            client_name: client?.name || 'Unknown Client',
            total_revenue: revenue as number,
            project_count: projectCount
          };
        })
        .sort((a, b) => b.total_revenue - a.total_revenue)
        .slice(0, 5);

      // Calculate payment trends (last 3 months)
      const paymentTrends = [];
      for (let i = 2; i >= 0; i--) {
        const date = new Date();
        date.setMonth(date.getMonth() - i);
        const monthKey = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;

        const monthPayments = allPayments.filter((pay: any) => pay.payment_date.startsWith(monthKey));
        const monthInvoices = allInvoices.filter((inv: any) => inv.issue_date.startsWith(monthKey));

        const paymentsReceived = monthPayments.reduce((sum: number, pay: any) => sum + pay.amount, 0);
        const invoicesSent = monthInvoices.reduce((sum: number, inv: any) => sum + inv.total_amount, 0);
        const outstanding = invoicesSent - paymentsReceived;

        paymentTrends.push({
          month: monthKey,
          payments_received: paymentsReceived,
          invoices_sent: invoicesSent,
          outstanding: Math.max(0, outstanding)
        });
      }

      // Calculate industry breakdown
      const industryMap = new Map();
      clients.forEach(client => {
        const industry = client.industry || 'Other';
        const current = industryMap.get(industry) || { client_count: 0, revenue: 0 };

        const clientRevenue = allInvoices
          .filter((inv: any) => inv.client_id === client.id)
          .reduce((sum: number, inv: any) => sum + inv.total_amount, 0);

        industryMap.set(industry, {
          client_count: current.client_count + 1,
          revenue: current.revenue + clientRevenue
        });
      });

      const industryBreakdown = Array.from(industryMap.entries())
        .map(([industry, data]) => ({
          industry,
          client_count: data.client_count,
          revenue: data.revenue
        }))
        .sort((a, b) => b.revenue - a.revenue);

      return {
        total_clients: clients.length,
        active_clients: clients.filter(c => c.status === 'Active').length,
        total_revenue: totalRevenue,
        outstanding_revenue: outstandingRevenue,
        overdue_revenue: overdueRevenue,
        average_payment_time: averagePaymentTime,
        top_clients: topClients,
        payment_trends: paymentTrends,
        industry_breakdown: industryBreakdown
      };
    } catch (error) {
      console.error('Error getting client analytics:', error);
      throw error;
    }
  }

  // Initialize financial data for all existing clients (removed - using Supabase only)
  static async initializeFinancialDataForExistingClients(): Promise<void> {
    console.log('Financial data initialization disabled - using Supabase only');
  }

  // Sample financial data creation removed - using Supabase only

  // Mock data removed - using Supabase only

  // Mock payments removed - using Supabase only

  // Mock invoices removed - using Supabase only
}
