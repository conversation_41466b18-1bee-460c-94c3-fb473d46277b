-- Minimal Tasks Table Setup for Supabase
-- This is the simplest version that should work without syntax errors
-- Copy and paste this entire script into your Supabase SQL Editor and run it

-- Drop existing table if you want to start fresh (OPTIONAL - uncomment if needed)
-- DROP TABLE IF EXISTS public.tasks;

-- Create the tasks table
CREATE TABLE IF NOT EXISTS public.tasks (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    name TEXT NOT NULL,
    description TEXT,
    status TEXT NOT NULL DEFAULT 'Not Started',
    priority TEXT NOT NULL DEFAULT 'Medium',
    start_date DATE,
    end_date DATE,
    progress INTEGER DEFAULT 0,
    assignee TEXT,
    project_id UUID NOT NULL,
    estimated_hours DECIMAL(10,2) DEFAULT 0,
    actual_hours DECIMAL(10,2) DEFAULT 0,
    dependencies JSONB DEFAULT '[]'::jsonb,
    tags JSONB DEFAULT '[]'::jsonb,
    parent_task_id UUID,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL
);

-- Create indexes
CREATE INDEX IF NOT EXISTS idx_tasks_project_id ON public.tasks(project_id);
CREATE INDEX IF NOT EXISTS idx_tasks_status ON public.tasks(status);
CREATE INDEX IF NOT EXISTS idx_tasks_created_at ON public.tasks(created_at);

-- Create update function
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = timezone('utc'::text, now());
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create trigger
DROP TRIGGER IF EXISTS update_tasks_updated_at ON public.tasks;
CREATE TRIGGER update_tasks_updated_at
    BEFORE UPDATE ON public.tasks
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Enable RLS
ALTER TABLE public.tasks ENABLE ROW LEVEL SECURITY;

-- Drop existing policy if it exists
DROP POLICY IF EXISTS "Allow all operations for authenticated users" ON public.tasks;

-- Create RLS policy (very permissive for now)
CREATE POLICY "Allow all operations for authenticated users" ON public.tasks
    FOR ALL USING (true);

-- Grant permissions
GRANT ALL ON public.tasks TO authenticated;
GRANT ALL ON public.tasks TO service_role;
GRANT ALL ON public.tasks TO anon;

-- Test the setup by inserting a sample task
INSERT INTO public.tasks (name, description, status, priority, project_id, progress, assignee, estimated_hours, actual_hours)
SELECT 
    'Setup Test Task',
    'This task verifies the tasks table is working correctly',
    'Completed',
    'Low',
    p.id,
    100,
    'System Setup',
    1.0,
    1.0
FROM public.projects p 
LIMIT 1;

-- If no projects exist, create one for testing
INSERT INTO public.projects (name, description, status, priority, start_date, end_date, budget, spent, progress, client_name, project_manager, location)
SELECT 
    'Test Project for Tasks',
    'Temporary project created for task testing',
    'Planning',
    'Medium',
    CURRENT_DATE,
    CURRENT_DATE + INTERVAL '30 days',
    10000,
    0,
    0,
    'Test Client',
    'System',
    'Test Location'
WHERE NOT EXISTS (SELECT 1 FROM public.projects);

-- Now insert the test task using the project we just ensured exists
INSERT INTO public.tasks (name, description, status, priority, project_id, progress, assignee, estimated_hours, actual_hours)
SELECT 
    'Setup Test Task',
    'This task verifies the tasks table is working correctly',
    'Completed',
    'Low',
    p.id,
    100,
    'System Setup',
    1.0,
    1.0
FROM public.projects p 
LIMIT 1
ON CONFLICT DO NOTHING;

-- Verify the setup
SELECT 
    'Setup completed successfully!' as message,
    COUNT(*) as total_tasks,
    COUNT(*) FILTER (WHERE name LIKE '%Setup Test Task%') as test_tasks
FROM public.tasks;
