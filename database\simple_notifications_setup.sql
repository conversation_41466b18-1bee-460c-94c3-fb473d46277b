-- Simple Notifications Setup
-- This creates a basic notifications table to fix network errors in MessagesCenter and NotificationCenter

-- 1. Create notifications table if it doesn't exist
CREATE TABLE IF NOT EXISTS public.notifications (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL,
    type VA<PERSON><PERSON><PERSON>(20) DEFAULT 'info',
    category VARCHAR(50) NOT NULL,
    title VARCHAR(255) NOT NULL,
    message TEXT NOT NULL,
    action_url VARCHAR(500),
    action_label VARCHAR(100),
    priority VARCHAR(20) DEFAULT 'medium',
    read BOOLEAN DEFAULT FALSE,
    related_entity_type VARCHAR(50),
    related_entity_id UUID,
    metadata JSONB DEFAULT '{}',
    expires_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_by <PERSON>UI<PERSON>
);

-- 2. Add foreign key constraints
DO $$
BEGIN
    -- Add foreign key to auth.users for user_id
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints 
        WHERE constraint_name = 'notifications_user_id_fkey'
        AND table_name = 'notifications'
    ) THEN
        ALTER TABLE public.notifications 
        ADD CONSTRAINT notifications_user_id_fkey 
        FOREIGN KEY (user_id) REFERENCES auth.users(id) ON DELETE CASCADE;
        RAISE NOTICE 'Added foreign key constraint: notifications_user_id_fkey';
    END IF;

    -- Add foreign key to auth.users for created_by
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints 
        WHERE constraint_name = 'notifications_created_by_fkey'
        AND table_name = 'notifications'
    ) THEN
        ALTER TABLE public.notifications 
        ADD CONSTRAINT notifications_created_by_fkey 
        FOREIGN KEY (created_by) REFERENCES auth.users(id) ON DELETE SET NULL;
        RAISE NOTICE 'Added foreign key constraint: notifications_created_by_fkey';
    END IF;
END $$;

-- 3. Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_notifications_user_id ON public.notifications(user_id);
CREATE INDEX IF NOT EXISTS idx_notifications_category ON public.notifications(category);
CREATE INDEX IF NOT EXISTS idx_notifications_read ON public.notifications(read);
CREATE INDEX IF NOT EXISTS idx_notifications_created_at ON public.notifications(created_at);
CREATE INDEX IF NOT EXISTS idx_notifications_expires_at ON public.notifications(expires_at);

-- 4. Set up Row Level Security
ALTER TABLE public.notifications ENABLE ROW LEVEL SECURITY;

-- Drop existing policies if they exist
DROP POLICY IF EXISTS "Users can view their own notifications" ON public.notifications;
DROP POLICY IF EXISTS "Users can update their own notifications" ON public.notifications;
DROP POLICY IF EXISTS "System can create notifications" ON public.notifications;

-- Create RLS policies
CREATE POLICY "Users can view their own notifications" ON public.notifications
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can update their own notifications" ON public.notifications
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "System can create notifications" ON public.notifications
    FOR INSERT WITH CHECK (true);

-- 5. Grant permissions
GRANT SELECT, UPDATE ON public.notifications TO authenticated;
GRANT INSERT ON public.notifications TO authenticated, anon;

-- 6. Create updated_at trigger
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS update_notifications_updated_at ON public.notifications;
CREATE TRIGGER update_notifications_updated_at
    BEFORE UPDATE ON public.notifications
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- 7. Create some sample notifications for testing
DO $$
DECLARE
    sample_user_id UUID;
BEGIN
    -- Get a sample user ID from auth.users
    SELECT id INTO sample_user_id FROM auth.users LIMIT 1;
    
    IF sample_user_id IS NOT NULL THEN
        -- Insert sample notifications
        INSERT INTO public.notifications (user_id, category, title, message, type, priority) VALUES
        (sample_user_id, 'message', 'Welcome to the System', 'Your account has been set up successfully.', 'success', 'medium'),
        (sample_user_id, 'system', 'System Update', 'The system has been updated with new features.', 'info', 'low'),
        (sample_user_id, 'project', 'Project Created', 'A new project has been assigned to you.', 'info', 'medium')
        ON CONFLICT DO NOTHING;
        
        RAISE NOTICE 'Created sample notifications for user: %', sample_user_id;
    ELSE
        RAISE NOTICE 'No users found in auth.users - sample notifications not created';
    END IF;
END $$;

-- 8. Create basic channels table for messages (if it doesn't exist)
CREATE TABLE IF NOT EXISTS public.channels (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(100) NOT NULL,
    description TEXT,
    type VARCHAR(20) DEFAULT 'public',
    created_by UUID,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Add foreign key for channels
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints 
        WHERE constraint_name = 'channels_created_by_fkey'
        AND table_name = 'channels'
    ) THEN
        ALTER TABLE public.channels 
        ADD CONSTRAINT channels_created_by_fkey 
        FOREIGN KEY (created_by) REFERENCES auth.users(id) ON DELETE SET NULL;
        RAISE NOTICE 'Added foreign key constraint: channels_created_by_fkey';
    END IF;
END $$;

-- Set up RLS for channels
ALTER TABLE public.channels ENABLE ROW LEVEL SECURITY;

DROP POLICY IF EXISTS "Everyone can view channels" ON public.channels;
CREATE POLICY "Everyone can view channels" ON public.channels FOR SELECT USING (true);

DROP POLICY IF EXISTS "Authenticated users can manage channels" ON public.channels;
CREATE POLICY "Authenticated users can manage channels" ON public.channels FOR ALL USING (auth.uid() IS NOT NULL);

GRANT SELECT, INSERT, UPDATE, DELETE ON public.channels TO authenticated;

-- Create a default general channel
INSERT INTO public.channels (name, description, type) VALUES
('General', 'General discussion channel', 'public')
ON CONFLICT DO NOTHING;

-- 9. Verification and status report
DO $$
DECLARE
    notification_count INTEGER;
    channel_count INTEGER;
BEGIN
    RAISE NOTICE '';
    RAISE NOTICE '=== SIMPLE NOTIFICATIONS SETUP COMPLETE ===';
    
    -- Check notifications table
    SELECT COUNT(*) INTO notification_count FROM public.notifications;
    RAISE NOTICE 'Notifications table created with % records', notification_count;
    
    -- Check channels table
    SELECT COUNT(*) INTO channel_count FROM public.channels;
    RAISE NOTICE 'Channels table created with % records', channel_count;
    
    RAISE NOTICE '';
    RAISE NOTICE 'Setup complete! The notification system should now work without network errors.';
    RAISE NOTICE 'You can now use:';
    RAISE NOTICE '- NotificationCenter component';
    RAISE NOTICE '- MessagesCenter component';
    RAISE NOTICE '- Message notifications';
    RAISE NOTICE '';
END $$;
