# 🔧 User Deletion Troubleshooting Guide

## ❌ **ISSUE: User Shows "Deleted" But Still Exists**

### **Symptoms:**
- Success message appears: "User has been permanently deleted"
- User still appears in the user list
- Browser console shows "User deletion completed successfully"
- Database still contains the user record

### **Root Causes:**
1. **Row Level Security (RLS)** blocking deletion
2. **Permission issues** preventing database writes
3. **Frontend caching** showing stale data
4. **Database function failures** with silent errors

## 🔧 **STEP-BY-STEP FIX**

### **Step 1: Run Database Debug Script**
```sql
-- Copy and paste debug_deletion.sql in Supabase SQL Editor
-- This will show exactly what's wrong
```

**Expected Output:**
```
=== CURRENT USER STATUS ===
Total users in database: [number]
[List of all users]

=== CHECKING PERMISSIONS ===
✓ Row Level Security is DISABLED on user_profiles

=== TESTING DELETION CAPABILITY ===
✓ Direct deletion WORKS

=== CHECKING FUNCTIONS ===
✓ simple_delete_user function exists
```

### **Step 2: Run Simple Delete Fix**
```sql
-- Copy and paste simple_delete_fix.sql in Supabase SQL Editor
-- This creates a guaranteed working delete function
```

### **Step 3: Test Deletion Immediately**
1. **Refresh browser page** (important!)
2. **Login as admin**
3. **Go to Settings → Users**
4. **Create a test user** (use fake <NAME_EMAIL>)
5. **Delete the test user**
6. **Check browser console** for detailed logs

### **Step 4: Verify in Database**
1. **Go to Supabase Dashboard**
2. **Navigate to Table Editor → user_profiles**
3. **Check if test user is gone**
4. **Count should decrease after deletion**

## 🔍 **DEBUGGING CHECKLIST**

### **✅ Database Issues**
- [ ] RLS is disabled on user_profiles table
- [ ] Authenticated users have DELETE permissions
- [ ] simple_delete_user function exists
- [ ] Direct DELETE queries work in SQL editor

### **✅ Frontend Issues**
- [ ] Browser page refreshed after SQL changes
- [ ] Console shows detailed deletion logs
- [ ] UserService.deleteUser completes without errors
- [ ] User list reloads after deletion

### **✅ Permission Issues**
- [ ] Logged in as admin user
- [ ] Admin role has proper permissions
- [ ] Not trying to delete own account
- [ ] User exists and is not already deleted

## 🧪 **MANUAL TESTING STEPS**

### **Test 1: Direct Database Deletion**
```sql
-- In Supabase SQL Editor, try direct deletion:
DELETE FROM public.user_profiles 
WHERE email = '<EMAIL>';

-- Check result:
SELECT COUNT(*) FROM public.user_profiles 
WHERE email = '<EMAIL>';
-- Should return 0 if deletion worked
```

### **Test 2: Function-Based Deletion**
```sql
-- Test the simple delete function:
SELECT public.simple_delete_user('[user-id-here]');
-- Should return true if deletion worked
```

### **Test 3: Frontend Deletion**
1. **Open browser console** (F12 → Console)
2. **Try deleting a user** through the UI
3. **Watch for these logs**:
   ```
   === STARTING USER DELETION ===
   User to delete: [id] [email]
   Current users count: [number]
   UserService.deleteUser completed successfully
   Users reloaded, new count: [lower number]
   User still exists in list: false
   === USER DELETION COMPLETED ===
   ```

## 🚨 **COMMON ISSUES & SOLUTIONS**

### **Issue 1: "User still exists in list: true"**
- **Cause**: Database deletion failed silently
- **Solution**: Run debug_deletion.sql to check permissions
- **Fix**: Disable RLS and grant permissions

### **Issue 2: "Permission denied" errors**
- **Cause**: RLS or insufficient permissions
- **Solution**: Run simple_delete_fix.sql
- **Fix**: Grants all permissions to authenticated users

### **Issue 3: "Function does not exist" errors**
- **Cause**: SQL scripts not run properly
- **Solution**: Re-run simple_delete_fix.sql
- **Fix**: Creates the simple_delete_user function

### **Issue 4: Frontend shows success but user remains**
- **Cause**: Frontend not refreshing data properly
- **Solution**: Check if loadUsers() is called after deletion
- **Fix**: Enhanced handleDeleteUser with verification

## 🔧 **GUARANTEED FIX PROCEDURE**

### **1. Nuclear Option - Complete Reset**
```sql
-- Run this if nothing else works:
ALTER TABLE public.user_profiles DISABLE ROW LEVEL SECURITY;
GRANT ALL ON public.user_profiles TO authenticated;
GRANT ALL ON public.user_profiles TO anon;

-- Test immediate deletion:
DELETE FROM public.user_profiles 
WHERE email LIKE '<EMAIL>';
```

### **2. Verify Fix Works**
```sql
-- Create test user:
INSERT INTO public.user_profiles (email, first_name, last_name, role_id)
VALUES ('<EMAIL>', 'Test', 'Delete', 
        (SELECT id FROM public.user_roles WHERE role_name = 'client' LIMIT 1));

-- Delete test user:
DELETE FROM public.user_profiles WHERE email = '<EMAIL>';

-- Verify deletion:
SELECT COUNT(*) FROM public.user_profiles WHERE email = '<EMAIL>';
-- Should return 0
```

### **3. Test Frontend**
1. **Refresh browser completely** (Ctrl+F5)
2. **Create new test user** through UI
3. **Delete test user** through UI
4. **Verify user disappears** from list

## 📋 **VERIFICATION STEPS**

### **After Running Fixes:**
1. **Database Level**:
   - [ ] Can delete users directly in SQL editor
   - [ ] simple_delete_user function works
   - [ ] RLS is disabled on user_profiles
   - [ ] Permissions granted to authenticated

2. **Frontend Level**:
   - [ ] Browser console shows detailed logs
   - [ ] User count decreases after deletion
   - [ ] "User still exists in list: false"
   - [ ] Success message appears

3. **End-to-End**:
   - [ ] Create test user → appears in list
   - [ ] Delete test user → disappears from list
   - [ ] Check database → user actually gone
   - [ ] No errors in console

## 🎯 **EXPECTED BEHAVIOR**

### **Successful Deletion Should Show:**
```
=== STARTING USER DELETION ===
User to delete: abc123 <EMAIL>
Current users count: 5
UserService: Attempting to delete user: abc123
UserService: Found user to delete: <EMAIL>
UserService: RPC delete result: true
UserService: Deletion verified - user no longer exists
UserService: User deletion completed successfully
Reloading users to verify deletion...
Users reloaded, new count: 4
User still exists in list: false
=== USER DELETION COMPLETED ===
```

### **In Database:**
- User record completely removed
- User count decreased by 1
- No trace of deleted user

### **In UI:**
- Success message appears
- User disappears from list immediately
- No errors in console

## 🚀 **QUICK FIX SUMMARY**

1. **Run**: `debug_deletion.sql` to identify issues
2. **Run**: `simple_delete_fix.sql` to fix permissions
3. **Refresh**: Browser page completely
4. **Test**: Create and delete a test user
5. **Verify**: User actually disappears from database

**If this doesn't work, there may be a deeper database configuration issue that requires Supabase support.**

## 📞 **Emergency Workaround**

If deletion still doesn't work, you can manually delete users:

1. **Go to Supabase Dashboard**
2. **Table Editor → user_profiles**
3. **Find the user row**
4. **Click delete button**
5. **Confirm deletion**

This bypasses all frontend code and deletes directly in the database.

**The enhanced deletion system should now work properly with detailed logging to show exactly what's happening!** 🎯
