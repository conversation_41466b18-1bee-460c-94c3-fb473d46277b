-- Clean Auth Fix - Focused solution for "Database error querying schema"
-- This script inspects and fixes auth configuration issues

-- 1. Inspect auth schema
DO $$
DECLARE
    table_info RECORD;
    column_info RECORD;
BEGIN
    RAISE NOTICE '';
    RAISE NOTICE '=== AUTH SCHEMA INSPECTION ===';
    
    -- List auth schema tables
    RAISE NOTICE 'Auth schema tables:';
    FOR table_info IN 
        SELECT table_name 
        FROM information_schema.tables 
        WHERE table_schema = 'auth'
        ORDER BY table_name
    LOOP
        RAISE NOTICE '  - %', table_info.table_name;
    END LOOP;
    
    -- Check auth.users columns
    RAISE NOTICE '';
    RAISE NOTICE 'auth.users columns:';
    FOR column_info IN 
        SELECT column_name, data_type, is_nullable
        FROM information_schema.columns 
        WHERE table_schema = 'auth' AND table_name = 'users'
        ORDER BY ordinal_position
    LOOP
        RAISE NOTICE '  - % (% - %)', column_info.column_name, column_info.data_type, 
            CASE WHEN column_info.is_nullable = 'YES' THEN 'nullable' ELSE 'not null' END;
    END LOOP;
    
    RAISE NOTICE '';
END $$;

-- 2. Fix <EMAIL> auth configuration
DO $$
DECLARE
    v_user_id UUID;
    v_password VARCHAR(255) := 'TempPass123!';
    v_current_user RECORD;
BEGIN
    RAISE NOTICE '=== FIXING <EMAIL> ===';
    
    -- Get current user state
    SELECT 
        id,
        email,
        encrypted_password IS NOT NULL as has_password,
        email_confirmed_at,
        aud,
        role,
        instance_id,
        created_at,
        updated_at
    INTO v_current_user
    FROM auth.users
    WHERE email = '<EMAIL>';
    
    IF v_current_user.id IS NULL THEN
        RAISE NOTICE '❌ User not found: <EMAIL>';
        RETURN;
    END IF;
    
    RAISE NOTICE 'Current user state:';
    RAISE NOTICE '  ID: %', v_current_user.id;
    RAISE NOTICE '  Has Password: %', v_current_user.has_password;
    RAISE NOTICE '  Email Confirmed: %', v_current_user.email_confirmed_at IS NOT NULL;
    RAISE NOTICE '  Aud: %', COALESCE(v_current_user.aud, 'NULL');
    RAISE NOTICE '  Role: %', COALESCE(v_current_user.role, 'NULL');
    RAISE NOTICE '  Instance ID: %', COALESCE(v_current_user.instance_id::text, 'NULL');
    
    -- Update user with all required fields
    UPDATE auth.users
    SET 
        encrypted_password = crypt(v_password, gen_salt('bf')),
        email_confirmed_at = COALESCE(email_confirmed_at, NOW()),
        aud = 'authenticated',
        role = 'authenticated',
        instance_id = COALESCE(instance_id, '00000000-0000-0000-0000-000000000000'::uuid),
        updated_at = NOW()
    WHERE id = v_current_user.id;
    
    RAISE NOTICE '✅ Updated user configuration';
    RAISE NOTICE '🔑 Password set to: %', v_password;
    
    RAISE NOTICE '';
END $$;

-- 3. Verify the fix
DO $$
DECLARE
    v_user RECORD;
    v_password VARCHAR(255) := 'TempPass123!';
    v_password_valid BOOLEAN;
BEGIN
    RAISE NOTICE '=== VERIFICATION ===';
    
    -- Get updated user state
    SELECT 
        id,
        email,
        encrypted_password,
        email_confirmed_at,
        aud,
        role,
        instance_id,
        updated_at
    INTO v_user
    FROM auth.users
    WHERE email = '<EMAIL>';
    
    IF v_user.id IS NULL THEN
        RAISE NOTICE '❌ User not found after update';
        RETURN;
    END IF;
    
    -- Test password verification
    IF v_user.encrypted_password IS NOT NULL THEN
        v_password_valid := (crypt(v_password, v_user.encrypted_password) = v_user.encrypted_password);
    ELSE
        v_password_valid := FALSE;
    END IF;
    
    RAISE NOTICE 'Updated user state:';
    RAISE NOTICE '  ✅ ID: %', v_user.id;
    RAISE NOTICE '  ✅ Email: %', v_user.email;
    RAISE NOTICE '  ✅ Has Password: %', v_user.encrypted_password IS NOT NULL;
    RAISE NOTICE '  ✅ Password Valid: %', v_password_valid;
    RAISE NOTICE '  ✅ Email Confirmed: %', v_user.email_confirmed_at IS NOT NULL;
    RAISE NOTICE '  ✅ Aud: %', v_user.aud;
    RAISE NOTICE '  ✅ Role: %', v_user.role;
    RAISE NOTICE '  ✅ Instance ID: %', v_user.instance_id;
    RAISE NOTICE '  ✅ Last Updated: %', v_user.updated_at;
    
    RAISE NOTICE '';
    RAISE NOTICE '🎯 LOGIN CREDENTIALS:';
    RAISE NOTICE '   Email: <EMAIL>';
    RAISE NOTICE '   Password: %', v_password;
    RAISE NOTICE '';
    
    IF v_password_valid AND v_user.aud = 'authenticated' AND v_user.role = 'authenticated' THEN
        RAISE NOTICE '✅ User should be able to login now!';
    ELSE
        RAISE NOTICE '❌ There may still be issues. Check Supabase Auth settings.';
    END IF;
    
    RAISE NOTICE '';
END $$;

-- 4. Show all auth users status
DO $$
DECLARE
    user_record RECORD;
    user_count INTEGER := 0;
BEGIN
    RAISE NOTICE '=== ALL AUTH USERS STATUS ===';
    
    FOR user_record IN 
        SELECT 
            email,
            encrypted_password IS NOT NULL as has_password,
            email_confirmed_at IS NOT NULL as email_confirmed,
            aud,
            role,
            created_at
        FROM auth.users
        ORDER BY email
    LOOP
        user_count := user_count + 1;
        RAISE NOTICE 'User %: % | Password: % | Confirmed: % | Aud: % | Role: %', 
            user_count,
            user_record.email,
            user_record.has_password,
            user_record.email_confirmed,
            COALESCE(user_record.aud, 'NULL'),
            COALESCE(user_record.role, 'NULL');
    END LOOP;
    
    RAISE NOTICE '';
    RAISE NOTICE 'Total auth users: %', user_count;
    RAISE NOTICE '';
    RAISE NOTICE '🎯 If "Database error querying schema" persists:';
    RAISE NOTICE '1. Check Supabase project Auth settings';
    RAISE NOTICE '2. Verify RLS policies are not blocking auth';
    RAISE NOTICE '3. Check if auth.users table has proper permissions';
    RAISE NOTICE '';
END $$;
