import React, { useState } from 'react';
import Layout from '@/components/layout/Layout';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { BookOpen, Plus, Edit, Trash2, DollarSign, Download, Printer, Search, Filter, AlertCircle, Wifi, WifiOff, RefreshCw, CheckCircle, XCircle } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { useToast } from '@/components/ui/use-toast';
import { useEmployees } from '@/hooks/useEmployees';
import { EmployeeService } from '@/lib/supabase';

const Register = () => {
  const { toast } = useToast();

  // Use the custom hook for employee management with Supabase
  const {
    employees,
    loading,
    error,
    addEmployee,
    updateEmployee,
    deleteEmployee,
    refreshEmployees,
    isOnline,
    syncStatus
  } = useEmployees();
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [editingEmployee, setEditingEmployee] = useState<any>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedPosition, setSelectedPosition] = useState('all');
  const [selectedMonth, setSelectedMonth] = useState('all');
  const [formData, setFormData] = useState({
    name: '',
    position: '',
    month: '',
    days: '',
    ratePerDay: '',
    credit: ''
  });
  const [formErrors, setFormErrors] = useState<Record<string, string>>({});

  const positions = [
    'Site Supervisor',
    'Project Manager',
    'Foreman',
    'Quality Controller',
    'Safety Officer',
    'Engineer',
    'Architect',
    'Mason',
    'Carpenter',
    'Electrician',
    'Plumber',
    'General Worker'
  ];

  const months = [
    'January 2024', 'February 2024', 'March 2024', 'April 2024',
    'May 2024', 'June 2024', 'July 2024', 'August 2024',
    'September 2024', 'October 2024', 'November 2024', 'December 2024'
  ];

  // Filter employees based on search and filters
  const filteredEmployees = employees.filter(employee => {
    const matchesSearch = employee.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         employee.position.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesPosition = selectedPosition === '' || selectedPosition === 'all' || employee.position === selectedPosition;
    const matchesMonth = selectedMonth === '' || selectedMonth === 'all' || employee.month === selectedMonth;

    return matchesSearch && matchesPosition && matchesMonth;
  });

  const grandTotal = filteredEmployees.reduce((sum, employee) => sum + Number(employee.total_salary), 0);

  // Form validation
  const validateForm = () => {
    const errors: Record<string, string> = {};
    
    if (!formData.name.trim()) errors.name = 'Name is required';
    if (!formData.position) errors.position = 'Position is required';
    if (!formData.month) errors.month = 'Month is required';
    if (!formData.days || parseInt(formData.days) <= 0) errors.days = 'Valid number of days is required';
    if (!formData.ratePerDay || parseFloat(formData.ratePerDay) <= 0) errors.ratePerDay = 'Valid rate per day is required';
    if (!formData.credit || parseFloat(formData.credit) < 0) errors.credit = 'Credit must be 0 or positive';
    
    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      toast({
        title: "Validation Error",
        description: "Please fix the errors in the form",
        variant: "destructive",
      });
      return;
    }

    try {
      const days = parseInt(formData.days);
      const ratePerDay = parseFloat(formData.ratePerDay);
      const credit = parseFloat(formData.credit);

      if (editingEmployee) {
        await updateEmployee(editingEmployee.id, {
          name: formData.name.trim(),
          position: formData.position,
          month: formData.month,
          days,
          rate_per_day: ratePerDay,
          credit
        });
        toast({
          title: "Success",
          description: "Employee updated successfully",
        });
      } else {
        await addEmployee({
          name: formData.name.trim(),
          position: formData.position,
          month: formData.month,
          days,
          rate_per_day: ratePerDay,
          credit
        });
        toast({
          title: "Success",
          description: "Employee added successfully",
        });
      }

      setFormData({
        name: '',
        position: '',
        month: '',
        days: '',
        ratePerDay: '',
        credit: ''
      });
      setFormErrors({});
      setEditingEmployee(null);
      setIsDialogOpen(false);
    } catch (error) {
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "An error occurred",
        variant: "destructive",
      });
    }
  };

  const handleEdit = (employee: any) => {
    setEditingEmployee(employee);
    setFormData({
      name: employee.name,
      position: employee.position,
      month: employee.month,
      days: employee.days.toString(),
      ratePerDay: employee.rate_per_day.toString(),
      credit: employee.credit.toString()
    });
    setIsDialogOpen(true);
  };

  const handleDelete = async (id: string) => {
    try {
      const employee = employees.find(emp => emp.id === id);
      await deleteEmployee(id);
      toast({
        title: "Success",
        description: `${employee?.name} removed from register`,
      });
    } catch (error) {
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to delete employee",
        variant: "destructive",
      });
    }
  };

  const resetForm = () => {
    setFormData({
      name: '',
      position: '',
      month: '',
      days: '',
      ratePerDay: '',
      credit: ''
    });
    setFormErrors({});
    setEditingEmployee(null);
  };

  // Export functionality
  const exportToCSV = async () => {
    try {
      const csvContent = await EmployeeService.exportToCSV();
      const blob = new Blob([csvContent], { type: 'text/csv' });
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `employee-register-${new Date().toISOString().split('T')[0]}.csv`;
      a.click();
      window.URL.revokeObjectURL(url);

      toast({
        title: "Success",
        description: "Employee register exported successfully",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to export data",
        variant: "destructive",
      });
    }
  };

  // Print functionality
  const handlePrint = () => {
    window.print();
  };

  // Clear all filters
  const clearFilters = () => {
    setSearchTerm('');
    setSelectedPosition('all');
    setSelectedMonth('all');
  };

  // Get sync status icon
  const getSyncStatusIcon = () => {
    switch (syncStatus) {
      case 'syncing':
        return <RefreshCw className="w-4 h-4 animate-spin text-blue-500" />;
      case 'synced':
        return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'offline':
        return <WifiOff className="w-4 h-4 text-orange-500" />;
      case 'error':
        return <XCircle className="w-4 h-4 text-red-500" />;
      default:
        return <Wifi className="w-4 h-4 text-gray-500" />;
    }
  };

  return (
    <Layout>
      <div className="space-y-6">
        {/* Connection Status Alert */}
        {error && (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              {error} - Using offline mode. Changes will sync when connection is restored.
            </AlertDescription>
          </Alert>
        )}

        {/* Loading State */}
        {loading && (
          <Alert>
            <RefreshCw className="h-4 w-4 animate-spin" />
            <AlertDescription>
              Loading employee data...
            </AlertDescription>
          </Alert>
        )}

        {/* Empty State Alert */}
        {!loading && employees.length === 0 && (
          <Alert>
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              Welcome to the Employee Salary Register! Start by adding your first employee to begin managing payroll.
            </AlertDescription>
          </Alert>
        )}

        {/* Header Section */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="w-12 h-12 bg-gradient-to-r from-purple-600 to-blue-600 rounded-xl flex items-center justify-center">
              <BookOpen className="w-6 h-6 text-white" />
            </div>
            <div>
              <h1 className="text-3xl font-bold text-gray-900 font-heading">Employee Salary Register</h1>
              <div className="flex items-center space-x-2">
                <p className="text-gray-600">Manage employee salaries and payroll</p>
                <div className="flex items-center space-x-1">
                  {getSyncStatusIcon()}
                  <span className="text-xs text-gray-500">
                    {syncStatus === 'offline' ? 'Offline' :
                     syncStatus === 'syncing' ? 'Syncing...' :
                     syncStatus === 'error' ? 'Error' : 'Online'}
                  </span>
                </div>
              </div>
            </div>
          </div>
          <div className="flex items-center space-x-2">
            <Button onClick={refreshEmployees} variant="outline" size="sm">
              <RefreshCw className="w-4 h-4 mr-2" />
              Refresh
            </Button>
            <Button onClick={exportToCSV} variant="outline" className="hidden sm:flex">
              <Download className="w-4 h-4 mr-2" />
              Export CSV
            </Button>
            <Button onClick={handlePrint} variant="outline" className="hidden sm:flex">
              <Printer className="w-4 h-4 mr-2" />
              Print
            </Button>
            <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
              <DialogTrigger asChild>
                <Button onClick={resetForm} className="bg-purple-600 hover:bg-purple-700">
                  <Plus className="w-4 h-4 mr-2" />
                  Add Employee
                </Button>
              </DialogTrigger>
              <DialogContent className="sm:max-w-[425px]">
                <DialogHeader>
                  <DialogTitle>
                    {editingEmployee ? 'Edit Employee' : 'Add New Employee'}
                  </DialogTitle>
                  <DialogDescription>
                    {editingEmployee ? 'Update employee salary information.' : 'Enter employee details for salary calculation.'}
                  </DialogDescription>
                </DialogHeader>
                <form onSubmit={handleSubmit}>
                  <div className="grid gap-4 py-4">
                    <div className="grid grid-cols-4 items-center gap-4">
                      <Label htmlFor="name" className="text-right">
                        Name
                      </Label>
                      <Input
                        id="name"
                        value={formData.name}
                        onChange={(e) => setFormData({...formData, name: e.target.value})}
                        className="col-span-3"
                        required
                      />
                      {formErrors.name && (
                        <span className="col-span-4 text-sm text-red-500">{formErrors.name}</span>
                      )}
                    </div>
                    <div className="grid grid-cols-4 items-center gap-4">
                      <Label htmlFor="position" className="text-right">
                        Position
                      </Label>
                      <Select value={formData.position} onValueChange={(value) => setFormData({...formData, position: value})}>
                        <SelectTrigger className="col-span-3">
                          <SelectValue placeholder="Select position" />
                        </SelectTrigger>
                        <SelectContent>
                          {positions.map((position) => (
                            <SelectItem key={position} value={position}>
                              {position}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      {formErrors.position && (
                        <span className="col-span-4 text-sm text-red-500">{formErrors.position}</span>
                      )}
                    </div>
                    <div className="grid grid-cols-4 items-center gap-4">
                      <Label htmlFor="month" className="text-right">
                        Month
                      </Label>
                      <Select value={formData.month} onValueChange={(value) => setFormData({...formData, month: value})}>
                        <SelectTrigger className="col-span-3">
                          <SelectValue placeholder="Select month" />
                        </SelectTrigger>
                        <SelectContent>
                          {months.map((month) => (
                            <SelectItem key={month} value={month}>
                              {month}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      {formErrors.month && (
                        <span className="col-span-4 text-sm text-red-500">{formErrors.month}</span>
                      )}
                    </div>
                    <div className="grid grid-cols-4 items-center gap-4">
                      <Label htmlFor="days" className="text-right">
                        Days
                      </Label>
                      <Input
                        id="days"
                        type="number"
                        value={formData.days}
                        onChange={(e) => setFormData({...formData, days: e.target.value})}
                        className="col-span-3"
                        required
                        min="1"
                        max="31"
                      />
                      {formErrors.days && (
                        <span className="col-span-4 text-sm text-red-500">{formErrors.days}</span>
                      )}
                    </div>
                    <div className="grid grid-cols-4 items-center gap-4">
                      <Label htmlFor="ratePerDay" className="text-right">
                        Rate/Day ($)
                      </Label>
                      <Input
                        id="ratePerDay"
                        type="number"
                        step="0.01"
                        value={formData.ratePerDay}
                        onChange={(e) => setFormData({...formData, ratePerDay: e.target.value})}
                        className="col-span-3"
                        required
                        min="0"
                      />
                      {formErrors.ratePerDay && (
                        <span className="col-span-4 text-sm text-red-500">{formErrors.ratePerDay}</span>
                      )}
                    </div>
                    <div className="grid grid-cols-4 items-center gap-4">
                      <Label htmlFor="credit" className="text-right">
                        Credit ($)
                      </Label>
                      <Input
                        id="credit"
                        type="number"
                        step="0.01"
                        value={formData.credit}
                        onChange={(e) => setFormData({...formData, credit: e.target.value})}
                        className="col-span-3"
                        required
                        min="0"
                      />
                      {formErrors.credit && (
                        <span className="col-span-4 text-sm text-red-500">{formErrors.credit}</span>
                      )}
                    </div>
                  </div>
                  <DialogFooter>
                    <Button type="submit">
                      {editingEmployee ? 'Update Employee' : 'Add Employee'}
                    </Button>
                  </DialogFooter>
                </form>
              </DialogContent>
            </Dialog>
          </div>
        </div>

        {/* Search and Filter Section */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Search className="w-5 h-5" />
              <span>Search & Filter</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <Input
                  placeholder="Search by name or position..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
              <Select value={selectedPosition} onValueChange={setSelectedPosition}>
                <SelectTrigger>
                  <SelectValue placeholder="Filter by position" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Positions</SelectItem>
                  {positions.map((position) => (
                    <SelectItem key={position} value={position}>
                      {position}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <Select value={selectedMonth} onValueChange={setSelectedMonth}>
                <SelectTrigger>
                  <SelectValue placeholder="Filter by month" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Months</SelectItem>
                  {months.map((month) => (
                    <SelectItem key={month} value={month}>
                      {month}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <Button onClick={clearFilters} variant="outline" className="w-full">
                <Filter className="w-4 h-4 mr-2" />
                Clear Filters
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Summary Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Employees</CardTitle>
              <BookOpen className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{employees.length}</div>
              <p className="text-xs text-muted-foreground">
                Active employees in register
              </p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Filtered Results</CardTitle>
              <Filter className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{filteredEmployees.length}</div>
              <p className="text-xs text-muted-foreground">
                Employees matching filters
              </p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Grand Total</CardTitle>
              <DollarSign className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">${grandTotal.toFixed(2)}</div>
              <p className="text-xs text-muted-foreground">
                Total salary (filtered)
              </p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Average Salary</CardTitle>
              <DollarSign className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                ${filteredEmployees.length > 0 ? (grandTotal / filteredEmployees.length).toFixed(2) : '0.00'}
              </div>
              <p className="text-xs text-muted-foreground">
                Average salary (filtered)
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Employee Table */}
        <Card>
          <CardHeader>
            <CardTitle>Employee Salary Register</CardTitle>
            <CardDescription>
              Complete list of employees with salary calculations
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Name</TableHead>
                    <TableHead>Position</TableHead>
                    <TableHead>Month</TableHead>
                    <TableHead className="text-center">Days</TableHead>
                    <TableHead className="text-right">Rate/Day</TableHead>
                    <TableHead className="text-right">Credit</TableHead>
                    <TableHead className="text-right">Total Salary</TableHead>
                    <TableHead className="text-center">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredEmployees.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={8} className="text-center py-8 text-gray-500">
                        {employees.length === 0
                          ? "No employees added yet. Click \"Add Employee\" to get started."
                          : "No employees match the current filters. Try adjusting your search criteria."
                        }
                      </TableCell>
                    </TableRow>
                  ) : (
                    filteredEmployees.map((employee) => (
                      <TableRow key={employee.id}>
                        <TableCell className="font-medium">{employee.name}</TableCell>
                        <TableCell>
                          <Badge variant="secondary">{employee.position}</Badge>
                        </TableCell>
                        <TableCell>{employee.month}</TableCell>
                        <TableCell className="text-center">{employee.days}</TableCell>
                        <TableCell className="text-right">${Number(employee.rate_per_day).toFixed(2)}</TableCell>
                        <TableCell className="text-right">${Number(employee.credit).toFixed(2)}</TableCell>
                        <TableCell className="text-right font-semibold">
                          ${Number(employee.total_salary).toFixed(2)}
                        </TableCell>
                        <TableCell className="text-center">
                          <div className="flex justify-center space-x-2">
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => handleEdit(employee)}
                            >
                              <Edit className="w-4 h-4" />
                            </Button>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => handleDelete(employee.id)}
                              className="text-red-600 hover:text-red-700"
                            >
                              <Trash2 className="w-4 h-4" />
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>
            </div>

            {/* Grand Total Row */}
            {filteredEmployees.length > 0 && (
              <div className="mt-4 p-4 bg-gray-50 rounded-lg">
                <div className="flex justify-between items-center">
                  <div className="text-lg font-semibold text-gray-900">
                    Grand Total ({filteredEmployees.length} employees{filteredEmployees.length !== employees.length ? ` of ${employees.length}` : ''})
                  </div>
                  <div className="text-2xl font-bold text-green-600">
                    ${grandTotal.toFixed(2)}
                  </div>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </Layout>
  );
};

export default Register;
