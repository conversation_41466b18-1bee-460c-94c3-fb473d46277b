-- Create function to properly check if auth user exists
-- This is needed because auth.users is not accessible via REST API

CREATE OR REPLACE FUNCTION public.check_auth_user_exists(p_email VARCHAR(255))
RETURNS jsonb AS $$
DECLARE
    v_auth_user RECORD;
    v_exists BOOLEAN := FALSE;
BEGIN
    -- Try to find the auth user
    SELECT id, email, email_confirmed_at, created_at, updated_at
    INTO v_auth_user
    FROM auth.users
    WHERE email = p_email;
    
    IF v_auth_user.id IS NOT NULL THEN
        v_exists := TRUE;
    END IF;
    
    RETURN jsonb_build_object(
        'exists', v_exists,
        'email_confirmed', v_auth_user.email_confirmed_at IS NOT NULL,
        'user_data', CASE 
            WHEN v_exists THEN jsonb_build_object(
                'id', v_auth_user.id,
                'email', v_auth_user.email,
                'email_confirmed_at', v_auth_user.email_confirmed_at,
                'created_at', v_auth_user.created_at,
                'updated_at', v_auth_user.updated_at
            )
            ELSE NULL
        END
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant permission
GRANT EXECUTE ON FUNCTION public.check_auth_user_exists(VARCHAR) TO authenticated;

-- Test the function
DO $$
DECLARE
    test_result jsonb;
BEGIN
    RAISE NOTICE '';
    RAISE NOTICE '=== TESTING AUTH CHECK FUNCTION ===';
    
    -- Test with a known email
    SELECT public.check_auth_user_exists('<EMAIL>') INTO test_result;
    
    RAISE NOTICE 'Test <NAME_EMAIL>:';
    RAISE NOTICE 'Exists: %', test_result->>'exists';
    RAISE NOTICE 'Email confirmed: %', test_result->>'email_confirmed';
    RAISE NOTICE 'User data: %', test_result->>'user_data';
    RAISE NOTICE '';
    RAISE NOTICE '✅ Auth check function is ready!';
    RAISE NOTICE '';
END $$;
