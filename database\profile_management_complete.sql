-- Complete Profile Management System with Picture Upload Support
-- Run this in your Supabase SQL Editor

-- 1. Create storage bucket for profile pictures
INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
VALUES (
  'profile-pictures',
  'profile-pictures',
  true,
  5242880, -- 5MB limit
  ARRAY['image/jpeg', 'image/png', 'image/webp', 'image/gif']
) ON CONFLICT (id) DO UPDATE SET
  public = true,
  file_size_limit = 5242880,
  allowed_mime_types = ARRAY['image/jpeg', 'image/png', 'image/webp', 'image/gif'];

-- 2. Create storage policies for profile pictures
CREATE POLICY "Users can view all profile pictures" ON storage.objects
FOR SELECT USING (bucket_id = 'profile-pictures');

CREATE POLICY "Users can upload their own profile picture" ON storage.objects
FOR INSERT WITH CHECK (
  bucket_id = 'profile-pictures' 
  AND auth.uid()::text = (storage.foldername(name))[1]
);

CREATE POLICY "Users can update their own profile picture" ON storage.objects
FOR UPDATE USING (
  bucket_id = 'profile-pictures' 
  AND auth.uid()::text = (storage.foldername(name))[1]
);

CREATE POLICY "Users can delete their own profile picture" ON storage.objects
FOR DELETE USING (
  bucket_id = 'profile-pictures' 
  AND auth.uid()::text = (storage.foldername(name))[1]
);

-- 3. Extend user_profiles table with additional profile fields
ALTER TABLE public.user_profiles 
ADD COLUMN IF NOT EXISTS profile_picture_url TEXT,
ADD COLUMN IF NOT EXISTS bio TEXT,
ADD COLUMN IF NOT EXISTS date_of_birth DATE,
ADD COLUMN IF NOT EXISTS location TEXT,
ADD COLUMN IF NOT EXISTS emergency_contact_name TEXT,
ADD COLUMN IF NOT EXISTS emergency_contact_phone TEXT,
ADD COLUMN IF NOT EXISTS emergency_contact_relationship TEXT,
ADD COLUMN IF NOT EXISTS hire_date DATE,
ADD COLUMN IF NOT EXISTS employee_id TEXT UNIQUE,
ADD COLUMN IF NOT EXISTS salary DECIMAL(10,2),
ADD COLUMN IF NOT EXISTS hourly_rate DECIMAL(8,2),
ADD COLUMN IF NOT EXISTS social_security_number TEXT,
ADD COLUMN IF NOT EXISTS bank_account_number TEXT,
ADD COLUMN IF NOT EXISTS bank_routing_number TEXT,
ADD COLUMN IF NOT EXISTS tax_id TEXT,
ADD COLUMN IF NOT EXISTS skills TEXT[],
ADD COLUMN IF NOT EXISTS certifications JSONB DEFAULT '[]'::jsonb,
ADD COLUMN IF NOT EXISTS preferences JSONB DEFAULT '{}'::jsonb,
ADD COLUMN IF NOT EXISTS last_login_at TIMESTAMP WITH TIME ZONE,
ADD COLUMN IF NOT EXISTS is_active BOOLEAN DEFAULT true,
ADD COLUMN IF NOT EXISTS notes TEXT;

-- 4. Create profile activity log table
CREATE TABLE IF NOT EXISTS public.profile_activity_log (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  action TEXT NOT NULL,
  field_changed TEXT,
  old_value TEXT,
  new_value TEXT,
  changed_by UUID REFERENCES auth.users(id),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 5. Create function to handle profile picture upload
CREATE OR REPLACE FUNCTION handle_profile_picture_upload(
  user_id UUID,
  file_path TEXT
)
RETURNS TEXT AS $$
DECLARE
  old_picture_url TEXT;
  new_picture_url TEXT;
BEGIN
  -- Get the old picture URL
  SELECT profile_picture_url INTO old_picture_url
  FROM public.user_profiles
  WHERE id = user_id;
  
  -- Generate the new picture URL
  new_picture_url := 'https://ygdaucsngasdutbvmevs.supabase.co/storage/v1/object/public/profile-pictures/' || file_path;
  
  -- Update the user profile with new picture URL
  UPDATE public.user_profiles
  SET 
    profile_picture_url = new_picture_url,
    updated_at = NOW()
  WHERE id = user_id;
  
  -- Log the activity
  INSERT INTO public.profile_activity_log (
    user_id,
    action,
    field_changed,
    old_value,
    new_value,
    changed_by
  ) VALUES (
    user_id,
    'profile_picture_updated',
    'profile_picture_url',
    old_picture_url,
    new_picture_url,
    user_id
  );
  
  RETURN new_picture_url;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 6. Create function to update profile information
CREATE OR REPLACE FUNCTION update_user_profile(
  p_user_id UUID,
  p_profile_data JSONB
)
RETURNS UUID AS $$
DECLARE
  v_field TEXT;
  v_old_value TEXT;
  v_new_value TEXT;
BEGIN
  -- Update the profile
  UPDATE public.user_profiles
  SET
    first_name = COALESCE(p_profile_data->>'first_name', first_name),
    last_name = COALESCE(p_profile_data->>'last_name', last_name),
    phone = COALESCE(p_profile_data->>'phone', phone),
    department = COALESCE(p_profile_data->>'department', department),
    job_title = COALESCE(p_profile_data->>'job_title', job_title),
    bio = COALESCE(p_profile_data->>'bio', bio),
    location = COALESCE(p_profile_data->>'location', location),
    date_of_birth = COALESCE((p_profile_data->>'date_of_birth')::date, date_of_birth),
    emergency_contact_name = COALESCE(p_profile_data->>'emergency_contact_name', emergency_contact_name),
    emergency_contact_phone = COALESCE(p_profile_data->>'emergency_contact_phone', emergency_contact_phone),
    emergency_contact_relationship = COALESCE(p_profile_data->>'emergency_contact_relationship', emergency_contact_relationship),
    skills = COALESCE(
      CASE 
        WHEN p_profile_data ? 'skills' THEN 
          ARRAY(SELECT jsonb_array_elements_text(p_profile_data->'skills'))
        ELSE skills
      END, 
      skills
    ),
    preferences = COALESCE(
      CASE 
        WHEN p_profile_data ? 'preferences' THEN 
          preferences || (p_profile_data->'preferences')
        ELSE preferences
      END,
      preferences
    ),
    updated_at = NOW()
  WHERE id = p_user_id;
  
  -- Log the activity for each changed field
  FOR v_field IN SELECT jsonb_object_keys(p_profile_data)
  LOOP
    v_new_value := p_profile_data ->> v_field;
    
    INSERT INTO public.profile_activity_log (
      user_id,
      action,
      field_changed,
      new_value,
      changed_by
    ) VALUES (
      p_user_id,
      'profile_updated',
      v_field,
      v_new_value,
      p_user_id
    );
  END LOOP;
  
  RETURN p_user_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 7. Create function to get complete profile with activity
CREATE OR REPLACE FUNCTION get_user_profile_complete(p_user_id UUID)
RETURNS JSONB AS $$
DECLARE
  v_profile JSONB;
  v_recent_activity JSONB;
BEGIN
  -- Get the profile data
  SELECT to_jsonb(up.*) INTO v_profile
  FROM public.user_profiles up
  WHERE up.id = p_user_id;
  
  -- Get recent activity (last 10 entries)
  SELECT jsonb_agg(
    jsonb_build_object(
      'action', action,
      'field_changed', field_changed,
      'created_at', created_at
    ) ORDER BY created_at DESC
  ) INTO v_recent_activity
  FROM (
    SELECT action, field_changed, created_at
    FROM public.profile_activity_log
    WHERE user_id = p_user_id
    ORDER BY created_at DESC
    LIMIT 10
  ) recent;
  
  -- Combine profile and activity
  RETURN jsonb_build_object(
    'profile', v_profile,
    'recent_activity', COALESCE(v_recent_activity, '[]'::jsonb)
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 8. Grant permissions
GRANT ALL ON public.profile_activity_log TO authenticated;
GRANT EXECUTE ON FUNCTION handle_profile_picture_upload(UUID, TEXT) TO authenticated;
GRANT EXECUTE ON FUNCTION update_user_profile(UUID, JSONB) TO authenticated;
GRANT EXECUTE ON FUNCTION get_user_profile_complete(UUID) TO authenticated;

-- 9. Enable RLS on new table
ALTER TABLE public.profile_activity_log ENABLE ROW LEVEL SECURITY;

-- 10. Create RLS policies for activity log
CREATE POLICY "Users can view their own activity log" ON public.profile_activity_log
FOR SELECT USING (user_id = auth.uid());

CREATE POLICY "System can insert activity log" ON public.profile_activity_log
FOR INSERT WITH CHECK (true);

-- 11. Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_profile_activity_log_user_id ON public.profile_activity_log(user_id);
CREATE INDEX IF NOT EXISTS idx_profile_activity_log_created_at ON public.profile_activity_log(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_user_profiles_employee_id ON public.user_profiles(employee_id);

-- 12. Create view for profile summary
CREATE OR REPLACE VIEW public.profile_summary AS
SELECT 
  up.id,
  up.first_name,
  up.last_name,
  up.first_name || ' ' || up.last_name AS full_name,
  up.email,
  up.phone,
  up.department,
  up.job_title,
  up.profile_picture_url,
  up.is_active,
  up.hire_date,
  up.last_login_at,
  r.role_name,
  COUNT(pal.id) as activity_count
FROM public.user_profiles up
LEFT JOIN public.roles r ON up.role_id = r.id
LEFT JOIN public.profile_activity_log pal ON up.id = pal.user_id
GROUP BY up.id, r.role_name;

GRANT SELECT ON public.profile_summary TO authenticated;

-- Success message
DO $$
BEGIN
  RAISE NOTICE 'Profile management system setup complete!';
  RAISE NOTICE 'Features enabled:';
  RAISE NOTICE '- Profile picture upload with 5MB limit';
  RAISE NOTICE '- Extended profile fields (bio, emergency contacts, etc.)';
  RAISE NOTICE '- Activity logging for profile changes';
  RAISE NOTICE '- Secure file storage with proper permissions';
  RAISE NOTICE '- Performance optimized with indexes';
END $$;
