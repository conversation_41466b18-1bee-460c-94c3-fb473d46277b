# Profile Management Setup Guide

## Overview
This guide will help you set up the complete profile management system with picture upload functionality for your construction management application.

## Features Included
- ✅ Profile picture upload with 5MB limit
- ✅ Extended profile fields (bio, emergency contacts, etc.)
- ✅ Activity logging for profile changes
- ✅ Secure file storage with proper permissions
- ✅ Performance optimized with indexes
- ✅ Emergency contact information
- ✅ Automatic profile picture URL management

## Step 1: Run the SQL Setup

1. Open your Supabase dashboard
2. Go to the SQL Editor
3. Copy and paste the contents of `database/profile_management_complete.sql`
4. Click "Run" to execute the script

This will:
- Create the `profile-pictures` storage bucket
- Set up storage policies for secure access
- Add new columns to the `user_profiles` table
- Create activity logging functionality
- Set up database functions for safe operations

## Step 2: Verify Storage Setup

After running the SQL script, verify the storage bucket:

1. Go to Storage in your Supabase dashboard
2. You should see a `profile-pictures` bucket
3. The bucket should be set to "Public" with a 5MB file size limit
4. Allowed MIME types: image/jpeg, image/png, image/webp, image/gif

## Step 3: Test the Functionality

The ProfileSettings component now includes:

### Profile Picture Upload
- Click the camera icon when editing your profile
- Select an image file (JPEG, PNG, WebP, or GIF)
- Maximum file size: 5MB
- Images are automatically resized and optimized

### Extended Profile Fields
- **Personal Information**: Name, email, phone, department, job title
- **Additional Details**: Bio, location, date of birth
- **Emergency Contact**: Name, phone, relationship

### Activity Logging
- All profile changes are automatically logged
- Includes timestamps and field-level tracking
- Accessible through the database for audit purposes

## Step 4: Database Schema Details

### New Columns Added to `user_profiles`:
```sql
profile_picture_url TEXT
bio TEXT
date_of_birth DATE
location TEXT
emergency_contact_name TEXT
emergency_contact_phone TEXT
emergency_contact_relationship TEXT
hire_date DATE
employee_id TEXT UNIQUE
salary DECIMAL(10,2)
hourly_rate DECIMAL(8,2)
social_security_number TEXT
bank_account_number TEXT
bank_routing_number TEXT
tax_id TEXT
skills TEXT[]
certifications JSONB
preferences JSONB
last_login_at TIMESTAMP WITH TIME ZONE
is_active BOOLEAN DEFAULT true
notes TEXT
```

### New Tables Created:
- `profile_activity_log`: Tracks all profile changes
- `profile_summary` (view): Optimized profile overview

### New Functions Created:
- `handle_profile_picture_upload()`: Manages picture uploads
- `update_user_profile()`: Safe profile updates with logging
- `get_user_profile_complete()`: Retrieves profile with activity

## Step 5: Security Features

### Storage Security:
- Users can only upload to their own folder (`user_id/filename`)
- Public read access for profile pictures
- Automatic cleanup of old profile pictures
- File type and size validation

### Database Security:
- Row Level Security (RLS) enabled
- Users can only access their own data
- Activity logging for audit trails
- Secure functions with proper permissions

## Step 6: Usage Examples

### Upload Profile Picture:
```typescript
const handlePictureUpload = async (file: File) => {
  const fileName = `${user.id}/profile-${Date.now()}.${fileExt}`;
  
  // Upload to storage
  const { data } = await supabase.storage
    .from('profile-pictures')
    .upload(fileName, file);
    
  // Update profile URL
  await supabase.rpc('handle_profile_picture_upload', {
    user_id: user.id,
    file_path: fileName
  });
};
```

### Update Profile Information:
```typescript
const updateProfile = async (profileData: ProfileData) => {
  await supabase.rpc('update_user_profile', {
    p_user_id: user.id,
    p_profile_data: profileData
  });
};
```

### Get Complete Profile:
```typescript
const getProfile = async () => {
  const { data } = await supabase.rpc('get_user_profile_complete', {
    p_user_id: user.id
  });
  return data;
};
```

## Step 7: Testing

Use the `ProfileUploadTest` component to verify everything is working:

1. Import and add to your Settings page
2. Run the built-in tests
3. Check the test log for any issues
4. Verify file uploads work correctly

## Troubleshooting

### Common Issues:

1. **Storage bucket not found**
   - Ensure the SQL script ran successfully
   - Check the Storage section in Supabase dashboard

2. **Permission denied errors**
   - Verify RLS policies are set correctly
   - Check user authentication status

3. **Function not found errors**
   - Re-run the SQL script
   - Check the Functions section in Supabase dashboard

4. **File upload fails**
   - Check file size (max 5MB)
   - Verify file type is supported
   - Check browser console for errors

### Support:
- Check Supabase logs for detailed error messages
- Use the ProfileUploadTest component for diagnostics
- Verify all SQL functions were created successfully

## Next Steps

After setup is complete, you can:
- Customize the profile fields as needed
- Add additional validation rules
- Implement profile picture cropping/resizing
- Add bulk profile management for admins
- Integrate with employee management systems

The system is now ready for production use with full profile management capabilities!
