-- Quick verification script to check your current setup
-- Run this to see what's missing

-- Check if tasks table exists
SELECT 
    CASE 
        WHEN EXISTS (SELECT FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'tasks')
        THEN '✅ Tasks table exists'
        ELSE '❌ Tasks table missing - run simple_tasks_setup.sql'
    END as table_status;

-- Check if projects table exists (needed for foreign key)
SELECT 
    CASE 
        WHEN EXISTS (SELECT FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'projects')
        THEN '✅ Projects table exists'
        ELSE '❌ Projects table missing - this is required for tasks'
    END as projects_status;

-- If tasks table exists, check its structure
SELECT 
    column_name,
    data_type,
    is_nullable
FROM information_schema.columns 
WHERE table_schema = 'public' 
AND table_name = 'tasks'
ORDER BY ordinal_position;

-- Check RLS policies
SELECT 
    tablename,
    policyname,
    permissive,
    roles,
    cmd
FROM pg_policies 
WHERE tablename = 'tasks';

-- Check if RLS is enabled
SELECT 
    tablename,
    rowsecurity as rls_enabled
FROM pg_tables 
WHERE tablename = 'tasks' 
AND schemaname = 'public';

-- Try a simple count query
SELECT 
    CASE 
        WHEN EXISTS (SELECT FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'tasks')
        THEN (SELECT COUNT(*)::text || ' tasks in table' FROM public.tasks)
        ELSE 'Tasks table does not exist'
    END as task_count;

-- Check permissions
SELECT 
    grantee,
    privilege_type
FROM information_schema.role_table_grants 
WHERE table_schema = 'public' 
AND table_name = 'tasks';

-- Final status
SELECT 
    CASE 
        WHEN EXISTS (SELECT FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'tasks')
        AND EXISTS (SELECT FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'projects')
        AND EXISTS (SELECT FROM pg_policies WHERE tablename = 'tasks')
        THEN '✅ Setup appears complete - try creating a task now'
        ELSE '⚠️ Setup incomplete - run simple_tasks_setup.sql'
    END as final_status;
