-- Time Tracking System Database Schema
-- Run this script in your Supabase SQL Editor to create time tracking tables and functions

-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create sites table for work locations
CREATE TABLE IF NOT EXISTS public.sites (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name VA<PERSON>HAR(255) NOT NULL,
  site_code VARCHAR(50) UNIQUE NOT NULL,
  address TEXT,
  description TEXT,
  project_id UUID REFERENCES public.projects(id),
  manager_id UUID REFERENCES auth.users(id),
  is_active BOOLEAN DEFAULT TRUE,
  location_lat DECIMAL(10, 8),
  location_lng DECIMAL(11, 8),
  geofence_radius INTEGER DEFAULT 100, -- meters
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create time_entries table for clock in/out records
CREATE TABLE IF NOT EXISTS public.time_entries (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  site_id UUID NOT NULL REFERENCES public.sites(id),
  project_id UUID REFERENCES public.projects(id),
  clock_in_time TIMESTAMP WITH TIME ZONE NOT NULL,
  clock_out_time TIMESTAMP WITH TIME ZONE,
  break_duration_minutes INTEGER DEFAULT 0,
  total_hours DECIMAL(5,2),
  overtime_hours DECIMAL(5,2) DEFAULT 0,
  work_description TEXT,
  status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'completed', 'break')),
  location_lat DECIMAL(10, 8),
  location_lng DECIMAL(11, 8),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create time_breaks table for break tracking
CREATE TABLE IF NOT EXISTS public.time_breaks (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  time_entry_id UUID NOT NULL REFERENCES public.time_entries(id) ON DELETE CASCADE,
  break_start TIMESTAMP WITH TIME ZONE NOT NULL,
  break_end TIMESTAMP WITH TIME ZONE,
  break_type VARCHAR(20) DEFAULT 'regular' CHECK (break_type IN ('regular', 'lunch', 'emergency')),
  duration_minutes INTEGER,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_time_entries_user_id ON public.time_entries(user_id);
CREATE INDEX IF NOT EXISTS idx_time_entries_site_id ON public.time_entries(site_id);
CREATE INDEX IF NOT EXISTS idx_time_entries_clock_in_time ON public.time_entries(clock_in_time);
CREATE INDEX IF NOT EXISTS idx_time_entries_status ON public.time_entries(status);
CREATE INDEX IF NOT EXISTS idx_sites_is_active ON public.sites(is_active);
CREATE INDEX IF NOT EXISTS idx_time_breaks_time_entry_id ON public.time_breaks(time_entry_id);

-- Enable RLS on all tables
ALTER TABLE public.sites ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.time_entries ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.time_breaks ENABLE ROW LEVEL SECURITY;

-- RLS Policies for sites
DROP POLICY IF EXISTS "Users can view active sites" ON public.sites;
CREATE POLICY "Users can view active sites" ON public.sites
  FOR SELECT USING (is_active = true);

DROP POLICY IF EXISTS "Admins can manage sites" ON public.sites;
CREATE POLICY "Admins can manage sites" ON public.sites
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM public.user_profiles 
      WHERE user_id = auth.uid() 
      AND role_name IN ('admin', 'management')
    )
  );

-- RLS Policies for time_entries
DROP POLICY IF EXISTS "Users can view own time entries" ON public.time_entries;
CREATE POLICY "Users can view own time entries" ON public.time_entries
  FOR SELECT USING (
    user_id = auth.uid() OR
    EXISTS (
      SELECT 1 FROM public.user_profiles 
      WHERE user_id = auth.uid() 
      AND role_name IN ('admin', 'management')
    )
  );

DROP POLICY IF EXISTS "Users can insert own time entries" ON public.time_entries;
CREATE POLICY "Users can insert own time entries" ON public.time_entries
  FOR INSERT WITH CHECK (user_id = auth.uid());

DROP POLICY IF EXISTS "Users can update own time entries" ON public.time_entries;
CREATE POLICY "Users can update own time entries" ON public.time_entries
  FOR UPDATE USING (
    user_id = auth.uid() OR
    EXISTS (
      SELECT 1 FROM public.user_profiles 
      WHERE user_id = auth.uid() 
      AND role_name IN ('admin', 'management')
    )
  );

-- RLS Policies for time_breaks
DROP POLICY IF EXISTS "Users can manage own breaks" ON public.time_breaks;
CREATE POLICY "Users can manage own breaks" ON public.time_breaks
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM public.time_entries te 
      WHERE te.id = time_entry_id 
      AND (te.user_id = auth.uid() OR
           EXISTS (
             SELECT 1 FROM public.user_profiles 
             WHERE user_id = auth.uid() 
             AND role_name IN ('admin', 'management')
           ))
    )
  );

-- Function to get active time entry for a user
CREATE OR REPLACE FUNCTION get_active_time_entry(p_user_id UUID)
RETURNS TABLE (
  id UUID,
  user_id UUID,
  site_id UUID,
  project_id UUID,
  clock_in_time TIMESTAMP WITH TIME ZONE,
  clock_out_time TIMESTAMP WITH TIME ZONE,
  break_duration_minutes INTEGER,
  total_hours DECIMAL(5,2),
  overtime_hours DECIMAL(5,2),
  work_description TEXT,
  status VARCHAR(20),
  location_lat DECIMAL(10, 8),
  location_lng DECIMAL(11, 8),
  created_at TIMESTAMP WITH TIME ZONE,
  updated_at TIMESTAMP WITH TIME ZONE
) AS $$
BEGIN
  RETURN QUERY
  SELECT te.*
  FROM public.time_entries te
  WHERE te.user_id = p_user_id 
  AND te.status = 'active'
  ORDER BY te.clock_in_time DESC
  LIMIT 1;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to clock in
CREATE OR REPLACE FUNCTION clock_in(
  p_user_id UUID,
  p_site_id UUID,
  p_project_id UUID DEFAULT NULL,
  p_work_description TEXT DEFAULT NULL,
  p_location_lat DECIMAL(10, 8) DEFAULT NULL,
  p_location_lng DECIMAL(11, 8) DEFAULT NULL
)
RETURNS JSON AS $$
DECLARE
  existing_entry UUID;
  new_entry_id UUID;
BEGIN
  -- Check if user already has an active time entry
  SELECT id INTO existing_entry
  FROM public.time_entries
  WHERE user_id = p_user_id AND status = 'active';
  
  IF existing_entry IS NOT NULL THEN
    RETURN json_build_object(
      'success', false,
      'error', 'User already has an active time entry. Please clock out first.'
    );
  END IF;
  
  -- Create new time entry
  INSERT INTO public.time_entries (
    user_id, site_id, project_id, clock_in_time, work_description, 
    location_lat, location_lng, status
  ) VALUES (
    p_user_id, p_site_id, p_project_id, NOW(), p_work_description,
    p_location_lat, p_location_lng, 'active'
  ) RETURNING id INTO new_entry_id;
  
  RETURN json_build_object(
    'success', true,
    'time_entry_id', new_entry_id,
    'message', 'Successfully clocked in'
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to clock out
CREATE OR REPLACE FUNCTION clock_out(
  p_user_id UUID,
  p_work_description TEXT DEFAULT NULL
)
RETURNS JSON AS $$
DECLARE
  active_entry_id UUID;
  clock_in_time TIMESTAMP WITH TIME ZONE;
  total_minutes INTEGER;
  calculated_hours DECIMAL(5,2);
  overtime_hours DECIMAL(5,2) := 0;
BEGIN
  -- Find active time entry
  SELECT id, clock_in_time INTO active_entry_id, clock_in_time
  FROM public.time_entries
  WHERE user_id = p_user_id AND status = 'active'
  ORDER BY clock_in_time DESC
  LIMIT 1;
  
  IF active_entry_id IS NULL THEN
    RETURN json_build_object(
      'success', false,
      'error', 'No active time entry found for user'
    );
  END IF;
  
  -- Calculate total hours
  total_minutes := EXTRACT(EPOCH FROM (NOW() - clock_in_time)) / 60;
  calculated_hours := ROUND((total_minutes::DECIMAL / 60), 2);
  
  -- Calculate overtime (over 8 hours)
  IF calculated_hours > 8 THEN
    overtime_hours := calculated_hours - 8;
  END IF;
  
  -- Update time entry
  UPDATE public.time_entries
  SET 
    clock_out_time = NOW(),
    total_hours = calculated_hours,
    overtime_hours = overtime_hours,
    status = 'completed',
    work_description = COALESCE(p_work_description, work_description),
    updated_at = NOW()
  WHERE id = active_entry_id;
  
  RETURN json_build_object(
    'success', true,
    'time_entry_id', active_entry_id,
    'total_hours', calculated_hours,
    'overtime_hours', overtime_hours,
    'message', 'Successfully clocked out'
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create updated_at trigger function
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create triggers for updated_at
DROP TRIGGER IF EXISTS update_sites_updated_at ON public.sites;
CREATE TRIGGER update_sites_updated_at
  BEFORE UPDATE ON public.sites
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_time_entries_updated_at ON public.time_entries;
CREATE TRIGGER update_time_entries_updated_at
  BEFORE UPDATE ON public.time_entries
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

-- Insert sample sites if none exist
INSERT INTO public.sites (name, site_code, address, description, is_active)
SELECT * FROM (VALUES
  ('Main Construction Site', 'MAIN-001', '123 Construction Ave, City', 'Primary construction site for downtown project', true),
  ('Warehouse Facility', 'WARE-001', '456 Storage Blvd, City', 'Equipment and materials storage facility', true),
  ('Office Building Project', 'OFFICE-001', '789 Business St, City', 'New office building construction', true)
) AS v(name, site_code, address, description, is_active)
WHERE NOT EXISTS (SELECT 1 FROM public.sites);

-- Success message
DO $$
BEGIN
  RAISE NOTICE '✅ Time tracking database schema created successfully!';
  RAISE NOTICE '📋 Tables created: sites, time_entries, time_breaks';
  RAISE NOTICE '🔧 Functions created: get_active_time_entry, clock_in, clock_out';
  RAISE NOTICE '🛡️ RLS policies enabled for data security';
  RAISE NOTICE '📍 Sample sites inserted if none existed';
  RAISE NOTICE '';
  RAISE NOTICE '🚀 Time tracking system is now ready to use!';
END $$;
