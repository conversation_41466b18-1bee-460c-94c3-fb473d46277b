-- Fix User Deletion to Remove from Both Auth and Profile Systems
-- This function properly deletes users from both user_profiles and auth.users tables

CREATE OR REPLACE FUNCTION public.admin_delete_user_complete(
    p_email VARCHAR(255)
)
RETURNS jsonb AS $$
DECLARE
    v_auth_user_id UUID;
    v_profile_id UUID;
    v_user_name VARCHAR(255);
BEGIN
    -- Get user information
    SELECT up.user_id, up.id, up.first_name || ' ' || up.last_name
    INTO v_auth_user_id, v_profile_id, v_user_name
    FROM public.user_profiles up
    WHERE up.email = p_email;
    
    IF v_auth_user_id IS NULL THEN
        RETURN jsonb_build_object(
            'success', false,
            'error', 'User not found: ' || p_email
        );
    END IF;
    
    BEGIN
        -- Delete from user_profiles first
        DELETE FROM public.user_profiles WHERE email = p_email;
        
        -- Delete from auth.users
        DELETE FROM auth.users WHERE id = v_auth_user_id;
        
        RETURN jsonb_build_object(
            'success', true,
            'message', 'User completely deleted from both profile and auth systems',
            'deleted_user', jsonb_build_object(
                'email', p_email,
                'name', v_user_name,
                'auth_user_id', v_auth_user_id,
                'profile_id', v_profile_id
            )
        );
        
    EXCEPTION
        WHEN OTHERS THEN
            RETURN jsonb_build_object(
                'success', false,
                'error', 'Failed to delete user: ' || SQLERRM
            );
    END;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant permissions
GRANT EXECUTE ON FUNCTION public.admin_delete_user_complete(VARCHAR) TO authenticated;

-- Test the function
DO $$
DECLARE
    test_result jsonb;
BEGIN
    RAISE NOTICE '';
    RAISE NOTICE '=== TESTING USER DELETION FUNCTION ===';
    RAISE NOTICE 'Function admin_delete_user_complete created successfully';
    RAISE NOTICE 'This function will:';
    RAISE NOTICE '1. Find user by email in user_profiles table';
    RAISE NOTICE '2. Delete from user_profiles table';
    RAISE NOTICE '3. Delete from auth.users table using the user_id';
    RAISE NOTICE '4. Return success confirmation with deleted user details';
    RAISE NOTICE '';
    RAISE NOTICE '✅ User deletion system is now ready!';
    RAISE NOTICE 'Users deleted via admin interface will be completely removed from both systems.';
    RAISE NOTICE '';
END $$;
