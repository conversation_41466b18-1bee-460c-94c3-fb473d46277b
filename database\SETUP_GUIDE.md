# Database Setup Guide

## Quick Setup Instructions

### Step 1: Create Cash Flow Function
```sql
-- Copy and paste the content from: database/cash_flow_function.sql
-- Run in Supabase SQL Editor
```

### Step 2: Create Asset Tables
```sql
-- Copy and paste the content from: database/company_assets_simple.sql
-- Run in Supabase SQL Editor
```

### Step 3: Add Sample Data (Optional)
```sql
-- Copy and paste the content from: database/quick_test_assets.sql
-- Run in Supabase SQL Editor
```

## What Each File Does

### `cash_flow_function.sql`
- Creates the `get_cash_flow_data()` function
- Fixes the 404 error for cash flow data
- Enables cash flow trend charts

### `company_assets_simple.sql`
- Creates all asset management tables
- Sets up categories, assets, maintenance, etc.
- Includes proper indexes and security

### `quick_test_assets.sql`
- Adds 5 sample assets for testing
- Includes different asset types
- Adds sample maintenance record

## Expected Results

After running all scripts:
- ✅ Cash Flow tab works without 404 errors
- ✅ Assets tab loads with sample data
- ✅ No Select component errors
- ✅ Professional asset management interface

## Troubleshooting

### If you get "table does not exist" errors:
1. Run `company_assets_simple.sql` first
2. Then run `quick_test_assets.sql`

### If you get "function does not exist" errors:
1. Run `cash_flow_function.sql`
2. Refresh the page

### If you get Select component errors:
- These are now fixed in the code
- Refresh the page after database setup
