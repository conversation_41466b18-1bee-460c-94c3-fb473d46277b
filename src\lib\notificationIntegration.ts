import { NotificationTriggers } from './notificationTriggers';
import { NotificationService } from './notificationService';
import { EmailNotificationService } from './emailNotificationService';

/**
 * Notification Integration Helper
 * 
 * This file provides helper functions to integrate notifications throughout the application.
 * Import these functions in your components and call them when relevant events occur.
 */

export class NotificationIntegration {
  // Project Management Integration
  static async handleProjectCreated(project: any, created_by: string) {
    await NotificationTriggers.onProjectCreated(project, created_by);
  }

  static async handleProjectUpdated(project: any, updated_by: string, changes: string[]) {
    await NotificationTriggers.onProjectUpdated(project, updated_by, changes);
  }

  static async handleProjectStatusChanged(project: any, old_status: string, new_status: string, updated_by: string) {
    await NotificationTriggers.onProjectUpdated(project, updated_by, [`Status changed from ${old_status} to ${new_status}`]);
  }

  // Financial Management Integration
  static async handleInvoiceCreated(invoice: any, created_by: string) {
    await NotificationTriggers.onInvoiceCreated(invoice, created_by);
  }

  static async handleQuotationCreated(quotation: any, created_by: string) {
    await NotificationTriggers.onQuotationCreated(quotation, created_by);
  }

  static async handleProjectUpdated(project: any, updated_by: string, changes: string[]) {
    await NotificationTriggers.onProjectUpdated(project, updated_by, changes);
  }

  static async handlePaymentReceived(payment: any, invoice: any) {
    await NotificationTriggers.onPaymentReceived(payment, invoice);
  }

  static async handlePaymentReceived(payment: any, invoice: any) {
    await NotificationTriggers.onPaymentReceived(payment, invoice);
  }

  static async handleInvoiceOverdue(invoice: any) {
    const recipients = await NotificationTriggers.getFinancialRecipients();
    
    await NotificationService.createFinancialNotification(
      recipients,
      'invoice_overdue',
      {
        invoice_number: invoice.invoice_number,
        client_name: invoice.client_name,
        amount: invoice.total_amount,
        due_date: invoice.due_date,
        days_overdue: Math.ceil((new Date().getTime() - new Date(invoice.due_date).getTime()) / (1000 * 60 * 60 * 24)),
        invoice_id: invoice.id
      },
      {
        priority: 'high',
        related_entity_type: 'invoice',
        related_entity_id: invoice.id,
        send_email: true
      }
    );
  }

  // Message System Integration
  static async handleNewMessage(message: any, channel: any, sender_id: string) {
    await NotificationTriggers.onNewMessage(message, channel, sender_id);
  }

  static async handleChannelCreated(channel: any, created_by: string) {
    const recipients = await NotificationTriggers.getChannelRecipients(channel.id, created_by);
    
    await NotificationService.createMessageNotification(
      recipients,
      'channel_created',
      {
        channel_name: channel.name,
        channel_description: channel.description,
        created_by_name: await NotificationTriggers.getUserName(created_by),
        channel_id: channel.id
      },
      {
        priority: 'medium',
        related_entity_type: 'channel',
        related_entity_id: channel.id,
        send_email: false
      }
    );
  }

  // User Management Integration
  static async handleUserCreated(user: any, created_by: string) {
    await NotificationTriggers.onUserCreated(user, created_by);
  }

  static async handleUserRoleChanged(user: any, old_role: string, new_role: string, updated_by: string) {
    // Notify the user about role change
    await NotificationService.createNotification({
      user_id: user.id,
      category: 'user',
      type: 'info',
      title: 'Role Updated',
      message: `Your role has been changed from ${old_role} to ${new_role}.`,
      priority: 'medium',
      send_email: true
    });

    // Notify admins
    const adminRecipients = await NotificationTriggers.getAdminRecipients(updated_by);
    if (adminRecipients.length > 0) {
      await NotificationService.createBulkNotifications(adminRecipients, {
        category: 'user',
        type: 'info',
        title: 'User Role Changed',
        message: `${user.first_name} ${user.last_name}'s role has been changed from ${old_role} to ${new_role}.`,
        priority: 'low',
        related_entity_type: 'user',
        related_entity_id: user.id
      });
    }
  }

  // Time Tracking Integration
  static async handleTimeTrackingReminder(user_id: string, site_name: string) {
    await NotificationTriggers.onTimeTrackingReminder(user_id, site_name);
  }

  static async handleOvertimeAlert(user_id: string, hours_worked: number, site_name: string) {
    await NotificationService.createNotification({
      user_id,
      category: 'time_tracking',
      type: 'warning',
      title: 'Overtime Alert',
      message: `You have worked ${hours_worked} hours at ${site_name}. Please review your time tracking.`,
      action_url: '/time-tracking',
      action_label: 'View Time Tracking',
      priority: 'medium',
      send_email: false
    });
  }

  // Asset Management Integration
  static async handleAssetMaintenanceDue(asset: any) {
    await NotificationTriggers.onAssetMaintenanceDue(asset);
  }

  static async handleAssetCreated(asset: any, created_by: string) {
    const recipients = await NotificationTriggers.getAssetManagerRecipients();
    
    await NotificationService.createBulkNotifications(recipients, {
      category: 'asset',
      type: 'success',
      title: 'New Asset Added',
      message: `A new asset "${asset.name}" (${asset.asset_id}) has been added to the system.`,
      action_url: `/assets/${asset.id}`,
      action_label: 'View Asset',
      priority: 'low',
      related_entity_type: 'asset',
      related_entity_id: asset.id
    });
  }

  static async handleAssetStatusChanged(asset: any, old_status: string, new_status: string, updated_by: string) {
    const recipients = await NotificationTriggers.getAssetManagerRecipients();
    
    await NotificationService.createBulkNotifications(recipients, {
      category: 'asset',
      type: 'info',
      title: 'Asset Status Changed',
      message: `Asset "${asset.name}" status changed from ${old_status} to ${new_status}.`,
      action_url: `/assets/${asset.id}`,
      action_label: 'View Asset',
      priority: new_status === 'out_of_service' ? 'high' : 'medium',
      related_entity_type: 'asset',
      related_entity_id: asset.id
    });
  }

  // System Notifications
  static async handleSystemMaintenance(maintenance_info: any) {
    await NotificationTriggers.onSystemMaintenance(maintenance_info);
  }

  static async handleSystemError(error_info: any) {
    const adminRecipients = await NotificationTriggers.getAdminRecipients();
    
    await NotificationService.createSystemNotification(
      adminRecipients,
      'system_error',
      {
        error_message: error_info.message,
        error_code: error_info.code,
        timestamp: new Date().toISOString(),
        affected_component: error_info.component
      },
      {
        priority: 'urgent',
        send_email: true
      }
    );
  }

  // Client Management Integration
  static async handleClientCreated(client: any, created_by: string) {
    const recipients = await NotificationTriggers.getFinancialRecipients(created_by);
    
    await NotificationService.createBulkNotifications(recipients, {
      category: 'financial',
      type: 'success',
      title: 'New Client Added',
      message: `A new client "${client.name}" has been added to the system.`,
      action_url: `/clients/${client.id}`,
      action_label: 'View Client',
      priority: 'low',
      related_entity_type: 'client',
      related_entity_id: client.id
    });
  }

  // Quote Management Integration
  static async handleQuoteCreated(quote: any, created_by: string) {
    const recipients = await NotificationTriggers.getFinancialRecipients(created_by);
    
    await NotificationService.createBulkNotifications(recipients, {
      category: 'financial',
      type: 'info',
      title: 'New Quote Created',
      message: `A new quote #${quote.quote_number} for ${quote.client_name} has been created.`,
      action_url: `/quotes/${quote.id}`,
      action_label: 'View Quote',
      priority: 'medium',
      related_entity_type: 'quote',
      related_entity_id: quote.id
    });
  }

  static async handleQuoteAccepted(quote: any) {
    const recipients = await NotificationTriggers.getFinancialRecipients();
    
    await NotificationService.createBulkNotifications(recipients, {
      category: 'financial',
      type: 'success',
      title: 'Quote Accepted',
      message: `Quote #${quote.quote_number} for ${quote.client_name} has been accepted!`,
      action_url: `/quotes/${quote.id}`,
      action_label: 'View Quote',
      priority: 'high',
      related_entity_type: 'quote',
      related_entity_id: quote.id,
      send_email: true
    });
  }

  // Expense Management Integration
  static async handleExpenseSubmitted(expense: any, submitted_by: string) {
    const recipients = await NotificationTriggers.getFinancialRecipients(submitted_by);
    
    await NotificationService.createBulkNotifications(recipients, {
      category: 'financial',
      type: 'info',
      title: 'New Expense Submitted',
      message: `A new expense of $${expense.amount} has been submitted for approval.`,
      action_url: `/expenses/${expense.id}`,
      action_label: 'Review Expense',
      priority: expense.amount > 1000 ? 'high' : 'medium',
      related_entity_type: 'expense',
      related_entity_id: expense.id
    });
  }

  static async handleExpenseApproved(expense: any, approved_by: string) {
    await NotificationService.createNotification({
      user_id: expense.submitted_by,
      category: 'financial',
      type: 'success',
      title: 'Expense Approved',
      message: `Your expense of $${expense.amount} has been approved.`,
      action_url: `/expenses/${expense.id}`,
      action_label: 'View Expense',
      priority: 'medium',
      related_entity_type: 'expense',
      related_entity_id: expense.id,
      send_email: true
    });
  }

  // Utility Functions
  static async testNotificationSystem(user_id: string) {
    await NotificationService.testNotificationSystem(user_id);
  }

  static async processEmailQueue() {
    return await EmailNotificationService.processEmailQueue();
  }

  static async createEmailDigests() {
    await EmailNotificationService.createEmailDigests();
  }

  static async processScheduledNotifications() {
    await NotificationTriggers.processScheduledNotifications();
  }

  // Batch operations for system maintenance
  static async cleanupExpiredNotifications() {
    return await NotificationService.cleanupExpiredNotifications();
  }

  static async getEmailQueueStatus() {
    return await EmailNotificationService.getEmailQueueStatus();
  }

  static async retryFailedEmails() {
    await EmailNotificationService.retryFailedEmails();
  }
}

// Export convenience functions for common use cases
export const notifyProjectCreated = NotificationIntegration.handleProjectCreated;
export const notifyProjectUpdated = NotificationIntegration.handleProjectUpdated;
export const notifyInvoiceCreated = NotificationIntegration.handleInvoiceCreated;
export const notifyPaymentReceived = NotificationIntegration.handlePaymentReceived;
export const notifyNewMessage = NotificationIntegration.handleNewMessage;
export const notifyUserCreated = NotificationIntegration.handleUserCreated;
export const notifyAssetMaintenanceDue = NotificationIntegration.handleAssetMaintenanceDue;
export const notifySystemMaintenance = NotificationIntegration.handleSystemMaintenance;

// Example usage in components:
/*
// In a project creation component:
import { notifyProjectCreated } from '@/lib/notificationIntegration';

const handleCreateProject = async (projectData) => {
  const project = await createProject(projectData);
  await notifyProjectCreated(project, currentUser.id);
};

// In an invoice component:
import { notifyInvoiceCreated } from '@/lib/notificationIntegration';

const handleCreateInvoice = async (invoiceData) => {
  const invoice = await createInvoice(invoiceData);
  await notifyInvoiceCreated(invoice, currentUser.id);
};

// In a message component:
import { notifyNewMessage } from '@/lib/notificationIntegration';

const handleSendMessage = async (messageData) => {
  const message = await sendMessage(messageData);
  await notifyNewMessage(message, channel, currentUser.id);
};
*/
