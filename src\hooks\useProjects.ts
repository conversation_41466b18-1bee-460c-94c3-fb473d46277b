import { useState, useEffect, useCallback } from 'react';
import { ProjectService, Project } from '@/lib/projects';
import { useLocalStorageFallback } from '@/lib/supabase';
import { useToast } from '@/components/ui/use-toast';
import { useAuth } from '@/contexts/AuthContext';

interface UseProjectsReturn {
  projects: Project[];
  loading: boolean;
  error: string | null;
  addProject: (project: Omit<Project, 'id' | 'date_added' | 'last_modified' | 'created_at'>) => Promise<Project>;
  updateProject: (id: string, updates: Partial<Omit<Project, 'id' | 'date_added' | 'created_at'>>) => Promise<Project>;
  deleteProject: (id: string) => Promise<void>;
  refreshProjects: () => Promise<void>;
  isOnline: boolean;
  syncStatus: 'synced' | 'syncing' | 'offline' | 'error';
}

// Local storage fallback functions
const loadFromLocalStorage = (): Project[] => {
  try {
    const saved = localStorage.getItem('projects');
    if (saved) {
      return JSON.parse(saved);
    }
  } catch (error) {
    console.error('Error loading projects from localStorage:', error);
  }
  
  // No sample data - use only Supabase data
  return [];
};

const saveToLocalStorage = (projects: Project[]) => {
  try {
    localStorage.setItem('projects', JSON.stringify(projects));
  } catch (error) {
    console.error('Error saving projects to localStorage:', error);
  }
};

export const useProjects = (): UseProjectsReturn => {
  const { toast } = useToast();
  const { user } = useAuth();
  // Start with empty array and load from database
  const [projects, setProjects] = useState<Project[]>([]);
  const [loading, setLoading] = useState(true); // Start as true to show loading state
  const [error, setError] = useState<string | null>(null);
  const [isOnline, setIsOnline] = useState(navigator.onLine);
  const [syncStatus, setSyncStatus] = useState<'synced' | 'syncing' | 'offline' | 'error'>('synced'); // Start as synced to prevent loops

  const useFallback = useLocalStorageFallback();

  // Monitor online status
  useEffect(() => {
    const handleOnline = () => {
      setIsOnline(true);
      // Don't auto-refresh to prevent loops - user can manually refresh
      console.log('Connection restored');
    };
    
    const handleOffline = () => {
      setIsOnline(false);
      setSyncStatus('offline');
    };

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []); // Remove useFallback dependency to prevent loops

  // Load projects
  const refreshProjects = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      setSyncStatus('syncing');

      if (useFallback || !isOnline) {
        // Use localStorage fallback
        const localProjects = loadFromLocalStorage();
        setProjects(localProjects);
        setSyncStatus(useFallback ? 'synced' : 'offline');
      } else {
        // Use Supabase with role-based filtering
        const data = await ProjectService.getProjects(user?.id);
        setProjects(data);
        setSyncStatus('synced');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to load projects';
      setError(errorMessage);
      setSyncStatus('error');
      
      // Fallback to localStorage on error
      if (!useFallback) {
        const localProjects = loadFromLocalStorage();
        setProjects(localProjects);
        // Note: Removed toast to prevent dependency loop
        console.warn("Connection Error: Using offline data. Changes will sync when connection is restored.");
      }
    } finally {
      setLoading(false);
    }
  }, [useFallback, isOnline, user]); // Include user for role-based filtering

  // Add project
  const addProject = useCallback(async (projectData: Omit<Project, 'id' | 'date_added' | 'last_modified' | 'created_at'>): Promise<Project> => {
    try {
      setSyncStatus('syncing');
      
      if (useFallback || !isOnline) {
        // Use localStorage fallback
        const newProject: Project = {
          id: Date.now().toString(),
          ...projectData,
          date_added: new Date().toISOString(),
          last_modified: new Date().toISOString(),
          created_at: new Date().toISOString()
        };
        
        const updatedProjects = [newProject, ...projects];
        setProjects(updatedProjects);
        saveToLocalStorage(updatedProjects);
        setSyncStatus(useFallback ? 'synced' : 'offline');
        
        return newProject;
      } else {
        // Use Supabase with notification integration
        const newProject = await ProjectService.addProject(projectData, user?.id);
        setProjects(prev => [newProject, ...prev]);
        setSyncStatus('synced');

        return newProject;
      }
    } catch (err) {
      setSyncStatus('error');
      const errorMessage = err instanceof Error ? err.message : 'Failed to add project';
      throw new Error(errorMessage);
    }
  }, [projects, useFallback, isOnline]);

  // Update project
  const updateProject = useCallback(async (id: string, updates: Partial<Omit<Project, 'id' | 'date_added' | 'created_at'>>): Promise<Project> => {
    try {
      setSyncStatus('syncing');
      
      if (useFallback || !isOnline) {
        // Use localStorage fallback
        const updatedProjects = projects.map(project => {
          if (project.id === id) {
            return {
              ...project,
              ...updates,
              last_modified: new Date().toISOString()
            };
          }
          return project;
        });
        
        setProjects(updatedProjects);
        saveToLocalStorage(updatedProjects);
        setSyncStatus(useFallback ? 'synced' : 'offline');
        
        const updatedProject = updatedProjects.find(project => project.id === id)!;
        return updatedProject;
      } else {
        // Use Supabase with notification integration
        const updatedProject = await ProjectService.updateProject(id, updates, user?.id);
        setProjects(prev => prev.map(project => project.id === id ? updatedProject : project));
        setSyncStatus('synced');

        return updatedProject;
      }
    } catch (err) {
      setSyncStatus('error');
      const errorMessage = err instanceof Error ? err.message : 'Failed to update project';
      throw new Error(errorMessage);
    }
  }, [projects, useFallback, isOnline]);

  // Delete project
  const deleteProject = useCallback(async (id: string): Promise<void> => {
    try {
      setSyncStatus('syncing');
      
      if (useFallback || !isOnline) {
        // Use localStorage fallback
        const updatedProjects = projects.filter(project => project.id !== id);
        setProjects(updatedProjects);
        saveToLocalStorage(updatedProjects);
        setSyncStatus(useFallback ? 'synced' : 'offline');
      } else {
        // Use Supabase
        await ProjectService.deleteProject(id);
        setProjects(prev => prev.filter(project => project.id !== id));
        setSyncStatus('synced');
      }
    } catch (err) {
      setSyncStatus('error');
      const errorMessage = err instanceof Error ? err.message : 'Failed to delete project';
      throw new Error(errorMessage);
    }
  }, [projects, useFallback, isOnline]);

  // Initial load (re-enabled for Kanban functionality)
  useEffect(() => {
    console.log('Projects: Loading projects for Kanban functionality');
    refreshProjects();
  }, [refreshProjects]);

  // Set up real-time subscription (temporarily disabled to prevent loops)
  useEffect(() => {
    // Temporarily disabled real-time subscriptions
    console.log('Real-time project subscriptions disabled to prevent refresh loops');
    return () => {
      // No cleanup needed
    };
  }, []);

  return {
    projects,
    loading,
    error,
    addProject,
    updateProject,
    deleteProject,
    refreshProjects,
    isOnline,
    syncStatus
  };
};
