import React, { useState, useEffect } from 'react';
import Layout from '@/components/layout/Layout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Tabs, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { 
  BarChart3, 
  PieChart, 
  TrendingUp, 
  Download, 
  Calendar, 
  Filter,
  RefreshCw,
  FileText,
  DollarSign,
  Users,
  AlertCircle,
  CheckCircle
} from 'lucide-react';
import { AnalyticsService, AnalyticsData } from '@/lib/analytics';
import { ExportService } from '@/lib/exportService';
import { useToast } from '@/components/ui/use-toast';

const Reports: React.FC = () => {
  const { toast } = useToast();
  const [analyticsData, setAnalyticsData] = useState<AnalyticsData | null>(null);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('overview');
  const [dateRange, setDateRange] = useState<{ start: string; end: string }>({
    start: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
    end: new Date().toISOString().split('T')[0]
  });
  const [reportPeriod, setReportPeriod] = useState('30days');

  useEffect(() => {
    loadAnalyticsData();
  }, [dateRange]);

  const loadAnalyticsData = async () => {
    try {
      setLoading(true);
      console.log('🔄 Loading analytics data for reports...');
      const data = await AnalyticsService.getAnalyticsData(dateRange.start, dateRange.end);
      setAnalyticsData(data);
      console.log('✅ Analytics data loaded successfully');
    } catch (error) {
      console.error('❌ Error loading analytics data:', error);
      toast({
        title: "Error Loading Data",
        description: "Failed to load analytics data. Please try again.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const handlePeriodChange = (period: string) => {
    setReportPeriod(period);
    const now = new Date();
    let startDate: Date;

    switch (period) {
      case '7days':
        startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
        break;
      case '30days':
        startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
        break;
      case '90days':
        startDate = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000);
        break;
      case '1year':
        startDate = new Date(now.getTime() - 365 * 24 * 60 * 60 * 1000);
        break;
      default:
        return;
    }

    setDateRange({
      start: startDate.toISOString().split('T')[0],
      end: now.toISOString().split('T')[0]
    });
  };

  const handleExportReport = async (type: 'pdf' | 'excel', reportType: 'financial' | 'revenue' | 'expenses' | 'cashflow' = 'financial') => {
    if (!analyticsData) {
      toast({
        title: "No Data Available",
        description: "Please load analytics data before exporting.",
        variant: "destructive",
      });
      return;
    }

    try {
      toast({
        title: "Generating Report",
        description: `Creating ${type.toUpperCase()} report...`,
      });

      if (type === 'pdf') {
        await ExportService.exportToPDF(analyticsData, reportType);
      } else {
        await ExportService.exportToExcel(analyticsData, reportType);
      }

      toast({
        title: "Report Generated",
        description: `${type.toUpperCase()} report has been generated successfully.`,
      });
    } catch (error) {
      console.error('Error exporting report:', error);
      toast({
        title: "Export Failed",
        description: `Failed to generate ${type.toUpperCase()} report. Please try again.`,
        variant: "destructive",
      });
    }
  };

  if (loading) {
    return (
      <Layout>
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <RefreshCw className="h-12 w-12 animate-spin text-blue-600 mx-auto mb-4" />
            <p className="text-gray-600">Loading reports and analytics...</p>
          </div>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Reports & Analytics</h1>
            <p className="text-gray-600 mt-1">
              Generate comprehensive insights and reports for your construction business
            </p>
          </div>
          <div className="flex items-center space-x-3">
            <Button variant="outline" onClick={loadAnalyticsData} disabled={loading}>
              <RefreshCw className={`w-4 h-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
              Refresh Data
            </Button>
            <Button onClick={() => handleExportReport('pdf')}>
              <Download className="w-4 h-4 mr-2" />
              Export PDF
            </Button>
            <Button variant="outline" onClick={() => handleExportReport('excel')}>
              <FileText className="w-4 h-4 mr-2" />
              Export Excel
            </Button>
          </div>
        </div>

        {/* Filters and Controls */}
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Report Filters</CardTitle>
            <CardDescription>Customize your report parameters and date ranges</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <div className="space-y-2">
                <Label htmlFor="period">Time Period</Label>
                <Select value={reportPeriod} onValueChange={handlePeriodChange}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select period" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="7days">Last 7 Days</SelectItem>
                    <SelectItem value="30days">Last 30 Days</SelectItem>
                    <SelectItem value="90days">Last 90 Days</SelectItem>
                    <SelectItem value="1year">Last Year</SelectItem>
                    <SelectItem value="custom">Custom Range</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="start-date">Start Date</Label>
                <Input
                  id="start-date"
                  type="date"
                  value={dateRange.start}
                  onChange={(e) => setDateRange(prev => ({ ...prev, start: e.target.value }))}
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="end-date">End Date</Label>
                <Input
                  id="end-date"
                  type="date"
                  value={dateRange.end}
                  onChange={(e) => setDateRange(prev => ({ ...prev, end: e.target.value }))}
                />
              </div>
              
              <div className="space-y-2">
                <Label>Quick Actions</Label>
                <div className="flex space-x-2">
                  <Button variant="outline" size="sm" onClick={loadAnalyticsData}>
                    <Filter className="w-4 h-4 mr-1" />
                    Apply
                  </Button>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Quick Stats Overview */}
        {analyticsData && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Revenue</CardTitle>
                <DollarSign className="h-4 w-4 text-green-500" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-green-600">
                  ${analyticsData.financialSummary.totalRevenue.toLocaleString()}
                </div>
                <p className="text-xs text-muted-foreground mt-1">
                  {analyticsData.financialSummary.growthRate > 0 ? '+' : ''}{analyticsData.financialSummary.growthRate.toFixed(1)}% growth
                </p>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Net Profit</CardTitle>
                <TrendingUp className="h-4 w-4" />
              </CardHeader>
              <CardContent>
                <div className={`text-2xl font-bold ${analyticsData.financialSummary.netProfit >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                  ${analyticsData.financialSummary.netProfit.toLocaleString()}
                </div>
                <p className="text-xs text-muted-foreground mt-1">
                  {analyticsData.financialSummary.profitMargin.toFixed(1)}% margin
                </p>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Active Clients</CardTitle>
                <Users className="h-4 w-4 text-blue-500" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-gray-900">
                  {analyticsData.clientAnalytics.activeClients}
                </div>
                <p className="text-xs text-muted-foreground mt-1">
                  Current active clients
                </p>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Cash Flow</CardTitle>
                <BarChart3 className="h-4 w-4" />
              </CardHeader>
              <CardContent>
                <div className={`text-2xl font-bold ${analyticsData.cashFlowAnalytics.netCashFlow >= 0 ? 'text-green-600' : 'text-yellow-600'}`}>
                  ${analyticsData.cashFlowAnalytics.netCashFlow.toLocaleString()}
                </div>
                <p className="text-xs text-muted-foreground mt-1">
                  Net cash flow
                </p>
              </CardContent>
            </Card>
          </div>
        )}

        {/* Business Insights */}
        {analyticsData && analyticsData.insights.length > 0 && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <AlertCircle className="h-5 w-5 text-blue-500" />
                <span>Key Business Insights</span>
                <Badge variant="secondary">{analyticsData.insights.length}</Badge>
              </CardTitle>
              <CardDescription>
                AI-generated insights and recommendations based on your financial data
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {analyticsData.insights.slice(0, 6).map((insight) => (
                  <Card key={insight.id} className="border-l-4 border-blue-500">
                    <CardHeader className="pb-3">
                      <CardTitle className="text-sm font-semibold">{insight.title}</CardTitle>
                      <Badge variant={insight.priority === 'high' ? 'destructive' : 'secondary'} className="text-xs w-fit">
                        {insight.priority.toUpperCase()}
                      </Badge>
                    </CardHeader>
                    <CardContent className="pt-0">
                      <p className="text-sm text-gray-700 mb-3">{insight.description}</p>
                      {insight.recommendation && (
                        <div className="bg-gray-50 rounded-md p-3">
                          <p className="text-xs font-medium text-gray-800 mb-1">Recommendation:</p>
                          <p className="text-xs text-gray-700">{insight.recommendation}</p>
                        </div>
                      )}
                    </CardContent>
                  </Card>
                ))}
              </div>
              {analyticsData.insights.length > 6 && (
                <div className="text-center mt-4">
                  <Button variant="outline" onClick={() => setActiveTab('insights')}>
                    View All {analyticsData.insights.length} Insights
                  </Button>
                </div>
              )}
            </CardContent>
          </Card>
        )}
      </div>
    </Layout>
  );
};

export default Reports;
