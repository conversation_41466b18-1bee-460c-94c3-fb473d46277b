-- Message Notifications Setup
-- This script adds automatic notification creation when new messages are sent

-- 1. Ensure notifications table exists
CREATE TABLE IF NOT EXISTS public.notifications (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL,
    type VA<PERSON><PERSON><PERSON>(20) DEFAULT 'info',
    category VA<PERSON>HAR(50) NOT NULL,
    title VARCHAR(255) NOT NULL,
    message TEXT NOT NULL,
    action_url VARCHAR(500),
    action_label VARCHAR(100),
    priority VARCHAR(20) DEFAULT 'medium',
    read BOOLEAN DEFAULT FALSE,
    related_entity_type VARCHAR(50),
    related_entity_id UUID,
    metadata JSONB DEFAULT '{}',
    expires_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_by <PERSON><PERSON><PERSON>
);

-- 2. Add foreign key constraints if they don't exist
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints 
        WHERE constraint_name = 'notifications_user_id_fkey'
        AND table_name = 'notifications'
    ) THEN
        ALTER TABLE public.notifications 
        ADD CONSTRAINT notifications_user_id_fkey 
        FOREIGN KEY (user_id) REFERENCES auth.users(id) ON DELETE CASCADE;
    END IF;
END $$;

-- 3. Set up Row Level Security for notifications
ALTER TABLE public.notifications ENABLE ROW LEVEL SECURITY;

DROP POLICY IF EXISTS "Users can view their own notifications" ON public.notifications;
DROP POLICY IF EXISTS "Users can update their own notifications" ON public.notifications;
DROP POLICY IF EXISTS "System can create notifications" ON public.notifications;

CREATE POLICY "Users can view their own notifications" ON public.notifications
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can update their own notifications" ON public.notifications
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "System can create notifications" ON public.notifications
    FOR INSERT WITH CHECK (true);

-- 4. Grant permissions
GRANT SELECT, UPDATE ON public.notifications TO authenticated;
GRANT INSERT ON public.notifications TO authenticated, anon;

-- 5. Create function to automatically create message notifications
CREATE OR REPLACE FUNCTION create_message_notifications()
RETURNS TRIGGER AS $$
DECLARE
    channel_name TEXT;
    sender_name TEXT;
    user_record RECORD;
    notification_count INTEGER := 0;
BEGIN
    -- Only create notifications for new messages (not updates or deleted messages)
    IF TG_OP = 'INSERT' AND NEW.is_deleted = FALSE THEN
        
        -- Get channel name
        SELECT name INTO channel_name 
        FROM public.channels 
        WHERE id = NEW.channel_id;
        
        -- Default channel name if not found
        IF channel_name IS NULL THEN
            channel_name := 'Unknown Channel';
        END IF;
        
        -- Get sender name from user_profiles
        SELECT CONCAT(first_name, ' ', last_name) INTO sender_name
        FROM public.user_profiles 
        WHERE user_id = NEW.created_by;
        
        -- If user_profiles doesn't exist or sender not found, use a default name
        IF sender_name IS NULL OR sender_name = ' ' THEN
            sender_name := 'User';
        END IF;
        
        -- Create notifications for all users except the sender
        -- Try user_profiles first, then fallback to auth.users
        IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'user_profiles') THEN
            FOR user_record IN 
                SELECT user_id 
                FROM public.user_profiles 
                WHERE user_id != NEW.created_by
                AND user_id IS NOT NULL
            LOOP
                INSERT INTO public.notifications (
                    user_id,
                    category,
                    type,
                    title,
                    message,
                    action_url,
                    action_label,
                    priority,
                    related_entity_type,
                    related_entity_id,
                    metadata,
                    created_by
                ) VALUES (
                    user_record.user_id,
                    'message',
                    'info',
                    'New message in ' || channel_name,
                    sender_name || ': ' || LEFT(NEW.content, 100) || CASE WHEN LENGTH(NEW.content) > 100 THEN '...' ELSE '' END,
                    '/messages?channel=' || NEW.channel_id,
                    'View Message',
                    'medium',
                    'message',
                    NEW.id,
                    jsonb_build_object(
                        'channel_name', channel_name,
                        'sender_name', sender_name,
                        'message_id', NEW.id,
                        'channel_id', NEW.channel_id
                    ),
                    NEW.created_by
                );
                notification_count := notification_count + 1;
            END LOOP;
        ELSE
            -- Fallback to auth.users if user_profiles doesn't exist
            FOR user_record IN 
                SELECT id as user_id 
                FROM auth.users 
                WHERE id != NEW.created_by
            LOOP
                INSERT INTO public.notifications (
                    user_id,
                    category,
                    type,
                    title,
                    message,
                    action_url,
                    action_label,
                    priority,
                    related_entity_type,
                    related_entity_id,
                    metadata,
                    created_by
                ) VALUES (
                    user_record.user_id,
                    'message',
                    'info',
                    'New message in ' || channel_name,
                    sender_name || ': ' || LEFT(NEW.content, 100) || CASE WHEN LENGTH(NEW.content) > 100 THEN '...' ELSE '' END,
                    '/messages?channel=' || NEW.channel_id,
                    'View Message',
                    'medium',
                    'message',
                    NEW.id,
                    jsonb_build_object(
                        'channel_name', channel_name,
                        'sender_name', sender_name,
                        'message_id', NEW.id,
                        'channel_id', NEW.channel_id
                    ),
                    NEW.created_by
                );
                notification_count := notification_count + 1;
            END LOOP;
        END IF;
        
        -- Log the notification creation (optional)
        RAISE NOTICE 'Created % message notifications for message ID: %', notification_count, NEW.id;
        
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 6. Create trigger for automatic message notifications
DROP TRIGGER IF EXISTS trigger_create_message_notifications ON public.messages;

-- Only create trigger if messages table exists
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'messages') THEN
        CREATE TRIGGER trigger_create_message_notifications
            AFTER INSERT ON public.messages
            FOR EACH ROW
            EXECUTE FUNCTION create_message_notifications();
        RAISE NOTICE 'Created trigger for automatic message notifications';
    ELSE
        RAISE NOTICE 'Messages table does not exist - trigger not created';
    END IF;
END $$;

-- 7. Grant execute permission on the function
GRANT EXECUTE ON FUNCTION create_message_notifications() TO authenticated;

-- 8. Test and verification
DO $$
DECLARE
    notifications_exists BOOLEAN;
    messages_exists BOOLEAN;
    trigger_exists BOOLEAN;
BEGIN
    RAISE NOTICE '';
    RAISE NOTICE '=== MESSAGE NOTIFICATIONS SETUP COMPLETE ===';
    
    -- Check if tables exist
    SELECT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'notifications') INTO notifications_exists;
    SELECT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'messages') INTO messages_exists;
    
    -- Check if trigger exists
    SELECT EXISTS (
        SELECT 1 FROM information_schema.triggers 
        WHERE trigger_name = 'trigger_create_message_notifications'
    ) INTO trigger_exists;
    
    RAISE NOTICE 'Setup status:';
    RAISE NOTICE '  notifications table: %', notifications_exists;
    RAISE NOTICE '  messages table: %', messages_exists;
    RAISE NOTICE '  notification trigger: %', trigger_exists;
    RAISE NOTICE '';
    
    IF notifications_exists AND messages_exists AND trigger_exists THEN
        RAISE NOTICE '✓ Message notifications are now fully configured!';
        RAISE NOTICE '✓ Users will automatically receive notifications when new messages are sent';
        RAISE NOTICE '✓ Notifications will appear in the NotificationCenter and MessagesCenter';
    ELSE
        RAISE NOTICE '⚠ Some components are missing - please check the setup';
    END IF;
    
    RAISE NOTICE '';
END $$;
