import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { 
  TrendingUp, 
  TrendingDown, 
  Minus,
  Target,
  AlertTriangle,
  CheckCircle,
  Clock,
  Zap,
  MoreHorizontal,
  Eye,
  EyeOff
} from 'lucide-react';

export interface KPIData {
  id: string;
  title: string;
  value: number | string;
  previousValue?: number;
  target?: number;
  unit?: string;
  format?: 'currency' | 'percentage' | 'number' | 'text';
  trend?: 'up' | 'down' | 'neutral';
  change?: number;
  changeLabel?: string;
  status?: 'success' | 'warning' | 'danger' | 'info';
  description?: string;
  icon?: React.ReactNode;
  color?: string;
  showProgress?: boolean;
  animated?: boolean;
}

interface KPIWidgetProps {
  data: KPIData;
  size?: 'sm' | 'md' | 'lg';
  variant?: 'default' | 'gradient' | 'minimal' | 'modern';
  className?: string;
  onRefresh?: () => void;
  loading?: boolean;
}

const KPIWidget: React.FC<KPIWidgetProps> = ({
  data,
  size = 'md',
  variant = 'modern',
  className = '',
  onRefresh,
  loading = false
}) => {
  const [isVisible, setIsVisible] = useState(true);
  const [animatedValue, setAnimatedValue] = useState(0);

  const {
    title,
    value,
    previousValue,
    target,
    unit = '',
    format = 'number',
    trend,
    change,
    changeLabel,
    status,
    description,
    icon,
    color,
    showProgress = false,
    animated = true
  } = data;

  useEffect(() => {
    if (animated && typeof value === 'number') {
      let start = 0;
      const end = value;
      const duration = 1000;
      const increment = end / (duration / 16);

      const timer = setInterval(() => {
        start += increment;
        if (start >= end) {
          setAnimatedValue(end);
          clearInterval(timer);
        } else {
          setAnimatedValue(Math.floor(start));
        }
      }, 16);

      return () => clearInterval(timer);
    } else {
      setAnimatedValue(typeof value === 'number' ? value : 0);
    }
  }, [value, animated]);

  const formatValue = (val: number | string): string => {
    if (typeof val === 'string') return val;
    
    switch (format) {
      case 'currency':
        return `$${val.toLocaleString()}`;
      case 'percentage':
        return `${val.toFixed(1)}%`;
      case 'number':
        return val.toLocaleString();
      default:
        return val.toString();
    }
  };

  const getStatusColor = () => {
    switch (status) {
      case 'success':
        return 'text-green-600 bg-green-50 border-green-200';
      case 'warning':
        return 'text-yellow-600 bg-yellow-50 border-yellow-200';
      case 'danger':
        return 'text-red-600 bg-red-50 border-red-200';
      case 'info':
        return 'text-blue-600 bg-blue-50 border-blue-200';
      default:
        return 'text-gray-600 bg-gray-50 border-gray-200';
    }
  };

  const getStatusIcon = () => {
    switch (status) {
      case 'success':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'warning':
        return <AlertTriangle className="h-4 w-4 text-yellow-500" />;
      case 'danger':
        return <AlertTriangle className="h-4 w-4 text-red-500" />;
      case 'info':
        return <Clock className="h-4 w-4 text-blue-500" />;
      default:
        return null;
    }
  };

  const getTrendIcon = () => {
    switch (trend) {
      case 'up':
        return <TrendingUp className="h-4 w-4 text-green-500" />;
      case 'down':
        return <TrendingDown className="h-4 w-4 text-red-500" />;
      default:
        return <Minus className="h-4 w-4 text-gray-500" />;
    }
  };

  const getTrendColor = () => {
    switch (trend) {
      case 'up':
        return 'text-green-600';
      case 'down':
        return 'text-red-600';
      default:
        return 'text-gray-600';
    }
  };

  const getSizeClasses = () => {
    switch (size) {
      case 'sm':
        return 'p-4';
      case 'lg':
        return 'p-8';
      default:
        return 'p-6';
    }
  };

  const getVariantClasses = () => {
    switch (variant) {
      case 'gradient':
        return 'bg-gradient-to-br from-blue-50 to-indigo-100 border-blue-200';
      case 'minimal':
        return 'border-0 shadow-none bg-transparent';
      case 'modern':
        return 'border-0 shadow-lg bg-white hover:shadow-xl transition-all duration-300';
      default:
        return '';
    }
  };

  const progressValue = target && typeof value === 'number' ? (value / target) * 100 : 0;

  return (
    <Card className={`${getVariantClasses()} ${className} transition-all duration-300`}>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <div className="flex items-center space-x-2">
          {icon && (
            <div className="p-2 rounded-lg bg-blue-50">
              {icon}
            </div>
          )}
          <div>
            <CardTitle className="text-sm font-medium text-gray-600">{title}</CardTitle>
            {description && (
              <CardDescription className="text-xs text-gray-500 mt-1">
                {description}
              </CardDescription>
            )}
          </div>
        </div>
        <div className="flex items-center space-x-2">
          {getStatusIcon()}
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setIsVisible(!isVisible)}
            className="h-6 w-6 p-0"
          >
            {isVisible ? <Eye className="h-3 w-3" /> : <EyeOff className="h-3 w-3" />}
          </Button>
        </div>
      </CardHeader>
      
      <CardContent className={getSizeClasses()}>
        {isVisible ? (
          <div className="space-y-4">
            {/* Main Value */}
            <div className="flex items-baseline space-x-2">
              <div 
                className="text-3xl font-bold"
                style={{ color: color || '#1f2937' }}
              >
                {loading ? (
                  <div className="animate-pulse bg-gray-200 h-8 w-24 rounded"></div>
                ) : (
                  <>
                    {formatValue(animated ? animatedValue : value)}
                    {unit && <span className="text-lg text-gray-500 ml-1">{unit}</span>}
                  </>
                )}
              </div>
              {trend && (
                <div className="flex items-center space-x-1">
                  {getTrendIcon()}
                  {change !== undefined && (
                    <span className={`text-sm font-medium ${getTrendColor()}`}>
                      {change > 0 ? '+' : ''}{change.toFixed(1)}%
                    </span>
                  )}
                </div>
              )}
            </div>

            {/* Change Label */}
            {changeLabel && (
              <p className="text-xs text-gray-500">
                {changeLabel}
              </p>
            )}

            {/* Progress Bar */}
            {showProgress && target && typeof value === 'number' && (
              <div className="space-y-2">
                <div className="flex justify-between text-xs text-gray-500">
                  <span>Progress to target</span>
                  <span>{formatValue(target)}</span>
                </div>
                <Progress 
                  value={progressValue} 
                  className="h-2"
                  style={{
                    background: progressValue >= 100 ? '#10b981' : progressValue >= 75 ? '#f59e0b' : '#ef4444'
                  }}
                />
                <div className="text-xs text-gray-500 text-center">
                  {progressValue.toFixed(1)}% of target
                </div>
              </div>
            )}

            {/* Status Badge */}
            {status && (
              <Badge 
                variant="outline" 
                className={`${getStatusColor()} text-xs`}
              >
                {status.charAt(0).toUpperCase() + status.slice(1)}
              </Badge>
            )}

            {/* Previous Value Comparison */}
            {previousValue !== undefined && typeof value === 'number' && (
              <div className="text-xs text-gray-500 border-t pt-2">
                Previous: {formatValue(previousValue)}
                <span className={`ml-2 ${value > previousValue ? 'text-green-600' : value < previousValue ? 'text-red-600' : 'text-gray-600'}`}>
                  ({value > previousValue ? '+' : ''}{((value - previousValue) / previousValue * 100).toFixed(1)}%)
                </span>
              </div>
            )}
          </div>
        ) : (
          <div className="flex items-center justify-center h-20">
            <div className="text-center">
              <EyeOff className="h-6 w-6 text-gray-400 mx-auto mb-1" />
              <p className="text-xs text-gray-500">Hidden</p>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

// Preset KPI configurations
export const createRevenueKPI = (value: number, previousValue?: number): KPIData => ({
  id: 'revenue',
  title: 'Total Revenue',
  value,
  previousValue,
  format: 'currency',
  trend: previousValue ? (value > previousValue ? 'up' : value < previousValue ? 'down' : 'neutral') : undefined,
  change: previousValue ? ((value - previousValue) / previousValue) * 100 : undefined,
  changeLabel: 'vs last period',
  status: value > 0 ? 'success' : 'warning',
  icon: <TrendingUp className="h-4 w-4 text-green-500" />,
  color: '#10b981',
  animated: true
});

export const createProfitKPI = (value: number, target?: number): KPIData => ({
  id: 'profit',
  title: 'Net Profit',
  value,
  target,
  format: 'currency',
  trend: value > 0 ? 'up' : value < 0 ? 'down' : 'neutral',
  status: value > 0 ? 'success' : value < 0 ? 'danger' : 'warning',
  icon: <Target className="h-4 w-4 text-blue-500" />,
  color: value > 0 ? '#10b981' : '#ef4444',
  showProgress: !!target,
  animated: true
});

export const createProjectsKPI = (value: number, total?: number): KPIData => ({
  id: 'projects',
  title: 'Active Projects',
  value,
  target: total,
  format: 'number',
  status: 'info',
  icon: <Zap className="h-4 w-4 text-purple-500" />,
  color: '#8b5cf6',
  showProgress: !!total,
  animated: true
});

export default KPIWidget;
