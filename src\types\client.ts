export interface Client {
  id: string;
  name: string;
  company_name?: string;
  email: string;
  phone?: string;
  address?: string;
  city?: string;
  state?: string;
  zip_code?: string;
  country?: string;
  contact_person?: string;
  tax_id?: string;
  payment_terms?: number; // days
  credit_limit?: number;
  status: 'Active' | 'Inactive' | 'Suspended';
  client_type: 'Individual' | 'Company' | 'Government' | 'Non-Profit';
  industry?: string;
  website?: string;
  notes?: string;
  created_at: string;
  updated_at: string;
  last_contact?: string;
  preferred_communication?: 'Email' | 'Phone' | 'SMS' | 'Mail';
  // Creator information
  created_by_user_id?: string;
  created_by_name?: string;
  created_by_avatar?: string;
}

export interface ClientPayment {
  id: string;
  client_id: string;
  invoice_id?: string;
  project_id?: string;
  amount: number;
  payment_date: string;
  payment_method: 'Cash' | 'Check' | 'Credit Card' | 'Bank Transfer' | 'Wire Transfer' | 'Other';
  reference_number?: string;
  description?: string;
  status: 'Pending' | 'Completed' | 'Failed' | 'Cancelled';
  created_at: string;
  updated_at: string;
  processed_by?: string;
  transaction_fee?: number;
  currency: string;
}

export interface ClientInvoice {
  id: string;
  client_id: string;
  project_id?: string;
  invoice_number: string;
  issue_date: string;
  due_date: string;
  amount: number;
  tax_amount?: number;
  discount_amount?: number;
  total_amount: number;
  status: 'Draft' | 'Sent' | 'Viewed' | 'Paid' | 'Overdue' | 'Cancelled';
  description?: string;
  line_items: InvoiceLineItem[];
  payment_terms?: number;
  notes?: string;
  created_at: string;
  updated_at: string;
  sent_date?: string;
  paid_date?: string;
  currency: string;
}

export interface InvoiceLineItem {
  id: string;
  description: string;
  quantity: number;
  unit_price: number;
  total: number;
  tax_rate?: number;
}

export interface ClientFinancialSummary {
  client_id: string;
  total_invoiced: number;
  total_paid: number;
  outstanding_amount: number;
  overdue_amount: number;
  credit_used: number;
  credit_available: number;
  last_payment_date?: string;
  last_payment_amount?: number;
  average_payment_days?: number;
  payment_history_months: number;
  total_projects: number;
  active_projects: number;
}

export interface ClientContact {
  id: string;
  client_id: string;
  name: string;
  title?: string;
  email?: string;
  phone?: string;
  is_primary: boolean;
  department?: string;
  notes?: string;
  created_at: string;
  updated_at: string;
}

export interface ClientDocument {
  id: string;
  client_id: string;
  name: string;
  type: 'Contract' | 'Invoice' | 'Receipt' | 'Agreement' | 'Proposal' | 'Other';
  file_path: string;
  file_size: number;
  mime_type: string;
  uploaded_by: string;
  uploaded_at: string;
  description?: string;
  tags?: string[];
}

export interface ClientActivity {
  id: string;
  client_id: string;
  activity_type: 'Payment' | 'Invoice' | 'Contact' | 'Project' | 'Document' | 'Note';
  description: string;
  details?: any;
  created_at: string;
  created_by: string;
}

// Filter and search types
export interface ClientFilters {
  status?: string;
  client_type?: string;
  industry?: string;
  payment_status?: 'Current' | 'Overdue' | 'Paid';
  search?: string;
  date_range?: {
    start: string;
    end: string;
  };
}

// Analytics types
export interface ClientAnalytics {
  total_clients: number;
  active_clients: number;
  total_revenue: number;
  outstanding_revenue: number;
  overdue_revenue: number;
  average_payment_time: number;
  top_clients: Array<{
    client_id: string;
    client_name: string;
    total_revenue: number;
    project_count: number;
  }>;
  payment_trends: Array<{
    month: string;
    payments_received: number;
    invoices_sent: number;
    outstanding: number;
  }>;
  industry_breakdown: Array<{
    industry: string;
    client_count: number;
    revenue: number;
  }>;
}
