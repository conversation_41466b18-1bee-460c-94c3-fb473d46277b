-- Quick fix for messages table creator fields
-- Run this first to fix the immediate error

-- Add creator fields to messages table
DO $$
BEGIN
  -- Check if messages table exists
  IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'messages' AND table_schema = 'public') THEN
    
    -- Add created_by_user_id field
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'messages' AND column_name = 'created_by_user_id' AND table_schema = 'public') THEN
      ALTER TABLE public.messages ADD COLUMN created_by_user_id UUID REFERENCES auth.users(id);
      RAISE NOTICE '✓ Added created_by_user_id to messages table';
    ELSE
      RAISE NOTICE '- created_by_user_id already exists in messages table';
    END IF;

    -- Add created_by_name field for display
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'messages' AND column_name = 'created_by_name' AND table_schema = 'public') THEN
      ALTER TABLE public.messages ADD COLUMN created_by_name TEXT;
      RAISE NOTICE '✓ Added created_by_name to messages table';
    ELSE
      RAISE NOTICE '- created_by_name already exists in messages table';
    END IF;

    -- Add created_by_avatar field for profile picture
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'messages' AND column_name = 'created_by_avatar' AND table_schema = 'public') THEN
      ALTER TABLE public.messages ADD COLUMN created_by_avatar TEXT;
      RAISE NOTICE '✓ Added created_by_avatar to messages table';
    ELSE
      RAISE NOTICE '- created_by_avatar already exists in messages table';
    END IF;

    RAISE NOTICE '✅ Messages table creator fields setup complete';
    
  ELSE
    RAISE NOTICE '⚠️ Messages table does not exist in public schema';
  END IF;
END $$;

-- Create function to automatically populate creator info for messages
CREATE OR REPLACE FUNCTION populate_message_creator_info()
RETURNS TRIGGER AS $$
DECLARE
  user_profile RECORD;
BEGIN
  -- Get current user's profile information
  SELECT 
    COALESCE(first_name || ' ' || last_name, email, 'Unknown User') as full_name,
    COALESCE(profile_picture_url, avatar_url) as avatar_url
  INTO user_profile
  FROM public.user_profiles 
  WHERE user_id = auth.uid()
  LIMIT 1;

  -- If no profile found, try to get from auth.users
  IF user_profile.full_name IS NULL THEN
    SELECT 
      COALESCE(raw_user_meta_data->>'full_name', email, 'Unknown User') as full_name,
      COALESCE(raw_user_meta_data->>'avatar_url', '') as avatar_url
    INTO user_profile
    FROM auth.users 
    WHERE id = auth.uid()
    LIMIT 1;
  END IF;

  -- Set creator fields
  NEW.created_by_user_id := auth.uid();
  NEW.created_by_name := COALESCE(user_profile.full_name, 'Unknown User');
  NEW.created_by_avatar := user_profile.avatar_url;

  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger for messages table if it exists
DO $$
BEGIN
  IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'messages' AND table_schema = 'public') THEN
    -- Drop existing trigger if it exists
    DROP TRIGGER IF EXISTS set_creator_info_messages ON public.messages;
    
    -- Create new trigger
    CREATE TRIGGER set_creator_info_messages
      BEFORE INSERT ON public.messages
      FOR EACH ROW
      EXECUTE FUNCTION populate_message_creator_info();
      
    RAISE NOTICE '✓ Created trigger for messages table';
  END IF;
END $$;

-- Grant necessary permissions
GRANT EXECUTE ON FUNCTION populate_message_creator_info() TO authenticated;

RAISE NOTICE '=== MESSAGES CREATOR FIELDS QUICK FIX COMPLETE ===';
RAISE NOTICE '✅ Messages table should now work with creator fields';
RAISE NOTICE '📝 Next: Run the full add_creator_fields.sql script for other tables';
