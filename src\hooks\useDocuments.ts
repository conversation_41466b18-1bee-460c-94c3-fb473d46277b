import { useState, useEffect, useCallback } from 'react';
import { DocumentService, Document, DEFAULT_COMPANY_INFO } from '@/lib/documents';
import { useLocalStorageFallback } from '@/lib/supabase';
import { useToast } from '@/components/ui/use-toast';

interface UseDocumentsReturn {
  documents: Document[];
  loading: boolean;
  error: string | null;
  addDocument: (document: Omit<Document, 'id' | 'date_added' | 'last_modified' | 'created_at'>) => Promise<Document>;
  updateDocument: (id: string, updates: Partial<Omit<Document, 'id' | 'date_added' | 'created_at'>>) => Promise<Document>;
  deleteDocument: (id: string) => Promise<void>;
  refreshDocuments: () => Promise<void>;
  isOnline: boolean;
  syncStatus: 'synced' | 'syncing' | 'offline' | 'error';
}

// Local storage fallback functions
const loadFromLocalStorage = (): Document[] => {
  try {
    const saved = localStorage.getItem('documents');
    if (saved) {
      return JSON.parse(saved);
    }
  } catch (error) {
    console.error('Error loading documents from localStorage:', error);
  }
  
  // No default documents - use only Supabase data
  return [];
};

const saveToLocalStorage = (documents: Document[]) => {
  try {
    localStorage.setItem('documents', JSON.stringify(documents));
  } catch (error) {
    console.error('Error saving documents to localStorage:', error);
  }
};

export const useDocuments = (): UseDocumentsReturn => {
  const { toast } = useToast();
  const [documents, setDocuments] = useState<Document[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isOnline, setIsOnline] = useState(navigator.onLine);
  const [syncStatus, setSyncStatus] = useState<'synced' | 'syncing' | 'offline' | 'error'>('synced');
  
  const useFallback = useLocalStorageFallback();

  // Monitor online status
  useEffect(() => {
    const handleOnline = () => {
      setIsOnline(true);
      if (!useFallback) {
        refreshDocuments();
      }
    };
    
    const handleOffline = () => {
      setIsOnline(false);
      setSyncStatus('offline');
    };

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, [useFallback]);

  // Load documents
  const refreshDocuments = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      setSyncStatus('syncing');

      if (useFallback || !isOnline) {
        // Use localStorage fallback
        const localDocuments = loadFromLocalStorage();
        setDocuments(localDocuments);
        setSyncStatus(useFallback ? 'synced' : 'offline');
      } else {
        // Use Supabase
        const data = await DocumentService.getDocuments();
        setDocuments(data);
        setSyncStatus('synced');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to load documents';
      setError(errorMessage);
      setSyncStatus('error');
      
      // Fallback to localStorage on error
      if (!useFallback) {
        const localDocuments = loadFromLocalStorage();
        setDocuments(localDocuments);
        toast({
          title: "Connection Error",
          description: "Using offline data. Changes will sync when connection is restored.",
          variant: "destructive",
        });
      }
    } finally {
      setLoading(false);
    }
  }, [useFallback, isOnline, toast]);

  // Add document
  const addDocument = useCallback(async (documentData: Omit<Document, 'id' | 'date_added' | 'last_modified' | 'created_at'>): Promise<Document> => {
    try {
      setSyncStatus('syncing');
      
      if (useFallback || !isOnline) {
        // Use localStorage fallback
        const newDocument: Document = {
          id: Date.now().toString(),
          ...documentData,
          date_added: new Date().toISOString(),
          last_modified: new Date().toISOString(),
          created_at: new Date().toISOString()
        };
        
        const updatedDocuments = [newDocument, ...documents];
        setDocuments(updatedDocuments);
        saveToLocalStorage(updatedDocuments);
        setSyncStatus(useFallback ? 'synced' : 'offline');
        
        return newDocument;
      } else {
        // Use Supabase
        const newDocument = await DocumentService.addDocument(documentData);
        setDocuments(prev => [newDocument, ...prev]);
        setSyncStatus('synced');
        
        return newDocument;
      }
    } catch (err) {
      setSyncStatus('error');
      const errorMessage = err instanceof Error ? err.message : 'Failed to add document';
      throw new Error(errorMessage);
    }
  }, [documents, useFallback, isOnline]);

  // Update document
  const updateDocument = useCallback(async (id: string, updates: Partial<Omit<Document, 'id' | 'date_added' | 'created_at'>>): Promise<Document> => {
    try {
      setSyncStatus('syncing');
      
      if (useFallback || !isOnline) {
        // Use localStorage fallback
        const updatedDocuments = documents.map(document => {
          if (document.id === id) {
            return {
              ...document,
              ...updates,
              last_modified: new Date().toISOString()
            };
          }
          return document;
        });
        
        setDocuments(updatedDocuments);
        saveToLocalStorage(updatedDocuments);
        setSyncStatus(useFallback ? 'synced' : 'offline');
        
        const updatedDocument = updatedDocuments.find(document => document.id === id)!;
        return updatedDocument;
      } else {
        // Use Supabase
        const updatedDocument = await DocumentService.updateDocument(id, updates);
        setDocuments(prev => prev.map(document => document.id === id ? updatedDocument : document));
        setSyncStatus('synced');
        
        return updatedDocument;
      }
    } catch (err) {
      setSyncStatus('error');
      const errorMessage = err instanceof Error ? err.message : 'Failed to update document';
      throw new Error(errorMessage);
    }
  }, [documents, useFallback, isOnline]);

  // Delete document
  const deleteDocument = useCallback(async (id: string): Promise<void> => {
    try {
      setSyncStatus('syncing');
      
      if (useFallback || !isOnline) {
        // Use localStorage fallback
        const updatedDocuments = documents.filter(document => document.id !== id);
        setDocuments(updatedDocuments);
        saveToLocalStorage(updatedDocuments);
        setSyncStatus(useFallback ? 'synced' : 'offline');
      } else {
        // Use Supabase
        await DocumentService.deleteDocument(id);
        setDocuments(prev => prev.filter(document => document.id !== id));
        setSyncStatus('synced');
      }
    } catch (err) {
      setSyncStatus('error');
      const errorMessage = err instanceof Error ? err.message : 'Failed to delete document';
      throw new Error(errorMessage);
    }
  }, [documents, useFallback, isOnline]);

  // Initial load
  useEffect(() => {
    refreshDocuments();
  }, [refreshDocuments]);

  // Set up real-time subscription (only if using Supabase)
  useEffect(() => {
    if (useFallback || !isOnline) return;

    const subscription = DocumentService.subscribeToChanges((payload) => {
      console.log('Real-time document update:', payload);
      
      switch (payload.eventType) {
        case 'INSERT':
          setDocuments(prev => [payload.new, ...prev]);
          break;
        case 'UPDATE':
          setDocuments(prev => prev.map(document => document.id === payload.new.id ? payload.new : document));
          break;
        case 'DELETE':
          setDocuments(prev => prev.filter(document => document.id !== payload.old.id));
          break;
      }
    });

    return () => {
      subscription.unsubscribe();
    };
  }, [useFallback, isOnline]);

  return {
    documents,
    loading,
    error,
    addDocument,
    updateDocument,
    deleteDocument,
    refreshDocuments,
    isOnline,
    syncStatus
  };
};
