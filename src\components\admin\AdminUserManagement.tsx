import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Switch } from '@/components/ui/switch';
import { Textarea } from '@/components/ui/textarea';
import { useToast } from '@/components/ui/use-toast';
import { UserService, UserProfile, UserRole } from '@/lib/userService';
import { useAuth } from '@/contexts/AuthContext';
import { supabase } from '@/lib/supabase';
import { useUserCreation } from '@/hooks/useUserCreation';
import { hasPermission, getRoleDisplayInfo } from '@/lib/permissions';
import {
  Plus, Edit, Trash2, UserCheck, UserX, Shield, AlertTriangle, Mail,
  Search, Filter, MoreHorizontal, Eye, Key, Clock, Users,
  Download, Upload, RefreshCw, Settings
} from 'lucide-react';

const AdminUserManagement = () => {
  const { toast } = useToast();
  const { user, profile } = useAuth();
  const { loading: userCreationLoading, loadRoles: loadUserCreationRoles, createUser } = useUserCreation();
  const [users, setUsers] = useState<UserProfile[]>([]);
  const [filteredUsers, setFilteredUsers] = useState<UserProfile[]>([]);
  const [roles, setRoles] = useState<UserRole[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterRole, setFilterRole] = useState('all');
  const [filterStatus, setFilterStatus] = useState('all');
  const [selectedUser, setSelectedUser] = useState<UserProfile | null>(null);
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isViewDialogOpen, setIsViewDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [createdUserCredentials, setCreatedUserCredentials] = useState<any>(null);
  const [activeTab, setActiveTab] = useState('overview');

  const [formData, setFormData] = useState({
    email: '',
    first_name: '',
    last_name: '',
    phone: '',
    role_name: 'client',
    department: '',
    job_title: '',
    notes: '',
    custom_password: '',
    use_custom_password: false
  });

  const [editFormData, setEditFormData] = useState({
    id: '',
    email: '',
    first_name: '',
    last_name: '',
    phone: '',
    role_name: '',
    department: '',
    job_title: '',
    is_active: true,
    notes: ''
  });

  // Check if current user is admin
  const isAdmin = profile?.role?.role_name === 'admin';

  useEffect(() => {
    if (isAdmin) {
      loadUsers();
      loadRoles();
      loadUserCreationRoles(); // Load roles for the useUserCreation hook
    }
  }, [isAdmin]);

  // Filter users based on search and filters
  useEffect(() => {
    let filtered = users;

    // Search filter
    if (searchTerm) {
      filtered = filtered.filter(user =>
        user.first_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        user.last_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        user.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
        user.department?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        user.job_title?.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Role filter
    if (filterRole !== 'all') {
      filtered = filtered.filter(user => user.role.role_name === filterRole);
    }

    // Status filter
    if (filterStatus !== 'all') {
      if (filterStatus === 'active') {
        filtered = filtered.filter(user => user.is_active);
      } else if (filterStatus === 'inactive') {
        filtered = filtered.filter(user => !user.is_active);
      } else if (filterStatus === 'setup_required') {
        filtered = filtered.filter(user => user.requires_password_setup);
      }
    }

    setFilteredUsers(filtered);
  }, [users, searchTerm, filterRole, filterStatus]);

  const loadUsers = async (forceRefresh = false) => {
    try {
      console.log('Loading users...', forceRefresh ? '(forced refresh)' : '');
      const data = await UserService.getAllUsers();
      console.log('Users loaded from database:', data.length, 'users');
      console.log('User emails:', data.map(u => u.email));
      setUsers(data);
    } catch (error) {
      console.error('Failed to load users:', error);
      toast({
        title: "Error",
        description: "Failed to load users",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const loadRoles = async () => {
    try {
      console.log('Loading roles...');
      const data = await UserService.getAllRoles();
      console.log('Roles loaded:', data);
      setRoles(data);
    } catch (error) {
      console.error('Failed to load roles:', error);
      toast({
        title: "Error",
        description: "Failed to load user roles",
        variant: "destructive",
      });
    }
  };

  const handleCreateUser = async () => {
    try {
      // Use the working user creation logic from standalone register
      const createResult = await createUser({
        email: formData.email,
        firstName: formData.first_name,
        lastName: formData.last_name,
        role: formData.role_name,
        password: formData.use_custom_password && formData.custom_password ? formData.custom_password : 'TempPass123!',
        phone: formData.phone,
        department: formData.department,
        jobTitle: formData.job_title
      });

      if (!createResult.success) {
        throw new Error(createResult.error);
      }

      // Store the created user credentials for display
      setCreatedUserCredentials({
        email: formData.email,
        password: createResult.credentials?.password || 'TempPass123!',
        role: formData.role_name,
        full_name: `${formData.first_name} ${formData.last_name}`,
        method: createResult.method
      });

      toast({
        title: "User Created Successfully",
        description: `Account created for ${formData.first_name} ${formData.last_name}`,
      });

      // Show credentials to admin
      const passwordType = formData.use_custom_password ? 'Custom' : 'Auto-generated';
      const credentials = `Login Credentials for ${formData.first_name} ${formData.last_name}:
Email: ${formData.email}
Password: ${createResult.credentials?.password || 'TempPass123!'} (${passwordType})
Role: ${formData.role_name}
Method: ${createResult.method || 'Unknown'}

Please share these credentials securely with the user.`;

      // Copy credentials to clipboard
      if (navigator.clipboard) {
        navigator.clipboard.writeText(credentials);
        toast({
          title: "Credentials Copied",
          description: `User credentials copied to clipboard. Password: ${createResult.credentials?.password || 'TempPass123!'}`,
        });
      }

      setIsCreateDialogOpen(false);
      resetCreateForm();
      loadUsers();
    } catch (error: any) {
      console.error('User creation error:', error);
      toast({
        title: "Error",
        description: error.message || "Failed to create user",
        variant: "destructive",
      });
    }
  };

  const handleEditUser = async () => {
    try {
      await UserService.updateUser(editFormData.id, {
        first_name: editFormData.first_name,
        last_name: editFormData.last_name,
        phone: editFormData.phone,
        role_name: editFormData.role_name,
        department: editFormData.department,
        job_title: editFormData.job_title,
        is_active: editFormData.is_active
      });

      toast({
        title: "User Updated",
        description: "User information updated successfully.",
      });

      setIsEditDialogOpen(false);
      setSelectedUser(null);
      loadUsers();
    } catch (error: any) {
      toast({
        title: "Error",
        description: error.message || "Failed to update user",
        variant: "destructive",
      });
    }
  };

  const handleDeleteUser = async () => {
    if (!selectedUser) return;

    console.log('=== STARTING USER DELETION ===');
    console.log('User to delete:', selectedUser.id, selectedUser.email);
    console.log('Current users count:', users.length);

    try {
      // Show loading state
      toast({
        title: "Deleting User",
        description: "Please wait while we delete the user...",
      });

      await UserService.deleteUser(selectedUser.id);

      console.log('UserService.deleteUser completed successfully');

      // Skip database verification for now - just trust the deletion worked
      console.log('Skipping database verification - trusting deletion succeeded');

      // Alternative: Try a simple verification
      try {
        console.log('Attempting simple verification...');
        const { data: verifyData, error: verifyError } = await supabase
          .from('user_profiles')
          .select('id')
          .eq('id', selectedUser.id)
          .maybeSingle();

        if (verifyError) {
          console.log('Verification error (expected):', verifyError);
        }

        if (verifyData) {
          console.warn('User still exists in database, but continuing anyway');
        } else {
          console.log('✓ User not found in verification check');
        }
      } catch (verifyError) {
        console.log('Verification check failed, but continuing:', verifyError);
      }

      // Reload users and update UI optimistically
      console.log('Reloading users after deletion...');

      // First, optimistically remove user from current list
      const updatedUsers = users.filter(u => u.id !== selectedUser.id);
      setUsers(updatedUsers);
      console.log('Optimistically removed user from list');

      // Then reload from database
      try {
        await loadUsers(true); // Force refresh
        console.log('Users reloaded from database');
      } catch (loadError) {
        console.warn('Failed to reload users, but deletion may have succeeded:', loadError);
        // Keep the optimistically updated list
      }

      // Wait a moment for state to update
      await new Promise(resolve => setTimeout(resolve, 200));

      console.log('Final user count:', users.length);

      // Check if the user still exists in the current list
      const userStillExists = users.some(u => u.id === selectedUser.id);
      console.log('User still exists in current list:', userStillExists);

      if (userStillExists) {
        console.warn('User still in list, but deletion may have worked in database');
        // Don't throw error - just log warning
      }

      toast({
        title: "User Deleted",
        description: `User ${selectedUser.first_name} ${selectedUser.last_name} has been permanently deleted.`,
      });

      setIsDeleteDialogOpen(false);
      setSelectedUser(null);

      console.log('=== USER DELETION COMPLETED ===');

    } catch (error: any) {
      console.error('=== USER DELETION FAILED ===');
      console.error('Delete user error:', error);
      toast({
        title: "Delete Failed",
        description: error.message || "Failed to delete user. Please try again.",
        variant: "destructive",
      });
    }
  };

  const handleToggleUserStatus = async (userId: string, currentStatus: boolean) => {
    try {
      await UserService.updateUser(userId, { is_active: !currentStatus });

      toast({
        title: "Status Updated",
        description: `User ${!currentStatus ? 'activated' : 'deactivated'} successfully.`,
      });

      loadUsers();
    } catch (error: any) {
      toast({
        title: "Error",
        description: error.message || "Failed to update user status",
        variant: "destructive",
      });
    }
  };

  const handleTestAdminConfig = async () => {
    console.log('Testing admin email configuration...');
    try {
      await UserService.testAdminEmailConfig();
    } catch (error) {
      console.error('Admin config test failed:', error);
    }
  };

  const handleResendSetupEmail = async (userId: string) => {
    console.log('Attempting to resend setup email for user:', userId);

    try {
      const result = await UserService.resendSetupEmail(userId);

      console.log('Setup email process completed for user:', userId);

      if (result.emailContent) {
        // Email content was generated
        const setupUrl = `https://your-domain.com/setup-password?token=${result.emailContent.setup_token}&email=${result.emailContent.to_email}`;

        toast({
          title: "Setup Email Generated",
          description: (
            <div className="space-y-2">
              <p>Email content generated successfully!</p>
              <p className="text-sm text-gray-600">
                Check browser console for full details, or copy the setup URL below:
              </p>
              <div className="bg-gray-100 p-2 rounded text-xs font-mono break-all">
                {setupUrl}
              </div>
              <p className="text-xs text-orange-600">
                Configure SMTP in Supabase to send emails automatically
              </p>
            </div>
          ),
          duration: 10000, // Show for 10 seconds
        });

        // Also copy to clipboard for convenience
        try {
          if (navigator.clipboard && window.isSecureContext) {
            await navigator.clipboard.writeText(setupUrl);
            console.log('Setup URL copied to clipboard');
          } else {
            // Fallback for non-HTTPS environments
            const textArea = document.createElement('textarea');
            textArea.value = setupUrl;
            document.body.appendChild(textArea);
            textArea.select();
            document.execCommand('copy');
            document.body.removeChild(textArea);
            console.log('Setup URL copied to clipboard (fallback method)');
          }
        } catch (clipboardError) {
          console.log('Could not copy to clipboard:', clipboardError);
        }
      } else {
        toast({
          title: "Setup Email Updated",
          description: result.status || "Password setup instructions have been updated.",
        });
      }

      // Refresh the user list to show updated status
      await loadUsers();

    } catch (error: any) {
      console.error('Resend setup email error:', error);
      toast({
        title: "Email Generation Failed",
        description: error.message || "Failed to generate setup email. Please try again.",
        variant: "destructive",
      });
    }
  };

  const resetCreateForm = () => {
    setFormData({
      email: '',
      first_name: '',
      last_name: '',
      phone: '',
      role_name: 'client',
      department: '',
      job_title: '',
      notes: '',
      custom_password: '',
      use_custom_password: false
    });
  };

  const openEditDialog = (user: UserProfile) => {
    setSelectedUser(user);
    setEditFormData({
      id: user.id,
      email: user.email,
      first_name: user.first_name,
      last_name: user.last_name,
      phone: user.phone || '',
      role_name: user.role.role_name,
      department: user.department || '',
      job_title: user.job_title || '',
      is_active: user.is_active,
      notes: ''
    });
    setIsEditDialogOpen(true);
  };

  const openViewDialog = (user: UserProfile) => {
    setSelectedUser(user);
    setIsViewDialogOpen(true);
  };

  const openDeleteDialog = (user: UserProfile) => {
    setSelectedUser(user);
    setIsDeleteDialogOpen(true);
  };

  const getRoleBadgeColor = (roleName: string) => {
    const roleInfo = getRoleDisplayInfo(roleName);
    return roleInfo.color;
  };

  if (!isAdmin) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Shield className="h-5 w-5" />
            Access Denied
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Alert>
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>
              You don't have permission to access user management. Only administrators can create and manage user accounts.
            </AlertDescription>
          </Alert>
        </CardContent>
      </Card>
    );
  }

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Loading Users...</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex justify-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  const getUserStats = () => {
    const totalUsers = users.length;
    const activeUsers = users.filter(u => u.is_active).length;
    const pendingSetup = users.filter(u => u.requires_password_setup).length;
    const roleDistribution = roles.map(role => ({
      role: role.role_name,
      count: users.filter(u => u.role.role_name === role.role_name).length
    }));

    return { totalUsers, activeUsers, pendingSetup, roleDistribution };
  };

  const stats = getUserStats();

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold flex items-center gap-2">
            <Shield className="h-8 w-8" />
            User Management
          </h1>
          <p className="text-gray-600 mt-1">
            Create and manage user accounts. Only administrators can access this section.
          </p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" onClick={() => loadUsers()}>
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
          <Button variant="outline" onClick={handleTestAdminConfig}>
            🔧 Test Email Config
          </Button>
          <Button onClick={() => setIsCreateDialogOpen(true)}>
            <Plus className="h-4 w-4 mr-2" />
            Create User
          </Button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <Users className="h-8 w-8 text-blue-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Total Users</p>
                <p className="text-2xl font-bold">{stats.totalUsers}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <UserCheck className="h-8 w-8 text-green-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Active Users</p>
                <p className="text-2xl font-bold">{stats.activeUsers}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <Clock className="h-8 w-8 text-orange-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Pending Setup</p>
                <p className="text-2xl font-bold">{stats.pendingSetup}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <Shield className="h-8 w-8 text-purple-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Administrators</p>
                <p className="text-2xl font-bold">
                  {stats.roleDistribution.find(r => r.role === 'admin')?.count || 0}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Main Content */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="users">All Users</TabsTrigger>
          <TabsTrigger value="roles">Role Distribution</TabsTrigger>
          <TabsTrigger value="activity">Recent Activity</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>User Overview</CardTitle>
              <CardDescription>
                Quick overview of user accounts and their status
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {/* Search and Filters */}
                <div className="flex flex-col sm:flex-row gap-4">
                  <div className="flex-1">
                    <div className="relative">
                      <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                      <Input
                        placeholder="Search users..."
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                        className="pl-10"
                      />
                    </div>
                  </div>
                  <Select value={filterRole} onValueChange={setFilterRole}>
                    <SelectTrigger className="w-[180px]">
                      <SelectValue placeholder="Filter by role" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Roles</SelectItem>
                      {roles.map((role) => (
                        <SelectItem key={role.id} value={role.role_name}>
                          {getRoleDisplayInfo(role.role_name).label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <Select value={filterStatus} onValueChange={setFilterStatus}>
                    <SelectTrigger className="w-[180px]">
                      <SelectValue placeholder="Filter by status" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Status</SelectItem>
                      <SelectItem value="active">Active</SelectItem>
                      <SelectItem value="inactive">Inactive</SelectItem>
                      <SelectItem value="setup_required">Setup Required</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                {/* Users Table */}
                <div className="border rounded-lg">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>User</TableHead>
                        <TableHead>Role</TableHead>
                        <TableHead>Department</TableHead>
                        <TableHead>Status</TableHead>
                        <TableHead>Created</TableHead>
                        <TableHead className="text-right">Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {filteredUsers.slice(0, 10).map((user) => (
                        <TableRow key={user.id}>
                          <TableCell>
                            <div>
                              <div className="font-medium">
                                {user.first_name} {user.last_name}
                              </div>
                              <div className="text-sm text-gray-500">{user.email}</div>
                            </div>
                          </TableCell>
                          <TableCell>
                            <Badge className={getRoleDisplayInfo(user.role.role_name).color}>
                              {getRoleDisplayInfo(user.role.role_name).label}
                            </Badge>
                          </TableCell>
                          <TableCell>{user.department || '-'}</TableCell>
                          <TableCell>
                            <div className="flex items-center gap-2">
                              {user.is_active ? (
                                <UserCheck className="h-4 w-4 text-green-600" />
                              ) : (
                                <UserX className="h-4 w-4 text-red-600" />
                              )}
                              <span className={user.is_active ? 'text-green-600' : 'text-red-600'}>
                                {user.is_active ? 'Active' : 'Inactive'}
                              </span>
                              {user.requires_password_setup && (
                                <Badge variant="outline" className="text-orange-600">
                                  Setup Required
                                </Badge>
                              )}
                            </div>
                          </TableCell>
                          <TableCell>
                            {new Date(user.created_at).toLocaleDateString()}
                          </TableCell>
                          <TableCell className="text-right">
                            <div className="flex items-center justify-end gap-2">
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => openViewDialog(user)}
                              >
                                <Eye className="h-4 w-4" />
                              </Button>
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => openEditDialog(user)}
                              >
                                <Edit className="h-4 w-4" />
                              </Button>
                              {user.requires_password_setup && (
                                <Button
                                  variant="outline"
                                  size="sm"
                                  onClick={() => handleResendSetupEmail(user.id)}
                                >
                                  <Mail className="h-4 w-4" />
                                </Button>
                              )}
                            </div>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>

                {filteredUsers.length > 10 && (
                  <div className="text-center">
                    <Button variant="outline" onClick={() => setActiveTab('users')}>
                      View All {filteredUsers.length} Users
                    </Button>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="users" className="space-y-4">
          <Card>
            <CardHeader>
              <div className="flex justify-between items-center">
                <div>
                  <CardTitle>All Users</CardTitle>
                  <CardDescription>
                    Complete list of all user accounts in the system
                  </CardDescription>
                </div>
                <div className="flex gap-2">
                  <Button variant="outline" size="sm">
                    <Download className="h-4 w-4 mr-2" />
                    Export
                  </Button>
                  <Button onClick={() => setIsCreateDialogOpen(true)}>
                    <Plus className="h-4 w-4 mr-2" />
                    Create User
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {/* Search and Filters */}
                <div className="flex flex-col sm:flex-row gap-4">
                  <div className="flex-1">
                    <div className="relative">
                      <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                      <Input
                        placeholder="Search users..."
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                        className="pl-10"
                      />
                    </div>
                  </div>
                  <Select value={filterRole} onValueChange={setFilterRole}>
                    <SelectTrigger className="w-[180px]">
                      <SelectValue placeholder="Filter by role" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Roles</SelectItem>
                      {roles.map((role) => (
                        <SelectItem key={role.id} value={role.role_name}>
                          {getRoleDisplayInfo(role.role_name).label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <Select value={filterStatus} onValueChange={setFilterStatus}>
                    <SelectTrigger className="w-[180px]">
                      <SelectValue placeholder="Filter by status" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Status</SelectItem>
                      <SelectItem value="active">Active</SelectItem>
                      <SelectItem value="inactive">Inactive</SelectItem>
                      <SelectItem value="setup_required">Setup Required</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                {/* Full Users Table */}
                <div className="border rounded-lg">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>User</TableHead>
                        <TableHead>Role</TableHead>
                        <TableHead>Department</TableHead>
                        <TableHead>Job Title</TableHead>
                        <TableHead>Status</TableHead>
                        <TableHead>Created</TableHead>
                        <TableHead className="text-right">Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {filteredUsers.map((user) => (
                        <TableRow key={user.id}>
                          <TableCell>
                            <div>
                              <div className="font-medium">
                                {user.first_name} {user.last_name}
                              </div>
                              <div className="text-sm text-gray-500">{user.email}</div>
                              {user.phone && (
                                <div className="text-sm text-gray-500">{user.phone}</div>
                              )}
                            </div>
                          </TableCell>
                          <TableCell>
                            <Badge className={getRoleDisplayInfo(user.role.role_name).color}>
                              {getRoleDisplayInfo(user.role.role_name).label}
                            </Badge>
                          </TableCell>
                          <TableCell>{user.department || '-'}</TableCell>
                          <TableCell>{user.job_title || '-'}</TableCell>
                          <TableCell>
                            <div className="flex items-center gap-2">
                              <Switch
                                checked={user.is_active}
                                onCheckedChange={() => handleToggleUserStatus(user.id, user.is_active)}
                              />
                              <span className={user.is_active ? 'text-green-600' : 'text-red-600'}>
                                {user.is_active ? 'Active' : 'Inactive'}
                              </span>
                              {user.requires_password_setup && (
                                <Badge variant="outline" className="text-orange-600">
                                  Setup Required
                                </Badge>
                              )}
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className="text-sm">
                              <div>{new Date(user.created_at).toLocaleDateString()}</div>
                              <div className="text-gray-500">
                                {new Date(user.created_at).toLocaleTimeString()}
                              </div>
                            </div>
                          </TableCell>
                          <TableCell className="text-right">
                            <div className="flex items-center justify-end gap-2">
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => openViewDialog(user)}
                              >
                                <Eye className="h-4 w-4" />
                              </Button>
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => openEditDialog(user)}
                              >
                                <Edit className="h-4 w-4" />
                              </Button>
                              {user.requires_password_setup && (
                                <Button
                                  variant="outline"
                                  size="sm"
                                  onClick={() => handleResendSetupEmail(user.id)}
                                  title="Resend setup email"
                                >
                                  <Mail className="h-4 w-4" />
                                </Button>
                              )}
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => openDeleteDialog(user)}
                                className="text-red-600 hover:text-red-700"
                              >
                                <Trash2 className="h-4 w-4" />
                              </Button>
                            </div>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>

                {filteredUsers.length === 0 && (
                  <div className="text-center py-8">
                    <Users className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                    <h3 className="text-lg font-medium text-gray-900 mb-2">No users found</h3>
                    <p className="text-gray-500 mb-4">
                      {searchTerm || filterRole !== 'all' || filterStatus !== 'all'
                        ? 'Try adjusting your search or filters'
                        : 'Get started by creating your first user account'
                      }
                    </p>
                    {(!searchTerm && filterRole === 'all' && filterStatus === 'all') && (
                      <Button onClick={() => setIsCreateDialogOpen(true)}>
                        <Plus className="h-4 w-4 mr-2" />
                        Create First User
                      </Button>
                    )}
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="roles" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Role Distribution</CardTitle>
              <CardDescription>
                Overview of user roles and their permissions
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {stats.roleDistribution.map((roleData) => {
                  const roleInfo = getRoleDisplayInfo(roleData.role);
                  return (
                    <div key={roleData.role} className="flex items-center justify-between p-4 border rounded-lg">
                      <div className="flex items-center gap-4">
                        <Badge className={roleInfo.color}>
                          {roleInfo.label}
                        </Badge>
                        <div>
                          <p className="font-medium">{roleInfo.label}</p>
                          <p className="text-sm text-gray-500">{roleInfo.description}</p>
                        </div>
                      </div>
                      <div className="text-right">
                        <p className="text-2xl font-bold">{roleData.count}</p>
                        <p className="text-sm text-gray-500">
                          {stats.totalUsers > 0 ? Math.round((roleData.count / stats.totalUsers) * 100) : 0}%
                        </p>
                      </div>
                    </div>
                  );
                })}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="activity" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Recent Activity</CardTitle>
              <CardDescription>
                Recent user management activities and system events
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <Alert>
                  <AlertTriangle className="h-4 w-4" />
                  <AlertDescription>
                    Activity logging is not yet implemented. This feature will show recent user creation,
                    modifications, and login activities.
                  </AlertDescription>
                </Alert>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Create User Dialog */}
      {/* Create User Dialog */}
      <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
              <DialogContent className="sm:max-w-[425px]">
                <DialogHeader>
                  <DialogTitle>Create New User</DialogTitle>
                  <DialogDescription>
                    Create a new user account with auto-generated credentials. You'll receive the login details to share with the user.
                  </DialogDescription>
                  {/* Debug info */}
                  <div className="text-xs text-gray-500 mt-2">
                    ✅ New System: {roles.length} roles loaded: {roles.map(r => r.role_name).join(', ')}
                  </div>
                </DialogHeader>
                <div className="grid gap-4 py-4">
                  <div className="grid grid-cols-4 items-center gap-4">
                    <Label htmlFor="email" className="text-right">
                      Email *
                    </Label>
                    <Input
                      id="email"
                      type="email"
                      value={formData.email}
                      onChange={(e) => setFormData(prev => ({ ...prev, email: e.target.value }))}
                      className="col-span-3"
                      placeholder="<EMAIL>"
                    />
                  </div>
                  <div className="grid grid-cols-4 items-center gap-4">
                    <Label htmlFor="first_name" className="text-right">
                      First Name *
                    </Label>
                    <Input
                      id="first_name"
                      value={formData.first_name}
                      onChange={(e) => setFormData(prev => ({ ...prev, first_name: e.target.value }))}
                      className="col-span-3"
                    />
                  </div>
                  <div className="grid grid-cols-4 items-center gap-4">
                    <Label htmlFor="last_name" className="text-right">
                      Last Name *
                    </Label>
                    <Input
                      id="last_name"
                      value={formData.last_name}
                      onChange={(e) => setFormData(prev => ({ ...prev, last_name: e.target.value }))}
                      className="col-span-3"
                    />
                  </div>
                  <div className="grid grid-cols-4 items-center gap-4">
                    <Label htmlFor="role" className="text-right">
                      Role *
                    </Label>
                    <Select value={formData.role_name} onValueChange={(value) => setFormData(prev => ({ ...prev, role_name: value }))}>
                      <SelectTrigger className="col-span-3">
                        <SelectValue placeholder="Select role" />
                      </SelectTrigger>
                      <SelectContent>
                        {roles.length === 0 ? (
                          <SelectItem value="" disabled>
                            No roles available - check database setup
                          </SelectItem>
                        ) : (
                          roles.map((role) => (
                            <SelectItem key={role.id} value={role.role_name}>
                              {getRoleDisplayInfo(role.role_name).label}
                            </SelectItem>
                          ))
                        )}
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="grid grid-cols-4 items-center gap-4">
                    <Label htmlFor="department" className="text-right">
                      Department
                    </Label>
                    <Input
                      id="department"
                      value={formData.department}
                      onChange={(e) => setFormData(prev => ({ ...prev, department: e.target.value }))}
                      className="col-span-3"
                    />
                  </div>
                  <div className="grid grid-cols-4 items-center gap-4">
                    <Label htmlFor="job_title" className="text-right">
                      Job Title
                    </Label>
                    <Input
                      id="job_title"
                      value={formData.job_title}
                      onChange={(e) => setFormData(prev => ({ ...prev, job_title: e.target.value }))}
                      className="col-span-3"
                    />
                  </div>
                  <div className="grid grid-cols-4 items-center gap-4">
                    <Label htmlFor="phone" className="text-right">
                      Phone
                    </Label>
                    <Input
                      id="phone"
                      value={formData.phone}
                      onChange={(e) => setFormData(prev => ({ ...prev, phone: e.target.value }))}
                      className="col-span-3"
                    />
                  </div>

                  {/* Password Options */}
                  <div className="grid grid-cols-4 items-center gap-4">
                    <Label className="text-right">Password</Label>
                    <div className="col-span-3 space-y-3">
                      <div className="flex items-center space-x-2">
                        <Switch
                          id="use_custom_password"
                          checked={formData.use_custom_password}
                          onCheckedChange={(checked) => setFormData(prev => ({
                            ...prev,
                            use_custom_password: checked,
                            custom_password: checked ? prev.custom_password : ''
                          }))}
                        />
                        <Label htmlFor="use_custom_password" className="text-sm">
                          Set custom password
                        </Label>
                      </div>
                      {formData.use_custom_password ? (
                        <Input
                          type="password"
                          placeholder="Enter custom password"
                          value={formData.custom_password}
                          onChange={(e) => setFormData(prev => ({ ...prev, custom_password: e.target.value }))}
                        />
                      ) : (
                        <p className="text-sm text-muted-foreground">
                          🔐 A secure password will be auto-generated
                        </p>
                      )}
                    </div>
                  </div>

                  <div className="grid grid-cols-4 items-center gap-4">
                    <Label htmlFor="notes" className="text-right">
                      Notes
                    </Label>
                    <Textarea
                      id="notes"
                      value={formData.notes}
                      onChange={(e) => setFormData(prev => ({ ...prev, notes: e.target.value }))}
                      className="col-span-3"
                      placeholder="Additional notes about this user..."
                      rows={3}
                    />
                  </div>
                </div>
                <DialogFooter>
                  <Button variant="outline" onClick={() => setIsCreateDialogOpen(false)}>
                    Cancel
                  </Button>
                  <Button
                    onClick={handleCreateUser}
                    disabled={
                      !formData.email ||
                      !formData.first_name ||
                      !formData.last_name ||
                      (formData.use_custom_password && !formData.custom_password)
                    }
                  >
                    Create User
                  </Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>

      {/* Edit User Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>Edit User</DialogTitle>
            <DialogDescription>
              Update user information and settings.
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="edit_email" className="text-right">
                Email
              </Label>
              <Input
                id="edit_email"
                type="email"
                value={editFormData.email}
                disabled
                className="col-span-3 bg-gray-50"
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="edit_first_name" className="text-right">
                First Name *
              </Label>
              <Input
                id="edit_first_name"
                value={editFormData.first_name}
                onChange={(e) => setEditFormData(prev => ({ ...prev, first_name: e.target.value }))}
                className="col-span-3"
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="edit_last_name" className="text-right">
                Last Name *
              </Label>
              <Input
                id="edit_last_name"
                value={editFormData.last_name}
                onChange={(e) => setEditFormData(prev => ({ ...prev, last_name: e.target.value }))}
                className="col-span-3"
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="edit_role" className="text-right">
                Role *
              </Label>
              <Select value={editFormData.role_name} onValueChange={(value) => setEditFormData(prev => ({ ...prev, role_name: value }))}>
                <SelectTrigger className="col-span-3">
                  <SelectValue placeholder="Select role" />
                </SelectTrigger>
                <SelectContent>
                  {roles.map((role) => (
                    <SelectItem key={role.id} value={role.role_name}>
                      {getRoleDisplayInfo(role.role_name).label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="edit_department" className="text-right">
                Department
              </Label>
              <Input
                id="edit_department"
                value={editFormData.department}
                onChange={(e) => setEditFormData(prev => ({ ...prev, department: e.target.value }))}
                className="col-span-3"
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="edit_job_title" className="text-right">
                Job Title
              </Label>
              <Input
                id="edit_job_title"
                value={editFormData.job_title}
                onChange={(e) => setEditFormData(prev => ({ ...prev, job_title: e.target.value }))}
                className="col-span-3"
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="edit_phone" className="text-right">
                Phone
              </Label>
              <Input
                id="edit_phone"
                value={editFormData.phone}
                onChange={(e) => setEditFormData(prev => ({ ...prev, phone: e.target.value }))}
                className="col-span-3"
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="edit_active" className="text-right">
                Active
              </Label>
              <div className="col-span-3">
                <Switch
                  id="edit_active"
                  checked={editFormData.is_active}
                  onCheckedChange={(checked) => setEditFormData(prev => ({ ...prev, is_active: checked }))}
                />
              </div>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsEditDialogOpen(false)}>
              Cancel
            </Button>
            <Button
              onClick={handleEditUser}
              disabled={!editFormData.first_name || !editFormData.last_name}
            >
              Update User
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* View User Dialog */}
      <Dialog open={isViewDialogOpen} onOpenChange={setIsViewDialogOpen}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>User Details</DialogTitle>
            <DialogDescription>
              Complete information about this user account.
            </DialogDescription>
          </DialogHeader>
          {selectedUser && (
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label className="text-sm font-medium text-gray-500">Name</Label>
                  <p className="text-sm">{selectedUser.first_name} {selectedUser.last_name}</p>
                </div>
                <div>
                  <Label className="text-sm font-medium text-gray-500">Email</Label>
                  <p className="text-sm">{selectedUser.email}</p>
                </div>
                <div>
                  <Label className="text-sm font-medium text-gray-500">Role</Label>
                  <Badge className={getRoleDisplayInfo(selectedUser.role.role_name).color}>
                    {getRoleDisplayInfo(selectedUser.role.role_name).label}
                  </Badge>
                </div>
                <div>
                  <Label className="text-sm font-medium text-gray-500">Status</Label>
                  <div className="flex items-center gap-2">
                    {selectedUser.is_active ? (
                      <UserCheck className="h-4 w-4 text-green-600" />
                    ) : (
                      <UserX className="h-4 w-4 text-red-600" />
                    )}
                    <span className={selectedUser.is_active ? 'text-green-600' : 'text-red-600'}>
                      {selectedUser.is_active ? 'Active' : 'Inactive'}
                    </span>
                  </div>
                </div>
                <div>
                  <Label className="text-sm font-medium text-gray-500">Department</Label>
                  <p className="text-sm">{selectedUser.department || '-'}</p>
                </div>
                <div>
                  <Label className="text-sm font-medium text-gray-500">Job Title</Label>
                  <p className="text-sm">{selectedUser.job_title || '-'}</p>
                </div>
                <div>
                  <Label className="text-sm font-medium text-gray-500">Phone</Label>
                  <p className="text-sm">{selectedUser.phone || '-'}</p>
                </div>
                <div>
                  <Label className="text-sm font-medium text-gray-500">Created</Label>
                  <p className="text-sm">{new Date(selectedUser.created_at).toLocaleDateString()}</p>
                </div>
              </div>

              {selectedUser.requires_password_setup && (
                <Alert>
                  <Clock className="h-4 w-4" />
                  <AlertDescription>
                    This user has not completed their account setup. They need to set up their password.
                  </AlertDescription>
                </Alert>
              )}
            </div>
          )}
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsViewDialogOpen(false)}>
              Close
            </Button>
            {selectedUser && (
              <Button onClick={() => {
                setIsViewDialogOpen(false);
                openEditDialog(selectedUser);
              }}>
                <Edit className="h-4 w-4 mr-2" />
                Edit User
              </Button>
            )}
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Delete User Dialog */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent className="sm:max-w-[400px]">
          <DialogHeader>
            <DialogTitle>Delete User</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete this user account? This action cannot be undone.
            </DialogDescription>
          </DialogHeader>
          {selectedUser && (
            <div className="space-y-4">
              <Alert>
                <AlertTriangle className="h-4 w-4" />
                <AlertDescription>
                  You are about to permanently delete the account for{' '}
                  <strong>{selectedUser.first_name} {selectedUser.last_name}</strong>{' '}
                  ({selectedUser.email}). This will remove all their data and access to the system.
                </AlertDescription>
              </Alert>
            </div>
          )}
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsDeleteDialogOpen(false)}>
              Cancel
            </Button>
            <Button
              variant="destructive"
              onClick={handleDeleteUser}
            >
              <Trash2 className="h-4 w-4 mr-2" />
              Delete User
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default AdminUserManagement;
