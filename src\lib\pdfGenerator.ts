import jsPDF from 'jspdf';
import { Document, CompanyInfo } from './documents';

export class PDFGenerator {
  static async generateDocumentPDF(document: Document): Promise<void> {
    try {
      console.log('PDF Generator - Starting generation');
      console.log('PDF Generator - Document:', document.document_number);
      console.log('PDF Generator - Company info received:', document.company_info);

      const pdf = new jsPDF('p', 'mm', 'a4');
      const pageWidth = pdf.internal.pageSize.getWidth();
      const pageHeight = pdf.internal.pageSize.getHeight();
      const margin = 20;
      const contentWidth = pageWidth - (margin * 2);

      let yPosition = margin;

      // Helper function to add text with word wrap
      const addText = (text: string, x: number, y: number, options: any = {}) => {
        const fontSize = options.fontSize || 10;
        const maxWidth = options.maxWidth || contentWidth;
        const align = options.align || 'left';

        pdf.setFontSize(fontSize);
        if (options.bold) pdf.setFont('helvetica', 'bold');
        else pdf.setFont('helvetica', 'normal');

        const lines = pdf.splitTextToSize(text || '', maxWidth);

        if (align === 'right') {
          x = pageWidth - margin;
        } else if (align === 'center') {
          x = pageWidth / 2;
        }

        pdf.text(lines, x, y, { align });
        return y + (lines.length * fontSize * 0.35);
      };

      // Header Section
      const company = document.company_info || {};
      const isQuotation = document.type === 'quotation';

      console.log('PDF Generator - Using company data:', company);

      // Company Logo (if available and it's a data URL)
      if (company.logo_url && company.logo_url.startsWith('data:')) {
        try {
          console.log('PDF Generator - Adding logo to PDF');
          pdf.addImage(company.logo_url, 'PNG', margin, yPosition, 50, 20);
        } catch (e) {
          console.warn('Could not add logo to PDF:', e);
        }
      } else {
        console.log('PDF Generator - No logo available or invalid format:', company.logo_url);
      }

      // Company Name and Info
      const companyName = company.name || 'Your Construction Company';
      const companyAddress = company.address || '123 Construction Street, Harare, Zimbabwe';
      const companyPhone = company.phone || '+263 4 123 4567';
      const companyEmail = company.email || '<EMAIL>';

      console.log('PDF Generator - Company details:', { companyName, companyAddress, companyPhone, companyEmail });

      yPosition = addText(companyName, margin, yPosition + 25, { fontSize: 16, bold: true });
      yPosition = addText(companyAddress.replace(/\n/g, ', '), margin, yPosition + 2, { fontSize: 9 });
      yPosition = addText(`Phone: ${companyPhone} | Email: ${companyEmail}`, margin, yPosition + 2, { fontSize: 9 });
      if (company.website) {
        yPosition = addText(`Website: ${company.website}`, margin, yPosition + 2, { fontSize: 9 });
      }

      // Document Type and Number (Right side)
      const docTypeY = margin + 25;
      addText(isQuotation ? 'QUOTATION' : 'INVOICE', 0, docTypeY, {
        fontSize: 20,
        bold: true,
        align: 'right'
      });
      addText(`Document #: ${document.document_number}`, 0, docTypeY + 10, {
        fontSize: 10,
        align: 'right'
      });
      addText(`Issue Date: ${new Date(document.issue_date).toLocaleDateString()}`, 0, docTypeY + 15, {
        fontSize: 10,
        align: 'right'
      });
      addText(`Due Date: ${new Date(document.due_date).toLocaleDateString()}`, 0, docTypeY + 20, {
        fontSize: 10,
        align: 'right'
      });
      addText(`Status: ${document.status}`, 0, docTypeY + 25, {
        fontSize: 10,
        bold: true,
        align: 'right'
      });

      yPosition = Math.max(yPosition, docTypeY + 35);

      // Line separator
      pdf.setDrawColor(200, 200, 200);
      pdf.line(margin, yPosition, pageWidth - margin, yPosition);
      yPosition += 10;

      // Document Title
      yPosition = addText(document.title, margin, yPosition, { fontSize: 14, bold: true });
      yPosition += 5;

      // Client Information
      yPosition = addText('BILL TO:', margin, yPosition, { fontSize: 10, bold: true });
      yPosition = addText(document.client_name, margin, yPosition + 2, { fontSize: 12, bold: true });
      if (document.client_address) {
        yPosition = addText(document.client_address, margin, yPosition + 2, { fontSize: 10 });
      }
      if (document.client_phone) {
        yPosition = addText(`Phone: ${document.client_phone}`, margin, yPosition + 2, { fontSize: 10 });
      }
      yPosition = addText(`Email: ${document.client_email}`, margin, yPosition + 2, { fontSize: 10 });

      // Project Information (Right side)
      const projectY = yPosition - 25;
      addText('PROJECT:', 0, projectY, { fontSize: 10, bold: true, align: 'right', maxWidth: 80 });
      addText(document.project_name, 0, projectY + 5, { fontSize: 12, bold: true, align: 'right', maxWidth: 80 });

      yPosition += 10;

      // Items Table Header
      const tableStartY = yPosition;
      const colWidths = [80, 20, 15, 25, 25]; // Description, Qty, Unit, Price, Total
      const colPositions = [margin];
      for (let i = 1; i < colWidths.length; i++) {
        colPositions[i] = colPositions[i-1] + colWidths[i-1];
      }

      // Table header background
      pdf.setFillColor(245, 245, 245);
      pdf.rect(margin, yPosition, contentWidth, 8, 'F');

      // Table headers
      pdf.setFont('helvetica', 'bold');
      pdf.setFontSize(10);
      pdf.text('Description', colPositions[0] + 2, yPosition + 5);
      pdf.text('Qty', colPositions[1] + 2, yPosition + 5);
      pdf.text('Unit', colPositions[2] + 2, yPosition + 5);
      pdf.text('Price', colPositions[3] + 2, yPosition + 5);
      pdf.text('Total', colPositions[4] + 2, yPosition + 5);

      yPosition += 8;

      // Table border
      pdf.setDrawColor(200, 200, 200);
      pdf.rect(margin, tableStartY, contentWidth, 8);

      // Items
      pdf.setFont('helvetica', 'normal');
      pdf.setFontSize(9);

      document.items.forEach((item, index) => {
        const rowHeight = 6;

        // Alternate row background
        if (index % 2 === 1) {
          pdf.setFillColor(250, 250, 250);
          pdf.rect(margin, yPosition, contentWidth, rowHeight, 'F');
        }

        // Item data
        const descLines = pdf.splitTextToSize(item.description, colWidths[0] - 4);
        pdf.text(descLines, colPositions[0] + 2, yPosition + 4);
        pdf.text(item.quantity.toString(), colPositions[1] + 2, yPosition + 4);
        pdf.text(item.unit, colPositions[2] + 2, yPosition + 4);
        pdf.text(`$${item.unit_price.toFixed(2)}`, colPositions[3] + 2, yPosition + 4);
        pdf.text(`$${item.total.toFixed(2)}`, colPositions[4] + 2, yPosition + 4);

        // Row border
        pdf.rect(margin, yPosition, contentWidth, rowHeight);

        yPosition += rowHeight;
      });

      yPosition += 10;

      // Totals Section
      const totalsX = pageWidth - margin - 60;
      yPosition = addText(`Subtotal: $${document.subtotal.toFixed(2)}`, totalsX, yPosition, { align: 'left' });

      if (document.discount_amount > 0) {
        yPosition = addText(`Discount (${document.discount_rate}%): -$${document.discount_amount.toFixed(2)}`, totalsX, yPosition + 2, { align: 'left' });
      }

      yPosition = addText(`Tax (${document.tax_rate}%): $${document.tax_amount.toFixed(2)}`, totalsX, yPosition + 2, { align: 'left' });

      // Total line
      pdf.setDrawColor(0, 0, 0);
      pdf.line(totalsX, yPosition + 2, pageWidth - margin, yPosition + 2);
      yPosition = addText(`TOTAL: $${document.total_amount.toFixed(2)}`, totalsX, yPosition + 6, { fontSize: 12, bold: true, align: 'left' });

      yPosition += 15;

      // Notes
      if (document.notes) {
        yPosition = addText('NOTES:', margin, yPosition, { fontSize: 10, bold: true });
        yPosition = addText(document.notes, margin, yPosition + 2, { fontSize: 9 });
        yPosition += 5;
      }

      // Terms & Conditions
      if (document.terms_conditions) {
        yPosition = addText('TERMS & CONDITIONS:', margin, yPosition, { fontSize: 10, bold: true });
        yPosition = addText(document.terms_conditions, margin, yPosition + 2, { fontSize: 9 });
      }

      // Footer
      if (yPosition < pageHeight - 40) {
        const footerY = pageHeight - 30;
        pdf.setDrawColor(200, 200, 200);
        pdf.line(margin, footerY - 5, pageWidth - margin, footerY - 5);

        if (company.tax_number) {
          addText(`Tax Number: ${company.tax_number}`, margin, footerY, { fontSize: 8 });
        }
        if (company.registration_number) {
          addText(`Registration Number: ${company.registration_number}`, 0, footerY, { fontSize: 8, align: 'right' });
        }
        addText('Thank you for your business!', 0, footerY + 8, { fontSize: 9, align: 'center' });
      }

      console.log('PDF Generator - PDF generation completed successfully');

      // Download the PDF
      const fileName = `${document.type}-${document.document_number}.pdf`;
      pdf.save(fileName);

    } catch (error) {
      console.error('Error generating PDF:', error);
      throw new Error(`Failed to generate PDF: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }


}
