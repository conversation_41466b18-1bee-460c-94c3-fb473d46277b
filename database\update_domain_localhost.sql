-- Quick fix to update domain to localhost for development
-- Run this in Supabase SQL Editor

-- Update the email generation function to use localhost
CREATE OR REPLACE FUNCTION public.generate_setup_email_simple(
    user_first_name TEXT,
    user_email TEXT,
    setup_token TEXT
)
RETURNS TABLE(subject TEXT, html_content TEXT, text_content TEXT, setup_url TEXT) AS $$
DECLARE
    company_name TEXT := 'Construction Management System';
    base_url TEXT := 'http://localhost:5173'; -- Updated for development
    full_setup_url TEXT;
BEGIN
    -- Generate setup URL
    full_setup_url := base_url || '/setup-password?token=' || setup_token || '&email=' || user_email;
    
    -- Return email content
    RETURN QUERY SELECT 
        'Complete Your Account Setup - ' || company_name,
        
        -- HTML Content
        '<html><body style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
            <div style="background: #f8f9fa; padding: 30px; border-radius: 8px; border: 1px solid #e9ecef;">
                <h2 style="color: #2563eb; margin-bottom: 20px;">Welcome to ' || company_name || '</h2>
                <p style="font-size: 16px; margin-bottom: 15px;">Hello ' || user_first_name || ',</p>
                <p style="margin-bottom: 20px;">Your account has been created! Please complete your setup by creating a password.</p>
                
                <div style="background: white; padding: 20px; border-radius: 6px; margin: 25px 0; border-left: 4px solid #2563eb;">
                    <p style="margin: 0 0 15px 0; font-weight: bold;">Click the button below to set up your password:</p>
                    <a href="' || full_setup_url || '" 
                       style="background: #2563eb; color: white; padding: 12px 24px; text-decoration: none; 
                              border-radius: 6px; display: inline-block; font-weight: bold;">
                        Complete Account Setup
                    </a>
                </div>
                
                <div style="background: #fff3cd; padding: 15px; border-radius: 6px; margin: 20px 0;">
                    <p style="margin: 0; font-size: 14px;"><strong>Setup Details:</strong></p>
                    <p style="margin: 5px 0; font-size: 14px;">Email: ' || user_email || '</p>
                    <p style="margin: 5px 0; font-size: 14px;">Token: ' || setup_token || '</p>
                </div>
                
                <p style="color: #666; font-size: 14px; margin-top: 25px;">
                    This link will expire in 24 hours. If you need assistance, please contact your administrator.
                </p>
                
                <hr style="margin: 30px 0; border: none; border-top: 1px solid #ddd;">
                <p style="color: #666; font-size: 12px; margin: 0;">
                    This email was sent by ' || company_name || '.
                </p>
            </div>
        </body></html>',
        
        -- Text Content
        'Welcome to ' || company_name || '

Hello ' || user_first_name || ',

Your account has been created! Please complete your setup by creating a password.

SETUP LINK:
' || full_setup_url || '

ACCOUNT DETAILS:
- Email: ' || user_email || '
- Setup Token: ' || setup_token || '

This link will expire in 24 hours. If you need assistance, please contact your administrator.

---
This email was sent by ' || company_name || '.',

        -- Setup URL
        full_setup_url;
END;
$$ LANGUAGE plpgsql;

-- Test the updated function
DO $$
DECLARE
    test_result RECORD;
BEGIN
    RAISE NOTICE '=== TESTING UPDATED DOMAIN ===';
    
    -- Test email generation with localhost
    SELECT * INTO test_result 
    FROM public.generate_setup_email_simple(
        'Test User',
        '<EMAIL>',
        'test_token_123'
    );
    
    RAISE NOTICE 'Generated setup URL: %', test_result.setup_url;
    
    IF test_result.setup_url LIKE 'http://localhost:5173%' THEN
        RAISE NOTICE '✓ Domain updated to localhost successfully!';
    ELSE
        RAISE NOTICE '❌ Domain not updated correctly';
    END IF;
END $$;
