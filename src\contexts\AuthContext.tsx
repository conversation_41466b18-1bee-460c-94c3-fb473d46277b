import React, { createContext, useContext, useEffect, useState } from 'react';
import { UserService, UserProfile } from '@/lib/userService';

interface AuthContextType {
  user: any;
  profile: UserProfile | null;
  loading: boolean;
  signIn: (email: string, password: string, role?: string) => Promise<void>;
  signOut: () => Promise<void>;
  refreshProfile: () => Promise<void>;
  isAuthenticated: boolean;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

interface AuthProviderProps {
  children: React.ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<any>(null);
  const [profile, setProfile] = useState<UserProfile | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    checkAuthStatus();
  }, []);

  const checkAuthStatus = async () => {
    try {
      const currentUser = await UserService.getCurrentUser();
      if (currentUser) {
        setUser(currentUser);
        const userProfile = await UserService.getCurrentUserProfile();
        setProfile(userProfile);
      }
    } catch (error) {
      console.error('Error checking auth status:', error);
      // Clear any invalid auth state
      setUser(null);
      setProfile(null);
    } finally {
      setLoading(false);
    }
  };

  const signIn = async (email: string, password: string, role?: string) => {
    try {
      const { user: authUser, profile: userProfile } = await UserService.signIn({
        email,
        password,
        role
      });
      
      setUser(authUser);
      setProfile(userProfile);
    } catch (error) {
      throw error;
    }
  };

  const signOut = async () => {
    try {
      await UserService.signOut();
      setUser(null);
      setProfile(null);
    } catch (error) {
      console.error('Error signing out:', error);
      // Force clear auth state even if signOut fails
      setUser(null);
      setProfile(null);
    }
  };

  const refreshProfile = async () => {
    try {
      if (user) {
        const userProfile = await UserService.getCurrentUserProfile();
        setProfile(userProfile);
      }
    } catch (error) {
      console.error('Error refreshing profile:', error);
    }
  };

  const value: AuthContextType = {
    user,
    profile,
    loading,
    signIn,
    signOut,
    refreshProfile,
    isAuthenticated: !!user && !!profile
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};

export default AuthContext;
