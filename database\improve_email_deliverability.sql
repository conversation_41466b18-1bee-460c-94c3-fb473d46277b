-- Improve Email Deliverability - Reduce Spam Score
-- This updates email templates to be more professional and less spam-like

-- 1. Update email templates to be more professional
CREATE OR REPLACE FUNCTION public.generate_professional_email_content(
    user_profile_id UUID
)
RETURNS TABLE(success BOOLEAN, message TEXT, email_data JSONB) AS $$
DECLARE
    user_record RECORD;
    current_count INTEGER;
    max_emails INTEGER := 50;
    email_content TEXT;
    email_subject TEXT;
    action_url TEXT;
    email_method TEXT;
    company_name TEXT := 'Construction Management System';
    support_email TEXT := '<EMAIL>';
BEGIN
    -- Get user information
    SELECT up.*, ur.role_name
    INTO user_record
    FROM public.user_profiles up
    JOIN public.user_roles ur ON up.role_id = ur.id
    WHERE up.id = user_profile_id;
    
    IF NOT FOUND THEN
        RETURN QUERY SELECT false, 'User not found', '{}'::jsonb;
        RETURN;
    END IF;
    
    -- Check email limits
    current_count := COALESCE(user_record.setup_email_count, 0);
    IF current_count >= max_emails THEN
        RETURN QUERY SELECT false, 'Maximum email limit reached (' || max_emails || ')', '{}'::jsonb;
        RETURN;
    END IF;
    
    -- Determine email type and content based on whether user has auth account
    IF user_record.user_id IS NULL THEN
        -- User needs to register first
        email_method := 'account_activation';
        email_subject := 'Account Activation Required - ' || company_name;
        action_url := 'http://************:8080/register?email=' || user_record.email || '&invited=true';
        
        email_content := 'Dear ' || user_record.first_name || ',

Your account has been created for ' || company_name || '.

Account Information:
• Email: ' || user_record.email || '
• Role: ' || INITCAP(user_record.role_name) || '
• Status: Activation Required

To activate your account:

1. Visit: ' || action_url || '
2. Complete the registration process
3. Set your secure password
4. Begin using the system

This link is valid for 7 days. If you experience any issues, please contact our support team.

Best regards,
' || company_name || ' Team

---
This is an automated message. Please do not reply to this email.
For support, contact: ' || support_email || '
' || company_name || ' | Professional Construction Management
Generated on: ' || TO_CHAR(CURRENT_DATE, 'Month DD, YYYY');

    ELSE
        -- User has auth account, can reset password
        email_method := 'password_reset';
        email_subject := 'Password Reset Request - ' || company_name;
        action_url := 'http://************:8080/login';
        
        email_content := 'Dear ' || user_record.first_name || ',

A password reset has been requested for your ' || company_name || ' account.

Account Information:
• Email: ' || user_record.email || '
• Role: ' || INITCAP(user_record.role_name) || '
• Status: Password Reset Required

To reset your password:

1. Visit: ' || action_url || '
2. Click "Forgot Password"
3. Enter your email address
4. Follow the instructions sent to your email

If you did not request this reset, please contact our support team immediately.

Best regards,
' || company_name || ' Team

---
This is an automated message. Please do not reply to this email.
For support, contact: ' || support_email || '
' || company_name || ' | Professional Construction Management
Generated on: ' || TO_CHAR(CURRENT_DATE, 'Month DD, YYYY');

    END IF;
    
    -- Update email count
    UPDATE public.user_profiles 
    SET 
        setup_email_sent_at = NOW(),
        setup_email_count = current_count + 1,
        email_status = 'professional_template',
        updated_at = NOW()
    WHERE id = user_profile_id;
    
    -- Return email content
    RETURN QUERY SELECT 
        true, 
        'Professional email content generated',
        jsonb_build_object(
            'to_email', user_record.email,
            'subject', email_subject,
            'text_content', email_content,
            'action_url', action_url,
            'user_name', user_record.first_name || ' ' || user_record.last_name,
            'method', email_method,
            'user_role', user_record.role_name,
            'has_auth_account', (user_record.user_id IS NOT NULL),
            'company_name', company_name,
            'support_email', support_email,
            'professional_template', true
        );
        
EXCEPTION
    WHEN OTHERS THEN
        RETURN QUERY SELECT false, 'Email generation failed: ' || SQLERRM, '{}'::jsonb;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 2. Update main RPC function to use professional templates
CREATE OR REPLACE FUNCTION public.rpc_resend_setup_email(user_profile_id UUID)
RETURNS jsonb AS $$
DECLARE
    v_requesting_user_id UUID;
    email_result RECORD;
BEGIN
    v_requesting_user_id := auth.uid();
    
    IF v_requesting_user_id IS NULL THEN
        RETURN jsonb_build_object('success', false, 'error', 'Not authenticated');
    END IF;
    
    -- Generate professional email content
    SELECT * INTO email_result 
    FROM public.generate_professional_email_content(user_profile_id);
    
    IF email_result.success THEN
        RETURN jsonb_build_object(
            'success', true, 
            'message', email_result.message,
            'email_data', email_result.email_data
        );
    ELSE
        RETURN jsonb_build_object('success', false, 'error', email_result.message);
    END IF;
    
EXCEPTION
    WHEN OTHERS THEN
        RETURN jsonb_build_object('success', false, 'error', SQLERRM);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 3. Grant permissions
GRANT EXECUTE ON FUNCTION public.generate_professional_email_content(UUID) TO authenticated;

-- 4. Update Supabase email settings recommendations
DO $$
BEGIN
    RAISE NOTICE '';
    RAISE NOTICE '=== EMAIL DELIVERABILITY IMPROVEMENTS ===';
    RAISE NOTICE '';
    RAISE NOTICE 'COMPLETED:';
    RAISE NOTICE '✓ Professional email templates created';
    RAISE NOTICE '✓ Reduced spam-trigger words';
    RAISE NOTICE '✓ Added proper business formatting';
    RAISE NOTICE '✓ Fixed localhost URLs to use IP address';
    RAISE NOTICE '';
    RAISE NOTICE 'SUPABASE SMTP SETTINGS TO IMPROVE DELIVERABILITY:';
    RAISE NOTICE '';
    RAISE NOTICE '1. SENDER NAME: Use "Construction Management System" (not personal name)';
    RAISE NOTICE '2. SENDER EMAIL: Use business email if possible (not Gmail)';
    RAISE NOTICE '3. SMTP PROVIDER: Consider using:';
    RAISE NOTICE '   • SendGrid (best for deliverability)';
    RAISE NOTICE '   • Mailgun (good alternative)';
    RAISE NOTICE '   • Amazon SES (enterprise option)';
    RAISE NOTICE '';
    RAISE NOTICE 'GMAIL SPECIFIC IMPROVEMENTS:';
    RAISE NOTICE '• Add your IP (************) to Gmail''s trusted senders';
    RAISE NOTICE '• Use a business domain email as sender if possible';
    RAISE NOTICE '• Enable DKIM/SPF records for your domain';
    RAISE NOTICE '';
    RAISE NOTICE 'USER INSTRUCTIONS:';
    RAISE NOTICE '• Check spam folder initially';
    RAISE NOTICE '• Mark emails as "Not Spam"';
    RAISE NOTICE '• Add sender to contacts';
    RAISE NOTICE '';
    RAISE NOTICE '✅ EMAIL DELIVERABILITY IMPROVED!';
END $$;
