# Company Management System Setup Guide

This guide will help you set up the comprehensive company management system with team members, activity logs, and permissions.

## 🚀 Quick Setup

### Step 1: Run the Database Schema
Execute the following SQL script in your Supabase SQL Editor:

```sql
-- Copy and paste the entire content of company_management_schema.sql
```

Or run it directly:
1. Go to your Supabase Dashboard
2. Navigate to SQL Editor
3. Copy the content from `database/company_management_schema.sql`
4. Execute the script

### Step 2: Verify Installation
After running the schema, verify that the following tables were created:
- `company_settings`
- `departments` 
- `team_memberships`
- `user_sessions`
- `audit_logs` (enhanced)

### Step 3: Access the Company Management
1. Navigate to `/company` in your application
2. You should see the Company Management interface with 4 tabs:
   - **Overview**: Company information and statistics
   - **Team Members**: Manage team members and their roles
   - **Activity Log**: View system activities and user actions
   - **Permissions**: Manage roles and permissions

## 📋 Features Included

### 1. Company Overview
- **Company Information Display**: Logo, name, address, contact details
- **Team Statistics**: Total members, active members, pending invites
- **Recent Activity Feed**: Latest team and system activities
- **Quick Actions**: Edit company information

### 2. Team Members Management
- **Member List**: View all team members with roles and status
- **Search & Filter**: Find members by name, role, department, or status
- **Member Actions**: 
  - Edit profiles
  - Activate/deactivate users
  - Send emails
  - Delete users (admin only)
- **Role-based Access**: Different permissions for different user roles
- **Status Tracking**: Active, Pending, Inactive member status

### 3. Activity Log
- **Comprehensive Logging**: All user actions and system events
- **Advanced Filtering**: Filter by action type, resource, date range
- **Search Functionality**: Search through activity descriptions
- **Export Capability**: Export activity logs to CSV
- **Real-time Updates**: Automatic refresh of activity data
- **Detailed Information**: User, timestamp, IP address, action details

### 4. Permissions & Roles
- **Role Management**: Create, edit, and delete user roles
- **Permission Matrix**: Visual representation of role permissions
- **Permission Groups**: Organized by functional areas:
  - User Management
  - Financial Management
  - Project Management
  - QS Functions
  - Reports & Analytics
  - System Settings
  - Basic Access
- **Role Hierarchy**: Admin > Management > Accountant > QS > Client

## 🔐 Security Features

### Row Level Security (RLS)
- **Audit Logs**: Only admins and management can view all logs; users see their own
- **Company Settings**: Everyone can read; only admins can modify
- **Role-based Access**: Different features available based on user role

### Audit Logging
- **Automatic Logging**: All significant actions are logged automatically
- **Detailed Tracking**: IP address, user agent, timestamps
- **Success/Failure Tracking**: Both successful and failed actions are recorded

## 🛠️ Technical Implementation

### Database Schema
- **Enhanced Tables**: Extended existing tables with new fields
- **New Tables**: Added company_settings, departments, team_memberships, user_sessions
- **Indexes**: Optimized for performance with proper indexing
- **Functions**: Utility functions for statistics and logging

### Frontend Components
- **CompanyManagement**: Main container component with tabs
- **CompanyOverview**: Company information and statistics
- **TeamMembers**: Team member management with search/filter
- **ActivityLog**: Activity monitoring with export functionality
- **CompanyPermissions**: Role and permission management

### Permission System
- **Hierarchical Roles**: Clear role hierarchy with inheritance
- **Granular Permissions**: Specific permissions for different actions
- **Dynamic Access Control**: UI elements shown/hidden based on permissions

## 📊 Usage Examples

### For Administrators
- View and manage all team members
- Access complete activity logs
- Modify company settings
- Create and manage user roles
- Export audit data

### For Management
- View team statistics and activity
- Manage team members (limited)
- Access departmental information
- View relevant audit logs

### For Other Roles
- View company information
- See team overview (limited)
- Access role-appropriate features

## 🔧 Customization

### Adding New Permissions
1. Add permission to `PERMISSIONS` object in `src/lib/permissions.ts`
2. Update role hierarchy if needed
3. Apply permission checks in components using `hasPermission()`

### Adding New Roles
1. Use the "Create Role" feature in the Permissions tab
2. Define role permissions
3. Assign users to the new role

### Extending Activity Logging
1. Use `logUserActivity()` function from `src/lib/initializeSettings.ts`
2. Add logging to new actions in your components
3. Customize log filtering in ActivityLog component

## 🚨 Troubleshooting

### Common Issues

1. **Permission Denied Errors**
   - Verify RLS policies are correctly applied
   - Check user role assignments
   - Ensure proper function permissions

2. **Activity Log Not Showing**
   - Verify audit_logs table exists
   - Check RLS policies on audit_logs
   - Ensure logging functions are being called

3. **Company Settings Not Loading**
   - Check company_settings table exists
   - Verify default data was inserted
   - Check table permissions

### Database Verification Queries

```sql
-- Check if tables exist
SELECT table_name FROM information_schema.tables 
WHERE table_schema = 'public' 
AND table_name IN ('company_settings', 'departments', 'team_memberships', 'audit_logs');

-- Check company settings
SELECT * FROM public.company_settings;

-- Check recent audit logs
SELECT * FROM public.audit_logs ORDER BY created_at DESC LIMIT 10;

-- Check user roles
SELECT * FROM public.user_roles;
```

## 📈 Next Steps

After setup, consider:
1. **Customizing Company Information**: Update company details in the Overview tab
2. **Setting Up Departments**: Add your specific departments
3. **Configuring Roles**: Adjust permissions based on your needs
4. **Training Users**: Familiarize team with new features
5. **Monitoring Usage**: Review activity logs regularly

## 🆘 Support

If you encounter issues:
1. Check the troubleshooting section above
2. Verify database schema was applied correctly
3. Review browser console for JavaScript errors
4. Check Supabase logs for database errors

The company management system provides a comprehensive solution for managing your construction company's team, tracking activities, and controlling permissions. All features are designed to work together seamlessly while maintaining security and performance.
