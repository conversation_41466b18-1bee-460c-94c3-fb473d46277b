-- Add Creator Information to Database Tables
-- This script adds creator fields to show who created various items

-- 1. Add creator fields to clients table
DO $$
BEGIN
  -- Add created_by_user_id field
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'clients' AND column_name = 'created_by_user_id') THEN
    ALTER TABLE public.clients ADD COLUMN created_by_user_id UUID REFERENCES auth.users(id);
    RAISE NOTICE '✓ Added created_by_user_id to clients table';
  ELSE
    RAISE NOTICE '- created_by_user_id already exists in clients table';
  END IF;

  -- Add created_by_name field for display
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'clients' AND column_name = 'created_by_name') THEN
    ALTER TABLE public.clients ADD COLUMN created_by_name TEXT;
    RAISE NOTICE '✓ Added created_by_name to clients table';
  ELSE
    RAISE NOTICE '- created_by_name already exists in clients table';
  END IF;

  -- Add created_by_avatar field for profile picture
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'clients' AND column_name = 'created_by_avatar') THEN
    ALTER TABLE public.clients ADD COLUMN created_by_avatar TEXT;
    RAISE NOTICE '✓ Added created_by_avatar to clients table';
  ELSE
    RAISE NOTICE '- created_by_avatar already exists in clients table';
  END IF;
END $$;

-- 2. Add creator fields to projects table
DO $$
BEGIN
  -- Add created_by_user_id field
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'projects' AND column_name = 'created_by_user_id') THEN
    ALTER TABLE public.projects ADD COLUMN created_by_user_id UUID REFERENCES auth.users(id);
    RAISE NOTICE '✓ Added created_by_user_id to projects table';
  ELSE
    RAISE NOTICE '- created_by_user_id already exists in projects table';
  END IF;

  -- Add created_by_name field for display
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'projects' AND column_name = 'created_by_name') THEN
    ALTER TABLE public.projects ADD COLUMN created_by_name TEXT;
    RAISE NOTICE '✓ Added created_by_name to projects table';
  ELSE
    RAISE NOTICE '- created_by_name already exists in projects table';
  END IF;

  -- Add created_by_avatar field for profile picture
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'projects' AND column_name = 'created_by_avatar') THEN
    ALTER TABLE public.projects ADD COLUMN created_by_avatar TEXT;
    RAISE NOTICE '✓ Added created_by_avatar to projects table';
  ELSE
    RAISE NOTICE '- created_by_avatar already exists in projects table';
  END IF;
END $$;

-- 3. Add creator fields to documents table (quotes/invoices)
DO $$
BEGIN
  -- Add created_by_user_id field
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'documents' AND column_name = 'created_by_user_id') THEN
    ALTER TABLE public.documents ADD COLUMN created_by_user_id UUID REFERENCES auth.users(id);
    RAISE NOTICE '✓ Added created_by_user_id to documents table';
  ELSE
    RAISE NOTICE '- created_by_user_id already exists in documents table';
  END IF;

  -- Add created_by_name field for display
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'documents' AND column_name = 'created_by_name') THEN
    ALTER TABLE public.documents ADD COLUMN created_by_name TEXT;
    RAISE NOTICE '✓ Added created_by_name to documents table';
  ELSE
    RAISE NOTICE '- created_by_name already exists in documents table';
  END IF;

  -- Add created_by_avatar field for profile picture
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'documents' AND column_name = 'created_by_avatar') THEN
    ALTER TABLE public.documents ADD COLUMN created_by_avatar TEXT;
    RAISE NOTICE '✓ Added created_by_avatar to documents table';
  ELSE
    RAISE NOTICE '- created_by_avatar already exists in documents table';
  END IF;
END $$;

-- 4. Add creator fields to messages table (if it exists)
DO $$
BEGIN
  IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'messages') THEN
    -- Add created_by_user_id field
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'messages' AND column_name = 'created_by_user_id') THEN
      ALTER TABLE public.messages ADD COLUMN created_by_user_id UUID REFERENCES auth.users(id);
      RAISE NOTICE '✓ Added created_by_user_id to messages table';
    ELSE
      RAISE NOTICE '- created_by_user_id already exists in messages table';
    END IF;

    -- Add created_by_avatar field for profile picture
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'messages' AND column_name = 'created_by_avatar') THEN
      ALTER TABLE public.messages ADD COLUMN created_by_avatar TEXT;
      RAISE NOTICE '✓ Added created_by_avatar to messages table';
    ELSE
      RAISE NOTICE '- created_by_avatar already exists in messages table';
    END IF;
  ELSE
    RAISE NOTICE '- messages table does not exist, skipping';
  END IF;
END $$;

-- 5. Create function to automatically populate creator info
CREATE OR REPLACE FUNCTION populate_creator_info()
RETURNS TRIGGER AS $$
DECLARE
  user_profile RECORD;
BEGIN
  -- Get current user's profile information
  SELECT 
    first_name || ' ' || last_name as full_name,
    COALESCE(profile_picture_url, avatar_url) as avatar_url
  INTO user_profile
  FROM public.user_profiles 
  WHERE user_id = auth.uid()
  LIMIT 1;

  -- Set creator fields
  NEW.created_by_user_id := auth.uid();
  NEW.created_by_name := COALESCE(user_profile.full_name, 'Unknown User');
  NEW.created_by_avatar := user_profile.avatar_url;

  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 6. Create triggers to automatically populate creator info on insert
DROP TRIGGER IF EXISTS set_creator_info_clients ON public.clients;
CREATE TRIGGER set_creator_info_clients
  BEFORE INSERT ON public.clients
  FOR EACH ROW
  EXECUTE FUNCTION populate_creator_info();

DROP TRIGGER IF EXISTS set_creator_info_projects ON public.projects;
CREATE TRIGGER set_creator_info_projects
  BEFORE INSERT ON public.projects
  FOR EACH ROW
  EXECUTE FUNCTION populate_creator_info();

DROP TRIGGER IF EXISTS set_creator_info_documents ON public.documents;
CREATE TRIGGER set_creator_info_documents
  BEFORE INSERT ON public.documents
  FOR EACH ROW
  EXECUTE FUNCTION populate_creator_info();

-- 7. Create triggers for messages table if it exists
DO $$
BEGIN
  IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'messages') THEN
    EXECUTE 'DROP TRIGGER IF EXISTS set_creator_info_messages ON public.messages';
    EXECUTE 'CREATE TRIGGER set_creator_info_messages
      BEFORE INSERT ON public.messages
      FOR EACH ROW
      EXECUTE FUNCTION populate_creator_info()';
    RAISE NOTICE '✓ Created trigger for messages table';
  END IF;
END $$;

-- 8. Grant necessary permissions
GRANT EXECUTE ON FUNCTION populate_creator_info() TO authenticated;

-- 9. Update existing records with current user info (optional - run manually if needed)
-- This is commented out to prevent accidental data modification
/*
UPDATE public.clients 
SET 
  created_by_user_id = auth.uid(),
  created_by_name = 'System Admin',
  created_by_avatar = NULL
WHERE created_by_user_id IS NULL;

UPDATE public.projects 
SET 
  created_by_user_id = auth.uid(),
  created_by_name = 'System Admin',
  created_by_avatar = NULL
WHERE created_by_user_id IS NULL;

UPDATE public.documents 
SET 
  created_by_user_id = auth.uid(),
  created_by_name = 'System Admin',
  created_by_avatar = NULL
WHERE created_by_user_id IS NULL;
*/

-- Final status messages
DO $$
BEGIN
  RAISE NOTICE '=== CREATOR FIELDS SETUP COMPLETE ===';
  RAISE NOTICE '✅ Creator fields added to all tables';
  RAISE NOTICE '✅ Automatic population triggers created';
  RAISE NOTICE '✅ New records will automatically include creator information';
  RAISE NOTICE '⚠️  To update existing records, uncomment and run the UPDATE statements at the end of this script';
END $$;
