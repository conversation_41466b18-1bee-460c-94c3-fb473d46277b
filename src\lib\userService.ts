import { supabase } from './supabase';
import { AdminEmailService } from './supabaseAdmin';
import { generateUUID } from './utils';

// Types for User Management
export interface UserRole {
  id: string;
  role_name: string;
  role_display_name: string;
  description?: string;
  permissions: Record<string, any>;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export interface UserProfile {
  id: string;
  user_id?: string;
  email: string;
  first_name: string;
  last_name: string;
  phone?: string;
  role_id: string;
  avatar_url?: string;
  profile_picture_url?: string;
  bio?: string;
  location?: string;
  date_of_birth?: string;
  emergency_contact_name?: string;
  emergency_contact_phone?: string;
  emergency_contact_relationship?: string;
  department?: string;
  job_title?: string;
  employee_id?: string;
  address?: string;
  city?: string;
  state?: string;
  postal_code?: string;
  country?: string;
  is_active: boolean;
  is_verified: boolean;
  last_login?: string;
  preferences?: Record<string, any>;
  created_by?: string;
  updated_by?: string;
  created_at: string;
  updated_at: string;
  role?: UserRole;
}

export interface UserSession {
  id: string;
  user_id: string;
  session_token: string;
  ip_address?: string;
  user_agent?: string;
  device_info?: Record<string, any>;
  is_active: boolean;
  expires_at: string;
  created_at: string;
}

export interface AuditLog {
  id: string;
  user_id?: string;
  action: string;
  resource_type?: string;
  resource_id?: string;
  old_values?: Record<string, any>;
  new_values?: Record<string, any>;
  ip_address?: string;
  user_agent?: string;
  created_at: string;
}

export interface LoginCredentials {
  email: string;
  password: string;
  role?: string;
}

export interface SignupData {
  email: string;
  password: string;
  first_name: string;
  last_name: string;
  role_name: string;
  phone?: string;
  department?: string;
  job_title?: string;
}

export class UserService {
  // Admin-controlled user creation
  static async adminCreateUser(userData: SignupData, adminUserId: string): Promise<{ profile: UserProfile }> {
    const { data: result, error } = await supabase.rpc('admin_create_user', {
      p_email: userData.email,
      p_password: userData.password || 'temp_password_123', // Temporary password
      p_first_name: userData.first_name,
      p_last_name: userData.last_name,
      p_role_name: userData.role_name,
      p_phone: userData.phone || null,
      p_department: userData.department || null,
      p_job_title: userData.job_title || null,
      p_created_by_admin_id: adminUserId
    });

    if (error) {
      throw new Error(`Failed to create user: ${error.message}`);
    }

    if (!result || !result[0] || !result[0].success) {
      const errorMessage = result?.[0]?.message || 'Unknown error during user creation';
      throw new Error(errorMessage);
    }

    // Get the full profile with role information
    const { data: fullProfile, error: fetchError } = await supabase
      .from('user_profiles')
      .select(`
        *,
        role:user_roles(*)
      `)
      .eq('id', result[0].profile_id)
      .single();

    if (fetchError) {
      throw new Error('User created but failed to load profile information.');
    }

    return { profile: fullProfile };
  }

  // NEW ADMIN USER CREATION SYSTEM
  static async adminCreateUserNew(userData: SignupData): Promise<{ profile: any; credentials: { email: string; password: string } }> {
    const { data: result, error } = await supabase.rpc('admin_create_user_account', {
      p_email: userData.email,
      p_first_name: userData.first_name,
      p_last_name: userData.last_name,
      p_role_name: userData.role_name,
      p_phone: userData.phone || null,
      p_department: userData.department || null,
      p_job_title: userData.job_title || null,
      p_temp_password: null // Let system generate password
    });

    if (error) {
      console.error('Admin user creation error:', error);
      throw new Error(`Failed to create user: ${error.message}`);
    }

    if (!result.success) {
      throw new Error(result.error || 'Failed to create user');
    }

    return {
      profile: result.user_data,
      credentials: {
        email: result.user_data.email,
        password: result.user_data.temp_password
      }
    };
  }

  // Reset user password (admin only)
  static async adminResetPassword(email: string, newPassword?: string): Promise<{ email: string; password: string }> {
    const { data: result, error } = await supabase.rpc('admin_reset_user_password', {
      p_email: email,
      p_new_password: newPassword || null
    });

    if (error) {
      console.error('Password reset error:', error);
      throw new Error(`Failed to reset password: ${error.message}`);
    }

    if (!result.success) {
      throw new Error(result.error || 'Failed to reset password');
    }

    return {
      email: result.email,
      password: result.new_password
    };
  }

  // Change own password (for logged-in users)
  static async changeMyPassword(currentPassword: string, newPassword: string): Promise<void> {
    const { data: result, error } = await supabase.rpc('change_my_password', {
      p_current_password: currentPassword,
      p_new_password: newPassword
    });

    if (error) {
      console.error('Password change error:', error);
      throw new Error(`Failed to change password: ${error.message}`);
    }

    if (!result.success) {
      throw new Error(result.error || 'Failed to change password');
    }
  }

  // Check user permissions
  static async checkPermission(userId: string, requiredRole: string): Promise<boolean> {
    const { data: hasPermission, error } = await supabase.rpc('check_user_permission', {
      p_user_id: userId,
      p_required_role: requiredRole
    });

    if (error) {
      console.error('Permission check failed:', error);
      return false;
    }

    return hasPermission || false;
  }

  // Initial admin registration (only for first admin - restricted use)
  static async signUp(userData: SignupData): Promise<{ user: any; profile: UserProfile }> {
    // Check if email already exists in profiles
    const { data: existingProfile } = await supabase
      .from('user_profiles')
      .select('email')
      .eq('email', userData.email)
      .single();

    if (existingProfile) {
      throw new Error('An account with this email address already exists. Please use a different email or try logging in.');
    }

    // Get role ID
    const { data: role, error: roleError } = await supabase
      .from('user_roles')
      .select('id')
      .eq('role_name', userData.role_name)
      .single();

    if (roleError) throw new Error(`Invalid role: ${userData.role_name}`);

    // Create auth user first
    const { data: authData, error: authError } = await supabase.auth.signUp({
      email: userData.email,
      password: userData.password,
      options: {
        data: {
          first_name: userData.first_name,
          last_name: userData.last_name,
          role_name: userData.role_name
        }
      }
    });

    if (authError) {
      console.error('Auth signup error:', authError);
      // Handle specific auth errors
      if (authError.message.includes('already registered') || authError.message.includes('already been registered')) {
        throw new Error('An account with this email address already exists. Please try logging in instead.');
      }
      if (authError.message.includes('Invalid email')) {
        throw new Error('Please enter a valid email address.');
      }
      if (authError.message.includes('Password')) {
        throw new Error('Password must be at least 6 characters long.');
      }
      throw new Error(`Registration failed: ${authError.message}`);
    }

    if (!authData.user) {
      throw new Error('Failed to create user account. Please try again.');
    }

    console.log('Auth user created successfully:', authData.user.id);

    // Wait for the auth user to be fully created and propagated
    await new Promise(resolve => setTimeout(resolve, 2000));

    // Verify the auth user actually exists before proceeding
    const { data: authExists, error: checkError } = await supabase.rpc('check_auth_user_exists', {
      user_id_param: authData.user.id
    });

    if (checkError) {
      console.error('Auth user verification failed:', checkError);
      throw new Error('Unable to verify user account. Please try again.');
    }

    if (!authExists) {
      console.error('Auth user does not exist after creation');
      throw new Error('User account was not created properly. Please try again.');
    }

    console.log('Auth user verified successfully');

    try {
      // Use the simple profile creation function
      const { data: profileId, error: profileError } = await supabase.rpc('create_user_profile_simple', {
        p_user_id: authData.user.id,
        p_email: userData.email,
        p_first_name: userData.first_name,
        p_last_name: userData.last_name,
        p_role_name: userData.role_name
      });

      if (profileError) {
        console.error('Profile creation failed:', profileError);

        if (profileError.message.includes('already exists') || profileError.message.includes('duplicate')) {
          throw new Error('An account with this email address already exists. Please use a different email or try logging in.');
        }
        throw new Error(`Failed to create user profile: ${profileError.message}`);
      }

      if (!profileId) {
        throw new Error('Profile creation returned no ID. Please try again.');
      }

      console.log('Profile created successfully:', profileId);

      // Update the profile with additional information if provided
      if (userData.phone || userData.department || userData.job_title) {
        const { error: updateError } = await supabase
          .from('user_profiles')
          .update({
            phone: userData.phone || null,
            department: userData.department || null,
            job_title: userData.job_title || null
          })
          .eq('id', profileId);

        if (updateError) {
          console.warn('Failed to update additional profile info:', updateError);
          // Don't fail registration for this
        }
      }

      // Get the full profile with role information
      const { data: fullProfile, error: fetchError } = await supabase
        .from('user_profiles')
        .select(`
          *,
          role:user_roles(*)
        `)
        .eq('id', profileId)
        .single();

      if (fetchError) {
        console.error('Failed to fetch full profile:', fetchError);
        // Try to fetch by email as fallback
        const { data: fallbackProfile, error: fallbackError } = await supabase
          .from('user_profiles')
          .select(`
            *,
            role:user_roles(*)
          `)
          .eq('email', userData.email)
          .single();

        if (fallbackError) {
          throw new Error('Account created but failed to load profile information.');
        }

        return { user: authData.user, profile: fallbackProfile };
      }

      return { user: authData.user, profile: fullProfile };

    } catch (error) {
      // Note: We don't clean up the auth user because admin API may not be available
      // The auth user will exist but without a profile, making it effectively inactive
      console.error('Registration failed, auth user remains but profile creation failed:', error);
      console.log('Auth user ID:', authData.user.id, 'will remain in auth.users but be inactive');
      throw error;
    }
  }

  static async signIn(credentials: LoginCredentials): Promise<{ user: any; profile: UserProfile }> {
    const { data: authData, error: authError } = await supabase.auth.signInWithPassword({
      email: credentials.email,
      password: credentials.password
    });

    if (authError) throw authError;

    // Get user profile
    const profile = await this.getCurrentUserProfile();
    if (!profile) throw new Error('User profile not found');

    // Update last login
    await this.updateProfile(profile.id, { last_login: new Date().toISOString() });

    // Log the login action
    await this.logAction('user_login');

    return { user: authData.user, profile };
  }

  static async signOut(): Promise<void> {
    await this.logAction('user_logout');
    const { error } = await supabase.auth.signOut();
    if (error) throw error;
  }

  static async getCurrentUser(): Promise<any> {
    const { data: { user } } = await supabase.auth.getUser();
    return user;
  }

  static async getCurrentUserProfile(): Promise<UserProfile | null> {
    const user = await this.getCurrentUser();
    if (!user) return null;

    const { data, error } = await supabase
      .from('user_profiles')
      .select(`
        *,
        role:user_roles(*)
      `)
      .eq('user_id', user.id)
      .single();

    if (error) {
      if (error.code === 'PGRST116') return null;
      throw error;
    }
    return data;
  }

  // User Profile Management
  static async getAllUsers(): Promise<UserProfile[]> {
    console.log('UserService: Fetching all users from database...');
    const { data, error } = await supabase
      .from('user_profiles')
      .select(`
        *,
        role:user_roles(*)
      `)
      .order('created_at', { ascending: false });

    if (error) {
      console.error('UserService: Error fetching users:', error);
      throw error;
    }

    console.log('UserService: Fetched', data?.length || 0, 'users from database');
    return data || [];
  }

  // Get user by ID (for verification)
  static async getUserById(userId: string): Promise<UserProfile | null> {
    console.log('UserService: Fetching user by ID:', userId);
    const { data, error } = await supabase
      .from('user_profiles')
      .select(`
        *,
        role:user_roles(*)
      `)
      .eq('id', userId)
      .single();

    if (error) {
      if (error.code === 'PGRST116') {
        console.log('UserService: User not found (expected after deletion)');
        return null;
      }
      console.error('UserService: Error fetching user by ID:', error);
      throw error;
    }

    console.log('UserService: Found user:', data?.email);
    return data;
  }

  // Update user (admin only)
  static async updateUser(userId: string, updates: Partial<UserProfile>): Promise<UserProfile> {
    const { data, error } = await supabase
      .from('user_profiles')
      .update(updates)
      .eq('id', userId)
      .select(`
        *,
        role:user_roles(*)
      `)
      .single();

    if (error) throw error;
    return data;
  }



  // Resend setup email (admin only) - Now actually sends emails via SMTP
  static async resendSetupEmail(userId: string): Promise<{ emailContent?: any; status: string }> {
    console.log('UserService: Resending setup email for user:', userId);

    try {
      // First, get user info to send password reset email
      const { data: userData, error: userError } = await supabase
        .rpc('rpc_resend_setup_email', { user_profile_id: userId });

      if (userError) {
        console.error('UserService: RPC error:', userError);
        throw new Error('Failed to prepare email: ' + userError.message);
      }

      if (!userData || !userData.success) {
        throw new Error(userData?.error || 'Failed to prepare email');
      }

      const emailData = userData.email_data;
      console.log('UserService: Email data prepared:', emailData);

      // Handle different email methods
      if (emailData.method === 'supabase_password_reset') {
        console.log('UserService: Sending password reset email via Supabase SMTP...');

        try {
          const { error: resetError } = await supabase.auth.resetPasswordForEmail(
            emailData.to_email,
            {
              redirectTo: `${window.location.origin}/setup-password`
            }
          );

          if (resetError) {
            console.error('UserService: Password reset email failed:', resetError);
            this.displayEmailContent(emailData);
            return {
              emailContent: emailData,
              status: 'SMTP email failed, but email content displayed for manual sending'
            };
          }

          console.log('UserService: ✅ Password reset email sent successfully via SMTP!');
          return {
            emailContent: emailData,
            status: '✅ Password reset email sent successfully! User should check their inbox.'
          };

        } catch (smtpError) {
          console.error('UserService: SMTP sending failed:', smtpError);
          this.displayEmailContent(emailData);
          return {
            emailContent: emailData,
            status: 'SMTP sending failed. Email content displayed for manual sending.'
          };
        }
      } else if (emailData.method === 'invitation' || emailData.method === 'registration_invitation') {
        console.log('UserService: User has no auth account - sending invitation email...');

        try {
          console.log('UserService: Attempting admin email sending...');

          // Try multiple approaches for sending email
          let inviteResult;

          // First try: Standard invitation
          console.log('UserService: Trying standard invitation...');
          inviteResult = await AdminEmailService.sendInvitationEmail(
            emailData.to_email,
            emailData.action_url
          );

          // If standard invitation fails due to database conflict, try custom email
          if (!inviteResult.success && inviteResult.message.includes('Database error')) {
            console.log('UserService: Standard invitation failed, trying custom email approach...');
            inviteResult = await AdminEmailService.sendCustomEmail(
              emailData.to_email,
              emailData.subject || 'Account Setup Required',
              emailData.text_content
            );
          }

          console.log('UserService: Final email result:', inviteResult);

          if (!inviteResult.success) {
            console.warn('UserService: All email methods failed:', inviteResult.message);
            this.displayEmailContent(emailData);
            return {
              emailContent: emailData,
              status: `❌ Email sending failed: ${inviteResult.message}. Email content displayed for manual sending.`
            };
          }

          console.log('UserService: ✅ Email sent successfully via SMTP!');
          return {
            emailContent: emailData,
            status: '✅ Email sent successfully! User should check their inbox.'
          };

        } catch (inviteError) {
          console.error('UserService: Email sending failed with exception:', inviteError);
          this.displayEmailContent(emailData);
          return {
            emailContent: emailData,
            status: `❌ Email sending failed: ${inviteError.message}. Email content displayed for manual sending.`
          };
        }
      } else {
        // Fallback to manual method
        this.displayEmailContent(emailData);
        return {
          emailContent: emailData,
          status: 'Email content generated. Check console for manual sending details.'
        };
      }

    } catch (error) {
      console.error('UserService: Resend setup email failed:', error);
      throw error;
    }
  }

  // Display email content (temporary solution until email service is configured)
  private static displayEmailContent(emailData: any): void {
    console.log('');
    console.log('🔥 === EMAIL CONTENT GENERATED === 🔥');
    console.log('');
    console.log('📧 To:', emailData.to_email);
    console.log('📝 Subject:', emailData.subject || 'Account Setup');
    console.log('👤 User:', emailData.user_name);

    // Show appropriate URL based on email type
    if (emailData.method === 'invitation' && emailData.invitation_url) {
      console.log('🔗 INVITATION URL (Copy this to send to user):');
      console.log(emailData.invitation_url);
    } else if (emailData.setup_url) {
      console.log('🔗 SETUP URL (Copy this to send to user):');
      console.log(emailData.setup_url);
    } else if (emailData.setup_token) {
      console.log('🔑 Setup Token:', emailData.setup_token);
      console.log('🔗 SETUP URL (Copy this to send to user):');
      console.log(`http://localhost:5173/setup-password?token=${emailData.setup_token}&email=${emailData.to_email}`);
    }

    console.log('');
    console.log('📄 EMAIL CONTENT:');
    console.log('─'.repeat(50));
    console.log(emailData.text_content);
    console.log('─'.repeat(50));
    console.log('');

    if (emailData.method === 'invitation') {
      console.log('📝 INSTRUCTIONS FOR USER:');
      console.log('1. Send the invitation URL to the user');
      console.log('2. User clicks the link and registers');
      console.log('3. User creates their password');
      console.log('4. User can then login normally');
    } else {
      console.log('📝 INSTRUCTIONS FOR USER:');
      console.log('1. Send the setup URL to the user');
      console.log('2. User clicks the link and sets password');
      console.log('3. User can then login normally');
    }

    console.log('');
    console.log('⚙️ TO ENABLE AUTOMATIC EMAIL SENDING:');
    console.log('1. Configure admin API permissions in Supabase');
    console.log('2. Or use external email service (SendGrid, Mailgun)');
    console.log('3. SMTP is configured but admin API needs permissions');
    console.log('');
  }

  // Test admin email configuration
  static async testAdminEmailConfig(): Promise<void> {
    console.log('=== TESTING ADMIN EMAIL CONFIGURATION ===');

    try {
      // Test admin service configuration
      AdminEmailService.debugConfiguration();

      // Test admin connection
      const connectionResult = await AdminEmailService.testAdminConnection();
      console.log('Admin connection test:', connectionResult);

      if (connectionResult.success) {
        console.log('✅ Admin API is working correctly!');
      } else {
        console.log('❌ Admin API issue:', connectionResult.message);
      }

    } catch (error) {
      console.error('❌ Admin email test failed:', error);
    }
  }



  static async updateProfile(id: string, updates: Partial<UserProfile>): Promise<UserProfile> {
    const { data, error } = await supabase
      .from('user_profiles')
      .update(updates)
      .eq('id', id)
      .select(`
        *,
        role:user_roles(*)
      `)
      .single();

    if (error) throw error;

    await this.logAction('profile_updated', 'user_profile', id, {}, updates);
    return data;
  }

  static async deleteUser(id: string): Promise<void> {
    console.log('UserService: deleteUser called for:', id);

    // Get the user profile to get email for the new deletion method
    const profile = await this.getUserById(id);
    if (!profile) throw new Error('User not found');

    console.log('UserService: Found profile, using complete admin delete function');

    // Use the new complete admin delete function that deletes from both systems
    const { data: result, error } = await supabase
      .rpc('admin_delete_user_complete', { p_email: profile.email });

    if (error) {
      console.error('UserService: Complete admin delete function failed:', error);
      throw new Error('Failed to delete user: ' + error.message);
    }

    if (!result.success) {
      console.error('UserService: Delete operation failed:', result.error);
      throw new Error(result.error || 'Failed to delete user');
    }

    console.log('UserService: ✅ User completely deleted:', result.message);
    console.log('UserService: Deleted user details:', result.deleted_user);

    await this.logAction('user_deleted', 'user_profile', id);
    console.log('UserService: deleteUser completed successfully');
  }

  static async activateUser(id: string): Promise<UserProfile> {
    return this.updateProfile(id, { is_active: true });
  }

  static async deactivateUser(id: string): Promise<UserProfile> {
    return this.updateProfile(id, { is_active: false });
  }

  // Role Management
  static async getAllRoles(): Promise<UserRole[]> {
    console.log('UserService: Loading all roles...');
    const { data, error } = await supabase
      .from('user_roles')
      .select('*')
      .eq('is_active', true)
      .order('role_display_name');

    if (error) {
      console.error('UserService: Error loading roles:', error);
      throw error;
    }

    console.log('UserService: Roles loaded successfully:', data);

    // If no roles found, return default roles for the dropdown
    if (!data || data.length === 0) {
      console.warn('UserService: No roles found in database, returning default roles');
      return [
        { id: 'admin', role_name: 'admin', role_display_name: 'Administrator', description: 'Full system access', permissions: {}, is_active: true, created_at: new Date().toISOString(), updated_at: new Date().toISOString() },
        { id: 'management', role_name: 'management', role_display_name: 'Management', description: 'Management access', permissions: {}, is_active: true, created_at: new Date().toISOString(), updated_at: new Date().toISOString() },
        { id: 'accountant', role_name: 'accountant', role_display_name: 'Accountant', description: 'Financial access', permissions: {}, is_active: true, created_at: new Date().toISOString(), updated_at: new Date().toISOString() },
        { id: 'qs', role_name: 'qs', role_display_name: 'Quantity Surveyor', description: 'QS access', permissions: {}, is_active: true, created_at: new Date().toISOString(), updated_at: new Date().toISOString() },
        { id: 'client', role_name: 'client', role_display_name: 'Client', description: 'Client access', permissions: {}, is_active: true, created_at: new Date().toISOString(), updated_at: new Date().toISOString() }
      ];
    }

    return data;
  }

  // Alias for getAllRoles for backward compatibility
  static async getRoles(): Promise<UserRole[]> {
    return this.getAllRoles();
  }

  static async getRoleById(id: string): Promise<UserRole | null> {
    const { data, error } = await supabase
      .from('user_roles')
      .select('*')
      .eq('id', id)
      .single();

    if (error) {
      if (error.code === 'PGRST116') return null;
      throw error;
    }
    return data;
  }

  static async updateUserRole(userId: string, roleId: string): Promise<UserProfile> {
    const updatedProfile = await this.updateProfile(userId, { role_id: roleId });
    await this.logAction('role_changed', 'user_profile', userId, {}, { role_id: roleId });
    return updatedProfile;
  }

  // Permission Checking
  static async hasPermission(permission: string, resource?: string): Promise<boolean> {
    const profile = await this.getCurrentUserProfile();
    if (!profile?.role) return false;

    const permissions = profile.role.permissions;
    if (!permissions) return false;

    if (resource) {
      return permissions[resource]?.[permission] === true;
    }

    // Check if user has the permission on any resource
    return Object.values(permissions).some((resourcePerms: any) => 
      resourcePerms[permission] === true
    );
  }

  static async requirePermission(permission: string, resource?: string): Promise<void> {
    const hasAccess = await this.hasPermission(permission, resource);
    if (!hasAccess) {
      throw new Error(`Access denied: Missing ${permission} permission${resource ? ` on ${resource}` : ''}`);
    }
  }

  static async isAdmin(): Promise<boolean> {
    const profile = await this.getCurrentUserProfile();
    return profile?.role?.role_name === 'admin';
  }

  // Audit Logging
  static async logAction(
    action: string,
    resourceType?: string,
    resourceId?: string,
    oldValues?: Record<string, any>,
    newValues?: Record<string, any>
  ): Promise<void> {
    try {
      const { error } = await supabase.rpc('log_user_action', {
        action_name: action,
        resource_type: resourceType,
        resource_id: resourceId,
        old_values: oldValues,
        new_values: newValues
      });

      if (error) console.error('Failed to log action:', error);
    } catch (error) {
      console.error('Failed to log action:', error);
    }
  }

  static async getAuditLogs(limit: number = 50, offset: number = 0): Promise<AuditLog[]> {
    const { data, error } = await supabase
      .from('audit_logs')
      .select('*')
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1);

    if (error) throw error;
    return data || [];
  }

  // Password Management
  static async changePassword(newPassword: string): Promise<void> {
    const { error } = await supabase.auth.updateUser({
      password: newPassword
    });

    if (error) throw error;
    await this.logAction('password_changed');
  }

  static async resetPassword(email: string): Promise<void> {
    const { error } = await supabase.auth.resetPasswordForEmail(email);
    if (error) throw error;
  }

  // Session Management
  static async getUserSessions(userId?: string): Promise<UserSession[]> {
    let query = supabase
      .from('user_sessions')
      .select('*')
      .eq('is_active', true)
      .order('created_at', { ascending: false });

    if (userId) {
      query = query.eq('user_id', userId);
    }

    const { data, error } = await query;
    if (error) throw error;
    return data || [];
  }

  static async revokeSession(sessionId: string): Promise<void> {
    const { error } = await supabase
      .from('user_sessions')
      .update({ is_active: false })
      .eq('id', sessionId);

    if (error) throw error;
    await this.logAction('session_revoked', 'user_session', sessionId);
  }

  // Complete invited user registration
  static async completeInvitedRegistration(userData: {
    email: string;
    password: string;
    first_name: string;
    last_name: string;
  }): Promise<void> {
    console.log('UserService: Completing invited registration for:', userData.email);

    try {
      // First, sign up the user with Supabase Auth
      const { data: authData, error: authError } = await supabase.auth.signUp({
        email: userData.email,
        password: userData.password,
      });

      if (authError) {
        console.error('UserService: Auth signup failed:', authError);
        throw new Error(`Authentication signup failed: ${authError.message}`);
      }

      if (!authData.user) {
        throw new Error('No user data returned from authentication signup');
      }

      console.log('UserService: Auth user created:', authData.user.id);

      // Wait a moment for the auth user to be fully committed
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Try direct profile update first (simpler approach)
      const { error: directUpdateError } = await supabase
        .from('user_profiles')
        .update({
          user_id: authData.user.id,
          first_name: userData.first_name,
          last_name: userData.last_name,
          is_verified: true,
          email_status: 'registration_completed',
          updated_at: new Date().toISOString()
        })
        .eq('email', userData.email)
        .is('user_id', null); // Only update if user_id is currently null

      if (directUpdateError) {
        console.error('UserService: Direct profile update failed:', directUpdateError);

        // Try using the robust registration function as fallback
        const { data: updateResult, error: rpcError } = await supabase
          .rpc('complete_user_registration', {
            p_email: userData.email,
            p_auth_user_id: authData.user.id,
            p_first_name: userData.first_name,
            p_last_name: userData.last_name,
            p_role_name: 'client'
          });

        if (rpcError || !updateResult?.success) {
          console.error('UserService: RPC fallback also failed:', rpcError || updateResult);

          // Clean up the auth user if both methods failed
          try {
            await supabase.auth.admin.deleteUser(authData.user.id);
            console.log('UserService: Cleaned up auth user after all update methods failed');
          } catch (cleanupError) {
            console.error('UserService: Failed to clean up auth user:', cleanupError);
          }

          const errorMessage = rpcError?.message || updateResult?.error || 'Profile creation failed';
          throw new Error(errorMessage);
        }

        console.log('UserService: Robust registration function succeeded:', updateResult.action);
      } else {
        console.log('UserService: Direct profile update succeeded');
      }

      console.log('UserService: ✅ Invited registration completed successfully');

    } catch (error: any) {
      console.error('UserService: Complete invited registration failed:', error);
      throw error;
    }
  }
}
