-- Verification script for Company Management System
-- Run this to verify that all tables and data are properly set up

-- Check if company_settings table exists and has data
SELECT 'company_settings' as table_name, COUNT(*) as record_count 
FROM public.company_settings;

-- Check if departments table exists and has data
SELECT 'departments' as table_name, COUNT(*) as record_count 
FROM public.departments;

-- Check if team_memberships table exists
SELECT 'team_memberships' as table_name, COUNT(*) as record_count 
FROM public.team_memberships;

-- Check if user_sessions table exists
SELECT 'user_sessions' as table_name, COUNT(*) as record_count 
FROM public.user_sessions;

-- Check if audit_logs table exists and has data
SELECT 'audit_logs' as table_name, COUNT(*) as record_count 
FROM public.audit_logs;

-- Check user profiles and roles
SELECT 'user_profiles' as table_name, COUNT(*) as record_count 
FROM public.user_profiles;

SELECT 'user_roles' as table_name, COUNT(*) as record_count 
FROM public.user_roles;

-- Show company settings details
SELECT 
    'Company Settings' as info_type,
    name,
    email,
    description,
    created_at
FROM public.company_settings
ORDER BY updated_at DESC
LIMIT 1;

-- Show department breakdown
SELECT 
    'Department Stats' as info_type,
    d.name as department_name,
    d.description,
    COUNT(up.id) as member_count,
    COUNT(CASE WHEN up.is_active = true THEN 1 END) as active_members
FROM public.departments d
LEFT JOIN public.user_profiles up ON up.department = d.name
GROUP BY d.id, d.name, d.description
ORDER BY d.name;

-- Show recent audit log activity (last 10 entries)
SELECT 
    'Recent Activity' as info_type,
    al.action,
    al.resource_type,
    al.resource_id,
    up.first_name || ' ' || up.last_name as user_name,
    al.created_at
FROM public.audit_logs al
LEFT JOIN public.user_profiles up ON up.id = al.user_id
ORDER BY al.created_at DESC
LIMIT 10;

-- Show user role distribution
SELECT 
    'User Role Stats' as info_type,
    ur.role_display_name,
    COUNT(up.id) as user_count,
    COUNT(CASE WHEN up.is_active = true THEN 1 END) as active_count,
    COUNT(CASE WHEN up.requires_password_setup = true THEN 1 END) as pending_count
FROM public.user_roles ur
LEFT JOIN public.user_profiles up ON up.role_id = ur.id
GROUP BY ur.id, ur.role_display_name
ORDER BY user_count DESC;

-- Check if required functions exist
SELECT 
    'Database Functions' as info_type,
    routine_name as function_name,
    routine_type
FROM information_schema.routines 
WHERE routine_schema = 'public' 
AND routine_name IN (
    'log_user_action',
    'log_audit_event',
    'admin_create_user',
    'get_company_stats'
)
ORDER BY routine_name;

-- Summary statistics
SELECT 
    'System Summary' as info_type,
    (SELECT COUNT(*) FROM public.user_profiles) as total_users,
    (SELECT COUNT(*) FROM public.user_profiles WHERE is_active = true) as active_users,
    (SELECT COUNT(*) FROM public.user_profiles WHERE requires_password_setup = true) as pending_users,
    (SELECT COUNT(*) FROM public.audit_logs WHERE created_at > NOW() - INTERVAL '24 hours') as recent_activity_24h,
    (SELECT COUNT(*) FROM public.departments) as total_departments,
    (SELECT COUNT(*) FROM public.user_roles) as total_roles;
