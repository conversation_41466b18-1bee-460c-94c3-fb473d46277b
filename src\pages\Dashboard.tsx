import React, { useState, useEffect, useCallback } from 'react';
import Layout from '@/components/layout/Layout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  BarChart3, 
  TrendingUp, 
  DollarSign, 
  Users, 
  Building, 
  Zap,
  RefreshCw,
  Settings,
  Grid3X3,
  Bell,
  Calendar
} from 'lucide-react';
import AdvancedChart, { ChartConfig } from '@/components/dashboard/AdvancedChart';
import KPIWidget, { createRevenueKPI, createProfitKPI, createProjectsKPI, KPIData } from '@/components/dashboard/KPIWidget';
import { realTimeDataService, DashboardData } from '@/lib/realTimeData';
import { useToast } from '@/components/ui/use-toast';
import RoleBasedDashboard from '@/components/dashboard/RoleBasedDashboard';
import { useRoleAccess } from '@/hooks/useRoleAccess';

const Dashboard: React.FC = () => {
  const { toast } = useToast();
  const { userRole, permissions } = useRoleAccess();
  const [dashboardData, setDashboardData] = useState<DashboardData | null>(null);
  const [loading, setLoading] = useState(true);
  const [realTimeEnabled, setRealTimeEnabled] = useState(false);
  const [lastUpdate, setLastUpdate] = useState<Date>(new Date());
  const [activeView, setActiveView] = useState('overview');

  useEffect(() => {
    loadDashboardData();
    
    // Subscribe to real-time updates
    realTimeDataService.subscribe('dashboard-all', handleRealTimeUpdate);
    
    return () => {
      realTimeDataService.unsubscribe('dashboard-all');
      realTimeDataService.stopRealTimeUpdates();
    };
  }, []);

  const loadDashboardData = async () => {
    try {
      setLoading(true);
      console.log('🔄 Loading advanced dashboard data...');
      const data = await realTimeDataService.getDashboardData();
      setDashboardData(data);
      setLastUpdate(new Date());
      console.log('✅ Dashboard data loaded successfully');
    } catch (error) {
      console.error('❌ Error loading dashboard data:', error);
      toast({
        title: "Error Loading Dashboard",
        description: "Failed to load dashboard data. Please try again.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const handleRealTimeUpdate = useCallback((update: any) => {
    const { type, data } = update;
    
    setDashboardData(prev => {
      if (!prev) return prev;
      
      switch (type) {
        case 'metrics':
          return { ...prev, metrics: data };
        case 'charts':
          return {
            ...prev,
            revenueChart: [...prev.revenueChart.slice(-29), data.revenue],
            expenseChart: [...prev.expenseChart.slice(-29), data.expense],
            cashFlowChart: [...prev.cashFlowChart.slice(-29), data.cashFlow]
          };
        default:
          return prev;
      }
    });
    
    setLastUpdate(new Date());
  }, []);

  const toggleRealTime = () => {
    if (realTimeEnabled) {
      realTimeDataService.stopRealTimeUpdates();
      setRealTimeEnabled(false);
      toast({
        title: "Real-time Updates Disabled",
        description: "Dashboard will no longer update automatically.",
      });
    } else {
      realTimeDataService.startRealTimeUpdates();
      setRealTimeEnabled(true);
      toast({
        title: "Real-time Updates Enabled",
        description: "Dashboard will update automatically every few seconds.",
      });
    }
  };

  if (loading) {
    return (
      <Layout>
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <RefreshCw className="h-12 w-12 animate-spin text-blue-600 mx-auto mb-4" />
            <p className="text-gray-600">Loading advanced dashboard...</p>
          </div>
        </div>
      </Layout>
    );
  }

  if (!dashboardData) {
    return (
      <Layout>
        <div className="text-center py-8">
          <p className="text-gray-500">No dashboard data available</p>
          <Button onClick={loadDashboardData} className="mt-4">
            Retry
          </Button>
        </div>
      </Layout>
    );
  }

  // Create role-specific KPI data
  const getAllKPIs = (): KPIData[] => [
    createRevenueKPI(dashboardData.metrics.revenue),
    createProfitKPI(dashboardData.metrics.profit),
    createProjectsKPI(dashboardData.metrics.activeProjects, dashboardData.metrics.activeProjects + dashboardData.metrics.completedProjects),
    {
      id: 'clients',
      title: 'Active Clients',
      value: dashboardData.metrics.activeClients,
      format: 'number',
      status: 'info',
      icon: <Users className="h-4 w-4 text-blue-500" />,
      color: '#3b82f6',
      animated: true
    },
    {
      id: 'cashflow',
      title: 'Cash Flow',
      value: dashboardData.metrics.cashFlow,
      format: 'currency',
      trend: dashboardData.metrics.cashFlow > 0 ? 'up' : 'down',
      status: dashboardData.metrics.cashFlow > 0 ? 'success' : 'warning',
      icon: <TrendingUp className="h-4 w-4 text-green-500" />,
      color: dashboardData.metrics.cashFlow > 0 ? '#10b981' : '#f59e0b',
      animated: true
    },
    {
      id: 'invoices',
      title: 'Pending Invoices',
      value: dashboardData.metrics.pendingInvoices,
      format: 'number',
      status: dashboardData.metrics.pendingInvoices > 10 ? 'warning' : 'success',
      icon: <Building className="h-4 w-4 text-purple-500" />,
      color: '#8b5cf6',
      animated: true
    }
  ];

  // Filter KPIs based on user role
  const getFilteredKPIs = (): KPIData[] => {
    const allKPIs = getAllKPIs();

    switch (userRole) {
      case 'admin':
      case 'management':
        // Admin and management see all KPIs
        return allKPIs;

      case 'accountant':
        // Accountants see financial KPIs only
        return allKPIs.filter(kpi =>
          ['revenue', 'profit', 'cashflow', 'invoices', 'clients'].includes(kpi.id)
        );

      case 'qs':
        // QS sees project-related KPIs only
        return allKPIs.filter(kpi =>
          ['projects', 'clients'].includes(kpi.id)
        );

      case 'client':
        // Clients see limited project info only
        return allKPIs.filter(kpi =>
          ['projects'].includes(kpi.id)
        );

      default:
        return allKPIs.filter(kpi =>
          ['projects'].includes(kpi.id)
        );
    }
  };

  const kpiData = getFilteredKPIs();

  // Create chart configurations
  const revenueChartConfig: ChartConfig = {
    type: 'area',
    title: 'Revenue Trend',
    description: 'Daily revenue over the last 30 days',
    data: dashboardData.revenueChart,
    dataKeys: ['value'],
    colors: ['#10b981'],
    height: 300,
    animated: true,
    gradient: true
  };

  const expenseChartConfig: ChartConfig = {
    type: 'bar',
    title: 'Expense Analysis',
    description: 'Daily expenses breakdown',
    data: dashboardData.expenseChart,
    dataKeys: ['value'],
    colors: ['#ef4444'],
    height: 300,
    animated: true
  };

  const cashFlowChartConfig: ChartConfig = {
    type: 'composed',
    title: 'Cash Flow Analysis',
    description: 'Inflow vs Outflow comparison',
    data: dashboardData.cashFlowChart,
    dataKeys: ['inflow', 'outflow', 'netFlow'],
    colors: ['#10b981', '#ef4444', '#3b82f6'],
    height: 350,
    animated: true
  };

  const projectChartConfig: ChartConfig = {
    type: 'pie',
    title: 'Project Status Distribution',
    description: 'Current project status breakdown',
    data: dashboardData.projectChart,
    dataKeys: ['value'],
    colors: ['#3b82f6', '#10b981', '#f59e0b', '#ef4444'],
    height: 300,
    animated: true
  };

  return (
    <Layout>
      <div className="space-y-6">
        {/* Role-Based Dashboard */}
        <RoleBasedDashboard />

        {/* Header */}
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Advanced Dashboard</h1>
            <p className="text-gray-600 mt-1">
              Real-time insights and comprehensive analytics for your construction business
            </p>
          </div>
          <div className="flex items-center space-x-3">
            <Badge variant={realTimeEnabled ? "default" : "secondary"} className="flex items-center space-x-1">
              <div className={`w-2 h-2 rounded-full ${realTimeEnabled ? 'bg-green-500 animate-pulse' : 'bg-gray-400'}`} />
              <span>{realTimeEnabled ? 'Live' : 'Static'}</span>
            </Badge>
            <Button variant="outline" onClick={toggleRealTime}>
              <Zap className={`w-4 h-4 mr-2 ${realTimeEnabled ? 'text-green-500' : ''}`} />
              {realTimeEnabled ? 'Disable' : 'Enable'} Real-time
            </Button>
            <Button variant="outline" onClick={loadDashboardData} disabled={loading}>
              <RefreshCw className={`w-4 h-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
              Refresh
            </Button>
            <Button variant="outline">
              <Settings className="w-4 h-4 mr-2" />
              Customize
            </Button>
          </div>
        </div>

        {/* Last Update Info */}
        <div className="flex items-center justify-between bg-blue-50 border border-blue-200 rounded-lg p-3">
          <div className="flex items-center space-x-2">
            <Calendar className="h-4 w-4 text-blue-500" />
            <span className="text-sm text-blue-700">
              Last updated: {lastUpdate.toLocaleTimeString()}
            </span>
          </div>
          {dashboardData.alerts.length > 0 && (
            <div className="flex items-center space-x-2">
              <Bell className="h-4 w-4 text-orange-500" />
              <span className="text-sm text-orange-700">
                {dashboardData.alerts.length} alert{dashboardData.alerts.length !== 1 ? 's' : ''}
              </span>
            </div>
          )}
        </div>

        {/* KPI Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-6">
          {kpiData.map((kpi) => (
            <KPIWidget
              key={kpi.id}
              data={kpi}
              variant="modern"
              size="md"
              loading={loading}
            />
          ))}
        </div>

        {/* Alerts */}
        {dashboardData.alerts.length > 0 && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {dashboardData.alerts.map((alert) => (
              <Card key={alert.id} className={`border-l-4 ${
                alert.type === 'warning' ? 'border-yellow-500 bg-yellow-50' :
                alert.type === 'info' ? 'border-blue-500 bg-blue-50' :
                'border-red-500 bg-red-50'
              }`}>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-semibold">{alert.title}</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-sm text-gray-700">{alert.message}</p>
                  <p className="text-xs text-gray-500 mt-2">
                    {alert.timestamp.toLocaleTimeString()}
                  </p>
                </CardContent>
              </Card>
            ))}
          </div>
        )}

        {/* Main Charts */}
        <Tabs value={activeView} onValueChange={setActiveView} className="space-y-6">
          <TabsList className={`grid w-full ${permissions.canAccessFinancials ? 'grid-cols-4' : 'grid-cols-2'}`}>
            <TabsTrigger value="overview" className="flex items-center space-x-2">
              <Grid3X3 className="w-4 h-4" />
              <span>Overview</span>
            </TabsTrigger>
            {permissions.canAccessFinancials && (
              <TabsTrigger value="financial" className="flex items-center space-x-2">
                <DollarSign className="w-4 h-4" />
                <span>Financial</span>
              </TabsTrigger>
            )}
            {permissions.canAccessProjects && (
              <TabsTrigger value="projects" className="flex items-center space-x-2">
                <Building className="w-4 h-4" />
                <span>Projects</span>
              </TabsTrigger>
            )}
            {permissions.canViewAnalytics && (
              <TabsTrigger value="analytics" className="flex items-center space-x-2">
                <BarChart3 className="w-4 h-4" />
                <span>Analytics</span>
              </TabsTrigger>
            )}
          </TabsList>

          <TabsContent value="overview" className="space-y-6">
            {permissions.canAccessFinancials ? (
              <>
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  <AdvancedChart
                    config={revenueChartConfig}
                    loading={loading}
                    onRefresh={loadDashboardData}
                  />
                  <AdvancedChart
                    config={expenseChartConfig}
                    loading={loading}
                    onRefresh={loadDashboardData}
                  />
                </div>
                <AdvancedChart
                  config={cashFlowChartConfig}
                  loading={loading}
                  onRefresh={loadDashboardData}
                />
              </>
            ) : (
              <div className="text-center py-12">
                <Building className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-gray-600 mb-2">
                  Welcome to your {userRole.charAt(0).toUpperCase() + userRole.slice(1)} Dashboard
                </h3>
                <p className="text-gray-500">
                  {userRole === 'client'
                    ? 'View your project progress and communicate with the team.'
                    : userRole === 'qs'
                    ? 'Access project information and cost estimation tools.'
                    : 'Access the features available to your role using the navigation menu.'
                  }
                </p>
              </div>
            )}
          </TabsContent>

          {permissions.canAccessFinancials && (
            <TabsContent value="financial" className="space-y-6">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <AdvancedChart
                  config={revenueChartConfig}
                  loading={loading}
                  onRefresh={loadDashboardData}
                />
                <AdvancedChart
                  config={cashFlowChartConfig}
                  loading={loading}
                  onRefresh={loadDashboardData}
                />
              </div>
            </TabsContent>
          )}

          <TabsContent value="projects" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <AdvancedChart
                config={projectChartConfig}
                loading={loading}
                onRefresh={loadDashboardData}
              />
              <Card>
                <CardHeader>
                  <CardTitle>Project Trends</CardTitle>
                  <CardDescription>Key project performance indicators</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {dashboardData.trends.map((trend, index) => (
                      <div key={index} className="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                        <div>
                          <p className="font-medium">{trend.metric}</p>
                          <p className="text-sm text-gray-500">{trend.period}</p>
                        </div>
                        <div className="flex items-center space-x-2">
                          <span className="font-bold">{trend.value}</span>
                          {trend.trend === 'up' && <TrendingUp className="h-4 w-4 text-green-500" />}
                          {trend.trend === 'down' && <TrendingUp className="h-4 w-4 text-red-500 rotate-180" />}
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          {permissions.canViewAnalytics && (
            <TabsContent value="analytics" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <AdvancedChart
                config={{
                  ...expenseChartConfig,
                  type: 'line',
                  title: 'Expense Trends',
                  description: 'Monthly expense analysis'
                }}
                loading={loading}
                onRefresh={loadDashboardData}
              />
              <Card>
                <CardHeader>
                  <CardTitle>Top Clients</CardTitle>
                  <CardDescription>Revenue by client</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {dashboardData.clientChart.map((client, index) => (
                      <div key={index} className="flex justify-between items-center">
                        <span className="font-medium">{client.clientName}</span>
                        <div className="text-right">
                          <span className="font-bold">${client.amount.toLocaleString()}</span>
                          <span className="text-sm text-gray-500 ml-2">
                            ({client.percentage.toFixed(1)}%)
                          </span>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>
            </TabsContent>
          )}
        </Tabs>
      </div>
    </Layout>
  );
};

export default Dashboard;
