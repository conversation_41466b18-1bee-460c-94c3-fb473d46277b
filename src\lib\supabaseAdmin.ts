import { createClient } from '@supabase/supabase-js'

// Admin client for server-side operations
const supabaseUrl = import.meta.env.VITE_SUPABASE_URL
const supabaseServiceRoleKey = import.meta.env.VITE_SUPABASE_SERVICE_ROLE_KEY

// Create admin client with service role key
export const supabaseAdmin = createClient(supabaseUrl, supabaseServiceRoleKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
})

// Admin email service
export class AdminEmailService {
  // Send invitation email using admin privileges
  static async sendInvitationEmail(email: string, redirectUrl?: string): Promise<{ success: boolean; message: string }> {
    try {
      console.log('AdminEmailService: Sending invitation to:', email);
      console.log('AdminEmailService: Service role key configured:', this.isConfigured());
      console.log('AdminEmailService: Redirect URL:', redirectUrl);

      if (!supabaseServiceRoleKey || supabaseServiceRoleKey === 'your_service_role_key_here') {
        console.error('AdminEmailService: Service role key not configured');
        return {
          success: false,
          message: 'Service role key not configured. Please add VITE_SUPABASE_SERVICE_ROLE_KEY to .env.local'
        };
      }

      // First check if user already exists in auth
      console.log('AdminEmailService: Checking if user exists in auth...');
      const { data: existingUsers } = await supabaseAdmin.auth.admin.listUsers();
      const existingUser = existingUsers.users.find(user => user.email === email);

      if (existingUser) {
        console.log('AdminEmailService: User already exists in auth, sending password reset instead...');
        return await this.sendPasswordResetEmail(email);
      }

      console.log('AdminEmailService: User not in auth, attempting admin invite...');
      const { data, error } = await supabaseAdmin.auth.admin.inviteUserByEmail(email, {
        redirectTo: redirectUrl || `http://192.168.1.37:8080/register?email=${email}&invited=true`
      });

      if (error) {
        console.error('AdminEmailService: Invitation failed with error:', error);
        console.error('AdminEmailService: Error details:', {
          message: error.message,
          status: error.status,
          statusText: error.statusText
        });

        // If it's a database error (user exists), try password reset instead
        if (error.message.includes('Database error') || error.message.includes('already exists')) {
          console.log('AdminEmailService: Database conflict, trying password reset...');
          return await this.sendPasswordResetEmail(email);
        }

        return {
          success: false,
          message: `Invitation failed: ${error.message} (Status: ${error.status})`
        };
      }

      console.log('AdminEmailService: ✅ Invitation sent successfully!');
      console.log('AdminEmailService: Response data:', data);
      return {
        success: true,
        message: 'Invitation email sent successfully via SMTP'
      };

    } catch (error: any) {
      console.error('AdminEmailService: Invitation error (catch block):', error);
      console.error('AdminEmailService: Error stack:', error.stack);
      return {
        success: false,
        message: `Invitation error: ${error.message}`
      };
    }
  }

  // Send password reset email using admin privileges
  static async sendPasswordResetEmail(email: string): Promise<{ success: boolean; message: string }> {
    try {
      console.log('AdminEmailService: Sending password reset to:', email);
      
      if (!supabaseServiceRoleKey || supabaseServiceRoleKey === 'your_service_role_key_here') {
        return {
          success: false,
          message: 'Service role key not configured. Please add VITE_SUPABASE_SERVICE_ROLE_KEY to .env.local'
        };
      }

      // Use regular client for password reset (doesn't require admin)
      const { error } = await supabaseAdmin.auth.resetPasswordForEmail(email, {
        redirectTo: `http://192.168.1.37:8080/setup-password`
      });

      if (error) {
        console.error('AdminEmailService: Password reset failed:', error);
        return {
          success: false,
          message: `Password reset failed: ${error.message}`
        };
      }

      console.log('AdminEmailService: Password reset sent successfully');
      return {
        success: true,
        message: 'Password reset email sent successfully via SMTP'
      };

    } catch (error: any) {
      console.error('AdminEmailService: Password reset error:', error);
      return {
        success: false,
        message: `Password reset error: ${error.message}`
      };
    }
  }

  // Test admin connection
  static async testAdminConnection(): Promise<{ success: boolean; message: string }> {
    try {
      if (!supabaseServiceRoleKey || supabaseServiceRoleKey === 'your_service_role_key_here') {
        return {
          success: false,
          message: 'Service role key not configured'
        };
      }

      // Test by listing users (admin operation)
      const { data, error } = await supabaseAdmin.auth.admin.listUsers();

      if (error) {
        return {
          success: false,
          message: `Admin connection failed: ${error.message}`
        };
      }

      return {
        success: true,
        message: `Admin connection successful. Found ${data.users.length} users.`
      };

    } catch (error: any) {
      return {
        success: false,
        message: `Admin connection error: ${error.message}`
      };
    }
  }

  // Check if service role key is configured
  static isConfigured(): boolean {
    return !!(supabaseServiceRoleKey && supabaseServiceRoleKey !== 'your_service_role_key_here');
  }

  // Send custom email using SMTP (bypasses auth user creation)
  static async sendCustomEmail(
    to: string,
    subject: string,
    content: string
  ): Promise<{ success: boolean; message: string }> {
    try {
      console.log('AdminEmailService: Sending custom email to:', to);

      if (!this.isConfigured()) {
        return {
          success: false,
          message: 'Service role key not configured'
        };
      }

      // Use Supabase Edge Function or direct SMTP call
      // For now, we'll use a simple approach with the auth reset function
      // which uses SMTP without creating users

      const { error } = await supabaseAdmin.auth.resetPasswordForEmail(to, {
        redirectTo: `http://192.168.1.37:8080/invited-register?email=${to}&invited=true`
      });

      if (error) {
        console.error('AdminEmailService: Custom email failed:', error);
        return {
          success: false,
          message: `Custom email failed: ${error.message}`
        };
      }

      console.log('AdminEmailService: ✅ Custom email sent successfully!');
      return {
        success: true,
        message: 'Custom email sent successfully via SMTP'
      };

    } catch (error: any) {
      console.error('AdminEmailService: Custom email error:', error);
      return {
        success: false,
        message: `Custom email error: ${error.message}`
      };
    }
  }

  // Debug function to check configuration
  static debugConfiguration(): void {
    console.log('=== ADMIN EMAIL SERVICE DEBUG ===');
    console.log('Supabase URL:', supabaseUrl);
    console.log('Service Role Key configured:', this.isConfigured());
    console.log('Service Role Key length:', supabaseServiceRoleKey?.length || 0);
    console.log('Service Role Key starts with:', supabaseServiceRoleKey?.substring(0, 20) + '...');
    console.log('Admin client created:', !!supabaseAdmin);
  }
}

export default supabaseAdmin;
