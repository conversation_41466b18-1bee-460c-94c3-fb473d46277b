
import React, { useState, useEffect } from 'react';
import Layout from '@/components/layout/Layout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import {
  DollarSign,
  TrendingUp,
  TrendingDown,
  Users,
  FileText,
  Receipt,
  CreditCard,
  Building,
  PieChart,
  BarChart3,
  Calendar,
  Filter,
  Download,
  Plus,
  Eye,
  AlertCircle,
  CheckCircle,
  Clock,
  RefreshCw
} from 'lucide-react';
import { useFinancials } from '@/hooks/useFinancials';
import ProtectedComponent from '@/components/auth/ProtectedComponent';
import { FinancialService } from '@/lib/financials';
import { FinancialService as ClientFinancialService } from '@/lib/financialService';
import ExpenseForm from '@/components/financials/ExpenseForm';
import InvoiceTracking from '@/components/financials/InvoiceTracking';
import CashFlowStatements from '@/components/financials/CashFlowStatements';
import RevenueManagement from '@/components/financials/RevenueManagement';
import CompanyAssets from '@/components/financials/CompanyAssets';



const Financials: React.FC = () => {
  const {
    clientFinancials,
    expenses,
    expenseCategories,
    cashFlowTransactions,
    companyAssets,
    paymentRecords,
    financialSummary,
    cashFlowData,
    cashFlowBreakdown,
    loading,
    error,
    loadFinancialData,
    createExpense,
    getCashFlowDataForPeriod,
    refreshFinancialSummary
  } = useFinancials();

  const [activeTab, setActiveTab] = useState('dashboard');
  const [integratedData, setIntegratedData] = useState<any>(null);
  const [integratedLoading, setIntegratedLoading] = useState(true);
  const [revenueEntries, setRevenueEntries] = useState<any[]>([]);
  const [revenueLoading, setRevenueLoading] = useState(true);
  const [recentActivity, setRecentActivity] = useState<any[]>([]);
  const [activityLoading, setActivityLoading] = useState(true);

  // Load revenue entries for accurate totals
  const loadRevenueEntries = async () => {
    try {
      setRevenueLoading(true);
      const entries = await FinancialService.getRevenueEntries();
      setRevenueEntries(entries);
    } catch (error) {
      console.error('Error loading revenue entries:', error);
      setRevenueEntries([]);
    } finally {
      setRevenueLoading(false);
    }
  };

  // Load real recent activity from database
  const loadRecentActivity = async () => {
    try {
      setActivityLoading(true);
      console.log('🔄 Loading recent activity from database...');
      const activity = await FinancialService.getRecentActivity(10);
      console.log('✅ Loaded recent activity:', activity);
      setRecentActivity(activity);
    } catch (error) {
      console.error('❌ Error loading recent activity:', error);
      setRecentActivity([]);
    } finally {
      setActivityLoading(false);
    }
  };

  // Load integrated financial data from client system
  useEffect(() => {
    const loadIntegratedData = async () => {
      try {
        setIntegratedLoading(true);
        const overview = await ClientFinancialService.getFinancialOverview();
        const recentActivity = await ClientFinancialService.getRecentActivity(10);
        const paymentTrends = await ClientFinancialService.getPaymentTrends(6);
        const topClients = await ClientFinancialService.getTopClients(5);
        const industryBreakdown = await ClientFinancialService.getIndustryBreakdown();

        setIntegratedData({
          overview,
          recentActivity,
          paymentTrends,
          topClients,
          industryBreakdown
        });
      } catch (error) {
        console.error('Error loading integrated financial data:', error);
      } finally {
        setIntegratedLoading(false);
      }
    };

    loadIntegratedData();
    loadRevenueEntries(); // Load revenue entries for accurate totals
    loadRecentActivity(); // Load real recent activity from database
  }, []);

  if (loading) {
    return (
      <Layout>
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p className="text-gray-600">Loading financial data...</p>
          </div>
        </div>
      </Layout>
    );
  }

  if (error) {
    return (
      <Layout>
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
            <p className="text-red-600 mb-4">{error}</p>
            <Button onClick={loadFinancialData}>Retry</Button>
          </div>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <ProtectedComponent resource="financials">
        <div className="space-y-6">
        {/* Header */}
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Financial Management</h1>
            <p className="text-gray-600 mt-1">
              Comprehensive financial tracking and analysis for your construction business
            </p>
          </div>
          <div className="flex items-center space-x-3">
            <Button
              variant="outline"
              onClick={async () => {
                setIntegratedLoading(true);
                await loadFinancialData();
                await refreshFinancialSummary();
                await loadRevenueEntries();
                await loadRecentActivity();

                // Refresh integrated client data
                try {
                  const overview = await ClientFinancialService.getFinancialOverview();
                  const recentActivity = await ClientFinancialService.getRecentActivity(10);
                  const paymentTrends = await ClientFinancialService.getPaymentTrends(6);
                  const topClients = await ClientFinancialService.getTopClients(5);
                  const industryBreakdown = await ClientFinancialService.getIndustryBreakdown();

                  setIntegratedData({
                    overview,
                    recentActivity,
                    paymentTrends,
                    topClients,
                    industryBreakdown
                  });
                } catch (error) {
                  console.error('Error refreshing integrated data:', error);
                } finally {
                  setIntegratedLoading(false);
                }
              }}
            >
              <RefreshCw className="w-4 h-4 mr-2" />
              Refresh Data
            </Button>



            <Button
              variant="outline"
              onClick={async () => {
                try {
                  const result = await FinancialService.syncPaidInvoicesToRevenue();
                  await loadRevenueEntries();
                  await loadRecentActivity();

                  // Show toast with results
                  if (result.synced > 0) {
                    alert(`✅ Successfully synced ${result.synced} paid invoices to revenue entries!`);
                  } else if (result.skipped > 0) {
                    alert(`ℹ️ All ${result.skipped} paid invoices already have revenue entries.`);
                  } else {
                    alert('ℹ️ No paid invoices found to sync.');
                  }
                } catch (error) {
                  console.error('❌ Error syncing invoices:', error);
                  alert('❌ Error syncing paid invoices. Check console for details.');
                }
              }}
            >
              <RefreshCw className="w-4 h-4 mr-2" />
              Sync Paid Invoices
            </Button>

            <Button
              variant="outline"
              onClick={async () => {
                try {
                  const result = await FinancialService.syncToCashFlowTransactions();
                  await loadFinancialData();
                  await refreshFinancialSummary();

                  // Show toast with results
                  if (result.synced > 0) {
                    alert(`✅ Successfully synced ${result.synced} transactions to cash flow!`);
                  } else if (result.skipped > 0) {
                    alert(`ℹ️ All ${result.skipped} transactions already in cash flow.`);
                  } else {
                    alert('ℹ️ No transactions found to sync to cash flow.');
                  }
                } catch (error) {
                  console.error('❌ Error syncing cash flow:', error);
                  alert('❌ Error syncing cash flow. Check console for details.');
                }
              }}
            >
              <BarChart3 className="w-4 h-4 mr-2" />
              Sync Cash Flow
            </Button>
            <Button variant="outline">
              <Download className="w-4 h-4 mr-2" />
              Export Report
            </Button>
            <ExpenseForm
              expenseCategories={expenseCategories}
              onExpenseCreated={createExpense}
            />
          </div>
        </div>

        {/* Quick Stats Cards - Integrated with Client Financial Data */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Revenue</CardTitle>
              <DollarSign className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-600">
                {revenueLoading ? "Loading..." : `$${revenueEntries.reduce((sum, entry) => sum + (entry.amount || 0), 0).toLocaleString()}`}
              </div>
              <p className="text-xs text-muted-foreground">
                <TrendingUp className="w-3 h-3 inline mr-1" />
                {revenueLoading ? "Loading revenue data..." : `${revenueEntries.length} revenue entries`}
                {!revenueLoading && revenueEntries.length === 0 && " (No revenue data found)"}
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Expenses</CardTitle>
              <Receipt className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-red-600">
                ${financialSummary.totalExpenses.toLocaleString()}
              </div>
              <p className="text-xs text-muted-foreground">
                Business expenses this period
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Net Profit</CardTitle>
              <TrendingUp className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className={`text-2xl font-bold ${
                (revenueEntries.reduce((sum, entry) => sum + (entry.amount || 0), 0) - financialSummary.totalExpenses >= 0 ? 'text-green-600' : 'text-red-600')}`}>
                ${(revenueEntries.reduce((sum, entry) => sum + (entry.amount || 0), 0) - financialSummary.totalExpenses).toLocaleString()}
              </div>
              <p className="text-xs text-muted-foreground">
                {(revenueEntries.reduce((sum, entry) => sum + (entry.amount || 0), 0) - financialSummary.totalExpenses >= 0) ? (
                  <TrendingUp className="w-3 h-3 inline mr-1" />
                ) : (
                  <TrendingDown className="w-3 h-3 inline mr-1" />
                )}
                {revenueEntries.length > 0 ?
                  `${(((revenueEntries.reduce((sum, entry) => sum + (entry.amount || 0), 0) - financialSummary.totalExpenses) / revenueEntries.reduce((sum, entry) => sum + (entry.amount || 0), 0)) * 100).toFixed(1)}% margin` :
                  'No revenue data'}
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Outstanding</CardTitle>
              <CreditCard className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className={`text-2xl font-bold ${integratedData ?
                (integratedData.overview.outstandingAmount > 0 ? 'text-orange-600' : 'text-green-600') :
                (financialSummary.netCashFlow >= 0 ? 'text-green-600' : 'text-red-600')}`}>
                ${integratedData ?
                  integratedData.overview.outstandingAmount.toLocaleString() :
                  financialSummary.netCashFlow.toLocaleString()}
              </div>
              <p className="text-xs text-muted-foreground">
                {integratedData ? (
                  integratedData.overview.outstandingAmount > 0 ? (
                    <AlertCircle className="w-3 h-3 inline mr-1" />
                  ) : (
                    <CheckCircle className="w-3 h-3 inline mr-1" />
                  )
                ) : (
                  financialSummary.netCashFlow >= 0 ? (
                    <CheckCircle className="w-3 h-3 inline mr-1" />
                  ) : (
                    <AlertCircle className="w-3 h-3 inline mr-1" />
                  )
                )}
                {integratedData ?
                  `${integratedData.overview.activeClients} active clients` :
                  'Net cash flow this period'}
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Main Content Tabs */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
          <TabsList className="grid w-full grid-cols-9">
            <TabsTrigger value="dashboard" className="flex items-center space-x-2">
              <PieChart className="w-4 h-4" />
              <span className="hidden sm:inline">Dashboard</span>
            </TabsTrigger>
            <TabsTrigger value="clients" className="flex items-center space-x-2">
              <Users className="w-4 h-4" />
              <span className="hidden sm:inline">Clients</span>
            </TabsTrigger>
            <TabsTrigger value="quotations" className="flex items-center space-x-2">
              <FileText className="w-4 h-4" />
              <span className="hidden sm:inline">Quotes</span>
            </TabsTrigger>
            <TabsTrigger value="invoices" className="flex items-center space-x-2">
              <Receipt className="w-4 h-4" />
              <span className="hidden sm:inline">Invoices</span>
            </TabsTrigger>
            <TabsTrigger value="expenses" className="flex items-center space-x-2">
              <CreditCard className="w-4 h-4" />
              <span className="hidden sm:inline">Expenses</span>
            </TabsTrigger>
            <TabsTrigger value="assets" className="flex items-center space-x-2">
              <Building className="w-4 h-4" />
              <span className="hidden sm:inline">Assets</span>
            </TabsTrigger>
            <TabsTrigger value="revenue" className="flex items-center space-x-2">
              <TrendingUp className="w-4 h-4" />
              <span className="hidden sm:inline">Revenue</span>
            </TabsTrigger>
            <TabsTrigger value="cashflow" className="flex items-center space-x-2">
              <BarChart3 className="w-4 h-4" />
              <span className="hidden sm:inline">Cash Flow</span>
            </TabsTrigger>

          </TabsList>

          <TabsContent value="dashboard" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle>Recent Client Activity</CardTitle>
                  <CardDescription>Latest payments and invoices from clients</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {activityLoading ? (
                      <div className="text-center py-4">
                        <p className="text-gray-500">Loading recent activity...</p>
                      </div>
                    ) : recentActivity.length > 0 ? (
                      recentActivity.slice(0, 5).map((activity: any, index: number) => (
                        <div key={`${activity.type}-${activity.id}-${index}`} className="flex items-center justify-between p-3 border rounded-lg">
                          <div>
                            <p className="font-medium">{activity.client}</p>
                            <p className="text-sm text-gray-500">
                              {new Date(activity.date).toLocaleDateString()} • {activity.description}
                            </p>
                            {activity.project && (
                              <p className="text-xs text-blue-600">Project: {activity.project}</p>
                            )}
                          </div>
                          <div className="text-right">
                            <p className={`font-bold ${activity.amount >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                              {activity.amount >= 0 ? '+' : ''}${Math.abs(activity.amount).toLocaleString()}
                            </p>
                            <Badge variant={
                              activity.type === 'revenue' ? 'default' :
                              activity.type === 'expense' ? 'destructive' :
                              activity.type === 'inflow' ? 'default' :
                              activity.type === 'outflow' ? 'destructive' :
                              'outline'
                            }>
                              {activity.type === 'revenue' ? 'Revenue' :
                               activity.type === 'expense' ? 'Expense' :
                               activity.type === 'inflow' ? 'Cash In' :
                               activity.type === 'outflow' ? 'Cash Out' :
                               activity.type}
                            </Badge>
                            {activity.payment_method && (
                              <p className="text-xs text-gray-500 mt-1">{activity.payment_method}</p>
                            )}
                          </div>
                        </div>
                      ))
                    ) : (
                      <p className="text-gray-500 text-center py-4">No recent activity found</p>
                    )}
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Financial Overview</CardTitle>
                  <CardDescription>Integrated client and business metrics</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="flex justify-between items-center p-3 bg-green-50 rounded-lg">
                      <span className="font-medium text-green-800">Client Revenue</span>
                      <span className="font-bold text-green-600">
                        ${revenueEntries.reduce((sum, entry) => sum + (entry.amount || 0), 0).toLocaleString()}
                      </span>
                    </div>
                    <div className="flex justify-between items-center p-3 bg-blue-50 rounded-lg">
                      <span className="font-medium text-blue-800">Payments Received</span>
                      <span className="font-bold text-blue-600">
                        ${integratedData ? integratedData.overview.totalPaid.toLocaleString() : '0'}
                      </span>
                    </div>
                    <div className="flex justify-between items-center p-3 bg-orange-50 rounded-lg">
                      <span className="font-medium text-orange-800">Outstanding</span>
                      <span className="font-bold text-orange-600">
                        ${integratedData ? integratedData.overview.outstandingAmount.toLocaleString() : '0'}
                      </span>
                    </div>
                    <div className="flex justify-between items-center p-3 bg-red-50 rounded-lg">
                      <span className="font-medium text-red-800">Business Expenses</span>
                      <span className="font-bold text-red-600">${financialSummary.totalExpenses.toLocaleString()}</span>
                    </div>
                    <div className={`flex justify-between items-center p-3 rounded-lg ${
                      (revenueEntries.reduce((sum, entry) => sum + (entry.amount || 0), 0) - financialSummary.totalExpenses >= 0 ? 'bg-emerald-50' : 'bg-red-50')
                    }`}>
                      <span className={`font-medium ${
                        (revenueEntries.reduce((sum, entry) => sum + (entry.amount || 0), 0) - financialSummary.totalExpenses >= 0 ? 'text-emerald-800' : 'text-red-800')
                      }`}>Net Profit</span>
                      <span className={`font-bold ${
                        (revenueEntries.reduce((sum, entry) => sum + (entry.amount || 0), 0) - financialSummary.totalExpenses >= 0 ? 'text-emerald-600' : 'text-red-600')
                      }`}>
                        ${(revenueEntries.reduce((sum, entry) => sum + (entry.amount || 0), 0) - financialSummary.totalExpenses).toLocaleString()}
                      </span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Additional Client Analytics */}
            {integratedData && (
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mt-6">
                <Card>
                  <CardHeader>
                    <CardTitle>Top Clients by Revenue</CardTitle>
                    <CardDescription>Highest revenue generating clients</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      {integratedData.topClients.slice(0, 5).map((client: any, index: number) => (
                        <div key={client.client_id} className="flex items-center justify-between p-3 border rounded-lg">
                          <div className="flex items-center space-x-3">
                            <div className={`w-8 h-8 rounded-full flex items-center justify-center text-white font-bold ${
                              index === 0 ? 'bg-yellow-500' :
                              index === 1 ? 'bg-gray-400' :
                              index === 2 ? 'bg-orange-500' : 'bg-blue-500'
                            }`}>
                              {index + 1}
                            </div>
                            <div>
                              <p className="font-medium">{client.client_name}</p>
                              <p className="text-sm text-gray-500">{client.payment_count} payments • {client.project_count} projects</p>
                            </div>
                          </div>
                          <div className="text-right">
                            <p className="font-bold text-green-600">${client.total_revenue.toLocaleString()}</p>
                            <Badge variant="outline">{client.industry}</Badge>
                          </div>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle>Industry Performance</CardTitle>
                    <CardDescription>Revenue breakdown by industry</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      {integratedData.industryBreakdown.slice(0, 5).map((industry: any) => (
                        <div key={industry.industry} className="space-y-2">
                          <div className="flex justify-between items-center">
                            <span className="font-medium">{industry.industry}</span>
                            <span className="font-bold">${industry.revenue.toLocaleString()}</span>
                          </div>
                          <div className="flex justify-between text-sm text-gray-500">
                            <span>{industry.client_count} clients</span>
                            <span>${industry.avg_revenue_per_client.toLocaleString()} avg/client</span>
                          </div>
                          <div className="w-full bg-gray-200 rounded-full h-2">
                            <div
                              className="bg-blue-600 h-2 rounded-full"
                              style={{
                                width: `${Math.min(100, (industry.revenue / Math.max(...integratedData.industryBreakdown.map((i: any) => i.revenue))) * 100)}%`
                              }}
                            ></div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </div>
            )}
          </TabsContent>

          <TabsContent value="clients" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Client Financial Management</CardTitle>
                <CardDescription>Track client payments and outstanding amounts</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-center py-8">
                  <Building className="h-12 w-12 text-blue-600 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">Client Financial Management</h3>
                  <p className="text-gray-600 mb-4">
                    Comprehensive client financial management is now available in the dedicated Clients section.
                  </p>
                  <div className="flex justify-center space-x-3">
                    <Button
                      onClick={() => window.location.href = '/clients'}
                      className="flex items-center"
                    >
                      <Building className="h-4 w-4 mr-2" />
                      Go to Client Management
                    </Button>
                    <Button
                      variant="outline"
                      onClick={() => window.location.href = '/clients?tab=financials'}
                    >
                      View Financial Dashboard
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="quotations" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Quotation Financial Tracking</CardTitle>
                <CardDescription>Monitor quotation values and conversion rates</CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-gray-500">Quotation tracking interface will be implemented here.</p>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="invoices" className="space-y-6">

            <InvoiceTracking
              paymentRecords={paymentRecords}
              onPaymentRecorded={async () => {
                console.log('🔄 Refreshing financial data after payment...');
                setIntegratedLoading(true);

                // Refresh integrated client data
                try {
                  const overview = await FinancialService.getFinancialOverview();
                  const recentActivity = await FinancialService.getRecentActivity(10);
                  const paymentTrends = await FinancialService.getPaymentTrends(6);
                  const topClients = await FinancialService.getTopClients(5);
                  const industryBreakdown = await FinancialService.getIndustryBreakdown();

                  setIntegratedData({
                    overview,
                    recentActivity,
                    paymentTrends,
                    topClients,
                    industryBreakdown
                  });
                  console.log('✅ Financial data refreshed after payment');
                } catch (error) {
                  console.error('Error refreshing financial data after payment:', error);
                } finally {
                  setIntegratedLoading(false);
                }
              }}
            />
          </TabsContent>

          <TabsContent value="expenses" className="space-y-6">
            <div className="flex justify-between items-center">
              <div>
                <h2 className="text-2xl font-bold">Expense Management</h2>
                <p className="text-gray-600">Track and categorize business expenses</p>
              </div>
              <ExpenseForm
                expenseCategories={expenseCategories}
                onExpenseCreated={createExpense}
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle className="text-sm font-medium">Total Expenses</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold text-red-600">
                    ${financialSummary.totalExpenses.toLocaleString()}
                  </div>
                  <p className="text-xs text-muted-foreground">
                    Total business expenses
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="text-sm font-medium">This Month</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">
                    ${expenses.reduce((sum, exp) => sum + exp.amount, 0).toLocaleString()}
                  </div>
                  <p className="text-xs text-muted-foreground">
                    {expenses.length} transactions
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="text-sm font-medium">Categories</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">
                    {expenseCategories.length}
                  </div>
                  <p className="text-xs text-muted-foreground">
                    Active expense categories
                  </p>
                </CardContent>
              </Card>
            </div>

            <Card>
              <CardHeader>
                <CardTitle>Recent Expenses</CardTitle>
                <CardDescription>Latest expense entries</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {expenses.slice(0, 5).map((expense, index) => (
                    <div key={expense.id || index} className="flex items-center justify-between p-4 border rounded-lg">
                      <div className="flex-1">
                        <p className="font-medium">{expense.description}</p>
                        <div className="flex items-center space-x-4 text-sm text-gray-500 mt-1">
                          <span>{expense.category?.name || 'Category'}</span>
                          <span>•</span>
                          <span>{new Date(expense.expense_date).toLocaleDateString()}</span>
                          {expense.vendor_name && (
                            <>
                              <span>•</span>
                              <span>{expense.vendor_name}</span>
                            </>
                          )}
                        </div>
                      </div>
                      <div className="text-right">
                        <p className="font-bold text-red-600">
                          ${expense.amount.toLocaleString()}
                        </p>
                        <Badge variant={expense.status === 'paid' ? 'default' : 'secondary'}>
                          {expense.status}
                        </Badge>
                      </div>
                    </div>
                  ))}
                  {expenses.length === 0 && (
                    <div className="text-center py-8">
                      <Receipt className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                      <p className="text-gray-500 mb-4">No expenses recorded yet</p>
                      <ExpenseForm
                        expenseCategories={expenseCategories}
                        onExpenseCreated={createExpense}
                      />
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="assets" className="space-y-6">
            <CompanyAssets />
          </TabsContent>

          <TabsContent value="revenue" className="space-y-6">
            <RevenueManagement
              onRevenueCreated={async (revenue) => {
                // Refresh financial data when revenue is created
                await loadFinancialData();
                await loadRevenueEntries(); // Refresh revenue entries for dashboard
                await loadRecentActivity(); // Refresh recent activity
              }}
            />
          </TabsContent>

          <TabsContent value="cashflow" className="space-y-6">
            <CashFlowStatements
              cashFlowTransactions={cashFlowTransactions}
              financialSummary={financialSummary}
              cashFlowData={cashFlowData}
              cashFlowBreakdown={cashFlowBreakdown}
              getCashFlowDataForPeriod={getCashFlowDataForPeriod}
            />
          </TabsContent>




        </Tabs>
        </div>
      </ProtectedComponent>
    </Layout>
  );
};

export default Financials;
