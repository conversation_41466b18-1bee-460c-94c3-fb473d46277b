# 🔧 Registration Troubleshooting Guide

## ❌ **Current Error**
```
Registration Failed
User account creation could not be verified. Please try again.
```

## ✅ **Step-by-Step Fix**

### Step 1: Run the Database Fix
You already have the fix script selected. **Run it now in Supabase SQL Editor:**

1. **Copy the entire content** of `database/fix_foreign_key_error.sql`
2. **Paste it into Supabase SQL Editor**
3. **Execute the script**
4. **Check for any error messages**

### Step 2: If Step 1 Doesn't Work, Try the Simple Fix
```sql
-- Copy and paste this simpler version:
-- database/simple_registration_fix.sql
```

### Step 3: Test Registration
1. **Navigate to** `/register`
2. **Try with a completely new email** (not one you've tried before)
3. **Use simple credentials**:
   - Email: `<EMAIL>`
   - Password: `password123`
   - Role: Administrator

### Step 4: Check Browser Console
1. **Open browser developer tools** (F12)
2. **Go to Console tab**
3. **Try registration again**
4. **Look for any error messages**

## 🔍 **Common Issues & Solutions**

### Issue 1: "User account creation could not be verified"
**Cause**: Auth user creation is failing
**Solution**: 
- Check Supabase project settings
- Verify authentication is enabled
- Try with a different email domain

### Issue 2: "Function does not exist"
**Cause**: Database functions weren't created properly
**Solution**: Run the simple fix script instead

### Issue 3: "Permission denied"
**Cause**: RLS policies are blocking access
**Solution**: The scripts disable RLS for testing

### Issue 4: "Duplicate key constraint"
**Cause**: Email already exists in database
**Solution**: Use a completely new email address

## 🛠️ **Manual Testing Steps**

### Test 1: Check Database Connection
```sql
-- Run this in Supabase SQL Editor:
SELECT COUNT(*) FROM public.user_roles;
-- Should return 5 (the number of roles)
```

### Test 2: Check Auth Settings
1. Go to Supabase Dashboard
2. Navigate to Authentication → Settings
3. Ensure "Enable email confirmations" is **OFF** for testing
4. Check that "Allow new users to sign up" is **ON**

### Test 3: Test Simple Profile Creation
```sql
-- Run this to test profile creation directly:
SELECT public.simple_create_profile(
    gen_random_uuid(),
    '<EMAIL>',
    'Test',
    'User',
    'client'
);
-- Should return a UUID without errors
```

## 🚨 **If Nothing Works**

### Nuclear Option: Reset Everything
```sql
-- WARNING: This deletes ALL user data
DELETE FROM public.user_profiles;
DELETE FROM auth.users;

-- Then re-run the main schema:
-- database/user_management_simple.sql
```

### Check Supabase Project Health
1. **Project URL**: Verify you're using the correct project
2. **API Keys**: Check that anon and service_role keys are correct
3. **Database**: Ensure database is running and accessible
4. **Authentication**: Verify auth service is enabled

### Alternative: Use Different Email Provider
Sometimes certain email domains cause issues:
- ✅ **Try**: gmail.com, outlook.com, yahoo.com
- ❌ **Avoid**: temporary email services, unusual domains

## 📋 **Quick Checklist**

Before trying registration:
- [ ] Database schema is created (`user_roles` table has 5 rows)
- [ ] RLS is disabled for testing
- [ ] Authentication is enabled in Supabase
- [ ] Using a completely new email address
- [ ] Password is at least 6 characters
- [ ] Browser console is open to see errors

## 🎯 **Expected Success Flow**

When working correctly:
1. ✅ **Registration form submits** without immediate errors
2. ✅ **"Registration Successful"** toast appears
3. ✅ **Redirects to login page**
4. ✅ **Can login immediately** with new credentials
5. ✅ **Profile page shows** correct information
6. ✅ **User appears** in Settings → Users (if admin)

## 🔄 **What the Fix Does**

### The Database Scripts:
1. **Clean up orphaned data** from previous failed attempts
2. **Disable problematic triggers** that cause conflicts
3. **Create safe functions** for profile creation
4. **Disable RLS** to avoid permission issues
5. **Provide fallback methods** if primary approach fails

### The Application Changes:
1. **Remove verification step** that was causing failures
2. **Add multiple fallback approaches** for profile creation
3. **Better error handling** with specific messages
4. **Automatic cleanup** if registration fails

## 🎉 **Success Indicators**

You'll know it's working when:
- ✅ **No "verification" errors**
- ✅ **Registration completes successfully**
- ✅ **Can login immediately after registration**
- ✅ **Profile shows correct role and information**
- ✅ **No errors in browser console**

## 📞 **Next Steps**

1. **Run the database fix script** (the one you have selected)
2. **Try registration** with a new email
3. **If it fails**, run the simple fix script
4. **If still failing**, check browser console and Supabase logs
5. **Report specific error messages** for further troubleshooting

**The registration should work after running the database fix!** 🎯
