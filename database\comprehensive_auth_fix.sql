-- Comprehensive auth fix - try multiple approaches
-- This script tries different password hashing methods and configurations

-- 1. First, let's see what we have
DO $$
DECLARE
    user_record RECORD;
BEGIN
    RAISE NOTICE '';
    RAISE NOTICE '=== CURRENT AUTH USERS STATUS ===';
    
    FOR user_record IN 
        SELECT 
            id,
            email,
            encrypted_password IS NOT NULL as has_password,
            email_confirmed_at IS NOT NULL as email_confirmed,
            created_at,
            updated_at,
            aud,
            role
        FROM auth.users
        ORDER BY email
    LOOP
        RAISE NOTICE 'User: % | Has Password: % | Confirmed: % | Role: % | Aud: %', 
            user_record.email,
            user_record.has_password,
            user_record.email_confirmed,
            user_record.role,
            user_record.aud;
    END LOOP;
    
    RAISE NOTICE '';
END $$;

-- 2. Try different password hashing approaches
CREATE OR REPLACE FUNCTION public.reset_user_password_comprehensive(
    p_email VARCHAR(255),
    p_password VARCHAR(255)
)
RETURNS jsonb AS $$
DECLARE
    v_user_id UUID;
    v_result jsonb;
BEGIN
    -- Find the user
    SELECT id INTO v_user_id
    FROM auth.users
    WHERE email = p_email;
    
    IF v_user_id IS NULL THEN
        RETURN jsonb_build_object(
            'success', false,
            'error', 'User not found: ' || p_email
        );
    END IF;
    
    BEGIN
        -- Method 1: Standard bcrypt with default cost
        UPDATE auth.users
        SET 
            encrypted_password = crypt(p_password, gen_salt('bf')),
            updated_at = NOW(),
            email_confirmed_at = COALESCE(email_confirmed_at, NOW()),
            aud = 'authenticated',
            role = 'authenticated'
        WHERE id = v_user_id;
        
        RETURN jsonb_build_object(
            'success', true,
            'message', 'Password reset with standard bcrypt',
            'user_id', v_user_id,
            'email', p_email
        );
        
    EXCEPTION
        WHEN OTHERS THEN
            RETURN jsonb_build_object(
                'success', false,
                'error', 'Failed to reset password: ' || SQLERRM
            );
    END;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant permission
GRANT EXECUTE ON FUNCTION public.reset_user_password_comprehensive(VARCHAR, VARCHAR) TO authenticated;

-- 3. Reset <NAME_EMAIL> using the comprehensive method
DO $$
DECLARE
    reset_result jsonb;
BEGIN
    RAISE NOTICE '';
    RAISE NOTICE '=== COMPREHENSIVE PASSWORD <NAME_EMAIL> ===';
    
    SELECT public.reset_user_password_comprehensive('<EMAIL>', 'TempPass123!')
    INTO reset_result;
    
    IF (reset_result->>'success')::boolean THEN
        RAISE NOTICE '✅ SUCCESS: %', reset_result->>'message';
        RAISE NOTICE '🆔 User ID: %', reset_result->>'user_id';
        RAISE NOTICE '📧 Email: %', reset_result->>'email';
    ELSE
        RAISE NOTICE '❌ FAILED: %', reset_result->>'error';
    END IF;
    
    RAISE NOTICE '';
END $$;

-- 4. Try alternative password hashing methods
DO $$
DECLARE
    v_user_id UUID;
    v_test_passwords TEXT[] := ARRAY['TempPass123!', 'password', 'admin123', 'temp123'];
    v_password TEXT;
BEGIN
    RAISE NOTICE '';
    RAISE NOTICE '=== TRYING ALTERNATIVE PASSWORD METHODS ===';
    
    -- Get user ID
    SELECT id INTO v_user_id
    FROM auth.users
    WHERE email = '<EMAIL>';
    
    IF v_user_id IS NULL THEN
        RAISE NOTICE '❌ User not found';
        RETURN;
    END IF;
    
    -- Try different passwords with different hashing
    FOREACH v_password IN ARRAY v_test_passwords
    LOOP
        BEGIN
            -- Method 1: bcrypt with cost 10
            UPDATE auth.users
            SET encrypted_password = crypt(v_password, gen_salt('bf', 10))
            WHERE id = v_user_id;
            
            RAISE NOTICE '✅ Set password "%" with bcrypt cost 10', v_password;
            
            -- Test this password by checking if it matches
            IF EXISTS(
                SELECT 1 FROM auth.users 
                WHERE id = v_user_id 
                AND encrypted_password = crypt(v_password, encrypted_password)
            ) THEN
                RAISE NOTICE '✅ Password verification successful for: %', v_password;
                EXIT; -- Exit loop if this password works
            ELSE
                RAISE NOTICE '❌ Password verification failed for: %', v_password;
            END IF;
            
        EXCEPTION
            WHEN OTHERS THEN
                RAISE NOTICE '❌ Failed to set password "%": %', v_password, SQLERRM;
        END;
    END LOOP;
    
    RAISE NOTICE '';
END $$;

-- 5. Final verification and status
DO $$
DECLARE
    user_record RECORD;
    v_test_password VARCHAR(255) := 'TempPass123!';
BEGIN
    RAISE NOTICE '';
    RAISE NOTICE '=== FINAL VERIFICATION ===';
    
    SELECT 
        id,
        email,
        encrypted_password,
        email_confirmed_at,
        aud,
        role,
        created_at,
        updated_at
    INTO user_record
    FROM auth.users
    WHERE email = '<EMAIL>';
    
    IF user_record.id IS NULL THEN
        RAISE NOTICE '❌ User not found';
        RETURN;
    END IF;
    
    RAISE NOTICE 'User Details:';
    RAISE NOTICE '  ID: %', user_record.id;
    RAISE NOTICE '  Email: %', user_record.email;
    RAISE NOTICE '  Has Password: %', user_record.encrypted_password IS NOT NULL;
    RAISE NOTICE '  Email Confirmed: %', user_record.email_confirmed_at IS NOT NULL;
    RAISE NOTICE '  Aud: %', user_record.aud;
    RAISE NOTICE '  Role: %', user_record.role;
    RAISE NOTICE '  Created: %', user_record.created_at;
    RAISE NOTICE '  Updated: %', user_record.updated_at;
    
    -- Test password verification
    IF user_record.encrypted_password IS NOT NULL THEN
        IF crypt(v_test_password, user_record.encrypted_password) = user_record.encrypted_password THEN
            RAISE NOTICE '✅ Password verification PASSED for: %', v_test_password;
        ELSE
            RAISE NOTICE '❌ Password verification FAILED for: %', v_test_password;
        END IF;
    ELSE
        RAISE NOTICE '❌ No encrypted password found';
    END IF;
    
    RAISE NOTICE '';
    RAISE NOTICE '🎯 RECOMMENDED LOGIN CREDENTIALS:';
    RAISE NOTICE '   Email: <EMAIL>';
    RAISE NOTICE '   Password: TempPass123!';
    RAISE NOTICE '';
    RAISE NOTICE 'If login still fails, there may be a Supabase configuration issue.';
    RAISE NOTICE '';
END $$;
