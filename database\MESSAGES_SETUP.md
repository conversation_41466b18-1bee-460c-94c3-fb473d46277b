# Messages System Setup Guide

## Quick Setup Instructions

### Step 1: Create Messages Database Schema
```sql
-- Copy and paste the content from: database/messages_schema.sql
-- Run in Supabase SQL Editor
```

## What the Schema Creates

### Tables Created:
1. **`message_channels`** - Channels for organizing conversations
2. **`messages`** - Individual messages within channels
3. **`message_reactions`** - Emoji reactions to messages
4. **`channel_members`** - Channel membership and permissions
5. **`message_read_status`** - Track which messages users have read

### Default Channels:
- **General** - General team discussions and announcements
- **Project Updates** - Project status updates and coordination
- **Team Chat** - Casual team conversations
- **Announcements** - Important company announcements
- **Technical Discussion** - Technical discussions and problem solving

### Sample Data:
- Welcome messages in each channel
- Sample conversations between team members
- Example emoji reactions
- Different message types (text, system, announcements)

## Features Included

### Channel Management:
- **Multiple channel types** - General, Project, Team, Direct, Announcement
- **Private channels** - Restricted access channels
- **Channel descriptions** - Clear purpose for each channel
- **Channel icons** - Visual identification

### Real-time Messaging:
- **Send messages** - Type and send messages instantly
- **Message types** - Text, system, announcements, files
- **Message editing** - Edit sent messages (marked as edited)
- **Message deletion** - Soft delete with recovery option
- **Message pinning** - Pin important messages

### Advanced Features:
- **Emoji reactions** - React to messages with emojis
- **Message replies** - Reply to specific messages
- **Read status** - Track who has read messages
- **Search functionality** - Search channels and messages
- **User roles** - Admin, moderator, member permissions

### User Interface:
- **Channel sidebar** - Browse and switch between channels
- **Message history** - Scroll through conversation history
- **Real-time updates** - Messages appear instantly
- **Responsive design** - Works on all screen sizes
- **Professional styling** - Clean, modern interface

## Expected Results

After running the schema:
- ✅ 5 default channels created
- ✅ Sample messages and conversations
- ✅ Working message sending and receiving
- ✅ Channel switching functionality
- ✅ Professional messaging interface

## Usage Instructions

### Sending Messages:
1. **Select a channel** from the sidebar
2. **Type your message** in the text area
3. **Press Enter** or click Send button
4. **Message appears** in the conversation

### Channel Features:
- **Click channels** to switch conversations
- **Search channels** using the search box
- **View channel info** in the header
- **See channel types** with icons

### Message Features:
- **Reply to messages** using the reply button
- **React with emojis** using the smile button
- **Edit messages** (shows "edited" indicator)
- **Pin important messages** for easy reference

## Troubleshooting

### If you get "table does not exist" errors:
1. Run `database/messages_schema.sql` in Supabase SQL Editor
2. Refresh the Messages page

### If messages don't send:
1. Check browser console for errors
2. Verify database connection
3. Ensure RLS policies are set correctly

### If channels don't load:
1. Verify the schema was created successfully
2. Check that default channels were inserted
3. Refresh the page

## Technical Details

### Database Structure:
- **UUID primary keys** for all tables
- **Foreign key relationships** between tables
- **Indexes** for performance optimization
- **Row Level Security** enabled
- **Automatic timestamps** for created/updated dates

### Real-time Features:
- **Instant message sending** via database operations
- **Auto-scroll** to latest messages
- **Message status indicators** (sending, sent, edited)
- **Channel switching** with message history

### Security:
- **RLS policies** control data access
- **User authentication** integration ready
- **Private channel** support
- **Role-based permissions** for channel management
