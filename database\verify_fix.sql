-- Verify the auth fix and show detailed information
SELECT 
    'VERIFICATION RESULTS' as status,
    (SELECT COUNT(*) FROM public.user_profiles WHERE email = '<EMAIL>') as profile_exists,
    (SELECT COUNT(*) FROM auth.users WHERE email = '<EMAIL>') as auth_exists,
    (SELECT 
        CASE 
            WHEN up.user_id = au.id THEN 'LINKED' 
            ELSE 'NOT_LINKED' 
        END
     FROM public.user_profiles up
     LEFT JOIN auth.users au ON up.user_id = au.id
     WHERE up.email = '<EMAIL>'
    ) as link_status;

-- Show profile details
SELECT 
    'PROFILE_DETAILS' as type,
    id as profile_id,
    user_id as auth_user_id,
    email,
    first_name,
    last_name,
    role_name,
    is_active,
    created_at
FROM public.user_profiles 
WHERE email = '<EMAIL>';

-- Show auth user details (limited fields for security)
SELECT 
    'AUTH_DETAILS' as type,
    id as auth_user_id,
    email,
    email_confirmed_at,
    created_at,
    updated_at,
    role,
    aud
FROM auth.users 
WHERE email = '<EMAIL>';

-- Check if there are any orphaned profiles remaining
SELECT 
    'ORPHANED_PROFILES' as type,
    COUNT(*) as count
FROM public.user_profiles up
LEFT JOIN auth.users au ON up.user_id = au.id
WHERE au.id IS NULL;

-- Show all profiles and their auth status
SELECT 
    'ALL_USERS_STATUS' as type,
    up.email,
    up.first_name,
    up.last_name,
    CASE 
        WHEN au.id IS NOT NULL THEN 'HAS_AUTH' 
        ELSE 'ORPHANED' 
    END as auth_status
FROM public.user_profiles up
LEFT JOIN auth.users au ON up.user_id = au.id
ORDER BY up.email;
