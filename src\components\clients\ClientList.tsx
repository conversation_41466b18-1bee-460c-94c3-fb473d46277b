import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON>eader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { 
  Search, 
  Filter, 
  Building, 
  Mail, 
  Phone, 
  MapPin,
  Edit,
  Trash2,
  Eye,
  DollarSign
} from 'lucide-react';
import { Client, ClientFilters } from '@/types/client';
import { CreatorInfo } from '@/components/ui/CreatorInfo';

interface ClientListProps {
  clients: Client[];
  onSelectClient: (client: Client) => void;
  onUpdateClient: (id: string, updates: Partial<Client>) => void;
  onDeleteClient: (id: string) => void;
  searchTerm: string;
  onSearchChange: (term: string) => void;
  filters: ClientFilters;
  onFiltersChange: (filters: ClientFilters) => void;
}

export const ClientList: React.FC<ClientListProps> = ({
  clients,
  onSelectClient,
  onUpdateClient,
  onDeleteClient,
  searchTerm,
  onSearchChange,
  filters,
  onFiltersChange
}) => {
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Active':
        return 'bg-green-100 text-green-800';
      case 'Inactive':
        return 'bg-gray-100 text-gray-800';
      case 'Suspended':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getClientTypeIcon = (type: string) => {
    switch (type) {
      case 'Company':
        return <Building className="h-4 w-4" />;
      case 'Government':
        return <Building className="h-4 w-4" />;
      default:
        return <Building className="h-4 w-4" />;
    }
  };

  return (
    <div className="space-y-6">
      {/* Filters and Search */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Filter className="h-5 w-5" />
            Search & Filter Clients
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-wrap items-center gap-4">
            {/* Search */}
            <div className="flex items-center gap-2 flex-1 min-w-64">
              <Search className="h-4 w-4 text-gray-400" />
              <Input
                placeholder="Search clients by name, company, or email..."
                value={searchTerm}
                onChange={(e) => onSearchChange(e.target.value)}
                className="flex-1"
              />
            </div>

            {/* Status Filter */}
            <Select 
              value={filters.status || 'all'} 
              onValueChange={(value) => onFiltersChange({
                ...filters, 
                status: value === 'all' ? undefined : value
              })}
            >
              <SelectTrigger className="w-32">
                <SelectValue placeholder="Status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value="Active">Active</SelectItem>
                <SelectItem value="Inactive">Inactive</SelectItem>
                <SelectItem value="Suspended">Suspended</SelectItem>
              </SelectContent>
            </Select>

            {/* Client Type Filter */}
            <Select 
              value={filters.client_type || 'all'} 
              onValueChange={(value) => onFiltersChange({
                ...filters, 
                client_type: value === 'all' ? undefined : value
              })}
            >
              <SelectTrigger className="w-36">
                <SelectValue placeholder="Type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Types</SelectItem>
                <SelectItem value="Individual">Individual</SelectItem>
                <SelectItem value="Company">Company</SelectItem>
                <SelectItem value="Government">Government</SelectItem>
                <SelectItem value="Non-Profit">Non-Profit</SelectItem>
              </SelectContent>
            </Select>

            {/* Industry Filter */}
            <Select 
              value={filters.industry || 'all'} 
              onValueChange={(value) => onFiltersChange({
                ...filters, 
                industry: value === 'all' ? undefined : value
              })}
            >
              <SelectTrigger className="w-36">
                <SelectValue placeholder="Industry" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Industries</SelectItem>
                <SelectItem value="Construction">Construction</SelectItem>
                <SelectItem value="Real Estate">Real Estate</SelectItem>
                <SelectItem value="Government">Government</SelectItem>
                <SelectItem value="Infrastructure">Infrastructure</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Client Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {clients.map((client) => (
          <Card key={client.id} className="hover:shadow-lg transition-shadow">
            <CardHeader className="pb-3">
              <div className="flex items-start justify-between">
                <div className="flex items-center space-x-3">
                  <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                    {getClientTypeIcon(client.client_type)}
                  </div>
                  <div>
                    <h3 className="font-semibold text-gray-900">{client.name}</h3>
                    {client.company_name && client.company_name !== client.name && (
                      <p className="text-sm text-gray-600">{client.company_name}</p>
                    )}
                  </div>
                </div>
                <Badge className={getStatusColor(client.status)}>
                  {client.status}
                </Badge>
              </div>
            </CardHeader>
            
            <CardContent className="space-y-3">
              {/* Contact Information */}
              <div className="space-y-2">
                <div className="flex items-center space-x-2 text-sm text-gray-600">
                  <Mail className="h-4 w-4" />
                  <span className="truncate">{client.email}</span>
                </div>
                {client.phone && (
                  <div className="flex items-center space-x-2 text-sm text-gray-600">
                    <Phone className="h-4 w-4" />
                    <span>{client.phone}</span>
                  </div>
                )}
                {client.city && client.state && (
                  <div className="flex items-center space-x-2 text-sm text-gray-600">
                    <MapPin className="h-4 w-4" />
                    <span>{client.city}, {client.state}</span>
                  </div>
                )}
              </div>

              {/* Client Details */}
              <div className="flex items-center justify-between text-sm">
                <span className="text-gray-600">Type:</span>
                <Badge variant="outline">{client.client_type}</Badge>
              </div>
              
              {client.industry && (
                <div className="flex items-center justify-between text-sm">
                  <span className="text-gray-600">Industry:</span>
                  <Badge variant="outline">{client.industry}</Badge>
                </div>
              )}

              {client.credit_limit && (
                <div className="flex items-center justify-between text-sm">
                  <span className="text-gray-600">Credit Limit:</span>
                  <span className="font-medium text-green-600">
                    ${client.credit_limit.toLocaleString()}
                  </span>
                </div>
              )}

              {client.payment_terms && (
                <div className="flex items-center justify-between text-sm">
                  <span className="text-gray-600">Payment Terms:</span>
                  <span className="font-medium">{client.payment_terms} days</span>
                </div>
              )}

              {/* Creator Information */}
              <div className="pt-2 border-t">
                <CreatorInfo
                  createdByName={client.created_by_name}
                  createdByAvatar={client.created_by_avatar}
                  createdAt={client.created_at}
                  variant="compact"
                  showLabel={false}
                />
              </div>

              {/* Action Buttons */}
              <div className="flex items-center space-x-2 pt-3 border-t">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => onSelectClient(client)}
                  className="flex-1"
                >
                  <Eye className="h-4 w-4 mr-1" />
                  View
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    // This would open an edit dialog
                    console.log('Edit client:', client.id);
                  }}
                >
                  <Edit className="h-4 w-4" />
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    if (confirm('Are you sure you want to delete this client?')) {
                      onDeleteClient(client.id);
                    }
                  }}
                  className="text-red-600 hover:text-red-700"
                >
                  <Trash2 className="h-4 w-4" />
                </Button>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Empty State */}
      {clients.length === 0 && (
        <Card>
          <CardContent className="text-center py-12">
            <Building className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No clients found</h3>
            <p className="text-gray-600 mb-4">
              {searchTerm || Object.values(filters).some(f => f) 
                ? 'Try adjusting your search or filters'
                : 'Get started by adding your first client'
              }
            </p>
          </CardContent>
        </Card>
      )}

      {/* Results Summary */}
      {clients.length > 0 && (
        <div className="text-center text-sm text-gray-600">
          Showing {clients.length} client{clients.length !== 1 ? 's' : ''}
        </div>
      )}
    </div>
  );
};
