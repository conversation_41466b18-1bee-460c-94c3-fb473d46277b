# 🔧 Fix Duplicate Email Error

## ❌ **The Problem**
You're getting this error when registering:
```
Registration Failed
Failed to create user profile: duplicate key value violates unique constraint "user_profiles_email_key"
```

**What's happening:**
1. ✅ Auth user gets created successfully 
2. ❌ Profile creation fails due to duplicate email
3. 🔄 User appears in database but registration shows error

## ✅ **The Solution**

### Step 1: Run the Cleanup Script
```sql
-- Copy and paste the ENTIRE content from:
-- database/cleanup_duplicates.sql
-- into your Supabase SQL Editor and execute
```

This will:
- ✅ Remove orphaned profiles (profiles without auth users)
- ✅ Remove duplicate profiles (keep oldest for each email)
- ✅ Create missing profiles for auth users
- ✅ Update the trigger to handle duplicates better
- ✅ Show you a summary of what was fixed

### Step 2: Test Registration Again
1. **Try registering** with a new email address
2. **Should work** without duplicate errors
3. **If using existing email** - will show proper error message

### Step 3: For Existing Problematic Accounts
If you have an account that was created but shows errors:

#### Option A: Login with Existing Account
1. Go to `/login`
2. Try logging in with the email/password
3. If it works, the account is fine

#### Option B: Use Different Email
1. Register with a different email address
2. Admin can later update the email in user management

## 🔧 **What Was Fixed**

### **Better Error Handling:**
- ✅ **Checks for existing emails** before creating auth user
- ✅ **Handles auto-created profiles** from database triggers
- ✅ **Cleans up failed registrations** automatically
- ✅ **Shows clear error messages** to users

### **Improved Database Trigger:**
- ✅ **Prevents duplicate profiles** with ON CONFLICT
- ✅ **Checks both user_id and email** for duplicates
- ✅ **Graceful error handling** doesn't break user creation
- ✅ **Better logging** for debugging

### **User Experience:**
- ✅ **Clear error messages** - "Email already exists"
- ✅ **Helpful suggestions** - "Try logging in instead"
- ✅ **No broken accounts** - Either works or fails cleanly
- ✅ **Proper cleanup** - Failed registrations don't leave orphans

## 🎯 **Expected Results**

After running the cleanup script:

### **For New Registrations:**
- ✅ **Works smoothly** with new email addresses
- ✅ **Clear error** if email already exists
- ✅ **No database inconsistencies**
- ✅ **Proper profile creation**

### **For Existing Issues:**
- ✅ **Orphaned data cleaned up**
- ✅ **Duplicates removed**
- ✅ **Missing profiles created**
- ✅ **Database consistency restored**

## 🚨 **If You Still Have Issues**

### **Check Database State:**
```sql
-- Run this to see current state:
SELECT 
    (SELECT COUNT(*) FROM auth.users) as auth_users,
    (SELECT COUNT(*) FROM public.user_profiles) as profiles,
    (SELECT COUNT(*) FROM public.user_profiles up 
     LEFT JOIN auth.users au ON up.user_id = au.id 
     WHERE au.id IS NULL) as orphaned_profiles;
```

### **Manual Cleanup:**
If you know the problematic email:
```sql
-- Replace '<EMAIL>' with the actual email
DELETE FROM public.user_profiles WHERE email = '<EMAIL>';
-- Then try registering again
```

### **Reset Everything:**
If you want to start fresh:
```sql
-- WARNING: This deletes ALL user data
DELETE FROM public.user_profiles;
-- Then re-run the main schema: user_management_simple.sql
```

## 🎉 **Success Indicators**

You'll know it's fixed when:
- ✅ **New registrations work** without errors
- ✅ **Duplicate email shows proper message** 
- ✅ **No orphaned data** in database
- ✅ **Login works** for created accounts
- ✅ **User management shows** all users correctly

## 📋 **Quick Fix Steps**

1. **Run cleanup script** - `database/cleanup_duplicates.sql`
2. **Test registration** - Try with new email
3. **Check existing accounts** - Try logging in
4. **Verify user management** - Check Settings → Users
5. **Create team accounts** - Should work smoothly now

**The duplicate email constraint error should now be completely resolved!** 🎯
