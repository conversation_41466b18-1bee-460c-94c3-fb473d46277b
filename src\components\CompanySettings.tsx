import React, { useState, useCallback } from 'react';
import { useDropzone } from 'react-dropzone';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Upload, X, Building, Save, Image as ImageIcon } from 'lucide-react';
import { useToast } from '@/components/ui/use-toast';
import { CompanyInfo, DEFAULT_COMPANY_INFO } from '@/lib/documents';

interface CompanySettingsProps {
  companyInfo: CompanyInfo;
  onUpdate: (companyInfo: CompanyInfo) => Promise<void>;
}

const CompanySettings: React.FC<CompanySettingsProps> = ({ companyInfo, onUpdate }) => {
  const { toast } = useToast();
  const [isOpen, setIsOpen] = useState(false);
  const [formData, setFormData] = useState<CompanyInfo>(companyInfo);
  const [logoPreview, setLogoPreview] = useState<string>(companyInfo.logo_url || '');

  // Handle logo upload
  const onDrop = useCallback((acceptedFiles: File[]) => {
    const file = acceptedFiles[0];
    if (file) {
      // Check file size (max 5MB)
      if (file.size > 5 * 1024 * 1024) {
        toast({
          title: "File too large",
          description: "Please select an image smaller than 5MB",
          variant: "destructive",
        });
        return;
      }

      // Check file type
      if (!file.type.startsWith('image/')) {
        toast({
          title: "Invalid file type",
          description: "Please select an image file",
          variant: "destructive",
        });
        return;
      }

      // Create preview URL
      const reader = new FileReader();
      reader.onload = (e) => {
        const result = e.target?.result as string;
        setLogoPreview(result);
        setFormData(prev => ({ ...prev, logo_url: result }));
      };
      reader.readAsDataURL(file);
    }
  }, [toast]);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'image/*': ['.png', '.jpg', '.jpeg', '.gif', '.svg']
    },
    multiple: false
  });

  const handleSave = async () => {
    // Validate required fields
    if (!formData.name.trim()) {
      toast({
        title: "Validation Error",
        description: "Company name is required",
        variant: "destructive",
      });
      return;
    }

    if (!formData.email.trim()) {
      toast({
        title: "Validation Error",
        description: "Company email is required",
        variant: "destructive",
      });
      return;
    }

    // Call the update handler (which will save to database)
    await onUpdate(formData);
    setIsOpen(false);
  };

  const handleReset = () => {
    setFormData(DEFAULT_COMPANY_INFO);
    setLogoPreview('');
  };

  const removeLogo = () => {
    setLogoPreview('');
    setFormData(prev => ({ ...prev, logo_url: '' }));
  };

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <Button variant="outline" size="sm">
          <Building className="w-4 h-4 mr-2" />
          Company Settings
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Company Settings</DialogTitle>
          <DialogDescription>
            Configure your company information and letterhead for documents
          </DialogDescription>
        </DialogHeader>
        
        <div className="grid gap-6 py-4">
          {/* Logo Upload Section */}
          <div className="space-y-4">
            <Label className="text-base font-semibold">Company Logo / Letterhead</Label>
            
            {logoPreview ? (
              <div className="relative">
                <div className="border-2 border-dashed border-gray-300 rounded-lg p-4 text-center bg-gray-50">
                  <img 
                    src={logoPreview} 
                    alt="Company Logo" 
                    className="max-h-32 mx-auto object-contain"
                  />
                </div>
                <Button
                  type="button"
                  variant="destructive"
                  size="sm"
                  className="absolute top-2 right-2"
                  onClick={removeLogo}
                >
                  <X className="w-4 h-4" />
                </Button>
              </div>
            ) : (
              <div
                {...getRootProps()}
                className={`border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-colors ${
                  isDragActive 
                    ? 'border-blue-400 bg-blue-50' 
                    : 'border-gray-300 hover:border-gray-400'
                }`}
              >
                <input {...getInputProps()} />
                <Upload className="w-12 h-12 mx-auto text-gray-400 mb-4" />
                <p className="text-lg font-medium text-gray-700 mb-2">
                  {isDragActive ? 'Drop your logo here' : 'Upload Company Logo'}
                </p>
                <p className="text-sm text-gray-500 mb-4">
                  Drag and drop your logo here, or click to browse
                </p>
                <p className="text-xs text-gray-400">
                  Supports PNG, JPG, JPEG, GIF, SVG (max 5MB)
                </p>
              </div>
            )}
          </div>

          {/* Company Information */}
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="company_name">Company Name *</Label>
              <Input
                id="company_name"
                value={formData.name}
                onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                placeholder="Your Construction Company"
                required
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="company_email">Email *</Label>
              <Input
                id="company_email"
                type="email"
                value={formData.email}
                onChange={(e) => setFormData(prev => ({ ...prev, email: e.target.value }))}
                placeholder="<EMAIL>"
                required
              />
            </div>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="company_phone">Phone</Label>
              <Input
                id="company_phone"
                value={formData.phone}
                onChange={(e) => setFormData(prev => ({ ...prev, phone: e.target.value }))}
                placeholder="+263 4 123 4567"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="company_website">Website</Label>
              <Input
                id="company_website"
                value={formData.website}
                onChange={(e) => setFormData(prev => ({ ...prev, website: e.target.value }))}
                placeholder="www.yourcompany.com"
              />
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="company_address">Address</Label>
            <Textarea
              id="company_address"
              value={formData.address}
              onChange={(e) => setFormData(prev => ({ ...prev, address: e.target.value }))}
              placeholder="123 Construction Street&#10;Harare, Zimbabwe"
              rows={3}
            />
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="tax_number">Tax Number</Label>
              <Input
                id="tax_number"
                value={formData.tax_number}
                onChange={(e) => setFormData(prev => ({ ...prev, tax_number: e.target.value }))}
                placeholder="TAX123456789"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="registration_number">Registration Number</Label>
              <Input
                id="registration_number"
                value={formData.registration_number}
                onChange={(e) => setFormData(prev => ({ ...prev, registration_number: e.target.value }))}
                placeholder="REG987654321"
              />
            </div>
          </div>

          {/* Preview Section */}
          <div className="space-y-2">
            <Label className="text-base font-semibold">Preview</Label>
            <Card>
              <CardContent className="p-4">
                <div className="flex items-start space-x-4">
                  {logoPreview && (
                    <img 
                      src={logoPreview} 
                      alt="Logo Preview" 
                      className="w-16 h-16 object-contain"
                    />
                  )}
                  <div className="flex-1">
                    <h3 className="font-bold text-lg">{formData.name || 'Company Name'}</h3>
                    <div className="text-sm text-gray-600 mt-1">
                      {formData.address && (
                        <div>{formData.address.split('\n').map((line, i) => (
                          <div key={i}>{line}</div>
                        ))}</div>
                      )}
                      {formData.phone && <div>Phone: {formData.phone}</div>}
                      {formData.email && <div>Email: {formData.email}</div>}
                      {formData.website && <div>Website: {formData.website}</div>}
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>

        <DialogFooter className="flex justify-between">
          <Button type="button" variant="outline" onClick={handleReset}>
            Reset to Default
          </Button>
          <Button type="button" onClick={handleSave}>
            <Save className="w-4 h-4 mr-2" />
            Save Settings
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default CompanySettings;
