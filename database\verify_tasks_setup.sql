-- Verification script for tasks table setup
-- Run this to check if everything is configured correctly

-- Check if tasks table exists
SELECT 
    CASE 
        WHEN EXISTS (SELECT FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'tasks')
        THEN '✅ Tasks table exists'
        ELSE '❌ Tasks table missing'
    END as table_status;

-- Check table structure
SELECT 
    column_name,
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_schema = 'public' 
AND table_name = 'tasks'
ORDER BY ordinal_position;

-- Check constraints
SELECT 
    constraint_name,
    constraint_type
FROM information_schema.table_constraints 
WHERE table_schema = 'public' 
AND table_name = 'tasks';

-- Check indexes
SELECT 
    indexname,
    indexdef
FROM pg_indexes 
WHERE tablename = 'tasks' 
AND schemaname = 'public';

-- Check triggers
SELECT 
    trigger_name,
    event_manipulation,
    action_timing
FROM information_schema.triggers 
WHERE event_object_table = 'tasks'
AND event_object_schema = 'public';

-- Check <PERSON><PERSON> policies
SELECT 
    policyname,
    permissive,
    roles,
    cmd,
    qual
FROM pg_policies 
WHERE tablename = 'tasks';

-- Check if <PERSON><PERSON> is enabled
SELECT 
    tablename,
    rowsecurity
FROM pg_tables 
WHERE tablename = 'tasks' 
AND schemaname = 'public';

-- Check views
SELECT 
    table_name,
    view_definition
FROM information_schema.views 
WHERE table_schema = 'public' 
AND table_name = 'task_statistics';

-- Test basic operations (optional - uncomment to test)
-- INSERT INTO public.tasks (name, description, status, priority, project_id) 
-- VALUES ('Test Task', 'This is a test task', 'Not Started', 'Medium', 
--         (SELECT id FROM public.projects LIMIT 1));

-- SELECT COUNT(*) as task_count FROM public.tasks;

-- Clean up test data (uncomment if you inserted test data above)
-- DELETE FROM public.tasks WHERE name = 'Test Task';

-- Final status check
SELECT 
    CASE 
        WHEN EXISTS (SELECT FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'tasks')
        AND EXISTS (SELECT FROM pg_policies WHERE tablename = 'tasks')
        AND EXISTS (SELECT FROM information_schema.triggers WHERE event_object_table = 'tasks')
        THEN '✅ Tasks table is fully configured and ready to use!'
        ELSE '⚠️ Tasks table setup incomplete - please run the setup script'
    END as final_status;
