-- Create tasks table for project management
-- Run this SQL in your Supabase SQL editor to create the tasks table

CREATE TABLE IF NOT EXISTS public.tasks (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    name VA<PERSON><PERSON><PERSON>(255) NOT NULL,
    description TEXT,
    status VARCHAR(50) NOT NULL DEFAULT 'Not Started',
    priority VARCHAR(50) NOT NULL DEFAULT 'Medium',
    start_date DATE,
    end_date DATE,
    progress INTEGER DEFAULT 0 CHECK (progress >= 0 AND progress <= 100),
    assignee VA<PERSON><PERSON><PERSON>(255),
    project_id UUID NOT NULL,
    estimated_hours DECIMAL(10,2) DEFAULT 0,
    actual_hours DECIMAL(10,2) DEFAULT 0,
    dependencies JSONB DEFAULT '[]'::jsonb,
    tags JSONB DEFAULT '[]'::jsonb,
    parent_task_id UUID,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL,
    
    -- Foreign key constraint (assuming you have a projects table)
    CONSTRAINT fk_tasks_project_id FOREIGN KEY (project_id) REFERENCES public.projects(id) ON DELETE CASCADE,
    
    -- Self-referencing foreign key for parent tasks
    CONSTRAINT fk_tasks_parent_task_id FOREIGN KEY (parent_task_id) REFERENCES public.tasks(id) ON DELETE SET NULL,
    
    -- Check constraints for valid values
    CONSTRAINT chk_tasks_status CHECK (status IN ('Not Started', 'In Progress', 'Completed', 'On Hold', 'Cancelled')),
    CONSTRAINT chk_tasks_priority CHECK (priority IN ('Low', 'Medium', 'High', 'Critical'))
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_tasks_project_id ON public.tasks(project_id);
CREATE INDEX IF NOT EXISTS idx_tasks_status ON public.tasks(status);
CREATE INDEX IF NOT EXISTS idx_tasks_priority ON public.tasks(priority);
CREATE INDEX IF NOT EXISTS idx_tasks_assignee ON public.tasks(assignee);
CREATE INDEX IF NOT EXISTS idx_tasks_start_date ON public.tasks(start_date);
CREATE INDEX IF NOT EXISTS idx_tasks_end_date ON public.tasks(end_date);
CREATE INDEX IF NOT EXISTS idx_tasks_parent_task_id ON public.tasks(parent_task_id);
CREATE INDEX IF NOT EXISTS idx_tasks_created_at ON public.tasks(created_at);

-- Create a function to automatically update the updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = timezone('utc'::text, now());
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create trigger to automatically update updated_at
DROP TRIGGER IF EXISTS update_tasks_updated_at ON public.tasks;
CREATE TRIGGER update_tasks_updated_at
    BEFORE UPDATE ON public.tasks
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Enable Row Level Security (RLS)
ALTER TABLE public.tasks ENABLE ROW LEVEL SECURITY;

-- Create policies for RLS (adjust based on your authentication setup)
-- Drop existing policy if it exists and recreate
DROP POLICY IF EXISTS "Allow all operations for authenticated users" ON public.tasks;
CREATE POLICY "Allow all operations for authenticated users" ON public.tasks
    FOR ALL USING (auth.role() = 'authenticated');

-- Alternative: More restrictive policy based on user ownership
-- CREATE POLICY "Users can view all tasks" ON public.tasks
--     FOR SELECT USING (true);
-- 
-- CREATE POLICY "Users can insert tasks" ON public.tasks
--     FOR INSERT WITH CHECK (true);
-- 
-- CREATE POLICY "Users can update tasks" ON public.tasks
--     FOR UPDATE USING (true);
-- 
-- CREATE POLICY "Users can delete tasks" ON public.tasks
--     FOR DELETE USING (true);

-- Grant permissions
GRANT ALL ON public.tasks TO authenticated;
GRANT ALL ON public.tasks TO service_role;

-- Insert some sample tasks (optional - remove if you don't want sample data)
INSERT INTO public.tasks (name, description, status, priority, start_date, end_date, progress, assignee, project_id, estimated_hours, actual_hours, tags) 
SELECT 
    'Foundation Excavation',
    'Excavate and prepare foundation area according to architectural plans',
    'In Progress',
    'High',
    CURRENT_DATE,
    CURRENT_DATE + INTERVAL '7 days',
    25,
    'John Smith',
    p.id,
    40.0,
    10.0,
    '["excavation", "foundation", "critical-path"]'::jsonb
FROM public.projects p 
WHERE p.name LIKE '%Construction%' 
LIMIT 1;

INSERT INTO public.tasks (name, description, status, priority, start_date, end_date, progress, assignee, project_id, estimated_hours, actual_hours, tags) 
SELECT 
    'Concrete Pouring',
    'Pour concrete for foundation and allow proper curing time',
    'Not Started',
    'Critical',
    CURRENT_DATE + INTERVAL '7 days',
    CURRENT_DATE + INTERVAL '10 days',
    0,
    'Mike Johnson',
    p.id,
    24.0,
    0.0,
    '["concrete", "foundation", "weather-dependent"]'::jsonb
FROM public.projects p 
WHERE p.name LIKE '%Construction%' 
LIMIT 1;

INSERT INTO public.tasks (name, description, status, priority, start_date, end_date, progress, assignee, project_id, estimated_hours, actual_hours, tags) 
SELECT 
    'Electrical Planning',
    'Review electrical plans and coordinate with electrical contractor',
    'Completed',
    'Medium',
    CURRENT_DATE - INTERVAL '5 days',
    CURRENT_DATE - INTERVAL '2 days',
    100,
    'Sarah Wilson',
    p.id,
    16.0,
    14.5,
    '["electrical", "planning", "coordination"]'::jsonb
FROM public.projects p 
WHERE p.name LIKE '%Renovation%' 
LIMIT 1;

-- Create a view for task statistics (optional)
CREATE OR REPLACE VIEW public.task_statistics AS
SELECT 
    project_id,
    COUNT(*) as total_tasks,
    COUNT(*) FILTER (WHERE status = 'Completed') as completed_tasks,
    COUNT(*) FILTER (WHERE status = 'In Progress') as in_progress_tasks,
    COUNT(*) FILTER (WHERE status = 'Not Started') as not_started_tasks,
    COUNT(*) FILTER (WHERE status = 'On Hold') as on_hold_tasks,
    COUNT(*) FILTER (WHERE status = 'Cancelled') as cancelled_tasks,
    ROUND(AVG(progress), 2) as average_progress,
    SUM(estimated_hours) as total_estimated_hours,
    SUM(actual_hours) as total_actual_hours,
    COUNT(*) FILTER (WHERE end_date < CURRENT_DATE AND status != 'Completed') as overdue_tasks
FROM public.tasks
GROUP BY project_id;

-- Grant access to the view
GRANT SELECT ON public.task_statistics TO authenticated;
GRANT SELECT ON public.task_statistics TO service_role;

-- Add comments for documentation
COMMENT ON TABLE public.tasks IS 'Project tasks with progress tracking and assignment management';
COMMENT ON COLUMN public.tasks.dependencies IS 'JSON array of task IDs that this task depends on';
COMMENT ON COLUMN public.tasks.tags IS 'JSON array of tags for categorizing and filtering tasks';
COMMENT ON COLUMN public.tasks.progress IS 'Task completion percentage (0-100)';
COMMENT ON COLUMN public.tasks.estimated_hours IS 'Estimated hours to complete the task';
COMMENT ON COLUMN public.tasks.actual_hours IS 'Actual hours spent on the task';
COMMENT ON COLUMN public.tasks.parent_task_id IS 'Parent task ID for creating task hierarchies/subtasks';
