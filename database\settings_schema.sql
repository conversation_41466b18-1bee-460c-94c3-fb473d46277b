-- Settings System Database Schema
-- User notification preferences and system configuration tables

-- Create user notification preferences table
CREATE TABLE IF NOT EXISTS public.user_notification_preferences (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    email_notifications <PERSON><PERSON><PERSON><PERSON><PERSON> DEFAULT true,
    push_notifications B<PERSON><PERSON><PERSON><PERSON> DEFAULT true,
    sms_notifications B<PERSON><PERSON>EAN DEFAULT false,
    project_updates B<PERSON><PERSON>EAN DEFAULT true,
    financial_alerts BOOLEAN DEFAULT true,
    time_tracking_reminders BOOLEAN DEFAULT true,
    team_messages B<PERSON><PERSON><PERSON>N DEFAULT true,
    system_announcements B<PERSON><PERSON><PERSON>N DEFAULT true,
    invoice_notifications B<PERSON><PERSON><PERSON><PERSON> DEFAULT true,
    deadline_reminders B<PERSON><PERSON><PERSON>N DEFAULT true,
    notification_frequency VARCHAR(20) DEFAULT 'immediate' CHECK (notification_frequency IN ('immediate', 'daily', 'weekly')),
    quiet_hours_enabled BOOLEAN DEFAULT false,
    quiet_hours_start TIME DEFAULT '22:00',
    quiet_hours_end TIME DEFAULT '08:00',
    two_factor_enabled BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(user_id)
);

-- Create system configuration table
CREATE TABLE IF NOT EXISTS public.system_config (
    id INTEGER PRIMARY KEY DEFAULT 1,
    company_name VARCHAR(255) DEFAULT 'Construction Management System',
    company_email VARCHAR(255) DEFAULT '<EMAIL>',
    company_phone VARCHAR(50) DEFAULT '+****************',
    company_address TEXT DEFAULT '123 Business Ave, City, State 12345',
    timezone VARCHAR(50) DEFAULT 'America/New_York',
    date_format VARCHAR(20) DEFAULT 'MM/DD/YYYY',
    currency VARCHAR(10) DEFAULT 'USD',
    session_timeout INTEGER DEFAULT 30,
    max_file_size INTEGER DEFAULT 10,
    backup_frequency VARCHAR(20) DEFAULT 'daily' CHECK (backup_frequency IN ('hourly', 'daily', 'weekly', 'monthly')),
    maintenance_mode BOOLEAN DEFAULT false,
    registration_enabled BOOLEAN DEFAULT true,
    email_verification_required BOOLEAN DEFAULT true,
    two_factor_required BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_by UUID REFERENCES auth.users(id),
    CHECK (id = 1) -- Ensure only one row
);

-- Create user sessions table for session management
CREATE TABLE IF NOT EXISTS public.user_sessions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    session_token VARCHAR(255) NOT NULL,
    ip_address INET,
    user_agent TEXT,
    last_activity TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create audit log table for security tracking
CREATE TABLE IF NOT EXISTS public.audit_logs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES auth.users(id) ON DELETE SET NULL,
    action VARCHAR(100) NOT NULL,
    resource_type VARCHAR(50),
    resource_id VARCHAR(255),
    old_values JSONB,
    new_values JSONB,
    ip_address INET,
    user_agent TEXT,
    success BOOLEAN DEFAULT true,
    error_message TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create system backups table
CREATE TABLE IF NOT EXISTS public.system_backups (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    backup_type VARCHAR(50) DEFAULT 'full' CHECK (backup_type IN ('full', 'incremental', 'differential')),
    file_path TEXT,
    file_size BIGINT,
    status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'in_progress', 'completed', 'failed')),
    started_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    completed_at TIMESTAMP WITH TIME ZONE,
    error_message TEXT,
    created_by UUID REFERENCES auth.users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_user_notification_preferences_user_id ON public.user_notification_preferences(user_id);
CREATE INDEX IF NOT EXISTS idx_user_sessions_user_id ON public.user_sessions(user_id);
CREATE INDEX IF NOT EXISTS idx_user_sessions_token ON public.user_sessions(session_token);
CREATE INDEX IF NOT EXISTS idx_user_sessions_expires ON public.user_sessions(expires_at);
CREATE INDEX IF NOT EXISTS idx_audit_logs_user_id ON public.audit_logs(user_id);
CREATE INDEX IF NOT EXISTS idx_audit_logs_action ON public.audit_logs(action);
CREATE INDEX IF NOT EXISTS idx_audit_logs_created_at ON public.audit_logs(created_at);
CREATE INDEX IF NOT EXISTS idx_system_backups_status ON public.system_backups(status);
CREATE INDEX IF NOT EXISTS idx_system_backups_created_at ON public.system_backups(created_at);

-- Create function to log audit events
CREATE OR REPLACE FUNCTION log_audit_event(
    p_user_id UUID,
    p_action VARCHAR(100),
    p_resource_type VARCHAR(50) DEFAULT NULL,
    p_resource_id VARCHAR(255) DEFAULT NULL,
    p_old_values JSONB DEFAULT NULL,
    p_new_values JSONB DEFAULT NULL,
    p_ip_address INET DEFAULT NULL,
    p_user_agent TEXT DEFAULT NULL,
    p_success BOOLEAN DEFAULT true,
    p_error_message TEXT DEFAULT NULL
)
RETURNS UUID AS $$
DECLARE
    v_audit_id UUID;
BEGIN
    INSERT INTO public.audit_logs (
        user_id,
        action,
        resource_type,
        resource_id,
        old_values,
        new_values,
        ip_address,
        user_agent,
        success,
        error_message
    ) VALUES (
        p_user_id,
        p_action,
        p_resource_type,
        p_resource_id,
        p_old_values,
        p_new_values,
        p_ip_address,
        p_user_agent,
        p_success,
        p_error_message
    ) RETURNING id INTO v_audit_id;
    
    RETURN v_audit_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create function to clean up expired sessions
CREATE OR REPLACE FUNCTION cleanup_expired_sessions()
RETURNS INTEGER AS $$
DECLARE
    v_deleted_count INTEGER;
BEGIN
    DELETE FROM public.user_sessions 
    WHERE expires_at < NOW() OR last_activity < NOW() - INTERVAL '7 days';
    
    GET DIAGNOSTICS v_deleted_count = ROW_COUNT;
    RETURN v_deleted_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create function to get system configuration
CREATE OR REPLACE FUNCTION get_system_config()
RETURNS TABLE (
    company_name VARCHAR(255),
    company_email VARCHAR(255),
    company_phone VARCHAR(50),
    company_address TEXT,
    timezone VARCHAR(50),
    date_format VARCHAR(20),
    currency VARCHAR(10),
    session_timeout INTEGER,
    max_file_size INTEGER,
    backup_frequency VARCHAR(20),
    maintenance_mode BOOLEAN,
    registration_enabled BOOLEAN,
    email_verification_required BOOLEAN,
    two_factor_required BOOLEAN
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        sc.company_name,
        sc.company_email,
        sc.company_phone,
        sc.company_address,
        sc.timezone,
        sc.date_format,
        sc.currency,
        sc.session_timeout,
        sc.max_file_size,
        sc.backup_frequency,
        sc.maintenance_mode,
        sc.registration_enabled,
        sc.email_verification_required,
        sc.two_factor_required
    FROM public.system_config sc
    WHERE sc.id = 1;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Apply triggers to tables
DROP TRIGGER IF EXISTS trigger_update_notification_preferences_updated_at ON public.user_notification_preferences;
CREATE TRIGGER trigger_update_notification_preferences_updated_at
    BEFORE UPDATE ON public.user_notification_preferences
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS trigger_update_system_config_updated_at ON public.system_config;
CREATE TRIGGER trigger_update_system_config_updated_at
    BEFORE UPDATE ON public.system_config
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Grant permissions
GRANT ALL ON public.user_notification_preferences TO authenticated;
GRANT ALL ON public.system_config TO authenticated;
GRANT ALL ON public.user_sessions TO authenticated;
GRANT ALL ON public.audit_logs TO authenticated;
GRANT ALL ON public.system_backups TO authenticated;

GRANT EXECUTE ON FUNCTION log_audit_event(UUID, VARCHAR(100), VARCHAR(50), VARCHAR(255), JSONB, JSONB, INET, TEXT, BOOLEAN, TEXT) TO authenticated;
GRANT EXECUTE ON FUNCTION cleanup_expired_sessions() TO authenticated;
GRANT EXECUTE ON FUNCTION get_system_config() TO authenticated;

-- Insert default system configuration
INSERT INTO public.system_config (
    id,
    company_name,
    company_email,
    company_phone,
    company_address,
    timezone,
    date_format,
    currency,
    session_timeout,
    max_file_size,
    backup_frequency,
    maintenance_mode,
    registration_enabled,
    email_verification_required,
    two_factor_required
) VALUES (
    1,
    'Construction Management System',
    '<EMAIL>',
    '+****************',
    '123 Business Ave, City, State 12345',
    'America/New_York',
    'MM/DD/YYYY',
    'USD',
    30,
    10,
    'daily',
    false,
    true,
    true,
    false
) ON CONFLICT (id) DO NOTHING;

-- Create a simplified view for user security summary (without session tracking for now)
CREATE OR REPLACE VIEW user_security_summary AS
SELECT
    u.id as user_id,
    u.email,
    COALESCE(up.first_name, '') as first_name,
    COALESCE(up.last_name, '') as last_name,
    COALESCE(up.role_name, 'user') as role_name,
    COALESCE(unp.two_factor_enabled, false) as two_factor_enabled,
    COALESCE(unp.email_notifications, true) as email_notifications,
    0 as active_sessions,
    u.created_at as last_activity,
    u.created_at as account_created
FROM auth.users u
LEFT JOIN public.user_profiles up ON u.id = up.user_id
LEFT JOIN public.user_notification_preferences unp ON u.id = unp.user_id;

GRANT SELECT ON user_security_summary TO authenticated;

-- Create default notification preferences for existing users
INSERT INTO public.user_notification_preferences (
    user_id,
    email_notifications,
    push_notifications,
    sms_notifications,
    project_updates,
    financial_alerts,
    time_tracking_reminders,
    team_messages,
    system_announcements,
    invoice_notifications,
    deadline_reminders,
    notification_frequency,
    quiet_hours_enabled,
    quiet_hours_start,
    quiet_hours_end,
    two_factor_enabled
)
SELECT
    u.id,
    true,
    true,
    false,
    true,
    true,
    true,
    true,
    true,
    true,
    true,
    'immediate',
    false,
    '22:00',
    '08:00',
    false
FROM auth.users u
WHERE NOT EXISTS (
    SELECT 1 FROM public.user_notification_preferences unp
    WHERE unp.user_id = u.id
)
ON CONFLICT (user_id) DO NOTHING;
