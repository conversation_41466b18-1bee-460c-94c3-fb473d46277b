-- Fix for function type mismatch error
-- Run this to update the get_user_accessible_projects function with correct types

-- Drop and recreate the function with correct return types
DROP FUNCTION IF EXISTS get_user_accessible_projects(UUID);

CREATE OR REPLACE FUNCTION get_user_accessible_projects(target_user_id UUID DEFAULT NULL)
RETURNS TABLE (
  id UUID,
  name VA<PERSON>HA<PERSON>(255),
  description VARCHAR(1000),
  status VARCHAR(50),
  priority VARCHAR(20),
  start_date VARCHAR(50),
  end_date VARCHAR(50),
  budget NUMERIC,
  spent NUMERIC,
  progress INTEGER,
  client_name VARCHAR(255),
  project_manager <PERSON><PERSON><PERSON><PERSON>(255),
  location VARCHAR(255),
  date_added VARCHAR(50),
  last_modified VARCHAR(50),
  created_at TIMESTAMP WITH TIME ZONE,
  created_by_user_id UUID,
  created_by_name VARCHA<PERSON>(255),
  created_by_avatar VARCHAR(500),
  client_id UUID
) AS $$
DECLARE
  user_id_to_check UUID;
  user_role VARCHAR(50);
BEGIN
  -- Use provided user_id or current authenticated user
  user_id_to_check := COALESCE(target_user_id, auth.uid());
  
  -- Get user role
  SELECT role_name INTO user_role
  FROM public.user_profiles 
  WHERE user_id = user_id_to_check;
  
  -- If user is admin, management, QS, or accountant, return all projects
  IF user_role IN ('admin', 'management', 'qs', 'accountant') THEN
    RETURN QUERY
    SELECT p.id, p.name, p.description, p.status, p.priority, p.start_date, p.end_date,
           p.budget, p.spent, p.progress, p.client_name, p.project_manager, p.location,
           p.date_added, p.last_modified, p.created_at, p.created_by_user_id,
           p.created_by_name, p.created_by_avatar, p.client_id
    FROM public.projects p
    ORDER BY p.date_added DESC;
  
  -- If user is client, return only their projects
  ELSIF user_role = 'client' THEN
    RETURN QUERY
    SELECT p.id, p.name, p.description, p.status, p.priority, p.start_date, p.end_date,
           p.budget, p.spent, p.progress, p.client_name, p.project_manager, p.location,
           p.date_added, p.last_modified, p.created_at, p.created_by_user_id,
           p.created_by_name, p.created_by_avatar, p.client_id
    FROM public.projects p
    INNER JOIN public.client_users cu ON p.client_id = cu.client_id
    WHERE cu.user_id = user_id_to_check
    ORDER BY p.date_added DESC;
  
  -- Default: return empty result for unknown roles
  ELSE
    RETURN;
  END IF;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Also fix the show_user_project_access function
DROP FUNCTION IF EXISTS show_user_project_access(TEXT);

CREATE OR REPLACE FUNCTION show_user_project_access(target_email TEXT DEFAULT NULL)
RETURNS TABLE (
  user_email VARCHAR(255),
  user_role VARCHAR(50),
  project_name VARCHAR(255),
  client_name VARCHAR(255),
  can_access BOOLEAN
) AS $$
DECLARE
  user_record RECORD;
BEGIN
  -- If specific email provided, check just that user
  IF target_email IS NOT NULL THEN
    SELECT user_id, email, role_name INTO user_record
    FROM public.user_profiles 
    WHERE email = target_email;
    
    IF user_record.user_id IS NOT NULL THEN
      RETURN QUERY
      SELECT 
        user_record.email::VARCHAR(255),
        user_record.role_name::VARCHAR(50),
        p.name,
        p.client_name,
        can_user_access_project(p.id, user_record.user_id) as can_access
      FROM public.projects p
      ORDER BY p.name;
    END IF;
  ELSE
    -- Show access for all client users
    FOR user_record IN 
      SELECT user_id, email, role_name 
      FROM public.user_profiles 
      WHERE role_name = 'client'
    LOOP
      RETURN QUERY
      SELECT 
        user_record.email::VARCHAR(255),
        user_record.role_name::VARCHAR(50),
        p.name,
        p.client_name,
        can_user_access_project(p.id, user_record.user_id) as can_access
      FROM public.projects p
      ORDER BY p.name;
    END LOOP;
  END IF;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Test the function
DO $$
BEGIN
  RAISE NOTICE '✅ Functions updated with correct types!';
  RAISE NOTICE 'Testing get_user_accessible_projects function...';
  
  -- Test if function works
  PERFORM get_user_accessible_projects();
  RAISE NOTICE '✅ Function test successful!';
  
EXCEPTION WHEN OTHERS THEN
  RAISE NOTICE '❌ Function test failed: %', SQLERRM;
END $$;

-- Show available functions
SELECT 
  routine_name,
  routine_type,
  data_type
FROM information_schema.routines 
WHERE routine_schema = 'public' 
AND routine_name IN ('get_user_accessible_projects', 'can_user_access_project', 'link_user_to_client')
ORDER BY routine_name;
