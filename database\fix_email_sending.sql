-- Fix Email Sending - Complete Email System Setup
-- This creates a working email system for user setup emails

-- 1. Check current Supabase email configuration
DO $$
BEGIN
    RAISE NOTICE '=== EMAIL SYSTEM DIAGNOSIS ===';
    RAISE NOTICE 'This script will set up email sending for user setup emails';
    RAISE NOTICE '';
    RAISE NOTICE 'IMPORTANT: After running this script, you need to:';
    RAISE NOTICE '1. Configure Supabase Auth email settings';
    RAISE NOTICE '2. Enable email confirmations (optional)';
    RAISE NOTICE '3. Set up custom SMTP (recommended for production)';
    RAISE NOTICE '';
END $$;

-- 2. Add email tracking columns if they don't exist
DO $$
BEGIN
    -- Add email tracking columns
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'user_profiles' AND column_name = 'setup_email_sent_at') THEN
        ALTER TABLE public.user_profiles ADD COLUMN setup_email_sent_at TIMESTAMP WITH TIME ZONE;
        RAISE NOTICE '✓ Added setup_email_sent_at column';
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'user_profiles' AND column_name = 'setup_email_count') THEN
        ALTER TABLE public.user_profiles ADD COLUMN setup_email_count INTEGER DEFAULT 0;
        RAISE NOTICE '✓ Added setup_email_count column';
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'user_profiles' AND column_name = 'email_status') THEN
        ALTER TABLE public.user_profiles ADD COLUMN email_status VARCHAR(50) DEFAULT 'pending';
        RAISE NOTICE '✓ Added email_status column';
    END IF;
    
EXCEPTION
    WHEN OTHERS THEN
        RAISE NOTICE '❌ Error adding columns: %', SQLERRM;
END $$;

-- 3. Create email template function
CREATE OR REPLACE FUNCTION public.generate_setup_email_content(
    user_first_name TEXT,
    user_email TEXT,
    setup_token TEXT,
    company_name TEXT DEFAULT 'Construction Management System'
)
RETURNS TABLE(subject TEXT, html_content TEXT, text_content TEXT) AS $$
DECLARE
    setup_url TEXT;
BEGIN
    -- Generate setup URL (you'll need to replace this with your actual domain)
    setup_url := 'https://your-domain.com/setup-password?token=' || setup_token || '&email=' || user_email;
    
    -- Return email content
    RETURN QUERY SELECT 
        'Complete Your Account Setup - ' || company_name,
        '<html><body style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
            <div style="background: #f8f9fa; padding: 20px; border-radius: 8px;">
                <h2 style="color: #2563eb;">Welcome to ' || company_name || '</h2>
                <p>Hello ' || user_first_name || ',</p>
                <p>Your account has been created! Please complete your setup by creating a password.</p>
                <div style="margin: 30px 0;">
                    <a href="' || setup_url || '" 
                       style="background: #2563eb; color: white; padding: 12px 24px; 
                              text-decoration: none; border-radius: 6px; display: inline-block;">
                        Complete Account Setup
                    </a>
                </div>
                <p><strong>Account Details:</strong></p>
                <ul>
                    <li>Email: ' || user_email || '</li>
                    <li>Setup Token: ' || setup_token || '</li>
                </ul>
                <p style="color: #666; font-size: 14px;">
                    This link will expire in 24 hours. If you need assistance, please contact your administrator.
                </p>
                <hr style="margin: 30px 0; border: none; border-top: 1px solid #ddd;">
                <p style="color: #666; font-size: 12px;">
                    This email was sent by ' || company_name || ' Construction Management System.
                </p>
            </div>
        </body></html>',
        'Welcome to ' || company_name || '
        
Hello ' || user_first_name || ',

Your account has been created! Please complete your setup by creating a password.

Setup URL: ' || setup_url || '

Account Details:
- Email: ' || user_email || '
- Setup Token: ' || setup_token || '

This link will expire in 24 hours. If you need assistance, please contact your administrator.

---
This email was sent by ' || company_name || ' Construction Management System.';
END;
$$ LANGUAGE plpgsql;

-- 4. Create comprehensive email sending function
CREATE OR REPLACE FUNCTION public.send_setup_email_via_supabase(
    user_profile_id UUID,
    force_resend BOOLEAN DEFAULT false
)
RETURNS TABLE(success BOOLEAN, message TEXT, email_content JSONB) AS $$
DECLARE
    user_record RECORD;
    email_template RECORD;
    setup_token TEXT;
    current_count INTEGER;
    max_emails INTEGER := 5; -- Maximum emails per user
BEGIN
    -- Get user information
    SELECT up.*, ur.role_name
    INTO user_record
    FROM public.user_profiles up
    JOIN public.user_roles ur ON up.role_id = ur.id
    WHERE up.id = user_profile_id;
    
    IF NOT FOUND THEN
        RETURN QUERY SELECT false, 'User not found', '{}'::jsonb;
        RETURN;
    END IF;
    
    -- Check email sending limits
    current_count := COALESCE(user_record.setup_email_count, 0);
    
    IF current_count >= max_emails AND NOT force_resend THEN
        RETURN QUERY SELECT false, 'Maximum email limit reached (' || max_emails || ')', '{}'::jsonb;
        RETURN;
    END IF;
    
    -- Generate new setup token
    setup_token := 'setup_' || EXTRACT(EPOCH FROM NOW())::TEXT || '_' || user_profile_id::TEXT;
    
    -- Update user profile with new token
    UPDATE public.user_profiles 
    SET 
        password_setup_token = setup_token,
        password_setup_expires_at = NOW() + INTERVAL '24 hours',
        setup_email_sent_at = NOW(),
        setup_email_count = current_count + 1,
        email_status = 'sent',
        requires_password_setup = true,
        updated_at = NOW()
    WHERE id = user_profile_id;
    
    -- Generate email content
    SELECT * INTO email_template 
    FROM public.generate_setup_email_content(
        user_record.first_name,
        user_record.email,
        setup_token,
        'Construction Management System'
    );
    
    -- Log the email sending attempt (optional - only if table exists)
    BEGIN
        INSERT INTO public.user_activity_logs (
            user_id,
            action_type,
            table_name,
            record_id,
            details,
            created_at
        ) VALUES (
            auth.uid(),
            'setup_email_sent',
            'user_profiles',
            user_profile_id,
            jsonb_build_object(
                'recipient_email', user_record.email,
                'setup_token', setup_token,
                'email_count', current_count + 1
            ),
            NOW()
        );
    EXCEPTION
        WHEN undefined_table THEN
            -- Table doesn't exist, skip logging
            NULL;
    END;
    
    -- Return success with email content for frontend to handle
    RETURN QUERY SELECT 
        true, 
        'Setup email prepared successfully',
        jsonb_build_object(
            'to_email', user_record.email,
            'subject', email_template.subject,
            'html_content', email_template.html_content,
            'text_content', email_template.text_content,
            'setup_token', setup_token,
            'user_name', user_record.first_name || ' ' || user_record.last_name
        );
    
EXCEPTION
    WHEN OTHERS THEN
        RETURN QUERY SELECT false, 'Email preparation failed: ' || SQLERRM, '{}'::jsonb;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 5. Create simple email status check function
CREATE OR REPLACE FUNCTION public.check_email_status(user_profile_id UUID)
RETURNS TABLE(
    email_sent BOOLEAN,
    last_sent_at TIMESTAMP WITH TIME ZONE,
    email_count INTEGER,
    token_expires_at TIMESTAMP WITH TIME ZONE,
    token_valid BOOLEAN
) AS $$
DECLARE
    user_record RECORD;
BEGIN
    SELECT 
        setup_email_sent_at,
        setup_email_count,
        password_setup_expires_at,
        password_setup_token
    INTO user_record
    FROM public.user_profiles
    WHERE id = user_profile_id;
    
    IF NOT FOUND THEN
        RETURN QUERY SELECT false, NULL::timestamp, 0, NULL::timestamp, false;
        RETURN;
    END IF;
    
    RETURN QUERY SELECT 
        user_record.setup_email_sent_at IS NOT NULL,
        user_record.setup_email_sent_at,
        COALESCE(user_record.setup_email_count, 0),
        user_record.password_setup_expires_at,
        (user_record.password_setup_token IS NOT NULL AND 
         user_record.password_setup_expires_at > NOW());
END;
$$ LANGUAGE plpgsql;

-- 6. Update the RPC function to use the new email system
CREATE OR REPLACE FUNCTION public.rpc_resend_setup_email(user_profile_id UUID)
RETURNS jsonb AS $$
DECLARE
    v_requesting_user_id UUID;
    v_requesting_role VARCHAR(50);
    email_result RECORD;
BEGIN
    -- Get the requesting user ID from the current session
    v_requesting_user_id := auth.uid();
    
    IF v_requesting_user_id IS NULL THEN
        RETURN jsonb_build_object('success', false, 'error', 'Not authenticated');
    END IF;
    
    -- Get requesting user's role
    SELECT ur.role_name INTO v_requesting_role
    FROM public.user_profiles up
    JOIN public.user_roles ur ON up.role_id = ur.id
    WHERE up.user_id = v_requesting_user_id;
    
    -- Check if user has admin privileges
    IF v_requesting_role != 'admin' THEN
        RETURN jsonb_build_object('success', false, 'error', 'Admin privileges required');
    END IF;
    
    -- Send the setup email
    SELECT * INTO email_result 
    FROM public.send_setup_email_via_supabase(user_profile_id, false);
    
    IF email_result.success THEN
        RETURN jsonb_build_object(
            'success', true, 
            'message', email_result.message,
            'email_data', email_result.email_content
        );
    ELSE
        RETURN jsonb_build_object('success', false, 'error', email_result.message);
    END IF;
    
EXCEPTION
    WHEN OTHERS THEN
        RETURN jsonb_build_object('success', false, 'error', SQLERRM);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 7. Grant permissions
GRANT EXECUTE ON FUNCTION public.generate_setup_email_content(TEXT, TEXT, TEXT, TEXT) TO authenticated;
GRANT EXECUTE ON FUNCTION public.send_setup_email_via_supabase(UUID, BOOLEAN) TO authenticated;
GRANT EXECUTE ON FUNCTION public.check_email_status(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION public.rpc_resend_setup_email(UUID) TO authenticated;

-- 8. Test the email system
DO $$
DECLARE
    test_user_id UUID;
    email_result RECORD;
    status_result RECORD;
BEGIN
    RAISE NOTICE '';
    RAISE NOTICE '=== TESTING EMAIL SYSTEM ===';
    
    -- Find a test user
    SELECT id INTO test_user_id 
    FROM public.user_profiles 
    WHERE email LIKE '%test%' OR email LIKE '%example%'
    LIMIT 1;
    
    IF test_user_id IS NOT NULL THEN
        RAISE NOTICE 'Testing with user ID: %', test_user_id;
        
        -- Test email generation
        SELECT * INTO email_result 
        FROM public.send_setup_email_via_supabase(test_user_id, true);
        
        RAISE NOTICE 'Email generation result:';
        RAISE NOTICE '  Success: %', email_result.success;
        RAISE NOTICE '  Message: %', email_result.message;
        
        -- Test status check
        SELECT * INTO status_result 
        FROM public.check_email_status(test_user_id);
        
        RAISE NOTICE 'Email status:';
        RAISE NOTICE '  Email sent: %', status_result.email_sent;
        RAISE NOTICE '  Email count: %', status_result.email_count;
        RAISE NOTICE '  Token valid: %', status_result.token_valid;
        
    ELSE
        RAISE NOTICE 'No test user found - create a user to test email functionality';
    END IF;
END $$;

-- 9. Final instructions
DO $$
BEGIN
    RAISE NOTICE '';
    RAISE NOTICE '=== EMAIL SYSTEM SETUP COMPLETE ===';
    RAISE NOTICE '';
    RAISE NOTICE 'NEXT STEPS:';
    RAISE NOTICE '1. Configure Supabase Auth Email Settings:';
    RAISE NOTICE '   - Go to Authentication → Settings';
    RAISE NOTICE '   - Configure SMTP settings (recommended)';
    RAISE NOTICE '   - Or use Supabase default email service';
    RAISE NOTICE '';
    RAISE NOTICE '2. Update Frontend Email Service:';
    RAISE NOTICE '   - The RPC function now returns email content';
    RAISE NOTICE '   - Frontend can display the email or send via external service';
    RAISE NOTICE '';
    RAISE NOTICE '3. Test Email Functionality:';
    RAISE NOTICE '   - Create a test user';
    RAISE NOTICE '   - Try resending setup email';
    RAISE NOTICE '   - Check email content in response';
    RAISE NOTICE '';
    RAISE NOTICE 'EMAIL FUNCTIONS CREATED:';
    RAISE NOTICE '✓ send_setup_email_via_supabase()';
    RAISE NOTICE '✓ generate_setup_email_content()';
    RAISE NOTICE '✓ check_email_status()';
    RAISE NOTICE '✓ rpc_resend_setup_email() (updated)';
END $$;
