import React, { useState } from 'react';
import Layout from '@/components/layout/Layout';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useClients, useClientAnalytics } from '@/hooks/useClients';
import { ClientFinancialDashboard } from '@/components/clients/ClientFinancialDashboard';
import { ClientList } from '@/components/clients/ClientList';
import { ClientDetails } from '@/components/clients/ClientDetails';
import { CreateClientDialog } from '@/components/clients/CreateClientDialog';
import { ClientAnalytics } from '@/components/clients/ClientAnalytics';
import { 
  Users, 
  Plus, 
  Search, 
  Filter, 
  DollarSign, 
  TrendingUp, 
  AlertTriangle,
  FileText,
  CreditCard,
  Building
} from 'lucide-react';
import { Client, ClientFilters } from '@/types/client';

export const Clients: React.FC = () => {
  const { clients, loading, error, createClient, updateClient, deleteClient } = useClients();
  const { analytics, refreshAnalytics } = useClientAnalytics();
  
  const [selectedClient, setSelectedClient] = useState<Client | null>(null);
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [activeTab, setActiveTab] = useState('overview');
  const [filters, setFilters] = useState<ClientFilters>({});
  const [searchTerm, setSearchTerm] = useState('');

  // Filter clients based on search and filters
  const filteredClients = clients.filter(client => {
    const matchesSearch = !searchTerm || 
      client.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      client.company_name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      client.email.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesStatus = !filters.status || client.status === filters.status;
    const matchesType = !filters.client_type || client.client_type === filters.client_type;
    const matchesIndustry = !filters.industry || client.industry === filters.industry;
    
    return matchesSearch && matchesStatus && matchesType && matchesIndustry;
  });

  const handleCreateClient = async (clientData: Omit<Client, 'id' | 'created_at' | 'updated_at'>) => {
    try {
      await createClient(clientData);
      setIsCreateDialogOpen(false);
      // Refresh analytics to show updated data
      await refreshAnalytics();
    } catch (error) {
      console.error('Failed to create client:', error);
    }
  };

  const handleUpdateClient = async (id: string, updates: Partial<Client>) => {
    try {
      await updateClient(id, updates);
    } catch (error) {
      console.error('Failed to update client:', error);
    }
  };

  const handleDeleteClient = async (id: string) => {
    try {
      await deleteClient(id);
      if (selectedClient?.id === id) {
        setSelectedClient(null);
      }
    } catch (error) {
      console.error('Failed to delete client:', error);
    }
  };

  if (loading) {
    return (
      <Layout>
        <div className="flex justify-center items-center h-64">
          <div className="text-center">
            <div className="w-8 h-8 border-4 border-blue-600 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
            <p className="text-gray-600">Loading clients...</p>
          </div>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 flex items-center gap-3">
              <Users className="h-8 w-8 text-blue-600" />
              Client Financial Management
            </h1>
            <p className="text-gray-600 mt-1">
              Track client payments, outstanding amounts, and financial relationships
            </p>
          </div>
          <CreateClientDialog
            open={isCreateDialogOpen}
            onOpenChange={setIsCreateDialogOpen}
            onCreateClient={handleCreateClient}
          />
        </div>

        {/* Quick Stats */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Total Clients</p>
                  <p className="text-2xl font-bold text-gray-900">{clients.length}</p>
                </div>
                <Building className="h-8 w-8 text-blue-600" />
              </div>
              <p className="text-xs text-gray-500 mt-2">
                {clients.filter(c => c.status === 'Active').length} active
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Industries</p>
                  <p className="text-2xl font-bold text-green-600">
                    {new Set(clients.map(c => c.industry).filter(Boolean)).size}
                  </p>
                </div>
                <DollarSign className="h-8 w-8 text-green-600" />
              </div>
              <p className="text-xs text-gray-500 mt-2">
                Different sectors
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Companies</p>
                  <p className="text-2xl font-bold text-orange-600">
                    {clients.filter(c => c.client_type === 'Company').length}
                  </p>
                </div>
                <FileText className="h-8 w-8 text-orange-600" />
              </div>
              <p className="text-xs text-gray-500 mt-2">
                Corporate clients
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">This Month</p>
                  <p className="text-2xl font-bold text-purple-600">
                    {clients.filter(c => {
                      const created = new Date(c.created_at);
                      const now = new Date();
                      return created.getMonth() === now.getMonth() &&
                             created.getFullYear() === now.getFullYear();
                    }).length}
                  </p>
                </div>
                <AlertTriangle className="h-8 w-8 text-purple-600" />
              </div>
              <p className="text-xs text-gray-500 mt-2">
                New clients added
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Main Content */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="clients">Client List</TabsTrigger>
            <TabsTrigger value="financials">Financial Dashboard</TabsTrigger>
            <TabsTrigger value="analytics">Analytics</TabsTrigger>
          </TabsList>

          {/* Overview Tab */}
          <TabsContent value="overview" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
              {/* Client List */}
              <div className="lg:col-span-2">
                <Card>
                  <CardHeader>
                    <div className="flex items-center justify-between">
                      <CardTitle className="flex items-center gap-2">
                        <Users className="h-5 w-5" />
                        Recent Clients
                      </CardTitle>
                      <Button 
                        variant="outline" 
                        size="sm"
                        onClick={() => setActiveTab('clients')}
                      >
                        View All
                      </Button>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      {filteredClients.slice(0, 5).map((client) => (
                        <div 
                          key={client.id}
                          className="flex items-center justify-between p-3 border rounded-lg hover:bg-gray-50 cursor-pointer"
                          onClick={() => setSelectedClient(client)}
                        >
                          <div className="flex items-center space-x-3">
                            <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                              <Building className="h-5 w-5 text-blue-600" />
                            </div>
                            <div>
                              <p className="font-medium text-gray-900">{client.name}</p>
                              <p className="text-sm text-gray-500">{client.email}</p>
                            </div>
                          </div>
                          <div className="text-right">
                            <Badge 
                              variant={client.status === 'Active' ? 'default' : 'secondary'}
                              className="mb-1"
                            >
                              {client.status}
                            </Badge>
                            <p className="text-sm text-gray-500">{client.industry}</p>
                          </div>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* Quick Actions */}
              <div>
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <TrendingUp className="h-5 w-5" />
                      Quick Actions
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-3">
                    <Button 
                      className="w-full justify-start" 
                      variant="outline"
                      onClick={() => setIsCreateDialogOpen(true)}
                    >
                      <Plus className="h-4 w-4 mr-2" />
                      Add New Client
                    </Button>
                    <Button 
                      className="w-full justify-start" 
                      variant="outline"
                      onClick={() => setActiveTab('financials')}
                    >
                      <DollarSign className="h-4 w-4 mr-2" />
                      Financial Dashboard
                    </Button>
                    <Button 
                      className="w-full justify-start" 
                      variant="outline"
                      onClick={() => setActiveTab('analytics')}
                    >
                      <TrendingUp className="h-4 w-4 mr-2" />
                      View Analytics
                    </Button>
                    <Button 
                      className="w-full justify-start" 
                      variant="outline"
                    >
                      <FileText className="h-4 w-4 mr-2" />
                      Generate Report
                    </Button>
                  </CardContent>
                </Card>
              </div>
            </div>
          </TabsContent>

          {/* Client List Tab */}
          <TabsContent value="clients" className="space-y-6">
            <ClientList
              clients={filteredClients}
              onSelectClient={setSelectedClient}
              onUpdateClient={handleUpdateClient}
              onDeleteClient={handleDeleteClient}
              searchTerm={searchTerm}
              onSearchChange={setSearchTerm}
              filters={filters}
              onFiltersChange={setFilters}
            />
          </TabsContent>

          {/* Financial Dashboard Tab */}
          <TabsContent value="financials" className="space-y-6">
            <ClientFinancialDashboard clients={clients} />
          </TabsContent>

          {/* Analytics Tab */}
          <TabsContent value="analytics" className="space-y-6">
            <ClientAnalytics analytics={analytics} />
          </TabsContent>
        </Tabs>

        {/* Client Details Modal */}
        {selectedClient && (
          <ClientDetails
            client={selectedClient}
            open={!!selectedClient}
            onClose={() => setSelectedClient(null)}
            onUpdate={handleUpdateClient}
            onDelete={handleDeleteClient}
          />
        )}
      </div>
    </Layout>
  );
};
