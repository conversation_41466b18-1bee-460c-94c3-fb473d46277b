-- Fix Messages Display - Add Creator Fields
-- Run this script in your Supabase SQL Editor

-- Add creator fields to messages table
ALTER TABLE public.messages 
ADD COLUMN IF NOT EXISTS created_by_user_id UUID REFERENCES auth.users(id),
ADD COLUMN IF NOT EXISTS created_by_name TEXT,
ADD COLUMN IF NOT EXISTS created_by_avatar TEXT;

-- Create function to automatically populate creator info for messages
CREATE OR REPLACE FUNCTION populate_message_creator_info()
RETURNS TRIGGER AS $$
DECLARE
  user_profile RECORD;
BEGIN
  -- Get current user's profile information
  SELECT 
    COALESCE(first_name || ' ' || last_name, email, 'Unknown User') as full_name,
    COALESCE(profile_picture_url, avatar_url) as avatar_url
  INTO user_profile
  FROM public.user_profiles 
  WHERE user_id = auth.uid()
  LIMIT 1;

  -- If no profile found, try to get from auth.users
  IF user_profile.full_name IS NULL THEN
    SELECT 
      COALESCE(raw_user_meta_data->>'full_name', email, 'Unknown User') as full_name,
      COALESCE(raw_user_meta_data->>'avatar_url', '') as avatar_url
    INTO user_profile
    FROM auth.users 
    WHERE id = auth.uid()
    LIMIT 1;
  END IF;

  -- Set creator fields
  NEW.created_by_user_id := auth.uid();
  NEW.created_by_name := COALESCE(user_profile.full_name, 'Unknown User');
  NEW.created_by_avatar := user_profile.avatar_url;

  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger for messages table
DROP TRIGGER IF EXISTS set_creator_info_messages ON public.messages;
CREATE TRIGGER set_creator_info_messages
  BEFORE INSERT ON public.messages
  FOR EACH ROW
  EXECUTE FUNCTION populate_message_creator_info();

-- Grant necessary permissions
GRANT EXECUTE ON FUNCTION populate_message_creator_info() TO authenticated;

-- Update existing messages with creator info (optional - uncomment if needed)
-- UPDATE public.messages 
-- SET 
--   created_by_name = COALESCE(sender_name, 'Unknown User'),
--   created_by_avatar = NULL
-- WHERE created_by_name IS NULL;

SELECT 'Messages creator fields setup complete!' as status;
