import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { useToast } from '@/components/ui/use-toast';
import { OrphanedUserFixer } from '@/utils/fixOrphanedUsers';
import { 
  UserX, 
  UserCheck, 
  RefreshCw, 
  AlertTriangle,
  CheckCircle,
  Copy
} from 'lucide-react';

const FixOrphanedUsers = () => {
  const { toast } = useToast();
  const [loading, setLoading] = useState(false);
  const [orphanedUsers, setOrphanedUsers] = useState<any[]>([]);
  const [fixResults, setFixResults] = useState<any>(null);

  const loadOrphanedUsers = async () => {
    try {
      setLoading(true);
      const users = await OrphanedUserFixer.getOrphanedUsers();
      setOrphanedUsers(users);
      
      toast({
        title: "Scan Complete",
        description: `Found ${users.length} orphaned users`,
      });
    } catch (error: any) {
      toast({
        title: "Error",
        description: error.message || "Failed to scan for orphaned users",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const fixAllUsers = async () => {
    try {
      setLoading(true);
      const results = await OrphanedUserFixer.fixAllOrphanedUsers();
      setFixResults(results);
      
      if (results.success) {
        toast({
          title: "Fix Complete",
          description: results.message,
        });
        
        // Refresh the list
        await loadOrphanedUsers();
      } else {
        toast({
          title: "Fix Failed",
          description: results.message,
          variant: "destructive",
        });
      }
    } catch (error: any) {
      toast({
        title: "Error",
        description: error.message || "Failed to fix orphaned users",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const fixSingleUser = async (email: string) => {
    try {
      setLoading(true);
      const result = await OrphanedUserFixer.fixOrphanedUser(email);
      
      if (result.success) {
        toast({
          title: "User Fixed",
          description: `${email} has been fixed. Password: ${result.credentials?.password}`,
        });
        
        // Copy credentials to clipboard
        if (result.credentials) {
          const credentials = `Email: ${result.credentials.email}\nPassword: ${result.credentials.password}`;
          navigator.clipboard?.writeText(credentials);
        }
        
        // Refresh the list
        await loadOrphanedUsers();
      } else {
        toast({
          title: "Fix Failed",
          description: result.message,
          variant: "destructive",
        });
      }
    } catch (error: any) {
      toast({
        title: "Error",
        description: error.message || "Failed to fix user",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const copyAllCredentials = () => {
    if (!fixResults?.fixed?.length) return;
    
    const credentials = fixResults.fixed
      .map((user: any) => `${user.email}: ${user.password}`)
      .join('\n');
    
    navigator.clipboard?.writeText(credentials);
    toast({
      title: "Credentials Copied",
      description: "All user credentials copied to clipboard",
    });
  };

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Fix Orphaned Users</h1>
          <p className="text-muted-foreground">
            Repair user profiles that are missing auth accounts
          </p>
        </div>
        <Button onClick={loadOrphanedUsers} disabled={loading}>
          <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
          Scan for Issues
        </Button>
      </div>

      {/* Scan Results */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <UserX className="h-5 w-5" />
            Orphaned Users ({orphanedUsers.length})
          </CardTitle>
          <CardDescription>
            Users with profiles but no authentication accounts
          </CardDescription>
        </CardHeader>
        <CardContent>
          {orphanedUsers.length === 0 ? (
            <Alert>
              <CheckCircle className="h-4 w-4" />
              <AlertDescription>
                No orphaned users found. All profiles have valid auth accounts.
              </AlertDescription>
            </Alert>
          ) : (
            <div className="space-y-4">
              <Alert>
                <AlertTriangle className="h-4 w-4" />
                <AlertDescription>
                  Found {orphanedUsers.length} users with missing auth accounts. 
                  These users cannot login until fixed.
                </AlertDescription>
              </Alert>
              
              <div className="flex gap-2 mb-4">
                <Button onClick={fixAllUsers} disabled={loading}>
                  <UserCheck className="h-4 w-4 mr-2" />
                  Fix All Users
                </Button>
              </div>

              <div className="space-y-2">
                {orphanedUsers.map((user) => (
                  <div key={user.id} className="flex items-center justify-between p-3 border rounded-lg">
                    <div>
                      <div className="font-medium">{user.first_name} {user.last_name}</div>
                      <div className="text-sm text-muted-foreground">{user.email}</div>
                      <div className="text-xs text-muted-foreground">Role: {user.role_name}</div>
                    </div>
                    <Button 
                      size="sm" 
                      onClick={() => fixSingleUser(user.email)}
                      disabled={loading}
                    >
                      Fix User
                    </Button>
                  </div>
                ))}
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Fix Results */}
      {fixResults && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <CheckCircle className="h-5 w-5" />
              Fix Results
            </CardTitle>
            <CardDescription>
              {fixResults.message}
            </CardDescription>
          </CardHeader>
          <CardContent>
            {fixResults.fixed?.length > 0 && (
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <h3 className="font-medium text-green-600">
                    Successfully Fixed ({fixResults.fixed.length})
                  </h3>
                  <Button size="sm" onClick={copyAllCredentials}>
                    <Copy className="h-4 w-4 mr-2" />
                    Copy All Credentials
                  </Button>
                </div>
                <div className="space-y-2">
                  {fixResults.fixed.map((user: any, index: number) => (
                    <div key={index} className="p-3 bg-green-50 border border-green-200 rounded-lg">
                      <div className="font-medium">{user.email}</div>
                      <div className="text-sm text-green-600">Password: {user.password}</div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {fixResults.failed?.length > 0 && (
              <div className="space-y-4 mt-4">
                <h3 className="font-medium text-red-600">
                  Failed to Fix ({fixResults.failed.length})
                </h3>
                <div className="space-y-2">
                  {fixResults.failed.map((user: any, index: number) => (
                    <div key={index} className="p-3 bg-red-50 border border-red-200 rounded-lg">
                      <div className="font-medium">{user.email}</div>
                      <div className="text-sm text-red-600">{user.error}</div>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default FixOrphanedUsers;
