-- Create the missing get_cash_flow_data function
-- Run this in your Supabase SQL Editor

CREATE OR REPLACE FUNCTION get_cash_flow_data(
    start_date DATE DEFAULT NULL,
    end_date DATE DEFAULT NULL,
    period_type TEXT DEFAULT 'month'
)
RETURNS TABLE (
    period_date DATE,
    period_label TEXT,
    total_inflow DECIMAL(15,2),
    total_outflow DECIMAL(15,2),
    net_flow DECIMAL(15,2)
) AS $$
BEGIN
    -- Set default dates if not provided
    IF start_date IS NULL THEN
        start_date := CURRENT_DATE - INTERVAL '12 months';
    END IF;
    
    IF end_date IS NULL THEN
        end_date := CURRENT_DATE;
    END IF;
    
    -- Return cash flow data grouped by period
    RETURN QUERY
    WITH date_series AS (
        SELECT 
            date_trunc(period_type, generate_series(start_date, end_date, ('1 ' || period_type)::interval))::date as period_start
    ),
    cash_flow_summary AS (
        SELECT 
            date_trunc(period_type, transaction_date)::date as period_start,
            SUM(CASE WHEN transaction_type = 'inflow' THEN amount ELSE 0 END) as inflow,
            SUM(CASE WHEN transaction_type = 'outflow' THEN amount ELSE 0 END) as outflow
        FROM cash_flow_transactions
        WHERE transaction_date >= start_date 
        AND transaction_date <= end_date
        GROUP BY date_trunc(period_type, transaction_date)::date
    )
    SELECT 
        ds.period_start as period_date,
        CASE 
            WHEN period_type = 'month' THEN to_char(ds.period_start, 'Mon YYYY')
            WHEN period_type = 'week' THEN 'Week of ' || to_char(ds.period_start, 'Mon DD')
            WHEN period_type = 'day' THEN to_char(ds.period_start, 'Mon DD, YYYY')
            ELSE to_char(ds.period_start, 'YYYY')
        END as period_label,
        COALESCE(cfs.inflow, 0) as total_inflow,
        COALESCE(cfs.outflow, 0) as total_outflow,
        COALESCE(cfs.inflow, 0) - COALESCE(cfs.outflow, 0) as net_flow
    FROM date_series ds
    LEFT JOIN cash_flow_summary cfs ON ds.period_start = cfs.period_start
    ORDER BY ds.period_start;
END;
$$ LANGUAGE plpgsql;
