import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import { 
  BarChart, 
  Bar, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  Legend, 
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell,
  LineChart,
  Line,
  Area,
  AreaChart
} from 'recharts';
import { 
  DollarSign, 
  TrendingUp, 
  TrendingDown, 
  PieChart as PieChartIcon,
  BarChart3,
  Calendar,
  Download
} from 'lucide-react';
import { AnalyticsService, AnalyticsData } from '@/lib/analytics';
import ReportCard from './ReportCard';
import ChartContainer from './ChartContainer';
import InsightCard from './InsightCard';
import DataTable from './DataTable';

const COLORS = ['#3b82f6', '#ef4444', '#10b981', '#f59e0b', '#8b5cf6', '#06b6d4', '#84cc16', '#f97316'];

const FinancialAnalytics: React.FC = () => {
  const [analyticsData, setAnalyticsData] = useState<AnalyticsData | null>(null);
  const [loading, setLoading] = useState(true);
  const [dateRange, setDateRange] = useState<{ start?: string; end?: string }>({});

  useEffect(() => {
    loadAnalyticsData();
  }, [dateRange]);

  const loadAnalyticsData = async () => {
    try {
      setLoading(true);
      const data = await AnalyticsService.getAnalyticsData(dateRange.start, dateRange.end);
      setAnalyticsData(data);
    } catch (error) {
      console.error('Error loading analytics data:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleExportReport = () => {
    // TODO: Implement PDF export
    console.log('Exporting financial report...');
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading financial analytics...</p>
        </div>
      </div>
    );
  }

  if (!analyticsData) {
    return (
      <div className="text-center py-8">
        <p className="text-gray-500">No analytics data available</p>
        <Button onClick={loadAnalyticsData} className="mt-4">
          Retry
        </Button>
      </div>
    );
  }

  const { financialSummary, revenueAnalytics, expenseAnalytics, cashFlowAnalytics, insights } = analyticsData;

  // Prepare chart data
  const profitLossData = [
    { name: 'Revenue', amount: financialSummary.totalRevenue, color: '#10b981' },
    { name: 'Expenses', amount: financialSummary.totalExpenses, color: '#ef4444' },
    { name: 'Net Profit', amount: financialSummary.netProfit, color: financialSummary.netProfit >= 0 ? '#3b82f6' : '#f59e0b' }
  ];

  const revenueBreakdownData = revenueAnalytics.revenueByCategory.map((item, index) => ({
    ...item,
    color: COLORS[index % COLORS.length]
  }));

  const expenseBreakdownData = expenseAnalytics.expensesByCategory.map((item, index) => ({
    ...item,
    color: COLORS[index % COLORS.length]
  }));

  const monthlyTrendData = revenueAnalytics.revenueByMonth.map(revenue => {
    const expense = expenseAnalytics.expensesByMonth.find(e => e.month === revenue.month);
    return {
      month: revenue.month,
      revenue: revenue.amount,
      expenses: expense?.amount || 0,
      profit: revenue.amount - (expense?.amount || 0)
    };
  });

  const cashFlowData = cashFlowAnalytics.cashFlowByMonth.map(item => ({
    month: item.month,
    inflow: cashFlowAnalytics.inflows,
    outflow: cashFlowAnalytics.outflows,
    netFlow: item.amount
  }));

  // Table data for detailed breakdown
  const revenueTableColumns = [
    { key: 'category', label: 'Category', sortable: true },
    { key: 'amount', label: 'Amount', type: 'currency', sortable: true },
    { key: 'percentage', label: 'Percentage', type: 'number', sortable: true, render: (value: number) => `${value.toFixed(1)}%` },
    { key: 'count', label: 'Transactions', type: 'number', sortable: true }
  ];

  const expenseTableColumns = [
    { key: 'category', label: 'Category', sortable: true },
    { key: 'amount', label: 'Amount', type: 'currency', sortable: true },
    { key: 'percentage', label: 'Percentage', type: 'number', sortable: true, render: (value: number) => `${value.toFixed(1)}%` },
    { key: 'count', label: 'Transactions', type: 'number', sortable: true }
  ];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold">Financial Analytics</h2>
          <p className="text-gray-600">Comprehensive financial insights and performance analysis</p>
        </div>
        <div className="flex space-x-2">
          <Button variant="outline" onClick={loadAnalyticsData}>
            <TrendingUp className="w-4 h-4 mr-2" />
            Refresh
          </Button>
          <Button onClick={handleExportReport}>
            <Download className="w-4 h-4 mr-2" />
            Export Report
          </Button>
        </div>
      </div>

      {/* Key Metrics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <ReportCard
          title="Total Revenue"
          value={financialSummary.totalRevenue}
          type="currency"
          status="positive"
          change={financialSummary.growthRate}
          changeLabel="vs previous period"
          trend={financialSummary.growthRate > 0 ? 'up' : financialSummary.growthRate < 0 ? 'down' : 'neutral'}
          icon={<DollarSign className="h-4 w-4 text-green-500" />}
        />
        <ReportCard
          title="Total Expenses"
          value={financialSummary.totalExpenses}
          type="currency"
          status="negative"
          description="Business expenses"
          icon={<TrendingDown className="h-4 w-4 text-red-500" />}
        />
        <ReportCard
          title="Net Profit"
          value={financialSummary.netProfit}
          type="currency"
          status={financialSummary.netProfit >= 0 ? 'positive' : 'negative'}
          change={financialSummary.profitMargin}
          changeLabel="profit margin"
          icon={<BarChart3 className="h-4 w-4" />}
        />
        <ReportCard
          title="Cash Flow"
          value={financialSummary.cashFlow}
          type="currency"
          status={financialSummary.cashFlow >= 0 ? 'positive' : 'warning'}
          description="Net cash flow"
          icon={<TrendingUp className="h-4 w-4" />}
        />
      </div>

      {/* Business Insights */}
      {insights.length > 0 && (
        <div className="space-y-4">
          <h3 className="text-lg font-semibold">Business Insights</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {insights.slice(0, 6).map((insight) => (
              <InsightCard
                key={insight.id}
                insight={insight}
                onDismiss={(id) => console.log('Dismiss insight:', id)}
                onAction={(id) => console.log('Take action on insight:', id)}
              />
            ))}
          </div>
        </div>
      )}

      {/* Charts and Analysis */}
      <Tabs defaultValue="overview" className="space-y-6">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="revenue">Revenue</TabsTrigger>
          <TabsTrigger value="expenses">Expenses</TabsTrigger>
          <TabsTrigger value="cashflow">Cash Flow</TabsTrigger>
          <TabsTrigger value="trends">Trends</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <ChartContainer
              title="Profit & Loss Overview"
              description="Revenue vs Expenses comparison"
              onExport={() => console.log('Export P&L chart')}
            >
              <ResponsiveContainer width="100%" height={300}>
                <BarChart data={profitLossData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="name" />
                  <YAxis />
                  <Tooltip formatter={(value) => [`$${Number(value).toLocaleString()}`, 'Amount']} />
                  <Bar dataKey="amount" fill="#3b82f6" />
                </BarChart>
              </ResponsiveContainer>
            </ChartContainer>

            <ChartContainer
              title="Monthly Financial Trend"
              description="Revenue, expenses, and profit over time"
              onExport={() => console.log('Export trend chart')}
            >
              <ResponsiveContainer width="100%" height={300}>
                <LineChart data={monthlyTrendData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="month" />
                  <YAxis />
                  <Tooltip formatter={(value) => [`$${Number(value).toLocaleString()}`, '']} />
                  <Legend />
                  <Line type="monotone" dataKey="revenue" stroke="#10b981" strokeWidth={2} name="Revenue" />
                  <Line type="monotone" dataKey="expenses" stroke="#ef4444" strokeWidth={2} name="Expenses" />
                  <Line type="monotone" dataKey="profit" stroke="#3b82f6" strokeWidth={2} name="Profit" />
                </LineChart>
              </ResponsiveContainer>
            </ChartContainer>
          </div>
        </TabsContent>

        <TabsContent value="revenue" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <ChartContainer
              title="Revenue by Category"
              description="Breakdown of revenue sources"
              onExport={() => console.log('Export revenue breakdown')}
            >
              <ResponsiveContainer width="100%" height={300}>
                <PieChart>
                  <Pie
                    data={revenueBreakdownData}
                    cx="50%"
                    cy="50%"
                    labelLine={false}
                    label={({ name, percentage }) => `${name}: ${percentage.toFixed(1)}%`}
                    outerRadius={80}
                    fill="#8884d8"
                    dataKey="amount"
                  >
                    {revenueBreakdownData.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={entry.color} />
                    ))}
                  </Pie>
                  <Tooltip formatter={(value) => [`$${Number(value).toLocaleString()}`, 'Amount']} />
                </PieChart>
              </ResponsiveContainer>
            </ChartContainer>

            <DataTable
              title="Revenue Breakdown"
              description="Detailed revenue analysis by category"
              columns={revenueTableColumns}
              data={revenueAnalytics.revenueByCategory}
              onExport={() => console.log('Export revenue table')}
            />
          </div>
        </TabsContent>

        <TabsContent value="expenses" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <ChartContainer
              title="Expense by Category"
              description="Breakdown of business expenses"
              onExport={() => console.log('Export expense breakdown')}
            >
              <ResponsiveContainer width="100%" height={300}>
                <PieChart>
                  <Pie
                    data={expenseBreakdownData}
                    cx="50%"
                    cy="50%"
                    labelLine={false}
                    label={({ name, percentage }) => `${name}: ${percentage.toFixed(1)}%`}
                    outerRadius={80}
                    fill="#8884d8"
                    dataKey="amount"
                  >
                    {expenseBreakdownData.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={entry.color} />
                    ))}
                  </Pie>
                  <Tooltip formatter={(value) => [`$${Number(value).toLocaleString()}`, 'Amount']} />
                </PieChart>
              </ResponsiveContainer>
            </ChartContainer>

            <DataTable
              title="Expense Breakdown"
              description="Detailed expense analysis by category"
              columns={expenseTableColumns}
              data={expenseAnalytics.expensesByCategory}
              onExport={() => console.log('Export expense table')}
            />
          </div>
        </TabsContent>

        <TabsContent value="cashflow" className="space-y-6">
          <ChartContainer
            title="Cash Flow Analysis"
            description="Monthly cash inflows and outflows"
            onExport={() => console.log('Export cash flow chart')}
          >
            <ResponsiveContainer width="100%" height={400}>
              <AreaChart data={cashFlowData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="month" />
                <YAxis />
                <Tooltip formatter={(value) => [`$${Number(value).toLocaleString()}`, '']} />
                <Legend />
                <Area type="monotone" dataKey="inflow" stackId="1" stroke="#10b981" fill="#10b981" fillOpacity={0.6} name="Inflow" />
                <Area type="monotone" dataKey="outflow" stackId="2" stroke="#ef4444" fill="#ef4444" fillOpacity={0.6} name="Outflow" />
                <Line type="monotone" dataKey="netFlow" stroke="#3b82f6" strokeWidth={3} name="Net Flow" />
              </AreaChart>
            </ResponsiveContainer>
          </ChartContainer>
        </TabsContent>

        <TabsContent value="trends" className="space-y-6">
          <div className="text-center py-8">
            <p className="text-gray-500">Trend analysis coming soon...</p>
            <p className="text-sm text-gray-400 mt-2">Historical data analysis and forecasting will be available here</p>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default FinancialAnalytics;
