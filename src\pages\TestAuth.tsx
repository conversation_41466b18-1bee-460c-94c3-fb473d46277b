import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { useToast } from '@/components/ui/use-toast';
import { SimpleAuthTester } from '@/utils/simpleAuthTest';
import { 
  TestTube, 
  CheckCircle, 
  XCircle, 
  Loader2,
  Key,
  Users
} from 'lucide-react';

const TestAuth = () => {
  const { toast } = useToast();
  const [loading, setLoading] = useState(false);
  const [testEmail, setTestEmail] = useState('<EMAIL>');
  const [testPassword, setTestPassword] = useState('TempPass123!');
  const [singleResult, setSingleResult] = useState<any>(null);
  const [allUsersResult, setAllUsersResult] = useState<any>(null);

  const testSingleLogin = async () => {
    try {
      setLoading(true);
      setSingleResult(null);
      
      const result = await SimpleAuthTester.testLogin(testEmail, testPassword);
      setSingleResult(result);
      
      if (result.success) {
        toast({
          title: "Login Test Successful",
          description: `${testEmail} can login with the provided password`,
        });
      } else {
        toast({
          title: "Login Test Failed",
          description: result.error || "Login failed",
          variant: "destructive",
        });
      }
    } catch (error: any) {
      toast({
        title: "Test Error",
        description: error.message || "Failed to run test",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const testCommonPasswords = async () => {
    try {
      setLoading(true);
      setSingleResult(null);
      
      const result = await SimpleAuthTester.testCommonPasswords(testEmail);
      setSingleResult(result);
      
      if (result.success) {
        toast({
          title: "Password Found",
          description: `Working password: ${result.workingPassword}`,
        });
      } else {
        toast({
          title: "No Working Password",
          description: "None of the common passwords worked",
          variant: "destructive",
        });
      }
    } catch (error: any) {
      toast({
        title: "Test Error",
        description: error.message || "Failed to run test",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const testAllUsers = async () => {
    try {
      setLoading(true);
      setAllUsersResult(null);
      
      const result = await SimpleAuthTester.testAllUsers();
      setAllUsersResult(result);
      
      toast({
        title: "All Users Test Complete",
        description: `${result.working.length} working, ${result.broken.length} broken`,
      });
    } catch (error: any) {
      toast({
        title: "Test Error",
        description: error.message || "Failed to run test",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Auth Testing</h1>
          <p className="text-muted-foreground">
            Test actual login functionality instead of debugging auth tables
          </p>
        </div>
      </div>

      {/* Single User Test */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <TestTube className="h-5 w-5" />
            Test Single User Login
          </CardTitle>
          <CardDescription>
            Test if a specific user can login with given credentials
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="email">Email</Label>
              <Input
                id="email"
                type="email"
                value={testEmail}
                onChange={(e) => setTestEmail(e.target.value)}
                placeholder="<EMAIL>"
              />
            </div>
            <div>
              <Label htmlFor="password">Password</Label>
              <Input
                id="password"
                type="password"
                value={testPassword}
                onChange={(e) => setTestPassword(e.target.value)}
                placeholder="password"
              />
            </div>
          </div>
          
          <div className="flex gap-2">
            <Button onClick={testSingleLogin} disabled={loading || !testEmail}>
              {loading ? <Loader2 className="h-4 w-4 mr-2 animate-spin" /> : <TestTube className="h-4 w-4 mr-2" />}
              Test Login
            </Button>
            <Button variant="outline" onClick={testCommonPasswords} disabled={loading || !testEmail}>
              {loading ? <Loader2 className="h-4 w-4 mr-2 animate-spin" /> : <Key className="h-4 w-4 mr-2" />}
              Try Common Passwords
            </Button>
          </div>

          {singleResult && (
            <Alert className={singleResult.success ? "border-green-200 bg-green-50" : "border-red-200 bg-red-50"}>
              {singleResult.success ? <CheckCircle className="h-4 w-4" /> : <XCircle className="h-4 w-4" />}
              <AlertDescription>
                <div className="space-y-2">
                  <div><strong>Result:</strong> {singleResult.success ? 'SUCCESS' : 'FAILED'}</div>
                  {singleResult.error && <div><strong>Error:</strong> {singleResult.error}</div>}
                  {singleResult.workingPassword && <div><strong>Working Password:</strong> {singleResult.workingPassword}</div>}
                  <div><strong>Profile Exists:</strong> {singleResult.profileExists ? 'Yes' : 'No'}</div>
                  {singleResult.results && (
                    <div>
                      <strong>Password Test Results:</strong>
                      <ul className="mt-1 space-y-1">
                        {singleResult.results.map((result: any, index: number) => (
                          <li key={index} className={`text-sm ${result.success ? 'text-green-600' : 'text-red-600'}`}>
                            {result.password}: {result.success ? '✅ Works' : '❌ Failed'}
                          </li>
                        ))}
                      </ul>
                    </div>
                  )}
                </div>
              </AlertDescription>
            </Alert>
          )}
        </CardContent>
      </Card>

      {/* All Users Test */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Users className="h-5 w-5" />
            Test All Users
          </CardTitle>
          <CardDescription>
            Test login for all users in the system with common passwords
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <Button onClick={testAllUsers} disabled={loading}>
            {loading ? <Loader2 className="h-4 w-4 mr-2 animate-spin" /> : <Users className="h-4 w-4 mr-2" />}
            Test All Users (This may take a while)
          </Button>

          {allUsersResult && (
            <div className="space-y-4">
              <Alert>
                <AlertDescription>
                  <div><strong>Total Users:</strong> {allUsersResult.total}</div>
                  <div><strong>Working Logins:</strong> {allUsersResult.working.length}</div>
                  <div><strong>Broken Logins:</strong> {allUsersResult.broken.length}</div>
                </AlertDescription>
              </Alert>

              {allUsersResult.working.length > 0 && (
                <Card>
                  <CardHeader>
                    <CardTitle className="text-green-600">Working Logins</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-2">
                      {allUsersResult.working.map((user: any, index: number) => (
                        <div key={index} className="p-2 bg-green-50 border border-green-200 rounded">
                          <div><strong>{user.email}</strong></div>
                          {user.password && <div className="text-sm text-green-600">Password: {user.password}</div>}
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              )}

              {allUsersResult.broken.length > 0 && (
                <Card>
                  <CardHeader>
                    <CardTitle className="text-red-600">Broken Logins</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-2">
                      {allUsersResult.broken.map((user: any, index: number) => (
                        <div key={index} className="p-2 bg-red-50 border border-red-200 rounded">
                          <div><strong>{user.email}</strong></div>
                          <div className="text-sm text-red-600">{user.error}</div>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              )}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default TestAuth;
