# Quick User Management Setup Guide

## 🚀 **IMMEDIATE SETUP STEPS (FIXED)**

### Step 1: Run the Simple Database Schema
```sql
-- Copy and paste the ENTIRE content from:
-- database/user_management_simple.sql
-- into your Supabase SQL Editor and execute
```

**This version fixes:**
- ❌ **Select component errors** (empty string values)
- ❌ **Database infinite recursion** (RLS policy conflicts)
- ✅ **Clean, working setup** with no errors

### Step 2: Test the System
1. **Navigate to** `http://localhost:8083/register`
2. **Create an admin account** with these details:
   - First Name: Admin
   - Last Name: User
   - Email: <EMAIL>
   - Password: admin123
   - Role: Administrator
3. **Navigate to** `http://localhost:8083/login`
4. **Sign in** with your admin credentials

### Step 3: Verify Everything Works
1. **Check Profile**: Navigate to `/profile`
2. **Check User Management**: Navigate to `/settings` → Users tab
3. **Create Test Users**: Use the admin panel to create other users

## 🔧 **What the Fixed Schema Does**

### ✅ **Fixes the Foreign Key Issue:**
- Removes the problematic default admin user insertion
- Improves the user creation trigger
- Adds better error handling for profile creation

### ✅ **Creates These Tables:**
- `user_roles` - 5 predefined roles (Admin, QS, Accountant, Management, Client)
- `user_profiles` - Extended user information
- `user_sessions` - Session tracking
- `audit_logs` - Action tracking
- `password_reset_tokens` - Password reset management

### ✅ **Sets Up Permissions:**
- Row Level Security policies
- Role-based access control
- Audit logging functions
- Automatic profile creation triggers

## 🎯 **Expected Results**

After running the schema, you should be able to:

1. **✅ Register new users** without foreign key errors
2. **✅ Login with role-based access** 
3. **✅ Manage profiles** with full editing
4. **✅ Admin user management** (create, edit, delete users)
5. **✅ Role assignment** and permission checking

## 🚨 **If You Still Get Errors**

### Error: "table does not exist"
- Make sure you ran the ENTIRE schema file
- Check that all tables were created in Supabase

### Error: "permission denied"
- The RLS policies are set to allow all operations
- Check that you're using the correct Supabase project

### Error: "role not found"
- Verify that the 5 default roles were inserted
- Check the `user_roles` table in Supabase

## 🎉 **Success Indicators**

You'll know it's working when:
- ✅ Registration page loads without errors
- ✅ You can create an admin account
- ✅ Login works with the admin account
- ✅ Profile page shows your information
- ✅ Settings → Users tab shows user management
- ✅ You can create additional users as admin

## 📋 **Quick Test Checklist**

1. **[ ]** Run `database/user_management_schema_fixed.sql`
2. **[ ]** Navigate to `/register`
3. **[ ]** Create admin account (<EMAIL> / admin123)
4. **[ ]** Navigate to `/login`
5. **[ ]** Sign in with admin credentials
6. **[ ]** Check `/profile` page loads
7. **[ ]** Check `/settings` → Users tab
8. **[ ]** Create a test user as admin
9. **[ ]** Sign out and test login with test user

## 🔐 **Default Roles Created**

- **👑 Administrator** - Full system access
- **📊 Quantity Surveyor** - Cost management and projects
- **💰 Accountant** - Financial management
- **👥 Management** - Team and project oversight  
- **🤝 Client** - Limited project visibility

## 🆘 **Need Help?**

If you're still having issues:
1. Check the browser console for JavaScript errors
2. Check the Supabase logs for database errors
3. Verify your Supabase project URL and API keys
4. Make sure authentication is enabled in Supabase

**The fixed schema should resolve the foreign key constraint error and get your user management system working!** 🎯
