import React, { useState, use<PERSON>emo } from 'react';
import { <PERSON>, <PERSON>Content, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Textarea } from '@/components/ui/textarea';
import { Progress } from '@/components/ui/progress';
import { 
  Plus, 
  MoreHorizontal, 
  User, 
  Calendar, 
  Clock,
  Flag,
  Target,
  CheckCircle,
  PlayCircle,
  PauseCircle,
  AlertTriangle,
  Filter,
  Search,
  GripVertical
} from 'lucide-react';

interface Task {
  id: string;
  name: string;
  description: string;
  status: 'Not Started' | 'In Progress' | 'Completed' | 'On Hold' | 'Cancelled';
  priority: 'Low' | 'Medium' | 'High' | 'Critical';
  start_date: string;
  end_date: string;
  progress: number;
  assignee: string;
  project_id: string;
  estimated_hours: number;
  actual_hours: number;
  created_at: string;
  updated_at: string;
  dependencies?: string[];
  tags?: string[];
  parent_task_id?: string | null;
}

interface Project {
  id: string;
  name: string;
  description: string;
  client_name: string;
  status: string;
  priority: string;
  start_date: string;
  end_date: string;
  budget: string;
  spent: string;
  progress: number;
  project_manager: string;
  location: string;
  date_added: string;
  last_modified: string;
  created_at: string;
}

interface TaskKanbanProps {
  projects: Project[];
  tasks: Task[];
  loading?: boolean;
  onTaskUpdate: (id: string, updates: Partial<Task>) => Promise<void>;
  onTaskCreate: (task: Omit<Task, 'id' | 'created_at' | 'updated_at'>) => Promise<void>;
  onTaskDelete: (id: string) => Promise<void>;
}

const KANBAN_COLUMNS = [
  { 
    id: 'Not Started', 
    title: 'Backlog', 
    color: 'bg-gray-100 border-gray-200', 
    headerColor: 'bg-gray-500',
    icon: <Target className="w-4 h-4" />
  },
  { 
    id: 'In Progress', 
    title: 'In Progress', 
    color: 'bg-blue-100 border-blue-200', 
    headerColor: 'bg-blue-500',
    icon: <PlayCircle className="w-4 h-4" />
  },
  { 
    id: 'Completed', 
    title: 'Completed', 
    color: 'bg-green-100 border-green-200', 
    headerColor: 'bg-green-500',
    icon: <CheckCircle className="w-4 h-4" />
  },
  { 
    id: 'On Hold', 
    title: 'On Hold', 
    color: 'bg-yellow-100 border-yellow-200', 
    headerColor: 'bg-yellow-500',
    icon: <PauseCircle className="w-4 h-4" />
  }
];

export const TaskKanban: React.FC<TaskKanbanProps> = ({
  projects,
  tasks,
  loading = false,
  onTaskUpdate,
  onTaskCreate,
  onTaskDelete
}) => {
  const [selectedProject, setSelectedProject] = useState<string>(projects[0]?.id || '');
  const [searchTerm, setSearchTerm] = useState('');
  const [filterPriority, setFilterPriority] = useState<string>('all');
  const [filterAssignee, setFilterAssignee] = useState<string>('all');
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [isCreatingTask, setIsCreatingTask] = useState(false);
  const [draggedTask, setDraggedTask] = useState<Task | null>(null);

  // Form state for new task creation
  const [newTask, setNewTask] = useState({
    name: '',
    description: '',
    priority: 'Medium' as Task['priority'],
    assignee: '',
    start_date: '',
    end_date: '',
    estimated_hours: 0,
    tags: ''
  });

  // Filter tasks by selected project and search/filter criteria
  const filteredTasks = useMemo(() => {
    return tasks.filter(task => {
      const matchesProject = task.project_id === selectedProject;
      const matchesSearch = task.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           task.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           task.assignee.toLowerCase().includes(searchTerm.toLowerCase());
      const matchesPriority = filterPriority === 'all' || task.priority === filterPriority;
      const matchesAssignee = filterAssignee === 'all' || task.assignee === filterAssignee;
      
      return matchesProject && matchesSearch && matchesPriority && matchesAssignee;
    });
  }, [tasks, selectedProject, searchTerm, filterPriority, filterAssignee]);

  // Get unique assignees for filter
  const uniqueAssignees = useMemo(() => {
    const assignees = tasks
      .filter(task => task.project_id === selectedProject)
      .map(task => task.assignee)
      .filter(assignee => assignee.trim() !== '');
    return [...new Set(assignees)];
  }, [tasks, selectedProject]);

  // Group tasks by status
  const tasksByStatus = useMemo(() => {
    const grouped: Record<string, Task[]> = {};
    KANBAN_COLUMNS.forEach(column => {
      grouped[column.id] = filteredTasks.filter(task => task.status === column.id);
    });
    return grouped;
  }, [filteredTasks]);

  // Get priority color
  const getPriorityColor = (priority: Task['priority']) => {
    switch (priority) {
      case 'Critical': return 'bg-red-100 text-red-800 border-red-200';
      case 'High': return 'bg-orange-100 text-orange-800 border-orange-200';
      case 'Medium': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'Low': return 'bg-green-100 text-green-800 border-green-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  // Get priority icon
  const getPriorityIcon = (priority: Task['priority']) => {
    switch (priority) {
      case 'Critical': return <AlertTriangle className="w-3 h-3" />;
      case 'High': return <Flag className="w-3 h-3" />;
      case 'Medium': return <Target className="w-3 h-3" />;
      case 'Low': return <CheckCircle className="w-3 h-3" />;
      default: return <Target className="w-3 h-3" />;
    }
  };

  // Handle drag start
  const handleDragStart = (e: React.DragEvent, task: Task) => {
    setDraggedTask(task);
    e.dataTransfer.effectAllowed = 'move';
  };

  // Handle drag over
  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    e.dataTransfer.dropEffect = 'move';
  };

  // Handle drop
  const handleDrop = async (e: React.DragEvent, newStatus: Task['status']) => {
    e.preventDefault();
    
    if (draggedTask && draggedTask.status !== newStatus) {
      try {
        await onTaskUpdate(draggedTask.id, { status: newStatus });
      } catch (error) {
        console.error('Failed to update task status:', error);
      }
    }
    
    setDraggedTask(null);
  };

  // Handle task creation
  const handleCreateTask = async () => {
    if (!newTask.name.trim() || !selectedProject) {
      console.error('Task name and project are required');
      return;
    }

    setIsCreatingTask(true);
    try {
      console.log('Creating task with data:', {
        ...newTask,
        project_id: selectedProject
      });

      await onTaskCreate({
        name: newTask.name.trim(),
        description: newTask.description.trim(),
        status: 'Not Started',
        priority: newTask.priority,
        start_date: newTask.start_date || new Date().toISOString().split('T')[0],
        end_date: newTask.end_date || new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
        progress: 0,
        assignee: newTask.assignee.trim(),
        project_id: selectedProject,
        estimated_hours: newTask.estimated_hours || 8,
        actual_hours: 0,
        dependencies: [],
        tags: newTask.tags ? newTask.tags.split(',').map(tag => tag.trim()).filter(tag => tag) : [],
        parent_task_id: null
      });

      // Reset form
      setNewTask({
        name: '',
        description: '',
        priority: 'Medium',
        assignee: '',
        start_date: '',
        end_date: '',
        estimated_hours: 0,
        tags: ''
      });

      setIsCreateDialogOpen(false);
      console.log('Task created successfully');
    } catch (error) {
      console.error('Failed to create task:', error);
      // You could add a toast notification here
    } finally {
      setIsCreatingTask(false);
    }
  };

  // Format date
  const formatDate = (dateString: string) => {
    if (!dateString) return 'No date';
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric'
    });
  };

  // Calculate days remaining
  const getDaysRemaining = (endDate: string) => {
    if (!endDate) return null;
    const today = new Date();
    const end = new Date(endDate);
    const diffTime = end.getTime() - today.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays;
  };

  const selectedProjectData = projects.find(p => p.id === selectedProject);

  // Show loading state
  if (loading) {
    return (
      <div className="space-y-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Target className="h-6 w-6 text-blue-600" />
              🎯 Task Kanban Board
            </CardTitle>
            <p className="text-sm text-gray-600">Loading tasks and projects...</p>
          </CardHeader>
        </Card>
        <div className="flex justify-center items-center h-64">
          <div className="text-center">
            <div className="w-8 h-8 border-4 border-blue-600 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
            <p className="text-gray-600">Loading your tasks...</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header with Project Selection and Filters */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <Target className="h-6 w-6 text-blue-600" />
                🎯 Task Kanban Board
              </CardTitle>
              <p className="text-sm text-gray-600 mt-1">
                Drag and drop tasks between columns to update their status
              </p>
            </div>
            <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
              <DialogTrigger asChild>
                <Button className="bg-blue-600 hover:bg-blue-700">
                  <Plus className="w-4 h-4 mr-2" />
                  Add Task
                </Button>
              </DialogTrigger>
              <DialogContent className="sm:max-w-[500px]">
                <DialogHeader>
                  <DialogTitle>Create New Task</DialogTitle>
                  <DialogDescription>
                    Add a new task to {selectedProjectData?.name || 'the selected project'}
                  </DialogDescription>
                </DialogHeader>
                <div className="grid gap-4 py-4">
                  <div className="space-y-2">
                    <Label htmlFor="task-name">Task Name</Label>
                    <Input
                      id="task-name"
                      value={newTask.name}
                      onChange={(e) => setNewTask({...newTask, name: e.target.value})}
                      placeholder="Enter task name"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="task-description">Description</Label>
                    <Textarea
                      id="task-description"
                      value={newTask.description}
                      onChange={(e) => setNewTask({...newTask, description: e.target.value})}
                      placeholder="Enter task description"
                      rows={3}
                    />
                  </div>
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="task-priority">Priority</Label>
                      <Select value={newTask.priority} onValueChange={(value: Task['priority']) => setNewTask({...newTask, priority: value})}>
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="Low">Low</SelectItem>
                          <SelectItem value="Medium">Medium</SelectItem>
                          <SelectItem value="High">High</SelectItem>
                          <SelectItem value="Critical">Critical</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="task-assignee">Assignee</Label>
                      <Input
                        id="task-assignee"
                        value={newTask.assignee}
                        onChange={(e) => setNewTask({...newTask, assignee: e.target.value})}
                        placeholder="Assign to..."
                      />
                    </div>
                  </div>
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="task-start">Start Date</Label>
                      <Input
                        id="task-start"
                        type="date"
                        value={newTask.start_date || new Date().toISOString().split('T')[0]}
                        onChange={(e) => setNewTask({...newTask, start_date: e.target.value})}
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="task-end">End Date</Label>
                      <Input
                        id="task-end"
                        type="date"
                        value={newTask.end_date || new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0]}
                        onChange={(e) => setNewTask({...newTask, end_date: e.target.value})}
                      />
                    </div>
                  </div>
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="task-hours">Estimated Hours</Label>
                      <Input
                        id="task-hours"
                        type="number"
                        min="0"
                        step="0.5"
                        value={newTask.estimated_hours || 8}
                        onChange={(e) => setNewTask({...newTask, estimated_hours: parseFloat(e.target.value) || 0})}
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="task-tags">Tags (comma-separated)</Label>
                      <Input
                        id="task-tags"
                        value={newTask.tags}
                        onChange={(e) => setNewTask({...newTask, tags: e.target.value})}
                        placeholder="e.g., frontend, urgent"
                      />
                    </div>
                  </div>
                </div>
                <DialogFooter>
                  <Button variant="outline" onClick={() => setIsCreateDialogOpen(false)}>
                    Cancel
                  </Button>
                  <Button
                    onClick={handleCreateTask}
                    disabled={!newTask.name.trim() || isCreatingTask}
                  >
                    {isCreatingTask ? (
                      <>
                        <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2" />
                        Creating...
                      </>
                    ) : (
                      'Create Task'
                    )}
                  </Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>
          </div>
        </CardHeader>
      </Card>

      {/* Project Selection and Filters */}
      <Card>
        <CardContent className="p-4">
          <div className="flex flex-wrap items-center gap-4">
            <div className="flex items-center gap-2">
              <Label className="text-sm font-medium">Project:</Label>
              <Select value={selectedProject} onValueChange={setSelectedProject}>
                <SelectTrigger className="w-64">
                  <SelectValue placeholder="Select a project" />
                </SelectTrigger>
                <SelectContent>
                  {projects.map(project => (
                    <SelectItem key={project.id} value={project.id}>
                      {project.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="flex items-center gap-2">
              <Search className="w-4 h-4 text-gray-400" />
              <Input
                placeholder="Search tasks..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-48"
              />
            </div>

            <div className="flex items-center gap-2">
              <Filter className="w-4 h-4 text-gray-400" />
              <Select value={filterPriority} onValueChange={setFilterPriority}>
                <SelectTrigger className="w-32">
                  <SelectValue placeholder="Priority" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Priority</SelectItem>
                  <SelectItem value="Critical">Critical</SelectItem>
                  <SelectItem value="High">High</SelectItem>
                  <SelectItem value="Medium">Medium</SelectItem>
                  <SelectItem value="Low">Low</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {uniqueAssignees.length > 0 && (
              <div className="flex items-center gap-2">
                <User className="w-4 h-4 text-gray-400" />
                <Select value={filterAssignee} onValueChange={setFilterAssignee}>
                  <SelectTrigger className="w-40">
                    <SelectValue placeholder="Assignee" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Assignees</SelectItem>
                    {uniqueAssignees.map(assignee => (
                      <SelectItem key={assignee} value={assignee}>
                        {assignee}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Kanban Board */}
      <div className="flex space-x-6 overflow-x-auto pb-4">
        {KANBAN_COLUMNS.map((column) => {
          const columnTasks = tasksByStatus[column.id] || [];

          return (
            <div
              key={column.id}
              className="flex-shrink-0 w-80"
              onDragOver={handleDragOver}
              onDrop={(e) => handleDrop(e, column.id as Task['status'])}
            >
              {/* Column Header */}
              <Card className={`${column.color} border-2 mb-4`}>
                <CardHeader className="pb-3">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <div className={`p-1 rounded ${column.headerColor} text-white`}>
                        {column.icon}
                      </div>
                      <CardTitle className="text-sm font-medium">
                        {column.title}
                      </CardTitle>
                      <Badge variant="secondary" className="text-xs">
                        {columnTasks.length}
                      </Badge>
                    </div>
                  </div>
                </CardHeader>
              </Card>

              {/* Task Cards */}
              <div className="space-y-3 max-h-[calc(100vh-300px)] overflow-y-auto">
                {columnTasks.map((task) => {
                  const daysRemaining = getDaysRemaining(task.end_date);
                  const isOverdue = daysRemaining !== null && daysRemaining < 0 && task.status !== 'Completed';

                  return (
                    <Card
                      key={task.id}
                      className={`cursor-move hover:shadow-lg transition-all duration-200 ${
                        draggedTask?.id === task.id ? 'opacity-50 transform rotate-1' : ''
                      } ${isOverdue ? 'border-red-300 bg-red-50' : ''}`}
                      draggable
                      onDragStart={(e) => handleDragStart(e, task)}
                      onDragEnd={() => setDraggedTask(null)}
                    >
                      <CardContent className="p-4">
                        {/* Task Header */}
                        <div className="flex items-start justify-between mb-3">
                          <div className="flex-1">
                            <h3 className="font-medium text-gray-900 text-sm leading-tight mb-1">
                              {task.name}
                            </h3>
                            {task.description && (
                              <p className="text-xs text-gray-600 mb-2 line-clamp-2">
                                {task.description}
                              </p>
                            )}
                          </div>
                          <div className="flex items-center gap-1">
                            <GripVertical className="w-4 h-4 text-gray-400" />
                            <Button variant="ghost" size="sm" className="h-6 w-6 p-0">
                              <MoreHorizontal className="w-4 h-4" />
                            </Button>
                          </div>
                        </div>

                        {/* Priority and Tags */}
                        <div className="flex items-center gap-2 mb-3">
                          <Badge className={`text-xs ${getPriorityColor(task.priority)}`}>
                            <div className="flex items-center gap-1">
                              {getPriorityIcon(task.priority)}
                              {task.priority}
                            </div>
                          </Badge>
                          {task.tags && task.tags.length > 0 && (
                            <Badge variant="outline" className="text-xs">
                              {task.tags[0]}
                              {task.tags.length > 1 && ` +${task.tags.length - 1}`}
                            </Badge>
                          )}
                        </div>

                        {/* Progress */}
                        {task.progress > 0 && (
                          <div className="mb-3">
                            <div className="flex items-center justify-between text-xs mb-1">
                              <span className="text-gray-600">Progress</span>
                              <span className="font-medium">{task.progress}%</span>
                            </div>
                            <Progress value={task.progress} className="h-1.5" />
                          </div>
                        )}

                        {/* Assignee */}
                        {task.assignee && (
                          <div className="flex items-center space-x-1 text-xs text-gray-500 mb-3">
                            <User className="w-3 h-3" />
                            <span>{task.assignee}</span>
                          </div>
                        )}

                        {/* Timeline and Hours */}
                        <div className="space-y-2">
                          {(task.start_date || task.end_date) && (
                            <div className="flex items-center justify-between text-xs">
                              <div className="flex items-center space-x-1 text-gray-500">
                                <Calendar className="w-3 h-3" />
                                <span>
                                  {task.start_date && task.end_date
                                    ? `${formatDate(task.start_date)} - ${formatDate(task.end_date)}`
                                    : task.start_date
                                    ? `From ${formatDate(task.start_date)}`
                                    : `Due ${formatDate(task.end_date)}`
                                  }
                                </span>
                              </div>
                              {task.status !== 'Completed' && daysRemaining !== null && (
                                <div className={`flex items-center space-x-1 ${
                                  isOverdue ? 'text-red-600' :
                                  daysRemaining <= 3 ? 'text-orange-600' : 'text-gray-500'
                                }`}>
                                  <Clock className="w-3 h-3" />
                                  <span>
                                    {isOverdue
                                      ? `${Math.abs(daysRemaining)}d overdue`
                                      : `${daysRemaining}d left`
                                    }
                                  </span>
                                </div>
                              )}
                            </div>
                          )}

                          {task.estimated_hours > 0 && (
                            <div className="flex items-center justify-between text-xs text-gray-500">
                              <span>Hours: {task.actual_hours || 0} / {task.estimated_hours}</span>
                              <span className={
                                task.actual_hours > task.estimated_hours ? 'text-red-600' : ''
                              }>
                                {task.estimated_hours > 0
                                  ? `${Math.round(((task.actual_hours || 0) / task.estimated_hours) * 100)}%`
                                  : '0%'
                                }
                              </span>
                            </div>
                          )}
                        </div>

                        {/* Risk Indicators */}
                        {(isOverdue || (task.actual_hours > task.estimated_hours)) && (
                          <div className="mt-3 pt-3 border-t border-gray-200">
                            <div className="flex items-center space-x-2">
                              <AlertTriangle className="w-4 h-4 text-red-500" />
                              <div className="text-xs text-red-600">
                                {isOverdue && <div>Behind schedule</div>}
                                {task.actual_hours > task.estimated_hours && <div>Over budget</div>}
                              </div>
                            </div>
                          </div>
                        )}
                      </CardContent>
                    </Card>
                  );
                })}

                {/* Empty State */}
                {columnTasks.length === 0 && (
                  <div className="text-center py-8 text-gray-400">
                    <div className={`w-12 h-12 mx-auto mb-3 rounded-full ${column.color} flex items-center justify-center`}>
                      {column.icon}
                    </div>
                    <p className="text-sm">No tasks in {column.title.toLowerCase()}</p>
                    {column.id === 'Not Started' && (
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => setIsCreateDialogOpen(true)}
                        className="mt-2 text-xs"
                      >
                        <Plus className="w-3 h-3 mr-1" />
                        Add Task
                      </Button>
                    )}
                  </div>
                )}
              </div>
            </div>
          );
        })}
      </div>

      {/* Summary Stats */}
      <Card>
        <CardContent className="p-4">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
            <div>
              <div className="text-2xl font-bold text-gray-600">{tasksByStatus['Not Started']?.length || 0}</div>
              <div className="text-sm text-gray-500">Backlog</div>
            </div>
            <div>
              <div className="text-2xl font-bold text-blue-600">{tasksByStatus['In Progress']?.length || 0}</div>
              <div className="text-sm text-gray-500">In Progress</div>
            </div>
            <div>
              <div className="text-2xl font-bold text-green-600">{tasksByStatus['Completed']?.length || 0}</div>
              <div className="text-sm text-gray-500">Completed</div>
            </div>
            <div>
              <div className="text-2xl font-bold text-yellow-600">{tasksByStatus['On Hold']?.length || 0}</div>
              <div className="text-sm text-gray-500">On Hold</div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
