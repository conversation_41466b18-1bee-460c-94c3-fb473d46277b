import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/components/ui/use-toast';
import { Users, Link, Unlink, Search, Plus, Trash2 } from 'lucide-react';
import { ProjectService } from '@/lib/projects';
import { useClients } from '@/hooks/useClients';
import { UserService } from '@/lib/userService';
import { supabase } from '@/lib/supabase';

interface ClientUserRelationship {
  id: string;
  client_id: string;
  user_id: string;
  role: string;
  access_level: string;
  is_primary: boolean;
  client: {
    id: string;
    name: string;
    company_name?: string;
  };
  user_profile?: {
    full_name: string;
    email: string;
    role_name: string;
  };
}

interface ClientUserManagementProps {
  className?: string;
}

export const ClientUserManagement: React.FC<ClientUserManagementProps> = ({ className = '' }) => {
  const { toast } = useToast();
  const { clients } = useClients();
  
  const [relationships, setRelationships] = useState<ClientUserRelationship[]>([]);
  const [users, setUsers] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  
  // Form state for adding new relationships
  const [selectedClient, setSelectedClient] = useState('');
  const [selectedUser, setSelectedUser] = useState('');
  const [selectedRole, setSelectedRole] = useState('client');
  const [selectedAccessLevel, setSelectedAccessLevel] = useState('read');
  const [isPrimary, setIsPrimary] = useState(false);

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    setLoading(true);
    try {
      // Load users (clients only for this management)
      const allUsers = await UserService.getAllUsers();
      const clientUsers = allUsers.filter(user => user.role_name === 'client');
      setUsers(clientUsers);
      
      // Load existing relationships
      await loadRelationships();
    } catch (error) {
      console.error('Error loading data:', error);
      toast({
        title: "Error",
        description: "Failed to load client-user data",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const loadRelationships = async () => {
    try {
      // Load actual client-user relationships from database
      const { data, error } = await supabase
        .from('client_users')
        .select(`
          *,
          client:clients(id, name, company_name),
          user:user_profiles(id, first_name, last_name, email)
        `);

      if (error) {
        console.error('Error loading relationships:', error);
        return;
      }

      // Transform the data to match our interface
      const transformedRelationships = (data || []).map(rel => ({
        id: rel.id,
        client_id: rel.client_id,
        user_id: rel.user_id,
        role: rel.role,
        access_level: rel.access_level,
        is_primary: rel.is_primary,
        client_name: rel.client?.name || 'Unknown Client',
        user_name: rel.user ? `${rel.user.first_name} ${rel.user.last_name}` : 'Unknown User',
        user_email: rel.user?.email || '',
        created_at: rel.created_at
      }));

      setRelationships(transformedRelationships);
    } catch (error) {
      console.error('Error loading relationships:', error);
    }
  };

  const handleLinkUser = async () => {
    if (!selectedClient || !selectedUser) {
      toast({
        title: "Error",
        description: "Please select both a client and a user",
        variant: "destructive",
      });
      return;
    }

    try {
      setLoading(true);
      
      await ProjectService.linkUserToClient(
        selectedUser,
        selectedClient,
        selectedRole,
        selectedAccessLevel,
        isPrimary
      );

      toast({
        title: "Success",
        description: "User linked to client successfully",
      });

      // Reset form
      setSelectedClient('');
      setSelectedUser('');
      setSelectedRole('client');
      setSelectedAccessLevel('read');
      setIsPrimary(false);
      
      // Reload data
      await loadRelationships();
    } catch (error) {
      console.error('Error linking user to client:', error);
      toast({
        title: "Error",
        description: "Failed to link user to client",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const handleUnlinkUser = async (relationshipId: string) => {
    try {
      setLoading(true);

      // Delete the relationship from database
      const { error } = await supabase
        .from('client_users')
        .delete()
        .eq('id', relationshipId);

      if (error) {
        throw new Error(`Failed to unlink user: ${error.message}`);
      }

      toast({
        title: "Success",
        description: "User unlinked from client successfully",
      });

      // Reload data
      await loadRelationships();
    } catch (error) {
      console.error('Error unlinking user from client:', error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to unlink user from client",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const filteredClients = clients.filter(client =>
    client.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    client.company_name?.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const filteredUsers = users.filter(user =>
    user.full_name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    user.email.toLowerCase().includes(searchTerm.toLowerCase())
  );

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Client-User Management</h2>
          <p className="text-gray-600">Link client users to their respective clients for project access control</p>
        </div>
      </div>

      {/* Link New User Form */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Link className="w-5 h-5" />
            Link User to Client
          </CardTitle>
          <CardDescription>
            Create a relationship between a client user and their client organization
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <div>
              <Label htmlFor="client-select">Client</Label>
              <Select value={selectedClient} onValueChange={setSelectedClient}>
                <SelectTrigger>
                  <SelectValue placeholder="Select a client" />
                </SelectTrigger>
                <SelectContent>
                  {clients.map((client) => (
                    <SelectItem key={client.id} value={client.id}>
                      {client.name} {client.company_name && `(${client.company_name})`}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label htmlFor="user-select">User</Label>
              <Select value={selectedUser} onValueChange={setSelectedUser}>
                <SelectTrigger>
                  <SelectValue placeholder="Select a user" />
                </SelectTrigger>
                <SelectContent>
                  {users.map((user) => (
                    <SelectItem key={user.user_id} value={user.user_id}>
                      {user.full_name || user.email} ({user.email})
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label htmlFor="role-select">Role</Label>
              <Select value={selectedRole} onValueChange={setSelectedRole}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="client">Client</SelectItem>
                  <SelectItem value="contact">Contact</SelectItem>
                  <SelectItem value="admin">Admin</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label htmlFor="access-select">Access Level</Label>
              <Select value={selectedAccessLevel} onValueChange={setSelectedAccessLevel}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="read">Read Only</SelectItem>
                  <SelectItem value="write">Read & Write</SelectItem>
                  <SelectItem value="admin">Full Admin</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="flex items-center space-x-2">
              <input
                type="checkbox"
                id="is-primary"
                checked={isPrimary}
                onChange={(e) => setIsPrimary(e.target.checked)}
                className="rounded"
              />
              <Label htmlFor="is-primary">Primary Contact</Label>
            </div>

            <div className="flex items-end">
              <Button 
                onClick={handleLinkUser} 
                disabled={loading || !selectedClient || !selectedUser}
                className="w-full"
              >
                <Plus className="w-4 h-4 mr-2" />
                Link User
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Search */}
      <div className="flex items-center space-x-2">
        <Search className="w-4 h-4 text-gray-400" />
        <Input
          placeholder="Search clients or users..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          className="max-w-sm"
        />
      </div>

      {/* Current Relationships */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Users className="w-5 h-5" />
            Current Client-User Relationships
          </CardTitle>
          <CardDescription>
            Existing relationships between users and clients
          </CardDescription>
        </CardHeader>
        <CardContent>
          {relationships.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              <Users className="w-12 h-12 mx-auto mb-4 opacity-50" />
              <p>No client-user relationships found</p>
              <p className="text-sm">Link users to clients using the form above</p>
            </div>
          ) : (
            <div className="space-y-4">
              {relationships.map((relationship) => (
                <div key={relationship.id} className="flex items-center justify-between p-4 border rounded-lg">
                  <div className="flex items-center space-x-4">
                    <div>
                      <p className="font-medium">{relationship.client_name}</p>
                    </div>
                    <div className="text-gray-400">→</div>
                    <div>
                      <p className="font-medium">{relationship.user_name}</p>
                      <p className="text-sm text-gray-500">{relationship.user_email}</p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Badge variant="outline">{relationship.role}</Badge>
                    <Badge variant="secondary">{relationship.access_level}</Badge>
                    {relationship.is_primary && <Badge>Primary</Badge>}
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleUnlinkUser(relationship.id)}
                      disabled={loading}
                    >
                      <Trash2 className="w-4 h-4" />
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Instructions */}
      <Card>
        <CardHeader>
          <CardTitle>Setup Instructions</CardTitle>
        </CardHeader>
        <CardContent className="space-y-2">
          <p className="text-sm text-gray-600">
            1. <strong>Run the database script:</strong> Execute <code>database/client_project_access_control.sql</code> in Supabase
          </p>
          <p className="text-sm text-gray-600">
            2. <strong>Link client users:</strong> Use the form above to link each client user to their respective client
          </p>
          <p className="text-sm text-gray-600">
            3. <strong>Test access:</strong> Login as a client user to verify they only see their projects
          </p>
        </CardContent>
      </Card>
    </div>
  );
};

export default ClientUserManagement;
