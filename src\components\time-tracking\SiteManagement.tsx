import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { useToast } from '@/components/ui/use-toast';
import { TimeTrackingService, Site } from '@/lib/timeTrackingService';
import { supabase } from '@/lib/supabase';
import {
  MapPin,
  Plus,
  Edit,
  Building,
  Users,
  Clock,
  Settings,
  Loader2,
  CheckCircle,
  XCircle
} from 'lucide-react';

interface SiteManagementProps {
  sites: Site[];
  onSiteUpdate: () => void;
}

interface Project {
  id: string;
  name: string;
}

const SiteManagement: React.FC<SiteManagementProps> = ({ sites, onSiteUpdate }) => {
  const { toast } = useToast();
  const [loading, setLoading] = useState(false);
  const [projects, setProjects] = useState<Project[]>([]);
  const [showCreateDialog, setShowCreateDialog] = useState(false);
  const [editingSite, setEditingSite] = useState<Site | null>(null);
  const [formData, setFormData] = useState({
    name: '',
    address: '',
    project_id: '',
    site_code: '',
    description: '',
    is_active: true
  });

  useEffect(() => {
    loadProjects();
  }, []);

  const loadProjects = async () => {
    try {
      const { data, error } = await supabase
        .from('projects')
        .select('id, name')
        .order('name');

      if (error) throw error;
      setProjects(data || []);
    } catch (error) {
      console.error('Error loading projects:', error);
    }
  };

  const resetForm = () => {
    setFormData({
      name: '',
      address: '',
      project_id: '',
      site_code: '',
      description: '',
      is_active: true
    });
    setEditingSite(null);
  };

  const handleEdit = (site: Site) => {
    setFormData({
      name: site.name,
      address: site.address || '',
      project_id: site.project_id || '',
      site_code: site.site_code,
      description: site.description || '',
      is_active: site.is_active
    });
    setEditingSite(site);
    setShowCreateDialog(true);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.name || !formData.site_code) {
      toast({
        title: "Validation Error",
        description: "Site name and code are required",
        variant: "destructive",
      });
      return;
    }

    setLoading(true);
    try {
      if (editingSite) {
        // Update existing site
        const { error } = await supabase
          .from('sites')
          .update({
            name: formData.name,
            address: formData.address || null,
            project_id: formData.project_id || null,
            site_code: formData.site_code,
            description: formData.description || null,
            is_active: formData.is_active,
            updated_at: new Date().toISOString()
          })
          .eq('id', editingSite.id);

        if (error) throw error;

        toast({
          title: "Site Updated",
          description: `${formData.name} has been updated successfully`,
        });
      } else {
        // Create new site
        await TimeTrackingService.createSite({
          name: formData.name,
          address: formData.address || undefined,
          project_id: formData.project_id || undefined,
          site_code: formData.site_code,
          description: formData.description || undefined,
          is_active: formData.is_active
        });

        toast({
          title: "Site Created",
          description: `${formData.name} has been created successfully`,
        });
      }

      setShowCreateDialog(false);
      resetForm();
      onSiteUpdate();
    } catch (error: any) {
      console.error('Error saving site:', error);
      toast({
        title: "Error",
        description: error.message || "Failed to save site",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const toggleSiteStatus = async (site: Site) => {
    try {
      const { error } = await supabase
        .from('sites')
        .update({ 
          is_active: !site.is_active,
          updated_at: new Date().toISOString()
        })
        .eq('id', site.id);

      if (error) throw error;

      toast({
        title: "Site Updated",
        description: `${site.name} has been ${!site.is_active ? 'activated' : 'deactivated'}`,
      });

      onSiteUpdate();
    } catch (error: any) {
      console.error('Error updating site status:', error);
      toast({
        title: "Error",
        description: "Failed to update site status",
        variant: "destructive",
      });
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <Card>
        <CardHeader>
          <div className="flex justify-between items-center">
            <div>
              <CardTitle className="flex items-center gap-2">
                <MapPin className="h-5 w-5" />
                Site Management
              </CardTitle>
              <CardDescription>
                Manage work sites and locations for time tracking
              </CardDescription>
            </div>
            <Dialog open={showCreateDialog} onOpenChange={setShowCreateDialog}>
              <DialogTrigger asChild>
                <Button onClick={resetForm}>
                  <Plus className="h-4 w-4 mr-2" />
                  Add Site
                </Button>
              </DialogTrigger>
              <DialogContent className="max-w-md">
                <DialogHeader>
                  <DialogTitle>
                    {editingSite ? 'Edit Site' : 'Create New Site'}
                  </DialogTitle>
                  <DialogDescription>
                    {editingSite ? 'Update site information' : 'Add a new work site for time tracking'}
                  </DialogDescription>
                </DialogHeader>
                <form onSubmit={handleSubmit} className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="site-name">Site Name *</Label>
                    <Input
                      id="site-name"
                      value={formData.name}
                      onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                      placeholder="Enter site name"
                      required
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="site-code">Site Code *</Label>
                    <Input
                      id="site-code"
                      value={formData.site_code}
                      onChange={(e) => setFormData(prev => ({ ...prev, site_code: e.target.value.toUpperCase() }))}
                      placeholder="e.g., MAIN-001"
                      required
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="project">Project</Label>
                    <Select 
                      value={formData.project_id} 
                      onValueChange={(value) => setFormData(prev => ({ ...prev, project_id: value }))}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select project (optional)" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="">No Project</SelectItem>
                        {projects.map((project) => (
                          <SelectItem key={project.id} value={project.id}>
                            {project.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="address">Address</Label>
                    <Textarea
                      id="address"
                      value={formData.address}
                      onChange={(e) => setFormData(prev => ({ ...prev, address: e.target.value }))}
                      placeholder="Enter site address"
                      rows={2}
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="description">Description</Label>
                    <Textarea
                      id="description"
                      value={formData.description}
                      onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                      placeholder="Enter site description"
                      rows={2}
                    />
                  </div>

                  <div className="flex justify-end gap-2">
                    <Button 
                      type="button" 
                      variant="outline" 
                      onClick={() => setShowCreateDialog(false)}
                    >
                      Cancel
                    </Button>
                    <Button type="submit" disabled={loading}>
                      {loading ? (
                        <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                      ) : editingSite ? (
                        <Edit className="h-4 w-4 mr-2" />
                      ) : (
                        <Plus className="h-4 w-4 mr-2" />
                      )}
                      {editingSite ? 'Update' : 'Create'} Site
                    </Button>
                  </div>
                </form>
              </DialogContent>
            </Dialog>
          </div>
        </CardHeader>
      </Card>

      {/* Sites List */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {sites.map((site) => (
          <Card key={site.id} className={!site.is_active ? 'opacity-60' : ''}>
            <CardHeader className="pb-3">
              <div className="flex justify-between items-start">
                <div>
                  <CardTitle className="text-lg">{site.name}</CardTitle>
                  <div className="flex items-center gap-2 mt-1">
                    <Badge variant="outline" className="text-xs">
                      {site.site_code}
                    </Badge>
                    <Badge 
                      className={site.is_active 
                        ? 'bg-green-100 text-green-800' 
                        : 'bg-red-100 text-red-800'
                      }
                    >
                      {site.is_active ? (
                        <CheckCircle className="w-3 h-3 mr-1" />
                      ) : (
                        <XCircle className="w-3 h-3 mr-1" />
                      )}
                      {site.is_active ? 'Active' : 'Inactive'}
                    </Badge>
                  </div>
                </div>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => handleEdit(site)}
                >
                  <Edit className="h-4 w-4" />
                </Button>
              </div>
            </CardHeader>
            <CardContent className="space-y-3">
              {site.project && (
                <div className="flex items-center gap-2 text-sm">
                  <Building className="h-4 w-4 text-blue-500" />
                  <span className="text-gray-600">Project:</span>
                  <span className="font-medium">{site.project.name}</span>
                </div>
              )}

              {site.address && (
                <div className="flex items-start gap-2 text-sm">
                  <MapPin className="h-4 w-4 text-gray-500 mt-0.5" />
                  <span className="text-gray-600">{site.address}</span>
                </div>
              )}

              {site.description && (
                <div className="text-sm text-gray-600">
                  {site.description}
                </div>
              )}

              <div className="flex justify-between items-center pt-2 border-t">
                <div className="text-xs text-gray-500">
                  Created {new Date(site.created_at).toLocaleDateString()}
                </div>
                <Button
                  variant={site.is_active ? "destructive" : "default"}
                  size="sm"
                  onClick={() => toggleSiteStatus(site)}
                >
                  {site.is_active ? 'Deactivate' : 'Activate'}
                </Button>
              </div>
            </CardContent>
          </Card>
        ))}

        {sites.length === 0 && (
          <Card className="col-span-full">
            <CardContent className="p-12 text-center">
              <MapPin className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-gray-600 mb-2">No Sites Found</h3>
              <p className="text-gray-500 mb-4">
                Create your first work site to start tracking time and attendance.
              </p>
              <Button onClick={() => setShowCreateDialog(true)}>
                <Plus className="h-4 w-4 mr-2" />
                Add First Site
              </Button>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
};

export default SiteManagement;
