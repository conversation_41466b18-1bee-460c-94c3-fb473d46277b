import React, { useState } from 'react';
import {
  Search,
  ChevronDown,
  ChevronUp,
  HelpCircle,
  MessageCircle,
  Mail,
  Phone,
  ExternalLink,
  Star,
  ThumbsUp,
  ThumbsDown,
  Building2,
  Users,
  CreditCard,
  Settings,
  Shield,
  BarChart3
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import { useToast } from '@/components/ui/use-toast';

interface FAQItem {
  id: string;
  question: string;
  answer: string;
  category: string;
  popular?: boolean;
  helpful: number;
  notHelpful: number;
}

interface FAQCategory {
  id: string;
  name: string;
  icon: React.ReactNode;
  count: number;
}

const FAQ: React.FC = () => {
  const { toast } = useToast();
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [openItems, setOpenItems] = useState<string[]>([]);

  const faqCategories: FAQCategory[] = [
    { id: 'all', name: 'All Questions', icon: <HelpCircle className="w-4 h-4" />, count: 45 },
    { id: 'getting-started', name: 'Getting Started', icon: <Building2 className="w-4 h-4" />, count: 12 },
    { id: 'project-management', name: 'Project Management', icon: <Building2 className="w-4 h-4" />, count: 8 },
    { id: 'financial', name: 'Financial Management', icon: <CreditCard className="w-4 h-4" />, count: 7 },
    { id: 'user-management', name: 'User Management', icon: <Users className="w-4 h-4" />, count: 6 },
    { id: 'reports', name: 'Reports & Analytics', icon: <BarChart3 className="w-4 h-4" />, count: 5 },
    { id: 'security', name: 'Security', icon: <Shield className="w-4 h-4" />, count: 4 },
    { id: 'settings', name: 'Settings', icon: <Settings className="w-4 h-4" />, count: 3 }
  ];

  const faqItems: FAQItem[] = [
    {
      id: '1',
      question: 'How do I create my first project in Martcosy?',
      answer: 'To create your first project, navigate to the Projects section from the main dashboard, click the "New Project" button, fill in the project details including name, description, start date, and budget, then click "Create Project". You can then add team members, tasks, and milestones to your project.',
      category: 'getting-started',
      popular: true,
      helpful: 156,
      notHelpful: 8
    },
    {
      id: '2',
      question: 'How do I invite team members to my project?',
      answer: 'You can invite team members by going to the User Management section, clicking "Add User", entering their email address, selecting their role (Admin, QS, Accountant, Management, or Client), and clicking "Send Invitation". They will receive an email with instructions to join your workspace.',
      category: 'user-management',
      popular: true,
      helpful: 142,
      notHelpful: 12
    },
    {
      id: '3',
      question: 'How do I create and send invoices?',
      answer: 'To create an invoice, go to the Financial Management section, click "Create Invoice", select the client and project, add line items with descriptions and amounts, set the due date, and click "Generate Invoice". You can then send it directly to the client via email or download it as a PDF.',
      category: 'financial',
      popular: true,
      helpful: 134,
      notHelpful: 6
    },
    {
      id: '4',
      question: 'What are the different user roles and their permissions?',
      answer: 'Martcosy has 5 user roles: Admin (full access), QS (quantity surveying and project management), Accountant (financial management), Management (reports and oversight), and Client (view assigned projects only). Each role has specific permissions tailored to their responsibilities.',
      category: 'user-management',
      helpful: 98,
      notHelpful: 4
    },
    {
      id: '5',
      question: 'How do I set up Gantt charts for project scheduling?',
      answer: 'In your project, go to the Timeline tab, click "Create Gantt Chart", add tasks with start and end dates, set dependencies between tasks, and assign team members. You can drag and drop to adjust timelines and the chart will automatically update dependencies.',
      category: 'project-management',
      helpful: 87,
      notHelpful: 9
    },
    {
      id: '6',
      question: 'How do I generate financial reports?',
      answer: 'Navigate to Reports & Analytics, select "Financial Reports", choose your date range and report type (Revenue, Expenses, Profit/Loss, etc.), select the projects to include, and click "Generate Report". You can export the report as PDF or Excel.',
      category: 'reports',
      helpful: 76,
      notHelpful: 3
    },
    {
      id: '7',
      question: 'Can I customize the dashboard?',
      answer: 'Yes! Click the "Customize Dashboard" button on your main dashboard. You can add, remove, and rearrange widgets, change chart types, set date ranges, and save different dashboard layouts for different purposes.',
      category: 'settings',
      helpful: 65,
      notHelpful: 7
    },
    {
      id: '8',
      question: 'How do I track time for my team?',
      answer: 'Use the Time Tracking feature where team members can clock in/out for specific projects and sites. Managers can view compiled time data, generate timesheets, and track productivity across different construction sites.',
      category: 'project-management',
      helpful: 58,
      notHelpful: 5
    },
    {
      id: '9',
      question: 'How do I set up two-factor authentication?',
      answer: 'Go to Settings > Security, click "Enable Two-Factor Authentication", scan the QR code with your authenticator app (Google Authenticator, Authy, etc.), enter the verification code, and save your backup codes in a secure location.',
      category: 'security',
      helpful: 52,
      notHelpful: 2
    },
    {
      id: '10',
      question: 'How do I backup my data?',
      answer: 'Martcosy automatically backs up your data daily. For manual backups, go to Settings > Data Management, click "Export Data", select what to include (projects, financial data, users, etc.), and download the backup file. You can also schedule regular automated exports.',
      category: 'settings',
      helpful: 45,
      notHelpful: 3
    }
  ];

  const toggleItem = (itemId: string) => {
    setOpenItems(prev => 
      prev.includes(itemId) 
        ? prev.filter(id => id !== itemId)
        : [...prev, itemId]
    );
  };

  const markHelpful = (itemId: string, helpful: boolean) => {
    toast({
      title: "Thank you for your feedback!",
      description: helpful ? "We're glad this was helpful." : "We'll work on improving this answer.",
    });
  };

  const filteredFAQs = faqItems.filter(item => {
    const matchesSearch = item.question.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         item.answer.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesCategory = selectedCategory === 'all' || item.category === selectedCategory;
    return matchesSearch && matchesCategory;
  });

  const popularFAQs = faqItems.filter(item => item.popular);

  return (
    <div className="space-y-8 p-6 max-w-6xl mx-auto">
      {/* Header */}
      <div className="text-center space-y-4">
        <div className="flex items-center justify-center space-x-3">
          <div className="w-12 h-12 rounded-xl bg-purple-50 dark:bg-purple-900/20 flex items-center justify-center">
            <HelpCircle className="w-6 h-6 text-purple-600 dark:text-purple-400" />
          </div>
          <div>
            <h1 className="text-3xl font-bold text-gray-900 dark:text-white">Frequently Asked Questions</h1>
            <p className="text-gray-600 dark:text-gray-400">Find quick answers to common questions</p>
          </div>
        </div>

        {/* Search */}
        <div className="max-w-2xl mx-auto">
          <div className="relative">
            <Search className="absolute left-3 top-3 h-5 w-5 text-gray-400" />
            <Input
              placeholder="Search FAQ..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10 py-3"
            />
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
        {/* Categories Sidebar */}
        <div className="lg:col-span-1">
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Categories</CardTitle>
            </CardHeader>
            <CardContent className="space-y-2">
              {faqCategories.map((category) => (
                <Button
                  key={category.id}
                  variant={selectedCategory === category.id ? "default" : "ghost"}
                  className="w-full justify-start"
                  onClick={() => setSelectedCategory(category.id)}
                >
                  {category.icon}
                  <span className="ml-2 flex-1 text-left">{category.name}</span>
                  <Badge variant="secondary" className="ml-2">
                    {category.count}
                  </Badge>
                </Button>
              ))}
            </CardContent>
          </Card>

          {/* Popular Questions */}
          <Card className="mt-6">
            <CardHeader>
              <CardTitle className="text-lg flex items-center gap-2">
                <Star className="w-5 h-5 text-yellow-500" />
                Popular Questions
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              {popularFAQs.slice(0, 3).map((item) => (
                <div key={item.id} className="space-y-2">
                  <button
                    onClick={() => {
                      setSelectedCategory(item.category);
                      toggleItem(item.id);
                    }}
                    className="text-sm font-medium text-left hover:text-blue-600 dark:hover:text-blue-400 transition-colors"
                  >
                    {item.question}
                  </button>
                  <div className="flex items-center gap-2 text-xs text-gray-500">
                    <ThumbsUp className="w-3 h-3" />
                    <span>{item.helpful}</span>
                  </div>
                  <Separator />
                </div>
              ))}
            </CardContent>
          </Card>
        </div>

        {/* FAQ Content */}
        <div className="lg:col-span-3 space-y-6">
          {filteredFAQs.length === 0 ? (
            <Card>
              <CardContent className="p-8 text-center">
                <HelpCircle className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">No results found</h3>
                <p className="text-gray-600 dark:text-gray-400 mb-4">
                  We couldn't find any FAQ items matching your search.
                </p>
                <Button variant="outline" onClick={() => setSearchQuery('')}>
                  Clear Search
                </Button>
              </CardContent>
            </Card>
          ) : (
            <div className="space-y-4">
              {filteredFAQs.map((item) => (
                <Card key={item.id}>
                  <Collapsible open={openItems.includes(item.id)} onOpenChange={() => toggleItem(item.id)}>
                    <CollapsibleTrigger asChild>
                      <CardHeader className="cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-800/50 transition-colors">
                        <div className="flex items-center justify-between">
                          <div className="flex items-start gap-3 flex-1">
                            <div className="flex items-center gap-2">
                              {item.popular && (
                                <Badge variant="secondary" className="text-xs">
                                  <Star className="w-3 h-3 mr-1" />
                                  Popular
                                </Badge>
                              )}
                            </div>
                            <CardTitle className="text-left text-lg font-medium">
                              {item.question}
                            </CardTitle>
                          </div>
                          {openItems.includes(item.id) ? (
                            <ChevronUp className="w-5 h-5 text-gray-400" />
                          ) : (
                            <ChevronDown className="w-5 h-5 text-gray-400" />
                          )}
                        </div>
                      </CardHeader>
                    </CollapsibleTrigger>
                    <CollapsibleContent>
                      <CardContent className="pt-0">
                        <div className="space-y-4">
                          <p className="text-gray-700 dark:text-gray-300 leading-relaxed">
                            {item.answer}
                          </p>
                          
                          <Separator />
                          
                          <div className="flex items-center justify-between">
                            <div className="flex items-center gap-4">
                              <span className="text-sm text-gray-600 dark:text-gray-400">Was this helpful?</span>
                              <div className="flex items-center gap-2">
                                <Button
                                  variant="outline"
                                  size="sm"
                                  onClick={() => markHelpful(item.id, true)}
                                  className="flex items-center gap-1"
                                >
                                  <ThumbsUp className="w-3 h-3" />
                                  <span className="text-xs">{item.helpful}</span>
                                </Button>
                                <Button
                                  variant="outline"
                                  size="sm"
                                  onClick={() => markHelpful(item.id, false)}
                                  className="flex items-center gap-1"
                                >
                                  <ThumbsDown className="w-3 h-3" />
                                  <span className="text-xs">{item.notHelpful}</span>
                                </Button>
                              </div>
                            </div>
                            
                            <Badge variant="outline" className="text-xs">
                              {faqCategories.find(cat => cat.id === item.category)?.name}
                            </Badge>
                          </div>
                        </div>
                      </CardContent>
                    </CollapsibleContent>
                  </Collapsible>
                </Card>
              ))}
            </div>
          )}

          {/* Still Need Help */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <MessageCircle className="w-5 h-5" />
                Still Need Help?
              </CardTitle>
              <CardDescription>
                Can't find the answer you're looking for? Our support team is here to help!
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <Button 
                  variant="outline" 
                  className="h-auto p-4 flex-col space-y-2"
                  onClick={() => window.open('https://help.martcosy.com/chat', '_blank')}
                >
                  <MessageCircle className="w-6 h-6" />
                  <span className="font-medium">Live Chat</span>
                  <span className="text-xs text-gray-500">Get instant help</span>
                  <ExternalLink className="w-3 h-3" />
                </Button>
                <Button 
                  variant="outline" 
                  className="h-auto p-4 flex-col space-y-2"
                  onClick={() => window.location.href = 'mailto:<EMAIL>'}
                >
                  <Mail className="w-6 h-6" />
                  <span className="font-medium">Email Support</span>
                  <span className="text-xs text-gray-500">Detailed assistance</span>
                </Button>
                <Button 
                  variant="outline" 
                  className="h-auto p-4 flex-col space-y-2"
                  onClick={() => window.location.href = 'tel:+263-4-123-4567'}
                >
                  <Phone className="w-6 h-6" />
                  <span className="font-medium">Phone Support</span>
                  <span className="text-xs text-gray-500">Mon-Fri, 8AM-6PM</span>
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default FAQ;
