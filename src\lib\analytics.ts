import { supabase } from './supabase';
import { FinancialService } from './financials';

export interface AnalyticsData {
  financialSummary: FinancialSummary;
  revenueAnalytics: RevenueAnalytics;
  expenseAnalytics: ExpenseAnalytics;
  cashFlowAnalytics: CashFlowAnalytics;
  projectAnalytics: ProjectAnalytics;
  clientAnalytics: ClientAnalytics;
  trends: TrendAnalytics;
  insights: BusinessInsight[];
}

export interface FinancialSummary {
  totalRevenue: number;
  totalExpenses: number;
  netProfit: number;
  profitMargin: number;
  cashFlow: number;
  growthRate: number;
  period: string;
}

export interface RevenueAnalytics {
  totalRevenue: number;
  revenueByCategory: CategoryBreakdown[];
  revenueByClient: ClientBreakdown[];
  revenueByMonth: MonthlyData[];
  averageProjectValue: number;
  topRevenueStreams: RevenueStream[];
  revenueGrowth: GrowthMetric;
}

export interface ExpenseAnalytics {
  totalExpenses: number;
  expensesByCategory: CategoryBreakdown[];
  expensesByMonth: MonthlyData[];
  topExpenses: ExpenseItem[];
  expenseGrowth: GrowthMetric;
  costPerProject: number;
  expenseRatio: number;
}

export interface CashFlowAnalytics {
  netCashFlow: number;
  inflows: number;
  outflows: number;
  cashFlowByMonth: MonthlyData[];
  cashFlowTrend: TrendData[];
  averageMonthlyFlow: number;
  cashFlowRatio: number;
}

export interface ProjectAnalytics {
  totalProjects: number;
  activeProjects: number;
  completedProjects: number;
  averageProjectDuration: number;
  projectProfitability: ProjectProfitability[];
  projectsByStatus: StatusBreakdown[];
  projectTimeline: TimelineData[];
}

export interface ClientAnalytics {
  totalClients: number;
  activeClients: number;
  clientValue: ClientValue[];
  clientRetention: number;
  averageClientValue: number;
  topClients: ClientBreakdown[];
}

export interface TrendAnalytics {
  revenueGrowth: TrendData[];
  expenseGrowth: TrendData[];
  profitTrend: TrendData[];
  cashFlowTrend: TrendData[];
  seasonalPatterns: SeasonalPattern[];
}

export interface BusinessInsight {
  id: string;
  type: 'positive' | 'negative' | 'neutral' | 'warning';
  category: 'revenue' | 'expenses' | 'cashflow' | 'projects' | 'clients';
  title: string;
  description: string;
  value?: number;
  change?: number;
  recommendation?: string;
  priority: 'high' | 'medium' | 'low';
}

// Supporting interfaces
export interface CategoryBreakdown {
  category: string;
  amount: number;
  percentage: number;
  count: number;
}

export interface ClientBreakdown {
  clientName: string;
  amount: number;
  percentage: number;
  projectCount: number;
}

export interface MonthlyData {
  month: string;
  amount: number;
  count: number;
  growth?: number;
}

export interface RevenueStream {
  source: string;
  amount: number;
  percentage: number;
  trend: 'up' | 'down' | 'stable';
}

export interface ExpenseItem {
  description: string;
  amount: number;
  category: string;
  date: string;
}

export interface GrowthMetric {
  currentPeriod: number;
  previousPeriod: number;
  growthRate: number;
  growthAmount: number;
}

export interface TrendData {
  period: string;
  value: number;
  change: number;
}

export interface ProjectProfitability {
  projectName: string;
  revenue: number;
  expenses: number;
  profit: number;
  margin: number;
}

export interface StatusBreakdown {
  status: string;
  count: number;
  percentage: number;
}

export interface TimelineData {
  date: string;
  projectsStarted: number;
  projectsCompleted: number;
  activeProjects: number;
}

export interface ClientValue {
  clientName: string;
  totalValue: number;
  projectCount: number;
  averageProjectValue: number;
  lastProject: string;
}

export interface SeasonalPattern {
  period: string;
  revenue: number;
  expenses: number;
  projects: number;
  pattern: 'high' | 'medium' | 'low';
}

export class AnalyticsService {
  // Main analytics data aggregation
  static async getAnalyticsData(
    startDate?: string,
    endDate?: string
  ): Promise<AnalyticsData> {
    try {
      console.log('🔄 Generating comprehensive analytics data...');

      const [
        financialSummary,
        revenueAnalytics,
        expenseAnalytics,
        cashFlowAnalytics,
        projectAnalytics,
        clientAnalytics,
        trends
      ] = await Promise.all([
        this.getFinancialSummary(startDate, endDate),
        this.getRevenueAnalytics(startDate, endDate),
        this.getExpenseAnalytics(startDate, endDate),
        this.getCashFlowAnalytics(startDate, endDate),
        this.getProjectAnalytics(startDate, endDate),
        this.getClientAnalytics(startDate, endDate),
        this.getTrendAnalytics(startDate, endDate)
      ]);

      // Generate business insights
      const insights = await this.generateBusinessInsights({
        financialSummary,
        revenueAnalytics,
        expenseAnalytics,
        cashFlowAnalytics,
        projectAnalytics,
        clientAnalytics,
        trends
      });

      console.log('✅ Analytics data generated successfully');

      return {
        financialSummary,
        revenueAnalytics,
        expenseAnalytics,
        cashFlowAnalytics,
        projectAnalytics,
        clientAnalytics,
        trends,
        insights
      };
    } catch (error) {
      console.error('❌ Error generating analytics data:', error);
      throw error;
    }
  }

  // Financial Summary Analytics
  static async getFinancialSummary(
    startDate?: string,
    endDate?: string
  ): Promise<FinancialSummary> {
    try {
      const summary = await FinancialService.getFinancialSummary(startDate, endDate);
      
      const profitMargin = summary.totalRevenue > 0 
        ? (summary.netProfit / summary.totalRevenue) * 100 
        : 0;

      // Calculate growth rate (comparing to previous period)
      const growthRate = await this.calculateGrowthRate('revenue', startDate, endDate);

      return {
        totalRevenue: summary.totalRevenue,
        totalExpenses: summary.totalExpenses,
        netProfit: summary.netProfit,
        profitMargin,
        cashFlow: summary.netCashFlow,
        growthRate,
        period: this.formatPeriod(startDate, endDate)
      };
    } catch (error) {
      console.error('Error getting financial summary:', error);
      throw error;
    }
  }

  // Revenue Analytics
  static async getRevenueAnalytics(
    startDate?: string,
    endDate?: string
  ): Promise<RevenueAnalytics> {
    try {
      console.log('📊 Analyzing revenue data...');

      // Get revenue entries
      let revenueQuery = supabase
        .from('revenue_entries')
        .select('*');

      if (startDate) revenueQuery = revenueQuery.gte('revenue_date', startDate);
      if (endDate) revenueQuery = revenueQuery.lte('revenue_date', endDate);

      const { data: revenueEntries } = await revenueQuery;
      const revenues = revenueEntries || [];

      const totalRevenue = revenues.reduce((sum, r) => sum + r.amount, 0);

      // Revenue by category
      const categoryMap = new Map<string, number>();
      revenues.forEach(r => {
        categoryMap.set(r.category, (categoryMap.get(r.category) || 0) + r.amount);
      });

      const revenueByCategory: CategoryBreakdown[] = Array.from(categoryMap.entries())
        .map(([category, amount]) => ({
          category,
          amount,
          percentage: totalRevenue > 0 ? (amount / totalRevenue) * 100 : 0,
          count: revenues.filter(r => r.category === category).length
        }))
        .sort((a, b) => b.amount - a.amount);

      // Revenue by client
      const clientMap = new Map<string, { amount: number; count: number }>();
      revenues.forEach(r => {
        const existing = clientMap.get(r.client_name) || { amount: 0, count: 0 };
        clientMap.set(r.client_name, {
          amount: existing.amount + r.amount,
          count: existing.count + 1
        });
      });

      const revenueByClient: ClientBreakdown[] = Array.from(clientMap.entries())
        .map(([clientName, data]) => ({
          clientName,
          amount: data.amount,
          percentage: totalRevenue > 0 ? (data.amount / totalRevenue) * 100 : 0,
          projectCount: data.count
        }))
        .sort((a, b) => b.amount - a.amount);

      // Monthly revenue data
      const revenueByMonth = await this.getMonthlyData('revenue_entries', 'revenue_date', 'amount', startDate, endDate);

      // Calculate metrics
      const averageProjectValue = revenues.length > 0 ? totalRevenue / revenues.length : 0;
      const revenueGrowth = await this.calculateGrowthMetric('revenue', startDate, endDate);

      // Top revenue streams
      const topRevenueStreams: RevenueStream[] = revenueByCategory.slice(0, 5).map(cat => ({
        source: cat.category,
        amount: cat.amount,
        percentage: cat.percentage,
        trend: 'stable' as const // TODO: Calculate actual trend
      }));

      return {
        totalRevenue,
        revenueByCategory,
        revenueByClient,
        revenueByMonth,
        averageProjectValue,
        topRevenueStreams,
        revenueGrowth
      };
    } catch (error) {
      console.error('Error analyzing revenue:', error);
      throw error;
    }
  }

  // Helper method to format period
  private static formatPeriod(startDate?: string, endDate?: string): string {
    if (!startDate && !endDate) return 'All Time';
    if (startDate && endDate) {
      return `${new Date(startDate).toLocaleDateString()} - ${new Date(endDate).toLocaleDateString()}`;
    }
    if (startDate) return `From ${new Date(startDate).toLocaleDateString()}`;
    if (endDate) return `Until ${new Date(endDate).toLocaleDateString()}`;
    return 'Custom Period';
  }

  // Helper method to calculate growth rate
  private static async calculateGrowthRate(
    metric: string,
    startDate?: string,
    endDate?: string
  ): Promise<number> {
    // TODO: Implement growth rate calculation
    return 0;
  }

  // Helper method to get monthly data
  private static async getMonthlyData(
    table: string,
    dateField: string,
    amountField: string,
    startDate?: string,
    endDate?: string
  ): Promise<MonthlyData[]> {
    try {
      let query = supabase
        .from(table)
        .select(`${dateField}, ${amountField}`);

      if (startDate) query = query.gte(dateField, startDate);
      if (endDate) query = query.lte(dateField, endDate);

      const { data } = await query;
      if (!data) return [];

      // Group by month
      const monthlyMap = new Map<string, { amount: number; count: number }>();
      
      data.forEach(item => {
        const date = new Date(item[dateField]);
        const monthKey = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;
        const existing = monthlyMap.get(monthKey) || { amount: 0, count: 0 };
        monthlyMap.set(monthKey, {
          amount: existing.amount + (item[amountField] || 0),
          count: existing.count + 1
        });
      });

      return Array.from(monthlyMap.entries())
        .map(([month, data]) => ({
          month,
          amount: data.amount,
          count: data.count
        }))
        .sort((a, b) => a.month.localeCompare(b.month));
    } catch (error) {
      console.error('Error getting monthly data:', error);
      return [];
    }
  }

  // Helper method to calculate growth metric
  private static async calculateGrowthMetric(
    type: string,
    startDate?: string,
    endDate?: string
  ): Promise<GrowthMetric> {
    // TODO: Implement growth metric calculation
    return {
      currentPeriod: 0,
      previousPeriod: 0,
      growthRate: 0,
      growthAmount: 0
    };
  }

  // Expense Analytics
  static async getExpenseAnalytics(
    startDate?: string,
    endDate?: string
  ): Promise<ExpenseAnalytics> {
    try {
      console.log('💰 Analyzing expense data...');

      // Get expenses
      let expenseQuery = supabase
        .from('expenses')
        .select('*, category:expense_categories(name)');

      if (startDate) expenseQuery = expenseQuery.gte('expense_date', startDate);
      if (endDate) expenseQuery = expenseQuery.lte('expense_date', endDate);

      const { data: expenses } = await expenseQuery;
      const expenseData = expenses || [];

      const totalExpenses = expenseData.reduce((sum, e) => sum + e.amount, 0);

      // Expenses by category
      const categoryMap = new Map<string, number>();
      expenseData.forEach(e => {
        const categoryName = e.category?.name || 'Uncategorized';
        categoryMap.set(categoryName, (categoryMap.get(categoryName) || 0) + e.amount);
      });

      const expensesByCategory: CategoryBreakdown[] = Array.from(categoryMap.entries())
        .map(([category, amount]) => ({
          category,
          amount,
          percentage: totalExpenses > 0 ? (amount / totalExpenses) * 100 : 0,
          count: expenseData.filter(e => e.category?.name === category).length
        }))
        .sort((a, b) => b.amount - a.amount);

      // Monthly expenses
      const expensesByMonth = await this.getMonthlyData('expenses', 'expense_date', 'amount', startDate, endDate);

      // Top expenses
      const topExpenses: ExpenseItem[] = expenseData
        .sort((a, b) => b.amount - a.amount)
        .slice(0, 10)
        .map(e => ({
          description: e.description,
          amount: e.amount,
          category: e.category?.name || 'Uncategorized',
          date: e.expense_date
        }));

      // Calculate metrics
      const expenseGrowth = await this.calculateGrowthMetric('expenses', startDate, endDate);
      const costPerProject = 0; // TODO: Calculate based on project data
      const expenseRatio = 0; // TODO: Calculate expense to revenue ratio

      return {
        totalExpenses,
        expensesByCategory,
        expensesByMonth,
        topExpenses,
        expenseGrowth,
        costPerProject,
        expenseRatio
      };
    } catch (error) {
      console.error('Error analyzing expenses:', error);
      throw error;
    }
  }

  // Cash Flow Analytics
  static async getCashFlowAnalytics(
    startDate?: string,
    endDate?: string
  ): Promise<CashFlowAnalytics> {
    try {
      console.log('💸 Analyzing cash flow data...');

      // Get cash flow transactions
      let cashFlowQuery = supabase
        .from('cash_flow_transactions')
        .select('*');

      if (startDate) cashFlowQuery = cashFlowQuery.gte('transaction_date', startDate);
      if (endDate) cashFlowQuery = cashFlowQuery.lte('transaction_date', endDate);

      const { data: transactions } = await cashFlowQuery;
      const cashFlowData = transactions || [];

      const inflows = cashFlowData
        .filter(t => t.transaction_type === 'inflow')
        .reduce((sum, t) => sum + t.amount, 0);

      const outflows = cashFlowData
        .filter(t => t.transaction_type === 'outflow')
        .reduce((sum, t) => sum + t.amount, 0);

      const netCashFlow = inflows - outflows;

      // Monthly cash flow
      const inflowsByMonth = await this.getMonthlyData('cash_flow_transactions', 'transaction_date', 'amount', startDate, endDate);
      const cashFlowByMonth = inflowsByMonth; // Simplified for now

      // Cash flow trend
      const cashFlowTrend: TrendData[] = cashFlowByMonth.map(month => ({
        period: month.month,
        value: month.amount,
        change: 0 // TODO: Calculate change from previous period
      }));

      const averageMonthlyFlow = cashFlowByMonth.length > 0
        ? cashFlowByMonth.reduce((sum, m) => sum + m.amount, 0) / cashFlowByMonth.length
        : 0;

      const cashFlowRatio = outflows > 0 ? inflows / outflows : 0;

      return {
        netCashFlow,
        inflows,
        outflows,
        cashFlowByMonth,
        cashFlowTrend,
        averageMonthlyFlow,
        cashFlowRatio
      };
    } catch (error) {
      console.error('Error analyzing cash flow:', error);
      throw error;
    }
  }

  // Project Analytics
  static async getProjectAnalytics(
    startDate?: string,
    endDate?: string
  ): Promise<ProjectAnalytics> {
    try {
      console.log('🏗️ Analyzing project data...');

      // Get projects from database
      let projectQuery = supabase
        .from('projects')
        .select('*');

      if (startDate) projectQuery = projectQuery.gte('start_date', startDate);
      if (endDate) projectQuery = projectQuery.lte('start_date', endDate);

      const { data: projects } = await projectQuery;
      const projectData = projects || [];

      const totalProjects = projectData.length;
      const activeProjects = projectData.filter(p => p.status === 'In Progress').length;
      const completedProjects = projectData.filter(p => p.status === 'Completed').length;

      // Calculate average project duration
      const completedProjectsWithDuration = projectData.filter(p =>
        p.status === 'Completed' && p.start_date && p.end_date
      );

      const averageProjectDuration = completedProjectsWithDuration.length > 0
        ? completedProjectsWithDuration.reduce((sum, p) => {
            const start = new Date(p.start_date);
            const end = new Date(p.end_date);
            const duration = Math.ceil((end.getTime() - start.getTime()) / (1000 * 60 * 60 * 24));
            return sum + duration;
          }, 0) / completedProjectsWithDuration.length
        : 0;

      // Project profitability (revenue from revenue_entries - project spent)
      const projectProfitability: ProjectProfitability[] = await Promise.all(
        projectData.map(async (project) => {
          // Get revenue for this project
          const { data: revenues } = await supabase
            .from('revenue_entries')
            .select('amount')
            .eq('project_name', project.name);

          const projectRevenue = revenues?.reduce((sum, r) => sum + r.amount, 0) || 0;
          const projectExpenses = project.spent || 0;
          const profit = projectRevenue - projectExpenses;
          const margin = projectRevenue > 0 ? (profit / projectRevenue) * 100 : 0;

          return {
            projectName: project.name,
            revenue: projectRevenue,
            expenses: projectExpenses,
            profit,
            margin
          };
        })
      );

      // Projects by status
      const statusMap = new Map<string, number>();
      projectData.forEach(p => {
        statusMap.set(p.status, (statusMap.get(p.status) || 0) + 1);
      });

      const projectsByStatus: StatusBreakdown[] = Array.from(statusMap.entries())
        .map(([status, count]) => ({
          status,
          count,
          percentage: totalProjects > 0 ? (count / totalProjects) * 100 : 0
        }));

      // Project timeline (simplified - group by month)
      const timelineMap = new Map<string, { started: number; completed: number; active: number }>();

      projectData.forEach(p => {
        const startMonth = new Date(p.start_date).toISOString().slice(0, 7); // YYYY-MM

        if (!timelineMap.has(startMonth)) {
          timelineMap.set(startMonth, { started: 0, completed: 0, active: 0 });
        }

        const monthData = timelineMap.get(startMonth)!;
        monthData.started++;

        if (p.status === 'Completed') {
          monthData.completed++;
        } else if (p.status === 'In Progress') {
          monthData.active++;
        }
      });

      const projectTimeline: TimelineData[] = Array.from(timelineMap.entries())
        .map(([date, data]) => ({
          date,
          projectsStarted: data.started,
          projectsCompleted: data.completed,
          activeProjects: data.active
        }))
        .sort((a, b) => a.date.localeCompare(b.date));

      return {
        totalProjects,
        activeProjects,
        completedProjects,
        averageProjectDuration,
        projectProfitability,
        projectsByStatus,
        projectTimeline
      };
    } catch (error) {
      console.error('Error analyzing projects:', error);
      throw error;
    }
  }

  // Client Analytics
  static async getClientAnalytics(
    startDate?: string,
    endDate?: string
  ): Promise<ClientAnalytics> {
    try {
      console.log('👥 Analyzing client data...');

      // Get client data from revenue entries and client_financials
      let revenueQuery = supabase
        .from('revenue_entries')
        .select('client_name, amount, project_name');

      if (startDate) revenueQuery = revenueQuery.gte('revenue_date', startDate);
      if (endDate) revenueQuery = revenueQuery.lte('revenue_date', endDate);

      const { data: revenueEntries } = await revenueQuery;
      const revenues = revenueEntries || [];

      // Aggregate client data
      const clientMap = new Map<string, { totalValue: number; projectCount: number; lastProject: string }>();

      revenues.forEach(r => {
        const existing = clientMap.get(r.client_name) || { totalValue: 0, projectCount: 0, lastProject: '' };
        clientMap.set(r.client_name, {
          totalValue: existing.totalValue + r.amount,
          projectCount: existing.projectCount + 1,
          lastProject: r.project_name || existing.lastProject
        });
      });

      const totalClients = clientMap.size;
      const activeClients = totalClients; // All clients with revenue are considered active

      const clientValue: ClientValue[] = Array.from(clientMap.entries())
        .map(([clientName, data]) => ({
          clientName,
          totalValue: data.totalValue,
          projectCount: data.projectCount,
          averageProjectValue: data.projectCount > 0 ? data.totalValue / data.projectCount : 0,
          lastProject: data.lastProject
        }))
        .sort((a, b) => b.totalValue - a.totalValue);

      const totalValue = clientValue.reduce((sum, c) => sum + c.totalValue, 0);
      const averageClientValue = totalClients > 0 ? totalValue / totalClients : 0;

      const topClients: ClientBreakdown[] = clientValue.slice(0, 10).map(c => ({
        clientName: c.clientName,
        amount: c.totalValue,
        percentage: totalValue > 0 ? (c.totalValue / totalValue) * 100 : 0,
        projectCount: c.projectCount
      }));

      return {
        totalClients,
        activeClients,
        clientValue,
        clientRetention: 0, // TODO: Calculate retention rate
        averageClientValue,
        topClients
      };
    } catch (error) {
      console.error('Error analyzing clients:', error);
      throw error;
    }
  }

  // Trend Analytics
  static async getTrendAnalytics(
    startDate?: string,
    endDate?: string
  ): Promise<TrendAnalytics> {
    try {
      console.log('📈 Analyzing trends...');

      // TODO: Implement comprehensive trend analysis
      return {
        revenueGrowth: [],
        expenseGrowth: [],
        profitTrend: [],
        cashFlowTrend: [],
        seasonalPatterns: []
      };
    } catch (error) {
      console.error('Error analyzing trends:', error);
      throw error;
    }
  }

  // Generate Business Insights
  static async generateBusinessInsights(data: Partial<AnalyticsData>): Promise<BusinessInsight[]> {
    const insights: BusinessInsight[] = [];

    try {
      // Revenue insights
      if (data.revenueAnalytics) {
        if (data.revenueAnalytics.totalRevenue > 0) {
          insights.push({
            id: 'revenue-positive',
            type: 'positive',
            category: 'revenue',
            title: 'Revenue Generated',
            description: `Total revenue of $${data.revenueAnalytics.totalRevenue.toLocaleString()} recorded`,
            value: data.revenueAnalytics.totalRevenue,
            priority: 'high'
          });
        }

        // Top revenue category insight
        if (data.revenueAnalytics.revenueByCategory.length > 0) {
          const topCategory = data.revenueAnalytics.revenueByCategory[0];
          insights.push({
            id: 'top-revenue-category',
            type: 'neutral',
            category: 'revenue',
            title: 'Top Revenue Category',
            description: `${topCategory.category} generates ${topCategory.percentage.toFixed(1)}% of total revenue`,
            value: topCategory.amount,
            recommendation: topCategory.percentage > 70 ? 'Consider diversifying revenue streams' : undefined,
            priority: topCategory.percentage > 70 ? 'medium' : 'low'
          });
        }
      }

      // Expense insights
      if (data.expenseAnalytics) {
        if (data.expenseAnalytics.totalExpenses > 0) {
          const expenseRatio = data.financialSummary && data.financialSummary.totalRevenue > 0
            ? (data.expenseAnalytics.totalExpenses / data.financialSummary.totalRevenue) * 100
            : 0;

          insights.push({
            id: 'expense-ratio',
            type: expenseRatio > 80 ? 'warning' : expenseRatio > 60 ? 'negative' : 'positive',
            category: 'expenses',
            title: 'Expense Ratio',
            description: `Expenses represent ${expenseRatio.toFixed(1)}% of revenue`,
            value: expenseRatio,
            recommendation: expenseRatio > 80 ? 'Review and optimize expenses' : undefined,
            priority: expenseRatio > 80 ? 'high' : 'medium'
          });
        }
      }

      // Profit insights
      if (data.financialSummary) {
        if (data.financialSummary.netProfit > 0) {
          insights.push({
            id: 'profit-positive',
            type: 'positive',
            category: 'revenue',
            title: 'Profitable Operations',
            description: `Net profit of $${data.financialSummary.netProfit.toLocaleString()} with ${data.financialSummary.profitMargin.toFixed(1)}% margin`,
            value: data.financialSummary.netProfit,
            priority: 'high'
          });
        } else if (data.financialSummary.netProfit < 0) {
          insights.push({
            id: 'profit-negative',
            type: 'warning',
            category: 'revenue',
            title: 'Operating at Loss',
            description: `Net loss of $${Math.abs(data.financialSummary.netProfit).toLocaleString()}`,
            value: data.financialSummary.netProfit,
            recommendation: 'Review expenses and increase revenue streams',
            priority: 'high'
          });
        }
      }

      // Cash flow insights
      if (data.cashFlowAnalytics) {
        if (data.cashFlowAnalytics.netCashFlow < 0) {
          insights.push({
            id: 'cashflow-negative',
            type: 'warning',
            category: 'cashflow',
            title: 'Negative Cash Flow',
            description: `Cash outflow exceeds inflow by $${Math.abs(data.cashFlowAnalytics.netCashFlow).toLocaleString()}`,
            value: data.cashFlowAnalytics.netCashFlow,
            recommendation: 'Monitor cash flow closely and consider improving collection processes',
            priority: 'high'
          });
        }
      }

      // Client insights
      if (data.clientAnalytics) {
        if (data.clientAnalytics.topClients.length > 0) {
          const topClient = data.clientAnalytics.topClients[0];
          if (topClient.percentage > 50) {
            insights.push({
              id: 'client-concentration',
              type: 'warning',
              category: 'clients',
              title: 'Client Concentration Risk',
              description: `${topClient.clientName} represents ${topClient.percentage.toFixed(1)}% of revenue`,
              value: topClient.percentage,
              recommendation: 'Diversify client base to reduce dependency risk',
              priority: 'medium'
            });
          }
        }
      }

      console.log(`✅ Generated ${insights.length} business insights`);
      return insights;
    } catch (error) {
      console.error('Error generating insights:', error);
      return insights;
    }
  }
}
