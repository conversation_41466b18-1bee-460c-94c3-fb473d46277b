import { createClient } from '@supabase/supabase-js'

// Supabase configuration
const supabaseUrl = import.meta.env.VITE_SUPABASE_URL || 'https://your-project.supabase.co'
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY || 'your-anon-key'

export const supabase = createClient(supabaseUrl, supabaseAnonKey, {
  realtime: {
    params: {
      eventsPerSecond: 10
    }
  },
  auth: {
    persistSession: true,
    autoRefreshToken: true
  }
})

// Database types
export interface Employee {
  id: string
  name: string
  position: string
  month: string
  days: number
  rate_per_day: number
  credit: number
  total_salary: number
  date_added: string
  last_modified: string
  created_at: string
}

// Database operations
export class EmployeeService {
  // Test database connection
  static async testConnection(): Promise<{ success: boolean; message: string; details?: any }> {
    try {
      console.log('Testing Supabase connection...');

      // First, test if we can connect to Supabase at all
      const { data, error } = await supabase
        .from('employees')
        .select('count', { count: 'exact', head: true })

      if (error) {
        console.error('Supabase connection error:', error);
        return {
          success: false,
          message: `Database error: ${error.message}`,
          details: error
        };
      }

      console.log('Connection test successful');
      return {
        success: true,
        message: 'Successfully connected to Supabase database',
        details: { count: data }
      };
    } catch (error) {
      console.error('Connection test failed:', error);
      return {
        success: false,
        message: error instanceof Error ? error.message : 'Unknown connection error',
        details: error
      };
    }
  }

  // Get all employees
  static async getEmployees(): Promise<Employee[]> {
    try {
      console.log('Fetching employees from Supabase...');

      const { data, error } = await supabase
        .from('employees')
        .select('*')
        .order('date_added', { ascending: false })

      if (error) {
        console.error('Error fetching employees:', error)
        throw new Error(`Database error: ${error.message}`)
      }

      console.log('Successfully fetched employees:', data);
      return data || []
    } catch (error) {
      console.error('Failed to fetch employees:', error)
      throw error
    }
  }

  // Add new employee
  static async addEmployee(employee: Omit<Employee, 'id' | 'total_salary' | 'date_added' | 'last_modified' | 'created_at'>): Promise<Employee> {
    try {
      const { data, error } = await supabase
        .from('employees')
        .insert([{
          name: employee.name,
          position: employee.position,
          month: employee.month,
          days: employee.days,
          rate_per_day: employee.rate_per_day,
          credit: employee.credit
        }])
        .select()
        .single()
      
      if (error) {
        console.error('Error adding employee:', error)
        throw error
      }
      
      return data
    } catch (error) {
      console.error('Failed to add employee:', error)
      throw error
    }
  }

  // Update employee
  static async updateEmployee(id: string, updates: Partial<Omit<Employee, 'id' | 'total_salary' | 'date_added' | 'created_at'>>): Promise<Employee> {
    try {
      const { data, error } = await supabase
        .from('employees')
        .update({
          ...updates,
          last_modified: new Date().toISOString()
        })
        .eq('id', id)
        .select()
        .single()
      
      if (error) {
        console.error('Error updating employee:', error)
        throw error
      }
      
      return data
    } catch (error) {
      console.error('Failed to update employee:', error)
      throw error
    }
  }

  // Delete employee
  static async deleteEmployee(id: string): Promise<void> {
    try {
      const { error } = await supabase
        .from('employees')
        .delete()
        .eq('id', id)
      
      if (error) {
        console.error('Error deleting employee:', error)
        throw error
      }
    } catch (error) {
      console.error('Failed to delete employee:', error)
      throw error
    }
  }

  // Subscribe to real-time changes
  static subscribeToChanges(callback: (payload: any) => void) {
    return supabase
      .channel('employees')
      .on('postgres_changes', 
        { 
          event: '*', 
          schema: 'public', 
          table: 'employees' 
        }, 
        callback
      )
      .subscribe()
  }

  // Migrate data from localStorage
  static async migrateFromLocalStorage(): Promise<void> {
    try {
      const localData = localStorage.getItem('employees')
      if (!localData) return

      const employees = JSON.parse(localData)
      
      // Check if we already have data in Supabase
      const existingData = await this.getEmployees()
      if (existingData.length > 0) {
        console.log('Database already has data, skipping migration')
        return
      }

      console.log('Migrating data from localStorage to Supabase...')
      
      for (const employee of employees) {
        await this.addEmployee({
          name: employee.name,
          position: employee.position,
          month: employee.month,
          days: employee.days,
          rate_per_day: employee.ratePerDay || employee.rate_per_day,
          credit: employee.credit
        })
      }
      
      console.log('Migration completed successfully')
      
      // Backup localStorage data before clearing
      localStorage.setItem('employees_backup', localData)
      localStorage.removeItem('employees')
      
    } catch (error) {
      console.error('Migration failed:', error)
      throw error
    }
  }

  // Export data to CSV
  static async exportToCSV(): Promise<string> {
    try {
      const employees = await this.getEmployees()
      const headers = ['Name', 'Position', 'Month', 'Days', 'Rate/Day', 'Credit', 'Total Salary', 'Date Added']
      
      const csvContent = [
        headers.join(','),
        ...employees.map(emp => [
          `"${emp.name}"`,
          `"${emp.position}"`,
          `"${emp.month}"`,
          emp.days,
          emp.rate_per_day,
          emp.credit,
          emp.total_salary,
          new Date(emp.date_added).toLocaleDateString()
        ].join(','))
      ].join('\n')

      return csvContent
    } catch (error) {
      console.error('Failed to export data:', error)
      throw error
    }
  }

  // Get summary statistics
  static async getSummaryStats() {
    try {
      const employees = await this.getEmployees()
      
      const totalEmployees = employees.length
      const grandTotal = employees.reduce((sum, emp) => sum + Number(emp.total_salary), 0)
      const averageSalary = totalEmployees > 0 ? grandTotal / totalEmployees : 0
      
      // Group by position
      const positionStats = employees.reduce((acc, emp) => {
        if (!acc[emp.position]) {
          acc[emp.position] = { count: 0, totalSalary: 0 }
        }
        acc[emp.position].count++
        acc[emp.position].totalSalary += Number(emp.total_salary)
        return acc
      }, {} as Record<string, { count: number; totalSalary: number }>)

      // Group by month
      const monthStats = employees.reduce((acc, emp) => {
        if (!acc[emp.month]) {
          acc[emp.month] = { count: 0, totalSalary: 0 }
        }
        acc[emp.month].count++
        acc[emp.month].totalSalary += Number(emp.total_salary)
        return acc
      }, {} as Record<string, { count: number; totalSalary: number }>)

      return {
        totalEmployees,
        grandTotal,
        averageSalary,
        positionStats,
        monthStats
      }
    } catch (error) {
      console.error('Failed to get summary stats:', error)
      throw error
    }
  }
}

// Utility function to check if Supabase is configured
export const isSupabaseConfigured = (): boolean => {
  return !!(supabaseUrl && supabaseAnonKey && 
           supabaseUrl !== 'https://your-project.supabase.co' && 
           supabaseAnonKey !== 'your-anon-key')
}

// Fallback to localStorage if Supabase is not configured
export const useLocalStorageFallback = (): boolean => {
  return !isSupabaseConfigured()
}
