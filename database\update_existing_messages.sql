-- Update existing messages with creator information
-- Run this after the main fix_messages_display.sql script

-- Update existing messages to have creator names from sender_name
UPDATE public.messages 
SET 
  created_by_name = COALESCE(sender_name, 'Unknown User'),
  created_by_avatar = NULL
WHERE created_by_name IS NULL AND sender_name IS NOT NULL;

-- Check the results
SELECT 
  id,
  sender_name,
  created_by_name,
  created_by_avatar,
  message_content,
  created_at
FROM public.messages 
ORDER BY created_at DESC 
LIMIT 10;
