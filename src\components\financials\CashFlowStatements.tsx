import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { 
  TrendingUp, 
  TrendingDown, 
  DollarSign, 
  Calendar, 
  BarChart3, 
  PieChart, 
  Download, 
  Filter,
  ArrowUpCircle,
  ArrowDownCircle,
  Activity,
  Target,
  AlertTriangle,
  CheckCircle
} from 'lucide-react';
import { LineChart, Line, AreaChart, Area, BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer, Pie<PERSON><PERSON> as RechartsPieChart, Pie, Cell } from 'recharts';

interface CashFlowTransaction {
  id: string;
  transaction_type: 'inflow' | 'outflow';
  category: string;
  description: string;
  amount: number;
  transaction_date: string;
  reference_type?: string;
  account_type: string;
  created_at: string;
}

interface CashFlowStatementsProps {
  cashFlowTransactions: CashFlowTransaction[];
  financialSummary: {
    totalRevenue: number;
    totalExpenses: number;
    netProfit: number;
    totalInflow: number;
    totalOutflow: number;
    netCashFlow: number;
    paidRevenue: number;
    paidExpenses: number;
    outstandingRevenue: number;
    pendingExpenses: number;
  };
  cashFlowData: any[];
  cashFlowBreakdown: any[];
  getCashFlowDataForPeriod: (periodType: 'day' | 'week' | 'month' | 'year', startDate?: string, endDate?: string) => Promise<any[]>;
}

const CashFlowStatements: React.FC<CashFlowStatementsProps> = ({
  cashFlowTransactions,
  financialSummary,
  cashFlowData,
  cashFlowBreakdown,
  getCashFlowDataForPeriod
}) => {
  const [selectedPeriod, setSelectedPeriod] = useState('month');
  const [selectedYear, setSelectedYear] = useState(new Date().getFullYear().toString());
  const [selectedMonth, setSelectedMonth] = useState((new Date().getMonth() + 1).toString());
  const [currentCashFlowData, setCurrentCashFlowData] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);



  // Load cash flow data when period changes or when we need fresh data
  const loadCashFlowData = async (forceRefresh = false) => {
    setLoading(true);
    try {
      const data = await getCashFlowDataForPeriod(selectedPeriod as any);

      if (data && data.length > 0) {
        const formattedData = data.map((item: any) => ({
          period: item.period_label || item.period,
          inflow: Number(item.total_inflow) || 0,
          outflow: Number(item.total_outflow) || 0,
          netFlow: Number(item.net_flow) || 0
        }));
        setCurrentCashFlowData(formattedData);
      } else {
        setCurrentCashFlowData([]);
      }
    } catch (error) {
      setCurrentCashFlowData([]);
    } finally {
      setLoading(false);
    }
  };

  // Load data when period changes
  useEffect(() => {
    loadCashFlowData();
  }, [selectedPeriod, getCashFlowDataForPeriod]);

  // Reload data when financial summary changes (indicates new transactions)
  useEffect(() => {
    console.log('Financial summary changed, reloading cash flow data');
    loadCashFlowData(true);
  }, [financialSummary.totalInflow, financialSummary.totalOutflow, financialSummary.netCashFlow]);

  // Initialize with existing data
  useEffect(() => {
    if (cashFlowData && cashFlowData.length > 0) {
      const formattedData = cashFlowData.map((item: any) => ({
        period: item.period_label || item.period,
        inflow: Number(item.total_inflow) || 0,
        outflow: Number(item.total_outflow) || 0,
        netFlow: Number(item.net_flow) || 0
      }));
      setCurrentCashFlowData(formattedData);
    }
  }, [cashFlowData]);

  // Process cash flow breakdown data
  const processBreakdownData = (type: 'inflow' | 'outflow') => {
    const colors = ['#10b981', '#3b82f6', '#8b5cf6', '#f59e0b', '#ef4444', '#f97316', '#84cc16', '#06b6d4', '#6366f1'];

    if (cashFlowBreakdown && cashFlowBreakdown.length > 0) {
      const filteredData = cashFlowBreakdown
        .filter((item: any) => item.type === type)
        .map((item: any, index: number) => ({
          name: item.category.charAt(0).toUpperCase() + item.category.slice(1),
          value: Number(item.amount) || 0,
          color: colors[index % colors.length]
        }));

      if (filteredData.length > 0) {
        return filteredData;
      }
    }

    // Return empty array if no data
    return [];
  };

  const inflowCategories = processBreakdownData('inflow');
  const outflowCategories = processBreakdownData('outflow');

  // Calculate key metrics from real data
  const totalInflow = financialSummary.totalInflow;
  const totalOutflow = financialSummary.totalOutflow;
  const netCashFlow = financialSummary.netCashFlow;
  const cashFlowTrend = netCashFlow > 0 ? 'positive' : 'negative';

  // Get data based on selected period
  const getChartData = () => {
    return currentCashFlowData;
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold">Cash Flow Statements</h2>
          <p className="text-gray-600">Monitor daily, monthly, and yearly cash flow</p>
        </div>
        <div className="flex items-center space-x-3">
          <Select value={selectedPeriod} onValueChange={setSelectedPeriod}>
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="daily">Daily</SelectItem>
              <SelectItem value="monthly">Monthly</SelectItem>
              <SelectItem value="yearly">Yearly</SelectItem>
            </SelectContent>
          </Select>
          <Button variant="outline" onClick={() => {
            console.log('🔄 Force refreshing cash flow data...');
            window.location.reload();
          }} disabled={loading}>
            <Activity className="w-4 h-4 mr-2" />
            Force Refresh Page
          </Button>
          <Button variant="outline">
            <Download className="w-4 h-4 mr-2" />
            Export
          </Button>
        </div>
      </div>



      {/* Key Metrics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Inflow</CardTitle>
            <ArrowUpCircle className="h-4 w-4 text-green-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">
              {formatCurrency(totalInflow)}
            </div>
            <p className="text-xs text-muted-foreground">
              Cash received this period
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Outflow</CardTitle>
            <ArrowDownCircle className="h-4 w-4 text-red-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-600">
              {formatCurrency(totalOutflow)}
            </div>
            <p className="text-xs text-muted-foreground">
              Cash spent this period
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Net Cash Flow</CardTitle>
            <Activity className="h-4 w-4 text-blue-500" />
          </CardHeader>
          <CardContent>
            <div className={`text-2xl font-bold ${netCashFlow >= 0 ? 'text-green-600' : 'text-red-600'}`}>
              {formatCurrency(netCashFlow)}
            </div>
            <p className="text-xs text-muted-foreground">
              {netCashFlow >= 0 ? (
                <CheckCircle className="w-3 h-3 inline mr-1" />
              ) : (
                <AlertTriangle className="w-3 h-3 inline mr-1" />
              )}
              {cashFlowTrend === 'positive' ? 'Healthy cash flow' : 'Needs attention'}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Cash Flow Ratio</CardTitle>
            <Target className="h-4 w-4 text-purple-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-purple-600">
              {((totalInflow / totalOutflow) * 100).toFixed(0)}%
            </div>
            <p className="text-xs text-muted-foreground">
              Inflow to outflow ratio
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Main Charts */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Cash Flow Trend Chart */}
        <Card>
          <CardHeader>
            <CardTitle>Cash Flow Trend</CardTitle>
            <CardDescription>
              {selectedPeriod.charAt(0).toUpperCase() + selectedPeriod.slice(1)} cash flow analysis
            </CardDescription>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <AreaChart data={getChartData()}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis
                  dataKey="period"
                  fontSize={12}
                />
                <YAxis fontSize={12} />
                <Tooltip formatter={(value) => formatCurrency(Number(value))} />
                <Legend />
                <Area
                  type="monotone"
                  dataKey="inflow"
                  stackId="1"
                  stroke="#10b981"
                  fill="#10b981"
                  fillOpacity={0.6}
                  name="Inflow"
                />
                <Area
                  type="monotone"
                  dataKey="outflow"
                  stackId="2"
                  stroke="#ef4444"
                  fill="#ef4444"
                  fillOpacity={0.6}
                  name="Outflow"
                />
                <Line
                  type="monotone"
                  dataKey="netFlow"
                  stroke="#3b82f6"
                  strokeWidth={3}
                  name="Net Flow"
                />
              </AreaChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        {/* Cash Flow Breakdown */}
        <Card>
          <CardHeader>
            <CardTitle>Cash Flow Breakdown</CardTitle>
            <CardDescription>
              Current month inflow vs outflow categories
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Tabs defaultValue="inflow" className="space-y-4">
              <TabsList className="grid w-full grid-cols-2">
                <TabsTrigger value="inflow">Inflow Sources</TabsTrigger>
                <TabsTrigger value="outflow">Outflow Categories</TabsTrigger>
              </TabsList>
              
              <TabsContent value="inflow">
                <ResponsiveContainer width="100%" height={250}>
                  <RechartsPieChart>
                    <Pie
                      data={inflowCategories}
                      cx="50%"
                      cy="50%"
                      outerRadius={80}
                      dataKey="value"
                      label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                    >
                      {inflowCategories.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={entry.color} />
                      ))}
                    </Pie>
                    <Tooltip formatter={(value) => formatCurrency(Number(value))} />
                  </RechartsPieChart>
                </ResponsiveContainer>
              </TabsContent>
              
              <TabsContent value="outflow">
                <ResponsiveContainer width="100%" height={250}>
                  <RechartsPieChart>
                    <Pie
                      data={outflowCategories}
                      cx="50%"
                      cy="50%"
                      outerRadius={80}
                      dataKey="value"
                      label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                    >
                      {outflowCategories.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={entry.color} />
                      ))}
                    </Pie>
                    <Tooltip formatter={(value) => formatCurrency(Number(value))} />
                  </RechartsPieChart>
                </ResponsiveContainer>
              </TabsContent>
            </Tabs>
          </CardContent>
        </Card>
      </div>

      {/* Detailed Analysis */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">

      </div>
    </div>
  );
};

export default CashFlowStatements;
