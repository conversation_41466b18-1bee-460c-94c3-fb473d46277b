import { supabase } from './supabase';

export interface EmailContent {
  to_email: string;
  subject: string;
  html_content: string;
  text_content: string;
  setup_token: string;
  user_name: string;
}

export interface EmailStatus {
  email_sent: boolean;
  last_sent_at: string | null;
  email_count: number;
  token_expires_at: string | null;
  token_valid: boolean;
}

export class EmailService {
  // Send setup email (returns email content for display/manual sending)
  static async sendSetupEmail(userId: string): Promise<EmailContent> {
    console.log('EmailService: Sending setup email for user:', userId);

    try {
      const { data, error } = await supabase
        .rpc('rpc_resend_setup_email', { user_profile_id: userId });

      if (error) {
        console.error('EmailService: RPC error:', error);
        throw new Error('Failed to generate setup email: ' + error.message);
      }

      if (!data || !data.success) {
        throw new Error(data?.error || 'Failed to generate setup email');
      }

      const emailContent = data.email_data;
      console.log('EmailService: Email content generated successfully');

      return emailContent;
    } catch (error) {
      console.error('EmailService: Send setup email failed:', error);
      throw error;
    }
  }

  // Get email status for a user
  static async getEmailStatus(userId: string): Promise<EmailStatus> {
    try {
      const { data, error } = await supabase
        .rpc('check_email_status', { user_profile_id: userId });

      if (error) {
        console.error('EmailService: Status check error:', error);
        throw new Error('Failed to check email status: ' + error.message);
      }

      return data[0] || {
        email_sent: false,
        last_sent_at: null,
        email_count: 0,
        token_expires_at: null,
        token_valid: false
      };
    } catch (error) {
      console.error('EmailService: Get email status failed:', error);
      throw error;
    }
  }

  // Display email content in a modal or console (for development)
  static displayEmailContent(emailContent: EmailContent): void {
    console.log('=== SETUP EMAIL GENERATED ===');
    console.log('To:', emailContent.to_email);
    console.log('Subject:', emailContent.subject);
    console.log('User:', emailContent.user_name);
    console.log('Setup Token:', emailContent.setup_token);
    console.log('');
    console.log('Setup URL:');
    console.log(`https://your-domain.com/setup-password?token=${emailContent.setup_token}&email=${emailContent.to_email}`);
    console.log('');
    console.log('Email Content:');
    console.log(emailContent.text_content);
    console.log('');
    console.log('HTML Content available in emailContent.html_content');
    console.log('');
    console.log('NEXT STEPS:');
    console.log('1. Configure SMTP in Supabase Authentication settings');
    console.log('2. Or integrate with external email service (SendGrid, Mailgun, etc.)');
    console.log('3. Or manually send this information to the user');
  }

  // Create a downloadable email file (for manual sending)
  static downloadEmailContent(emailContent: EmailContent): void {
    const emailText = `
Setup Email for ${emailContent.user_name}
=====================================

To: ${emailContent.to_email}
Subject: ${emailContent.subject}

Setup URL: https://your-domain.com/setup-password?token=${emailContent.setup_token}&email=${emailContent.to_email}

Setup Token: ${emailContent.setup_token}

Email Content:
${emailContent.text_content}

HTML Content:
${emailContent.html_content}
`;

    const blob = new Blob([emailText], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `setup-email-${emailContent.to_email}-${Date.now()}.txt`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  }

  // Copy email content to clipboard
  static async copyEmailToClipboard(emailContent: EmailContent): Promise<void> {
    const setupUrl = `https://your-domain.com/setup-password?token=${emailContent.setup_token}&email=${emailContent.to_email}`;
    
    const emailText = `Subject: ${emailContent.subject}

${emailContent.text_content}

Setup URL: ${setupUrl}`;

    try {
      await navigator.clipboard.writeText(emailText);
      console.log('Email content copied to clipboard');
    } catch (error) {
      console.error('Failed to copy to clipboard:', error);
      // Fallback: select text in a temporary textarea
      const textarea = document.createElement('textarea');
      textarea.value = emailText;
      document.body.appendChild(textarea);
      textarea.select();
      document.execCommand('copy');
      document.body.removeChild(textarea);
    }
  }

  // Generate setup URL for manual sharing
  static generateSetupUrl(email: string, token: string): string {
    return `https://your-domain.com/setup-password?token=${token}&email=${email}`;
  }

  // Validate setup token
  static async validateSetupToken(token: string, email: string): Promise<boolean> {
    try {
      const { data, error } = await supabase
        .from('user_profiles')
        .select('password_setup_token, password_setup_expires_at')
        .eq('email', email)
        .eq('password_setup_token', token)
        .single();

      if (error || !data) {
        return false;
      }

      // Check if token is expired
      const expiresAt = new Date(data.password_setup_expires_at);
      const now = new Date();

      return expiresAt > now;
    } catch (error) {
      console.error('EmailService: Token validation failed:', error);
      return false;
    }
  }

  // Mark email as manually sent (for tracking)
  static async markEmailAsSent(userId: string): Promise<void> {
    try {
      const { error } = await supabase
        .from('user_profiles')
        .update({
          email_status: 'manually_sent',
          setup_email_sent_at: new Date().toISOString()
        })
        .eq('id', userId);

      if (error) {
        console.error('EmailService: Failed to mark email as sent:', error);
      }
    } catch (error) {
      console.error('EmailService: Mark as sent failed:', error);
    }
  }
}

// Email configuration helper
export class EmailConfigHelper {
  // Check if Supabase email is configured
  static async checkSupabaseEmailConfig(): Promise<{ configured: boolean; message: string }> {
    try {
      // Try to get auth settings (this will fail if not properly configured)
      const { data, error } = await supabase.auth.getSession();
      
      if (error) {
        return {
          configured: false,
          message: 'Supabase authentication not properly configured'
        };
      }

      return {
        configured: true,
        message: 'Supabase authentication is configured. Check SMTP settings in dashboard.'
      };
    } catch (error) {
      return {
        configured: false,
        message: 'Unable to check email configuration'
      };
    }
  }

  // Get email configuration instructions
  static getEmailSetupInstructions(): string {
    return `
EMAIL SETUP INSTRUCTIONS:

1. SUPABASE SMTP CONFIGURATION:
   - Go to Supabase Dashboard → Authentication → Settings
   - Scroll to "SMTP Settings"
   - Configure your email provider (Gmail, SendGrid, Mailgun, etc.)
   - Test the configuration

2. ALTERNATIVE EMAIL SERVICES:
   - SendGrid: Add API key and integrate
   - Mailgun: Add API key and domain
   - AWS SES: Configure AWS credentials
   - Gmail SMTP: Use app passwords

3. CURRENT WORKAROUND:
   - Email content is generated and displayed in console
   - Copy the content and send manually
   - Or download the email content as a file

4. PRODUCTION SETUP:
   - Configure proper SMTP in Supabase
   - Or integrate with external email service
   - Update EmailService to actually send emails
`;
  }
}

export default EmailService;
