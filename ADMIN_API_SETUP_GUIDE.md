# 🔧 Admin API Setup Guide - Enable Automatic Email Sending

## 📋 **Step 1: Get Your Service Role Key**

1. **Go to Supabase Dashboard**
   - Visit: https://app.supabase.com
   - Select your project: `ygdaucsngasdutbvmevs`

2. **Navigate to Settings → API**
   - Click on "Settings" in the left sidebar
   - Click on "API" 

3. **Copy the Service Role Key**
   - Look for "Service Role Key" section
   - Click "Copy" next to the service role key
   - **⚠️ IMPORTANT:** This key has admin privileges - keep it secure!

## 📋 **Step 2: Add Service Role Key to Environment**

1. **Open your `.env.local` file**
2. **Replace the placeholder** with your actual service role key:

```env
# Replace this line:
VITE_SUPABASE_SERVICE_ROLE_KEY=your_service_role_key_here

# With your actual service role key:
VITE_SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InlnZGF1Y3NuZ2FzZHV0YnZtZXZzIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MDI0Mzg0NSwiZXhwIjoyMDY1ODE5ODQ1fQ.YOUR_ACTUAL_SERVICE_ROLE_KEY_HERE
```

## 📋 **Step 3: Restart Your Development Server**

1. **Stop your current dev server** (Ctrl+C)
2. **Restart it:**
   ```bash
   npm run dev
   # or
   yarn dev
   ```

## 📋 **Step 4: Test Automatic Email Sending**

1. **Refresh your browser**
2. **Go to User Management**
3. **Click "Resend Setup Email"**
4. **You should now see:**
   - ✅ "Invitation email sent successfully via SMTP!"
   - ✅ Real email delivered to user's inbox
   - ✅ No more manual copying needed

## 🔧 **Step 5: Verify SMTP Configuration**

Make sure your SMTP settings are configured in Supabase:

1. **Go to Supabase Dashboard → Authentication → Settings**
2. **Scroll to "SMTP Settings"**
3. **Verify your SMTP configuration:**
   - ✅ SMTP Host (e.g., smtp.gmail.com)
   - ✅ SMTP Port (e.g., 587)
   - ✅ SMTP User (your email)
   - ✅ SMTP Password (app password)
   - ✅ "Enable Custom SMTP" is checked

## 🧪 **Testing Checklist**

### **Test 1: Admin Connection**
Open browser console and run:
```javascript
// Test admin connection
import('./src/lib/supabaseAdmin.js').then(module => {
  module.AdminEmailService.testAdminConnection().then(result => {
    console.log('Admin test result:', result);
  });
});
```

### **Test 2: Email Sending**
1. **Click "Resend Setup Email"**
2. **Check console for:**
   - ✅ "AdminEmailService: Sending invitation to: [email]"
   - ✅ "Invitation email sent successfully via SMTP"
3. **Check user's email inbox**
4. **Should receive actual email**

## ✅ **Expected Results After Setup**

### **Before (Manual):**
- ❌ 403 Admin API error
- ❌ Email content only in console
- ❌ Manual copying and sending required

### **After (Automatic):**
- ✅ No API errors
- ✅ Real emails sent automatically
- ✅ Users receive emails in inbox
- ✅ Professional Supabase email templates
- ✅ Automatic SMTP delivery

## 🚨 **Troubleshooting**

### **Issue: "Service role key not configured"**
- **Solution:** Make sure you replaced `your_service_role_key_here` with actual key
- **Check:** Restart dev server after adding key

### **Issue: "Admin connection failed"**
- **Solution:** Verify the service role key is correct
- **Check:** Copy the key again from Supabase dashboard

### **Issue: "Invitation failed"**
- **Solution:** Check SMTP settings in Supabase Authentication
- **Check:** Verify SMTP credentials are correct

### **Issue: Still getting 403 errors**
- **Solution:** Make sure you're using the SERVICE ROLE key, not the ANON key
- **Check:** Service role key starts with `eyJ...` and is much longer

## 🔐 **Security Notes**

1. **Service Role Key Security:**
   - ✅ Never commit service role key to version control
   - ✅ Keep `.env.local` in `.gitignore`
   - ✅ Only use service role key on server-side or admin operations
   - ✅ Regenerate key if compromised

2. **Environment Variables:**
   - ✅ Service role key should only be in `.env.local`
   - ✅ Don't expose service role key in client-side code
   - ✅ Use different keys for development and production

## 🎯 **Final Test**

After completing all steps:

1. **Click "Resend Setup Email"**
2. **Should see:** "✅ Invitation email sent successfully! User should check their inbox."
3. **User receives real email** with professional template
4. **User clicks link** → Registration page opens
5. **User completes registration** → Can login immediately

**🚀 Automatic email sending should now be fully functional!**

## 📧 **Email Templates**

With admin API enabled, users will receive:

### **Invitation Email (Professional Supabase Template):**
- Clean, professional design
- "Complete your signup" button
- Secure invitation link
- Automatic SMTP delivery

### **Password Reset Email (Professional Supabase Template):**
- "Reset your password" button
- Secure reset link
- Professional branding
- Automatic SMTP delivery

**The system will automatically choose the right email type based on whether the user has an auth account or not.**
