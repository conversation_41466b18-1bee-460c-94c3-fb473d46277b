-- Fix Supabase Auth Schema Issues
-- This addresses "Database error querying schema" problems

-- 1. Check auth schema integrity
DO $$
DECLARE
    schema_issues TEXT := '';
    missing_columns TEXT := '';
BEGIN
    RAISE NOTICE '';
    RAISE NOTICE '=== CHECKING AUTH SCHEMA INTEGRITY ===';
    
    -- Check if auth.users table exists and has required columns
    IF NOT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_schema = 'auth' AND table_name = 'users') THEN
        schema_issues := schema_issues || 'auth.users table missing; ';
    ELSE
        RAISE NOTICE '✅ auth.users table exists';
        
        -- Check for required columns
        IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_schema = 'auth' AND table_name = 'users' AND column_name = 'aud') THEN
            missing_columns := missing_columns || 'aud, ';
        END IF;
        
        IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_schema = 'auth' AND table_name = 'users' AND column_name = 'role') THEN
            missing_columns := missing_columns || 'role, ';
        END IF;
        
        IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_schema = 'auth' AND table_name = 'users' AND column_name = 'email_confirmed_at') THEN
            missing_columns := missing_columns || 'email_confirmed_at, ';
        END IF;
        
        IF missing_columns != '' THEN
            RAISE NOTICE '❌ Missing columns: %', missing_columns;
        ELSE
            RAISE NOTICE '✅ All required columns present';
        END IF;
    END IF;
    
    -- Check auth.sessions table
    IF NOT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_schema = 'auth' AND table_name = 'sessions') THEN
        schema_issues := schema_issues || 'auth.sessions table missing; ';
    ELSE
        RAISE NOTICE '✅ auth.sessions table exists';
    END IF;
    
    -- Check auth.refresh_tokens table
    IF NOT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_schema = 'auth' AND table_name = 'refresh_tokens') THEN
        schema_issues := schema_issues || 'auth.refresh_tokens table missing; ';
    ELSE
        RAISE NOTICE '✅ auth.refresh_tokens table exists';
    END IF;
    
    IF schema_issues != '' THEN
        RAISE NOTICE '❌ Schema issues found: %', schema_issues;
    ELSE
        RAISE NOTICE '✅ Auth schema appears intact';
    END IF;
    
    RAISE NOTICE '';
END $$;

-- 2. Fix common auth user issues
DO $$
DECLARE
    user_record RECORD;
    fix_count INTEGER := 0;
BEGIN
    RAISE NOTICE '=== FIXING AUTH USER RECORDS ===';
    
    -- Fix users with missing or incorrect aud/role fields
    FOR user_record IN 
        SELECT id, email, aud, role, email_confirmed_at
        FROM auth.users
        WHERE aud IS NULL OR aud != 'authenticated' OR role IS NULL OR role != 'authenticated'
    LOOP
        UPDATE auth.users
        SET 
            aud = 'authenticated',
            role = 'authenticated',
            email_confirmed_at = COALESCE(email_confirmed_at, NOW()),
            updated_at = NOW()
        WHERE id = user_record.id;
        
        RAISE NOTICE '✅ Fixed auth fields for: %', user_record.email;
        fix_count := fix_count + 1;
    END LOOP;
    
    IF fix_count = 0 THEN
        RAISE NOTICE '✅ All auth user records are properly configured';
    ELSE
        RAISE NOTICE '✅ Fixed % auth user records', fix_count;
    END IF;
    
    RAISE NOTICE '';
END $$;

-- 3. Ensure proper auth <NAME_EMAIL>
DO $$
DECLARE
    v_user_id UUID;
    v_password VARCHAR(255) := 'TempPass123!';
BEGIN
    RAISE NOTICE '=== CONFIGURING <EMAIL> ===';
    
    -- Get user ID
    SELECT id INTO v_user_id
    FROM auth.users
    WHERE email = '<EMAIL>';
    
    IF v_user_id IS NULL THEN
        RAISE NOTICE '❌ User not found: <EMAIL>';
        RETURN;
    END IF;
    
    -- Ensure all auth fields are properly set
    UPDATE auth.users
    SET 
        encrypted_password = crypt(v_password, gen_salt('bf')),
        email_confirmed_at = NOW(),
        aud = 'authenticated',
        role = 'authenticated',
        updated_at = NOW(),
        confirmation_token = '',
        recovery_token = '',
        email_change_token_new = '',
        email_change_token_current = '',
        email_change_confirm_status = 0
    WHERE id = v_user_id;
    
    RAISE NOTICE '✅ Updated auth <NAME_EMAIL>';
    RAISE NOTICE '🔑 Password set to: %', v_password;
    
    RAISE NOTICE '';
END $$;

-- 4. Clean up any orphaned sessions or tokens
DO $$
DECLARE
    cleanup_count INTEGER;
BEGIN
    RAISE NOTICE '=== CLEANING UP AUTH ARTIFACTS ===';

    -- Clean up old sessions (check what columns exist first)
    BEGIN
        DELETE FROM auth.sessions WHERE created_at < NOW() - INTERVAL '7 days';
        GET DIAGNOSTICS cleanup_count = ROW_COUNT;
        RAISE NOTICE '✅ Cleaned up % old sessions', cleanup_count;
    EXCEPTION
        WHEN OTHERS THEN
            RAISE NOTICE '⚠️ Could not clean sessions: %', SQLERRM;
    END;

    -- Clean up old refresh tokens
    BEGIN
        DELETE FROM auth.refresh_tokens WHERE created_at < NOW() - INTERVAL '7 days';
        GET DIAGNOSTICS cleanup_count = ROW_COUNT;
        RAISE NOTICE '✅ Cleaned up % old refresh tokens', cleanup_count;
    EXCEPTION
        WHEN OTHERS THEN
            RAISE NOTICE '⚠️ Could not clean refresh tokens: %', SQLERRM;
    END;

    RAISE NOTICE '';
END $$;

-- 5. Final status check
DO $$
DECLARE
    user_record RECORD;
BEGIN
    RAISE NOTICE '=== FINAL AUTH STATUS ===';
    
    SELECT 
        id,
        email,
        encrypted_password IS NOT NULL as has_password,
        email_confirmed_at IS NOT NULL as email_confirmed,
        aud,
        role,
        created_at,
        updated_at
    INTO user_record
    FROM auth.users
    WHERE email = '<EMAIL>';
    
    IF user_record.id IS NULL THEN
        RAISE NOTICE '❌ User not found';
        RETURN;
    END IF;
    
    RAISE NOTICE '<EMAIL> Status:';
    RAISE NOTICE '  ✅ ID: %', user_record.id;
    RAISE NOTICE '  ✅ Has Password: %', user_record.has_password;
    RAISE NOTICE '  ✅ Email Confirmed: %', user_record.email_confirmed;
    RAISE NOTICE '  ✅ Aud: %', user_record.aud;
    RAISE NOTICE '  ✅ Role: %', user_record.role;
    RAISE NOTICE '  ✅ Updated: %', user_record.updated_at;
    
    RAISE NOTICE '';
    RAISE NOTICE '🎯 LOGIN CREDENTIALS:';
    RAISE NOTICE '   Email: <EMAIL>';
    RAISE NOTICE '   Password: TempPass123!';
    RAISE NOTICE '';
    RAISE NOTICE 'If "Database error querying schema" persists, check:';
    RAISE NOTICE '1. Supabase project settings';
    RAISE NOTICE '2. Auth configuration in dashboard';
    RAISE NOTICE '3. RLS policies on auth tables';
    RAISE NOTICE '';
END $$;
