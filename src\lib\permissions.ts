// Role-based permission system
export interface UserRole {
  id: string;
  role_name: string;
  description?: string;
}

export interface UserProfile {
  id: string;
  user_id?: string;
  email: string;
  first_name: string;
  last_name: string;
  role: UserRole;
  department?: string;
  job_title?: string;
  is_active: boolean;
  is_verified: boolean;
  created_by_admin_id?: string;
  requires_password_setup?: boolean;
}

// Permission levels
export enum PermissionLevel {
  ADMIN = 'admin',
  MANAGEMENT = 'management', 
  FINANCIAL = 'financial',
  QS = 'qs',
  BASIC = 'basic'
}

// Role hierarchy (higher roles inherit lower role permissions)
const ROLE_HIERARCHY: Record<string, number> = {
  'client': 1,
  'qs': 2,
  'accountant': 3,
  'management': 4,
  'admin': 5
};

// Permission definitions
export const PERMISSIONS = {
  // User Management
  CREATE_USERS: 'admin',
  EDIT_USERS: 'admin',
  DELETE_USERS: 'admin',
  VIEW_ALL_USERS: 'admin',
  
  // Financial Management
  VIEW_FINANCIAL_DATA: 'financial',
  EDIT_FINANCIAL_DATA: 'financial',
  VIEW_REVENUE: 'financial',
  MANAGE_INVOICES: 'financial',
  VIEW_CASH_FLOW: 'financial',
  MANAGE_ASSETS: 'management',
  
  // Project Management
  CREATE_PROJECTS: 'management',
  EDIT_ALL_PROJECTS: 'management',
  DELETE_PROJECTS: 'management',
  VIEW_ALL_PROJECTS: 'qs',
  EDIT_OWN_PROJECTS: 'qs',
  
  // QS Functions
  MANAGE_QUANTITIES: 'qs',
  CREATE_ESTIMATES: 'qs',
  APPROVE_ESTIMATES: 'management',
  
  // Reports & Analytics
  VIEW_ADVANCED_REPORTS: 'management',
  VIEW_BASIC_REPORTS: 'qs',
  EXPORT_DATA: 'management',
  
  // System Settings
  MANAGE_SYSTEM_SETTINGS: 'admin',
  VIEW_AUDIT_LOGS: 'admin',
  MANAGE_ROLES: 'admin',
  
  // Basic Access
  VIEW_DASHBOARD: 'basic',
  EDIT_OWN_PROFILE: 'basic',
  VIEW_MESSAGES: 'basic',
  SEND_MESSAGES: 'basic'
} as const;

// Check if user has specific permission
export function hasPermission(userRole: string, requiredPermission: keyof typeof PERMISSIONS): boolean {
  const requiredRole = PERMISSIONS[requiredPermission];
  const userLevel = ROLE_HIERARCHY[userRole] || 0;
  const requiredLevel = ROLE_HIERARCHY[requiredRole] || 999;
  
  return userLevel >= requiredLevel;
}

// Check if user can access a specific route/feature
export function canAccess(userRole: string, feature: string): boolean {
  switch (feature) {
    case 'dashboard':
      return hasPermission(userRole, 'VIEW_DASHBOARD');
    
    case 'projects':
      return hasPermission(userRole, 'VIEW_ALL_PROJECTS') || hasPermission(userRole, 'EDIT_OWN_PROJECTS');
    
    case 'financials':
      return hasPermission(userRole, 'VIEW_FINANCIAL_DATA');
    
    case 'reports':
      return hasPermission(userRole, 'VIEW_BASIC_REPORTS');
    
    case 'messages':
      return hasPermission(userRole, 'VIEW_MESSAGES');
    
    case 'settings':
      return hasPermission(userRole, 'MANAGE_SYSTEM_SETTINGS') || hasPermission(userRole, 'EDIT_OWN_PROFILE');
    
    case 'user-management':
      return hasPermission(userRole, 'VIEW_ALL_USERS');
    
    case 'assets':
      return hasPermission(userRole, 'MANAGE_ASSETS');
    
    default:
      return false;
  }
}

// Get filtered navigation items based on user role
export function getFilteredNavigation(userRole: string) {
  const allNavItems = [
    { name: 'Dashboard', path: '/dashboard', permission: 'VIEW_DASHBOARD' },
    { name: 'Projects', path: '/projects', permission: 'VIEW_ALL_PROJECTS' },
    { name: 'Financials', path: '/financials', permission: 'VIEW_FINANCIAL_DATA' },
    { name: 'Reports', path: '/reports', permission: 'VIEW_BASIC_REPORTS' },
    { name: 'Messages', path: '/messages', permission: 'VIEW_MESSAGES' },
    { name: 'Assets', path: '/assets', permission: 'MANAGE_ASSETS' },
    { name: 'Settings', path: '/settings', permission: 'EDIT_OWN_PROFILE' }
  ];

  return allNavItems.filter(item => 
    hasPermission(userRole, item.permission as keyof typeof PERMISSIONS)
  );
}

// Get role-specific dashboard widgets
export function getRoleDashboardConfig(userRole: string) {
  const baseWidgets = ['profile', 'messages'];
  
  switch (userRole) {
    case 'admin':
      return [...baseWidgets, 'user-management', 'system-health', 'financial-overview', 'project-overview', 'reports'];
    
    case 'management':
      return [...baseWidgets, 'financial-overview', 'project-overview', 'team-performance', 'reports'];
    
    case 'accountant':
      return [...baseWidgets, 'financial-overview', 'invoice-tracking', 'cash-flow', 'financial-reports'];
    
    case 'qs':
      return [...baseWidgets, 'project-overview', 'quantity-tracking', 'estimates', 'project-reports'];
    
    case 'client':
      return [...baseWidgets, 'project-status', 'invoices', 'basic-reports'];
    
    default:
      return baseWidgets;
  }
}

// Get financial data access level
export function getFinancialAccessLevel(userRole: string): 'full' | 'limited' | 'none' {
  if (hasPermission(userRole, 'VIEW_FINANCIAL_DATA')) {
    return userRole === 'admin' || userRole === 'management' ? 'full' : 'limited';
  }
  return 'none';
}

// Check if user can see sensitive information
export function canViewSensitiveData(userRole: string, dataType: 'financial' | 'personal' | 'system'): boolean {
  switch (dataType) {
    case 'financial':
      return hasPermission(userRole, 'VIEW_FINANCIAL_DATA');
    
    case 'personal':
      return hasPermission(userRole, 'VIEW_ALL_USERS');
    
    case 'system':
      return hasPermission(userRole, 'MANAGE_SYSTEM_SETTINGS');
    
    default:
      return false;
  }
}

// Get role display information
export function getRoleDisplayInfo(roleName: string) {
  const roleInfo = {
    'admin': {
      label: 'Administrator',
      color: 'bg-red-100 text-red-800',
      description: 'Full system access and user management'
    },
    'management': {
      label: 'Management',
      color: 'bg-purple-100 text-purple-800',
      description: 'Project oversight and financial management'
    },
    'accountant': {
      label: 'Accountant',
      color: 'bg-green-100 text-green-800',
      description: 'Financial data and invoice management'
    },
    'qs': {
      label: 'Quantity Surveyor',
      color: 'bg-blue-100 text-blue-800',
      description: 'Project quantities and cost estimation'
    },
    'client': {
      label: 'Client',
      color: 'bg-gray-100 text-gray-800',
      description: 'Project status and invoice viewing'
    }
  };

  return roleInfo[roleName as keyof typeof roleInfo] || {
    label: roleName,
    color: 'bg-gray-100 text-gray-800',
    description: 'Standard user access'
  };
}
