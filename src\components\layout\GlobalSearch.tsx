import React, { useState, useEffect, useRef } from 'react';
import {
  Search,
  X,
  Clock,
  Building,
  Users,
  FileText,
  DollarSign,
  Calendar,
  Folder,
  User,
  TrendingUp,
  ArrowRight,
  Loader2
} from 'lucide-react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent } from '@/components/ui/card';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';
import {
  Command,
  CommandDialog,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
  CommandSeparator,
} from '@/components/ui/command';
import { ProjectService } from '@/lib/projects';
import { FinancialService } from '@/lib/financials';
import { supabase } from '@/lib/supabase';
import { useToast } from '@/components/ui/use-toast';
import { useNavigate } from 'react-router-dom';

interface SearchResult {
  id: string;
  title: string;
  description: string;
  category: 'projects' | 'employees' | 'documents' | 'financial' | 'clients';
  url: string;
  metadata?: {
    status?: string;
    amount?: number;
    date?: string;
    priority?: string;
  };
}

interface GlobalSearchProps {
  className?: string;
}

const GlobalSearch: React.FC<GlobalSearchProps> = ({ className = '' }) => {
  const { toast } = useToast();
  const navigate = useNavigate();
  const [open, setOpen] = useState(false);
  const [query, setQuery] = useState('');
  const [results, setResults] = useState<SearchResult[]>([]);
  const [loading, setLoading] = useState(false);
  const [recentSearches, setRecentSearches] = useState<string[]>(() => {
    // Load from localStorage if available
    if (typeof window !== 'undefined') {
      const saved = localStorage.getItem('recentSearches');
      return saved ? JSON.parse(saved) : [
        'Downtown Office Complex',
        'John Smith',
        'Budget Report Q4',
        'ABC Construction'
      ];
    }
    return [];
  });

  // Real search function
  const performSearch = async (searchQuery: string): Promise<SearchResult[]> => {
    if (!searchQuery.trim()) return [];

    console.log('🔍 Performing search for:', searchQuery);
    setLoading(true);
    const searchResults: SearchResult[] = [];

    try {
      // Search projects
      console.log('🏗️ Searching projects...');
      const projects = await ProjectService.getProjects();
      console.log('📊 Found projects:', projects.length);

      const projectResults = projects
        .filter(project =>
          project.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
          project.description?.toLowerCase().includes(searchQuery.toLowerCase()) ||
          project.client_name?.toLowerCase().includes(searchQuery.toLowerCase())
        )
        .map(project => ({
          id: `project-${project.id}`,
          title: project.name,
          description: project.description || `Project for ${project.client_name}`,
          category: 'projects' as const,
          url: `/projects/${project.id}`,
          metadata: {
            status: project.status,
            amount: project.budget,
            date: project.end_date
          }
        }));

      console.log('🎯 Filtered project results:', projectResults.length);
      searchResults.push(...projectResults);

      // Search revenue entries (clients)
      const { data: revenues } = await supabase
        .from('revenue_entries')
        .select('*')
        .or(`client_name.ilike.%${searchQuery}%,project_name.ilike.%${searchQuery}%`);

      if (revenues) {
        const clientResults = revenues
          .map(revenue => ({
            id: `client-${revenue.id}`,
            title: revenue.client_name,
            description: `Client for ${revenue.project_name}`,
            category: 'clients' as const,
            url: `/clients/${revenue.client_name.toLowerCase().replace(/\s+/g, '-')}`,
            metadata: {
              amount: revenue.amount,
              date: revenue.revenue_date
            }
          }));

        searchResults.push(...clientResults);
      }

      // Search expenses (financial)
      const { data: expenses } = await supabase
        .from('expenses')
        .select('*')
        .or(`description.ilike.%${searchQuery}%,vendor.ilike.%${searchQuery}%`);

      if (expenses) {
        const expenseResults = expenses
          .map(expense => ({
            id: `expense-${expense.id}`,
            title: expense.description,
            description: `Expense from ${expense.vendor}`,
            category: 'financial' as const,
            url: `/financials/expenses/${expense.id}`,
            metadata: {
              amount: expense.amount,
              date: expense.expense_date
            }
          }));

        searchResults.push(...expenseResults);
      }

      // Add mock employees and documents for now
      const mockEmployees = [
        {
          id: 'emp-1',
          title: 'John Smith',
          description: 'Project Manager - Downtown Office Complex',
          category: 'employees' as const,
          url: '/workforce/john-smith',
          metadata: { status: 'Active' }
        },
        {
          id: 'emp-2',
          title: 'Sarah Johnson',
          description: 'Site Engineer - Bridge Renovation Project',
          category: 'employees' as const,
          url: '/workforce/sarah-johnson',
          metadata: { status: 'Active' }
        },
        {
          id: 'emp-3',
          title: 'Mike Davis',
          description: 'Project Manager - Residential Complex',
          category: 'employees' as const,
          url: '/workforce/mike-davis',
          metadata: { status: 'Active' }
        }
      ];

      const mockDocuments = [
        {
          id: 'doc-1',
          title: 'Q4 Financial Report',
          description: 'Quarterly financial analysis and projections',
          category: 'documents' as const,
          url: '/documents/q4-report',
          metadata: { date: '2024-01-15' }
        },
        {
          id: 'doc-2',
          title: 'Budget Analysis 2024',
          description: 'Annual budget breakdown and allocation',
          category: 'documents' as const,
          url: '/documents/budget-2024',
          metadata: { date: '2024-02-01' }
        },
        {
          id: 'doc-3',
          title: 'Equipment Rental Agreement',
          description: 'Crane rental contract for Q2 2024',
          category: 'documents' as const,
          url: '/documents/equipment-rental',
          metadata: { date: '2024-06-30' }
        }
      ];

      // Filter mock data based on search query
      const filteredEmployees = mockEmployees.filter(emp =>
        emp.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        emp.description.toLowerCase().includes(searchQuery.toLowerCase())
      );

      const filteredDocuments = mockDocuments.filter(doc =>
        doc.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        doc.description.toLowerCase().includes(searchQuery.toLowerCase())
      );

      searchResults.push(...filteredEmployees, ...filteredDocuments);

      // If no results found, add some fallback suggestions
      if (searchResults.length === 0) {
        const fallbackResults: SearchResult[] = [
          {
            id: 'fallback-1',
            title: 'Create New Project',
            description: `Create a new project named "${searchQuery}"`,
            category: 'projects',
            url: '/projects/new',
            metadata: { status: 'New' }
          },
          {
            id: 'fallback-2',
            title: 'Search in Documents',
            description: `Search for "${searchQuery}" in all documents`,
            category: 'documents',
            url: `/documents/search?q=${encodeURIComponent(searchQuery)}`,
            metadata: {}
          },
          {
            id: 'fallback-3',
            title: 'Add to Expenses',
            description: `Add "${searchQuery}" as a new expense`,
            category: 'financial',
            url: '/financials/expenses/new',
            metadata: {}
          }
        ];
        searchResults.push(...fallbackResults);
      }

    } catch (error) {
      console.error('Search error:', error);
      toast({
        title: "Search Error",
        description: "Failed to perform search. Please try again.",
        variant: "destructive",
      });

      // Return fallback results even on error
      return [
        {
          id: 'error-fallback-1',
          title: 'Search Offline',
          description: 'Search functionality is temporarily unavailable',
          category: 'documents',
          url: '/search',
          metadata: {}
        }
      ];
    } finally {
      setLoading(false);
    }

    console.log('✅ Total search results:', searchResults.length);
    return searchResults;
  };

  useEffect(() => {
    const down = (e: KeyboardEvent) => {
      if (e.key === 'k' && (e.metaKey || e.ctrlKey)) {
        e.preventDefault();
        setOpen((open) => !open);
      }
    };

    document.addEventListener('keydown', down);
    return () => document.removeEventListener('keydown', down);
  }, []);

  useEffect(() => {
    const searchTimeout = setTimeout(async () => {
      if (query.length > 0) {
        const searchResults = await performSearch(query);
        setResults(searchResults);
      } else {
        setResults([]);
      }
    }, 300); // Debounce search by 300ms

    return () => clearTimeout(searchTimeout);
  }, [query]);

  const getCategoryIcon = (category: SearchResult['category']) => {
    switch (category) {
      case 'projects':
        return <Building className="h-4 w-4" />;
      case 'employees':
        return <Users className="h-4 w-4" />;
      case 'documents':
        return <FileText className="h-4 w-4" />;
      case 'financial':
        return <DollarSign className="h-4 w-4" />;
      case 'clients':
        return <User className="h-4 w-4" />;
      default:
        return <Search className="h-4 w-4" />;
    }
  };

  const getCategoryColor = (category: SearchResult['category']) => {
    switch (category) {
      case 'projects':
        return 'bg-blue-100 text-blue-800';
      case 'employees':
        return 'bg-green-100 text-green-800';
      case 'documents':
        return 'bg-purple-100 text-purple-800';
      case 'financial':
        return 'bg-yellow-100 text-yellow-800';
      case 'clients':
        return 'bg-pink-100 text-pink-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const handleSelect = (result: SearchResult) => {
    // Add to recent searches
    setRecentSearches(prev => {
      const updated = [result.title, ...prev.filter(s => s !== result.title)].slice(0, 5);
      // Save to localStorage
      if (typeof window !== 'undefined') {
        localStorage.setItem('recentSearches', JSON.stringify(updated));
      }
      return updated;
    });

    setOpen(false);
    setQuery('');

    // Show success message
    toast({
      title: "Opening",
      description: `Navigating to ${result.title}...`,
    });

    // Actually navigate to the result
    try {
      navigate(result.url);
    } catch (error) {
      console.error('Navigation error:', error);
      toast({
        title: "Navigation Error",
        description: "Could not navigate to the selected item.",
        variant: "destructive",
      });
    }
  };

  const clearRecentSearches = () => {
    setRecentSearches([]);
    if (typeof window !== 'undefined') {
      localStorage.removeItem('recentSearches');
    }
  };

  const handleQuickAction = (action: string, path?: string) => {
    setOpen(false);

    if (path) {
      navigate(path);
      toast({
        title: "Quick Action",
        description: `Opening ${action}...`,
      });
    } else {
      // For actions that need dialogs, we'll trigger them via events
      // This is a simple approach - in a real app you might use a state management solution
      const event = new CustomEvent('openQuickAction', { detail: { action } });
      window.dispatchEvent(event);

      toast({
        title: "Quick Action",
        description: `${action} dialog opened.`,
      });
    }
  };

  return (
    <>
      <div className={`relative ${className}`}>
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
        <Input
          placeholder="Search projects, documents, employees..."
          className="pl-10 w-80 bg-gray-50 border-gray-200 focus:bg-white transition-colors cursor-pointer hover:bg-gray-100"
          onClick={() => setOpen(true)}
          readOnly
        />
        <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
          <Badge variant="outline" className="text-xs bg-white">
            ⌘K
          </Badge>
        </div>
      </div>

      <CommandDialog open={open} onOpenChange={setOpen}>
        <CommandInput 
          placeholder="Search projects, documents, employees..." 
          value={query}
          onValueChange={setQuery}
        />
        <CommandList>
          {query.length === 0 && recentSearches.length > 0 && (
            <>
              <CommandGroup heading="Recent Searches">
                {recentSearches.map((search, index) => (
                  <CommandItem
                    key={index}
                    onSelect={() => setQuery(search)}
                    className="flex items-center space-x-2"
                  >
                    <Clock className="h-4 w-4 text-gray-400" />
                    <span>{search}</span>
                  </CommandItem>
                ))}
              </CommandGroup>
              <div className="flex justify-end p-2">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={clearRecentSearches}
                  className="text-xs text-gray-500"
                >
                  Clear recent
                </Button>
              </div>
              <CommandSeparator />
            </>
          )}

          {query.length === 0 && (
            <CommandGroup heading="Quick Actions">
              <CommandItem onSelect={() => handleQuickAction('Create New Project')}>
                <Building className="mr-2 h-4 w-4" />
                <span>Create New Project</span>
              </CommandItem>
              <CommandItem onSelect={() => handleQuickAction('Add Employee')}>
                <Users className="mr-2 h-4 w-4" />
                <span>Add Employee</span>
              </CommandItem>
              <CommandItem onSelect={() => handleQuickAction('Upload Document')}>
                <FileText className="mr-2 h-4 w-4" />
                <span>Upload Document</span>
              </CommandItem>
              <CommandItem onSelect={() => handleQuickAction('Record Expense')}>
                <DollarSign className="mr-2 h-4 w-4" />
                <span>Record Expense</span>
              </CommandItem>
              <CommandItem onSelect={() => handleQuickAction('View Financial Report', '/reports')}>
                <TrendingUp className="mr-2 h-4 w-4" />
                <span>View Financial Report</span>
              </CommandItem>
            </CommandGroup>
          )}

          {query.length > 0 && loading && (
            <CommandGroup>
              <CommandItem className="flex items-center justify-center py-6">
                <Loader2 className="h-4 w-4 animate-spin mr-2" />
                <span>Searching...</span>
              </CommandItem>
            </CommandGroup>
          )}

          {query.length > 0 && !loading && results.length === 0 && (
            <CommandEmpty>No results found for "{query}"</CommandEmpty>
          )}

          {results.length > 0 && (
            <>
              {['projects', 'employees', 'documents', 'financial', 'clients'].map(category => {
                const categoryResults = results.filter(r => r.category === category);
                if (categoryResults.length === 0) return null;

                return (
                  <CommandGroup 
                    key={category} 
                    heading={category.charAt(0).toUpperCase() + category.slice(1)}
                  >
                    {categoryResults.map((result) => (
                      <CommandItem
                        key={result.id}
                        onSelect={() => handleSelect(result)}
                        className="flex items-center justify-between p-3"
                      >
                        <div className="flex items-center space-x-3 flex-1">
                          <div className="flex-shrink-0">
                            {getCategoryIcon(result.category)}
                          </div>
                          <div className="flex-1 min-w-0">
                            <div className="flex items-center space-x-2 mb-1">
                              <p className="text-sm font-medium truncate">
                                {result.title}
                              </p>
                              <Badge 
                                variant="outline" 
                                className={`text-xs ${getCategoryColor(result.category)}`}
                              >
                                {result.category}
                              </Badge>
                            </div>
                            <p className="text-xs text-gray-500 truncate">
                              {result.description}
                            </p>
                            {result.metadata && (
                              <div className="flex items-center space-x-2 mt-1">
                                {result.metadata.status && (
                                  <Badge variant="outline" className="text-xs">
                                    {result.metadata.status}
                                  </Badge>
                                )}
                                {result.metadata.amount && (
                                  <span className="text-xs text-green-600 font-medium">
                                    ${result.metadata.amount.toLocaleString()}
                                  </span>
                                )}
                                {result.metadata.date && (
                                  <span className="text-xs text-gray-500">
                                    {new Date(result.metadata.date).toLocaleDateString()}
                                  </span>
                                )}
                              </div>
                            )}
                          </div>
                        </div>
                        <ArrowRight className="h-4 w-4 text-gray-400" />
                      </CommandItem>
                    ))}
                  </CommandGroup>
                );
              })}
            </>
          )}
        </CommandList>
      </CommandDialog>
    </>
  );
};

export default GlobalSearch;
