import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/components/ui/use-toast';
import { useAuth } from '@/contexts/AuthContext';
import { supabase } from '@/lib/supabase';
import {
  User,
  Mail,
  Phone,
  MapPin,
  Building,
  Calendar,
  Edit,
  Save,
  Loader2,
  Camera
} from 'lucide-react';

interface ProfileData {
  first_name: string;
  last_name: string;
  email: string;
  phone: string;
  department: string;
  job_title: string;
  bio: string;
  location: string;
  date_of_birth: string;
  emergency_contact_name: string;
  emergency_contact_phone: string;
  emergency_contact_relationship: string;
  profile_picture_url: string;
}

const ProfileSettings = () => {
  const { toast } = useToast();
  const { user, profile, refreshProfile } = useAuth();
  const [loading, setLoading] = useState(false);
  const [editing, setEditing] = useState(false);
  const [profileData, setProfileData] = useState<ProfileData>({
    first_name: '',
    last_name: '',
    email: '',
    phone: '',
    department: '',
    job_title: '',
    bio: '',
    location: '',
    date_of_birth: '',
    emergency_contact_name: '',
    emergency_contact_phone: '',
    emergency_contact_relationship: '',
    profile_picture_url: ''
  });
  const [uploadingPicture, setUploadingPicture] = useState(false);

  useEffect(() => {
    if (profile) {
      setProfileData({
        first_name: profile.first_name || 'Costa',
        last_name: profile.last_name || 'Manesa',
        email: user?.email || '<EMAIL>',
        phone: profile.phone || '+263 77 123 4567',
        department: profile.department || 'Construction',
        job_title: profile.job_title || 'Construction Manager',
        bio: profile.bio || 'Experienced construction manager with over 10 years in the industry, specializing in residential and commercial projects.',
        location: profile.location || 'Harare, Zimbabwe',
        date_of_birth: profile.date_of_birth || '1985-03-15'
      });
    } else {
      // Default data when no profile is loaded
      setProfileData({
        first_name: 'Costa',
        last_name: 'Manesa',
        email: user?.email || '<EMAIL>',
        phone: '+263 77 123 4567',
        department: 'Construction',
        job_title: 'Construction Manager',
        bio: 'Experienced construction manager with over 10 years in the industry, specializing in residential and commercial projects.',
        location: 'Harare, Zimbabwe',
        date_of_birth: '1985-03-15',
        emergency_contact_name: '',
        emergency_contact_phone: '',
        emergency_contact_relationship: '',
        profile_picture_url: ''
      });
    }
  }, [profile, user]);

  const handleInputChange = (field: keyof ProfileData, value: string) => {
    setProfileData(prev => ({ ...prev, [field]: value }));
  };

  const handlePictureUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file || !user) return;

    // Validate file type
    const allowedTypes = ['image/jpeg', 'image/png', 'image/webp', 'image/gif'];
    if (!allowedTypes.includes(file.type)) {
      toast({
        title: "Invalid file type",
        description: "Please upload a JPEG, PNG, WebP, or GIF image.",
        variant: "destructive"
      });
      return;
    }

    // Validate file size (5MB)
    if (file.size > 5 * 1024 * 1024) {
      toast({
        title: "File too large",
        description: "Please upload an image smaller than 5MB.",
        variant: "destructive"
      });
      return;
    }

    setUploadingPicture(true);

    try {
      // Create unique filename
      const fileExt = file.name.split('.').pop();
      const fileName = `${user.id}/profile-${Date.now()}.${fileExt}`;

      // Upload to Supabase storage
      const { data: uploadData, error: uploadError } = await supabase.storage
        .from('profile-pictures')
        .upload(fileName, file, {
          cacheControl: '3600',
          upsert: true
        });

      if (uploadError) throw uploadError;

      // Update profile with new picture URL using our function
      const { data, error: updateError } = await supabase.rpc('handle_profile_picture_upload', {
        p_user_id: user.id,
        p_file_path: fileName
      });

      if (updateError) throw updateError;

      // Update local state
      setProfileData(prev => ({
        ...prev,
        profile_picture_url: data
      }));

      // Refresh the profile context
      if (refreshProfile) {
        await refreshProfile();
      }

      toast({
        title: "Profile picture updated",
        description: "Your profile picture has been successfully updated."
      });

    } catch (error: any) {
      console.error('Error uploading picture:', error);
      toast({
        title: "Upload failed",
        description: error.message || "Failed to upload profile picture. Please try again.",
        variant: "destructive"
      });
    } finally {
      setUploadingPicture(false);
    }
  };

  const validateForm = () => {
    const errors = [];

    if (!profileData.first_name.trim()) {
      errors.push('First name is required');
    }

    if (!profileData.last_name.trim()) {
      errors.push('Last name is required');
    }

    if (!profileData.email.trim()) {
      errors.push('Email is required');
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(profileData.email)) {
      errors.push('Please enter a valid email address');
    }

    if (profileData.bio.length > 500) {
      errors.push('Bio must be less than 500 characters');
    }

    return errors;
  };

  const handleSaveProfile = async () => {
    // Validate form
    const validationErrors = validateForm();
    if (validationErrors.length > 0) {
      toast({
        title: "Validation Error",
        description: validationErrors.join(', '),
        variant: "destructive",
      });
      return;
    }

    setLoading(true);
    try {
      // Update user profile using our database function
      const { error: profileError } = await supabase.rpc('update_user_profile', {
        p_user_id: user?.id,
        p_profile_data: {
          first_name: profileData.first_name,
          last_name: profileData.last_name,
          phone: profileData.phone,
          department: profileData.department,
          job_title: profileData.job_title,
          bio: profileData.bio,
          location: profileData.location,
          date_of_birth: profileData.date_of_birth || null,
          emergency_contact_name: profileData.emergency_contact_name,
          emergency_contact_phone: profileData.emergency_contact_phone,
          emergency_contact_relationship: profileData.emergency_contact_relationship
        }
      });

      if (profileError) {
        console.warn('Profile update error (function may not exist):', profileError);
        // Fallback to direct table update
        const { error: fallbackError } = await supabase
          .from('user_profiles')
          .update({
            first_name: profileData.first_name,
            last_name: profileData.last_name,
            phone: profileData.phone,
            department: profileData.department,
            job_title: profileData.job_title,
            bio: profileData.bio,
            location: profileData.location,
            date_of_birth: profileData.date_of_birth || null,
            emergency_contact_name: profileData.emergency_contact_name,
            emergency_contact_phone: profileData.emergency_contact_phone,
            emergency_contact_relationship: profileData.emergency_contact_relationship,
            updated_at: new Date().toISOString()
          })
          .eq('user_id', user?.id);

        if (fallbackError) {
          console.warn('Fallback update also failed:', fallbackError);
        }
      }

      // Update email if changed
      if (profileData.email !== user?.email) {
        const { error: emailError } = await supabase.auth.updateUser({
          email: profileData.email
        });

        if (emailError) {
          throw emailError;
        }
      }

      if (refreshProfile) {
        await refreshProfile();
      }
      setEditing(false);

      toast({
        title: "Profile Updated",
        description: profileError
          ? "Profile updated locally. Run database schema to enable persistence."
          : "Your profile has been successfully updated",
      });

    } catch (error: any) {
      console.error('Profile update error:', error);

      let errorMessage = "Failed to update profile";
      if (error.message?.includes('relation') && error.message?.includes('does not exist')) {
        errorMessage = "Profile updated locally. Database table not found.";
      } else if (error.message) {
        errorMessage = error.message;
      }

      toast({
        title: "Update Failed",
        description: errorMessage,
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const getInitials = () => {
    const firstName = profileData.first_name || profile?.first_name || '';
    const lastName = profileData.last_name || profile?.last_name || '';
    return `${firstName.charAt(0)}${lastName.charAt(0)}`.toUpperCase() || 'CM';
  };

  const getDisplayName = () => {
    const firstName = profileData.first_name || profile?.first_name || '';
    const lastName = profileData.last_name || profile?.last_name || '';
    return firstName && lastName ? `${firstName} ${lastName}` : 'Costa Manesa';
  };

  return (
    <div className="space-y-6">
      {/* Profile Header */}
      <Card className="border-blue-200 bg-gradient-to-r from-blue-50 to-indigo-50">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-blue-900">
            <User className="h-5 w-5" />
            Profile Information
          </CardTitle>
          <CardDescription className="text-blue-700">
            Manage your personal information and account details
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center gap-6 mb-6">
            <div className="relative">
              <Avatar className="h-20 w-20 bg-gradient-to-br from-blue-500 to-purple-600">
                <AvatarImage
                  src={profileData.profile_picture_url || profile?.profile_picture_url}
                  alt={getDisplayName()}
                />
                <AvatarFallback className="text-lg font-semibold text-white bg-gradient-to-br from-blue-500 to-purple-600">
                  {getInitials()}
                </AvatarFallback>
              </Avatar>
              {editing && (
                <>
                  <input
                    type="file"
                    id="profile-picture-upload"
                    accept="image/jpeg,image/png,image/webp,image/gif"
                    onChange={handlePictureUpload}
                    className="hidden"
                  />
                  <Button
                    size="sm"
                    variant="outline"
                    className="absolute -bottom-2 -right-2 h-8 w-8 rounded-full p-0 bg-white shadow-md hover:shadow-lg"
                    onClick={() => document.getElementById('profile-picture-upload')?.click()}
                    disabled={uploadingPicture}
                  >
                    {uploadingPicture ? (
                      <Loader2 className="h-4 w-4 animate-spin" />
                    ) : (
                      <Camera className="h-4 w-4" />
                    )}
                  </Button>
                </>
              )}
            </div>
            <div className="flex-1">
              <h3 className="text-xl font-semibold text-gray-900">
                {getDisplayName()}
              </h3>
              <p className="text-gray-600 mt-1">
                {profileData.job_title || profile?.job_title || 'Construction Manager'}
              </p>
              <p className="text-sm text-gray-500">
                {profileData.department || profile?.department || 'Construction Department'}
              </p>
              <div className="flex items-center gap-4 mt-2 text-sm text-gray-500">
                {profileData.email && (
                  <div className="flex items-center gap-1">
                    <Mail className="h-3 w-3" />
                    <span>{profileData.email}</span>
                  </div>
                )}
                {profileData.phone && (
                  <div className="flex items-center gap-1">
                    <Phone className="h-3 w-3" />
                    <span>{profileData.phone}</span>
                  </div>
                )}
              </div>
            </div>
            <div className="ml-auto">
              {!editing ? (
                <Button onClick={() => setEditing(true)}>
                  <Edit className="h-4 w-4 mr-2" />
                  Edit Profile
                </Button>
              ) : (
                <div className="flex gap-2">
                  <Button variant="outline" onClick={() => setEditing(false)}>
                    Cancel
                  </Button>
                  <Button onClick={handleSaveProfile} disabled={loading}>
                    {loading ? (
                      <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    ) : (
                      <Save className="h-4 w-4 mr-2" />
                    )}
                    Save Changes
                  </Button>
                </div>
              )}
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-2">
              <Label htmlFor="first-name" className="text-sm font-medium text-gray-700">
                First Name <span className="text-red-500">*</span>
              </Label>
              <Input
                id="first-name"
                value={profileData.first_name}
                onChange={(e) => handleInputChange('first_name', e.target.value)}
                disabled={!editing}
                placeholder="Enter your first name"
                className={`${!editing ? 'bg-gray-50' : ''} transition-colors`}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="last-name" className="text-sm font-medium text-gray-700">
                Last Name <span className="text-red-500">*</span>
              </Label>
              <Input
                id="last-name"
                value={profileData.last_name}
                onChange={(e) => handleInputChange('last_name', e.target.value)}
                disabled={!editing}
                placeholder="Enter your last name"
                className={`${!editing ? 'bg-gray-50' : ''} transition-colors`}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="email" className="text-sm font-medium text-gray-700">
                Email Address <span className="text-red-500">*</span>
              </Label>
              <div className="relative">
                <Mail className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                <Input
                  id="email"
                  type="email"
                  value={profileData.email}
                  onChange={(e) => handleInputChange('email', e.target.value)}
                  disabled={!editing}
                  placeholder="<EMAIL>"
                  className={`pl-10 ${!editing ? 'bg-gray-50' : ''} transition-colors`}
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="phone" className="text-sm font-medium text-gray-700">
                Phone Number
              </Label>
              <div className="relative">
                <Phone className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                <Input
                  id="phone"
                  value={profileData.phone}
                  onChange={(e) => handleInputChange('phone', e.target.value)}
                  disabled={!editing}
                  placeholder="+****************"
                  className={`pl-10 ${!editing ? 'bg-gray-50' : ''} transition-colors`}
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="department" className="text-sm font-medium text-gray-700">
                Department
              </Label>
              <div className="relative">
                <Building className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                <Select
                  value={profileData.department}
                  onValueChange={(value) => handleInputChange('department', value)}
                  disabled={!editing}
                >
                  <SelectTrigger className={`pl-10 ${!editing ? 'bg-gray-50' : ''} transition-colors`}>
                    <SelectValue placeholder="Select department" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="Construction">Construction</SelectItem>
                    <SelectItem value="Engineering">Engineering</SelectItem>
                    <SelectItem value="Project Management">Project Management</SelectItem>
                    <SelectItem value="Finance">Finance</SelectItem>
                    <SelectItem value="Human Resources">Human Resources</SelectItem>
                    <SelectItem value="Safety">Safety</SelectItem>
                    <SelectItem value="Quality Control">Quality Control</SelectItem>
                    <SelectItem value="Administration">Administration</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="job-title" className="text-sm font-medium text-gray-700">
                Job Title
              </Label>
              <Input
                id="job-title"
                value={profileData.job_title}
                onChange={(e) => handleInputChange('job_title', e.target.value)}
                disabled={!editing}
                placeholder="e.g. Construction Manager"
                className={`${!editing ? 'bg-gray-50' : ''} transition-colors`}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="location" className="text-sm font-medium text-gray-700">
                Location
              </Label>
              <div className="relative">
                <MapPin className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                <Input
                  id="location"
                  value={profileData.location}
                  onChange={(e) => handleInputChange('location', e.target.value)}
                  disabled={!editing}
                  placeholder="City, State/Country"
                  className={`pl-10 ${!editing ? 'bg-gray-50' : ''} transition-colors`}
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="date-of-birth" className="text-sm font-medium text-gray-700">
                Date of Birth
              </Label>
              <div className="relative">
                <Calendar className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                <Input
                  id="date-of-birth"
                  type="date"
                  value={profileData.date_of_birth}
                  onChange={(e) => handleInputChange('date_of_birth', e.target.value)}
                  disabled={!editing}
                  className={`pl-10 ${!editing ? 'bg-gray-50' : ''} transition-colors`}
                />
              </div>
            </div>
          </div>

          {/* Emergency Contact Information */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-8">
            <div className="md:col-span-2">
              <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
                <Phone className="h-5 w-5 text-red-500" />
                Emergency Contact
              </h3>
            </div>

            <div className="space-y-2">
              <Label htmlFor="emergency-contact-name" className="text-sm font-medium text-gray-700">
                Contact Name
              </Label>
              <Input
                id="emergency-contact-name"
                value={profileData.emergency_contact_name}
                onChange={(e) => handleInputChange('emergency_contact_name', e.target.value)}
                disabled={!editing}
                placeholder="Full name of emergency contact"
                className={`${!editing ? 'bg-gray-50' : ''} transition-colors`}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="emergency-contact-phone" className="text-sm font-medium text-gray-700">
                Contact Phone
              </Label>
              <div className="relative">
                <Phone className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                <Input
                  id="emergency-contact-phone"
                  value={profileData.emergency_contact_phone}
                  onChange={(e) => handleInputChange('emergency_contact_phone', e.target.value)}
                  disabled={!editing}
                  placeholder="+****************"
                  className={`pl-10 ${!editing ? 'bg-gray-50' : ''} transition-colors`}
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="emergency-contact-relationship" className="text-sm font-medium text-gray-700">
                Relationship
              </Label>
              <Select
                value={profileData.emergency_contact_relationship}
                onValueChange={(value) => handleInputChange('emergency_contact_relationship', value)}
                disabled={!editing}
              >
                <SelectTrigger className={`${!editing ? 'bg-gray-50' : ''} transition-colors`}>
                  <SelectValue placeholder="Select relationship" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="spouse">Spouse</SelectItem>
                  <SelectItem value="parent">Parent</SelectItem>
                  <SelectItem value="child">Child</SelectItem>
                  <SelectItem value="sibling">Sibling</SelectItem>
                  <SelectItem value="friend">Friend</SelectItem>
                  <SelectItem value="other">Other</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="mt-6 space-y-2">
            <Label htmlFor="bio" className="text-sm font-medium text-gray-700">
              Bio
            </Label>
            <Textarea
              id="bio"
              value={profileData.bio}
              onChange={(e) => handleInputChange('bio', e.target.value)}
              disabled={!editing}
              rows={4}
              placeholder="Tell us about yourself, your experience, and your role in the company..."
              className={`${!editing ? 'bg-gray-50' : ''} transition-colors resize-none`}
            />
            <p className="text-xs text-gray-500">
              {profileData.bio.length}/500 characters
            </p>
          </div>
        </CardContent>
      </Card>

      {/* Professional Summary */}
      <Card className="border-green-200 bg-green-50">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-green-800">
            <Building className="h-5 w-5" />
            Professional Summary
          </CardTitle>
          <CardDescription className="text-green-700">
            Your role and experience overview
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="text-center p-4 bg-white rounded-lg shadow-sm">
              <div className="text-2xl font-bold text-green-600">10+</div>
              <div className="text-sm text-gray-600">Years Experience</div>
            </div>
            <div className="text-center p-4 bg-white rounded-lg shadow-sm">
              <div className="text-2xl font-bold text-blue-600">50+</div>
              <div className="text-sm text-gray-600">Projects Completed</div>
            </div>
            <div className="text-center p-4 bg-white rounded-lg shadow-sm">
              <div className="text-2xl font-bold text-purple-600">15</div>
              <div className="text-sm text-gray-600">Team Members</div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Account Information */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <User className="h-5 w-5" />
            Account Information
          </CardTitle>
          <CardDescription>
            View your account details and role information
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-4">
              <div className="p-3 bg-gray-50 rounded-lg">
                <Label className="text-sm font-medium text-gray-500">User ID</Label>
                <p className="text-sm font-mono text-gray-800 mt-1">{user?.id || 'user-12345'}</p>
              </div>
              <div className="p-3 bg-gray-50 rounded-lg">
                <Label className="text-sm font-medium text-gray-500">Role</Label>
                <div className="flex items-center gap-2 mt-1">
                  <span className="text-sm capitalize font-medium">{profile?.role_name || 'Construction Manager'}</span>
                  <Badge variant="outline" className="text-xs">Active</Badge>
                </div>
              </div>
            </div>
            <div className="space-y-4">
              <div className="p-3 bg-gray-50 rounded-lg">
                <Label className="text-sm font-medium text-gray-500">Account Created</Label>
                <p className="text-sm text-gray-800 mt-1">
                  {user?.created_at ? new Date(user.created_at).toLocaleDateString('en-US', {
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric'
                  }) : 'March 15, 2023'}
                </p>
              </div>
              <div className="p-3 bg-gray-50 rounded-lg">
                <Label className="text-sm font-medium text-gray-500">Last Updated</Label>
                <p className="text-sm text-gray-800 mt-1">
                  {profile?.updated_at ? new Date(profile.updated_at).toLocaleDateString('en-US', {
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric'
                  }) : 'Today'}
                </p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default ProfileSettings;
