
import React, { useState, useEffect } from 'react';
import Layout from '@/components/layout/Layout';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { FileText, Plus, Edit, Trash2, DollarSign, Download, Printer, Search, Filter, AlertCircle, Wifi, WifiOff, Refresh<PERSON>w, CheckCircle, XCircle, Calendar, Eye, Copy, Send, Building } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { useToast } from '@/components/ui/use-toast';
import { useDocuments } from '@/hooks/useDocuments';
import { DocumentService, DOCUMENT_STATUSES, COMMON_UNITS, DEFAULT_COMPANY_INFO, DocumentItem } from '@/lib/documents';
import { PDFGenerator } from '@/lib/pdfGenerator';
import CompanySettings from '@/components/CompanySettings';
import DocumentPreview from '@/components/DocumentPreview';
import { CompanySettingsService } from '@/lib/companySettings';
import { CreatorInfo } from '@/components/ui/CreatorInfo';

const Documents = () => {
  const { toast } = useToast();

  // Use the custom hook for document management with Supabase
  const {
    documents,
    loading,
    error,
    addDocument,
    updateDocument,
    deleteDocument,
    refreshDocuments,
    isOnline,
    syncStatus
  } = useDocuments();

  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [editingDocument, setEditingDocument] = useState<any>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedType, setSelectedType] = useState('all');
  const [selectedStatus, setSelectedStatus] = useState('all');
  const [documentType, setDocumentType] = useState<'quotation' | 'invoice'>('quotation');

  // Form data
  const [formData, setFormData] = useState({
    title: '',
    client_name: '',
    client_email: '',
    client_address: '',
    client_phone: '',
    project_name: '',
    issue_date: new Date().toISOString().split('T')[0],
    due_date: '',
    status: 'Draft',
    tax_rate: '15',
    discount_rate: '0',
    notes: '',
    terms_conditions: ''
  });

  const [items, setItems] = useState<DocumentItem[]>([
    {
      id: '1',
      description: '',
      quantity: 1,
      unit: 'pcs',
      unit_price: 0,
      total: 0
    }
  ]);

  const [companyInfo, setCompanyInfo] = useState(DEFAULT_COMPANY_INFO);

  // Load company settings on component mount
  useEffect(() => {
    const loadCompanySettings = async () => {
      try {
        const settings = await CompanySettingsService.getCompanySettings();
        setCompanyInfo(settings);
      } catch (error) {
        console.error('Error loading company settings:', error);
      }
    };

    loadCompanySettings();
  }, []);
  const [formErrors, setFormErrors] = useState<Record<string, string>>({});

  // Filter documents based on search and filters
  const filteredDocuments = documents.filter(document => {
    const matchesSearch = document.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         document.client_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         document.project_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         document.document_number.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesType = selectedType === 'all' || document.type === selectedType;
    const matchesStatus = selectedStatus === 'all' || document.status === selectedStatus;

    return matchesSearch && matchesType && matchesStatus;
  });

  const totalValue = filteredDocuments.reduce((sum, document) => sum + document.total_amount, 0);
  const quotations = filteredDocuments.filter(d => d.type === 'quotation');
  const invoices = filteredDocuments.filter(d => d.type === 'invoice');

  // Calculate totals for current items
  const calculateTotals = () => {
    const subtotal = items.reduce((sum, item) => sum + item.total, 0);
    const taxRate = parseFloat(formData.tax_rate) || 0;
    const discountRate = parseFloat(formData.discount_rate) || 0;

    const discountAmount = (subtotal * discountRate) / 100;
    const taxableAmount = subtotal - discountAmount;
    const taxAmount = (taxableAmount * taxRate) / 100;
    const totalAmount = taxableAmount + taxAmount;

    return {
      subtotal,
      discountAmount,
      taxAmount,
      totalAmount
    };
  };

  // Update item total when quantity or unit price changes
  const updateItemTotal = (index: number, field: 'quantity' | 'unit_price', value: number) => {
    const updatedItems = [...items];
    updatedItems[index][field] = value;
    updatedItems[index].total = updatedItems[index].quantity * updatedItems[index].unit_price;
    setItems(updatedItems);
  };

  // Add new item
  const addItem = () => {
    const newItem: DocumentItem = {
      id: Date.now().toString(),
      description: '',
      quantity: 1,
      unit: 'pcs',
      unit_price: 0,
      total: 0
    };
    setItems([...items, newItem]);
  };

  // Remove item
  const removeItem = (index: number) => {
    if (items.length > 1) {
      setItems(items.filter((_, i) => i !== index));
    }
  };

  // Form validation
  const validateForm = () => {
    const errors: Record<string, string> = {};

    if (!formData.title.trim()) errors.title = 'Title is required';
    if (!formData.client_name.trim()) errors.client_name = 'Client name is required';
    if (!formData.client_email.trim()) errors.client_email = 'Client email is required';
    if (!formData.project_name.trim()) errors.project_name = 'Project name is required';
    if (!formData.issue_date) errors.issue_date = 'Issue date is required';
    if (!formData.due_date) errors.due_date = 'Due date is required';

    // Validate items
    const hasValidItems = items.some(item =>
      item.description.trim() && item.quantity > 0 && item.unit_price > 0
    );
    if (!hasValidItems) errors.items = 'At least one valid item is required';

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const getStatusColor = (status: string, type: string) => {
    if (type === 'quotation') {
      switch (status) {
        case 'Sent':
          return 'bg-blue-100 text-blue-800';
        case 'Accepted':
          return 'bg-green-100 text-green-800';
        case 'Rejected':
          return 'bg-red-100 text-red-800';
        case 'Expired':
          return 'bg-gray-100 text-gray-800';
        case 'Draft':
          return 'bg-yellow-100 text-yellow-800';
        default:
          return 'bg-gray-100 text-gray-800';
      }
    } else {
      switch (status) {
        case 'Paid':
          return 'bg-green-100 text-green-800';
        case 'Sent':
          return 'bg-blue-100 text-blue-800';
        case 'Overdue':
          return 'bg-red-100 text-red-800';
        case 'Cancelled':
          return 'bg-gray-100 text-gray-800';
        case 'Draft':
          return 'bg-yellow-100 text-yellow-800';
        default:
          return 'bg-gray-100 text-gray-800';
      }
    }
  };

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'quotation':
        return 'bg-purple-100 text-purple-800';
      case 'invoice':
        return 'bg-orange-100 text-orange-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      toast({
        title: "Validation Error",
        description: "Please fix the errors in the form",
        variant: "destructive",
      });
      return;
    }

    try {
      const totals = calculateTotals();
      const documentNumber = editingDocument ?
        editingDocument.document_number :
        DocumentService.generateDocumentNumber(documentType);

      const documentData = {
        type: documentType,
        document_number: documentNumber,
        title: formData.title.trim(),
        client_name: formData.client_name.trim(),
        client_email: formData.client_email.trim(),
        client_address: formData.client_address.trim(),
        client_phone: formData.client_phone.trim(),
        project_name: formData.project_name.trim(),
        issue_date: formData.issue_date,
        due_date: formData.due_date,
        status: formData.status,
        items: items.filter(item => item.description.trim()),
        subtotal: totals.subtotal,
        tax_rate: parseFloat(formData.tax_rate),
        tax_amount: totals.taxAmount,
        discount_rate: parseFloat(formData.discount_rate),
        discount_amount: totals.discountAmount,
        total_amount: totals.totalAmount,
        notes: formData.notes.trim(),
        terms_conditions: formData.terms_conditions.trim(),
        company_info: companyInfo
      };

      if (editingDocument) {
        await updateDocument(editingDocument.id, documentData);
        toast({
          title: "Success",
          description: `${documentType === 'quotation' ? 'Quotation' : 'Invoice'} updated successfully`,
        });
      } else {
        await addDocument(documentData);
        toast({
          title: "Success",
          description: `${documentType === 'quotation' ? 'Quotation' : 'Invoice'} created successfully`,
        });
      }

      resetForm();
      setIsDialogOpen(false);
    } catch (error) {
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "An error occurred",
        variant: "destructive",
      });
    }
  };

  const handleEdit = (document: any) => {
    setEditingDocument(document);
    setDocumentType(document.type);
    setFormData({
      title: document.title,
      client_name: document.client_name,
      client_email: document.client_email,
      client_address: document.client_address,
      client_phone: document.client_phone,
      project_name: document.project_name,
      issue_date: document.issue_date,
      due_date: document.due_date,
      status: document.status,
      tax_rate: document.tax_rate.toString(),
      discount_rate: document.discount_rate.toString(),
      notes: document.notes || '',
      terms_conditions: document.terms_conditions || ''
    });
    setItems(document.items || []);
    setCompanyInfo(document.company_info || DEFAULT_COMPANY_INFO);
    setIsDialogOpen(true);
  };

  const handleDelete = async (id: string) => {
    try {
      const document = documents.find(d => d.id === id);
      await deleteDocument(id);
      toast({
        title: "Success",
        description: `${document?.type === 'quotation' ? 'Quotation' : 'Invoice'} deleted successfully`,
      });
    } catch (error) {
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to delete document",
        variant: "destructive",
      });
    }
  };

  const resetForm = () => {
    setFormData({
      title: '',
      client_name: '',
      client_email: '',
      client_address: '',
      client_phone: '',
      project_name: '',
      issue_date: new Date().toISOString().split('T')[0],
      due_date: '',
      status: 'Draft',
      tax_rate: '15',
      discount_rate: '0',
      notes: '',
      terms_conditions: ''
    });
    setItems([{
      id: '1',
      description: '',
      quantity: 1,
      unit: 'pcs',
      unit_price: 0,
      total: 0
    }]);
    setCompanyInfo(DEFAULT_COMPANY_INFO);
    setFormErrors({});
    setEditingDocument(null);
    setDocumentType('quotation');
  };

  // Export functionality
  const exportToCSV = async () => {
    try {
      const csvContent = await DocumentService.exportToCSV();
      const blob = new Blob([csvContent], { type: 'text/csv' });
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `documents-${new Date().toISOString().split('T')[0]}.csv`;
      a.click();
      window.URL.revokeObjectURL(url);

      toast({
        title: "Success",
        description: "Documents exported successfully",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to export data",
        variant: "destructive",
      });
    }
  };

  // PDF Download functionality
  const handleDownloadPDF = async (document: any) => {
    try {
      console.log('Starting PDF generation for document:', document.document_number);
      console.log('Current company info:', companyInfo);
      console.log('Document company info:', document.company_info);

      // Always use the latest company info from settings
      const documentWithCompany = {
        ...document,
        company_info: companyInfo // Always use current company settings
      };

      console.log('Final document with company info:', documentWithCompany.company_info);

      await PDFGenerator.generateDocumentPDF(documentWithCompany);

      toast({
        title: "Success",
        description: `PDF for ${document.type} ${document.document_number} downloaded successfully`,
      });
    } catch (error) {
      console.error('PDF generation error:', error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to generate PDF",
        variant: "destructive",
      });
    }
  };

  // Print functionality
  const handlePrint = () => {
    window.print();
  };

  // Update company info
  const handleCompanyInfoUpdate = async (newCompanyInfo: any) => {
    setCompanyInfo(newCompanyInfo);

    try {
      const success = await CompanySettingsService.saveCompanySettings(newCompanyInfo);

      if (success) {
        toast({
          title: "Company Settings Saved",
          description: "Your company information has been saved to the database and will be used in all PDFs.",
        });
      } else {
        toast({
          title: "Settings Saved Locally",
          description: "Company information saved locally. Database sync may have failed.",
          variant: "destructive",
        });
      }
    } catch (error) {
      toast({
        title: "Error Saving Settings",
        description: "Could not save to database, but settings are saved locally.",
        variant: "destructive",
      });
    }
  };

  // Clear all filters
  const clearFilters = () => {
    setSearchTerm('');
    setSelectedType('all');
    setSelectedStatus('all');
  };

  // Get sync status icon
  const getSyncStatusIcon = () => {
    switch (syncStatus) {
      case 'syncing':
        return <RefreshCw className="w-4 h-4 animate-spin text-blue-500" />;
      case 'synced':
        return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'offline':
        return <WifiOff className="w-4 h-4 text-orange-500" />;
      case 'error':
        return <XCircle className="w-4 h-4 text-red-500" />;
      default:
        return <Wifi className="w-4 h-4 text-gray-500" />;
    }
  };

  return (
    <Layout>
      <div className="space-y-6">
        {/* Connection Status Alert */}
        {error && (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              {error} - Using offline mode. Changes will sync when connection is restored.
            </AlertDescription>
          </Alert>
        )}

        {/* Loading State */}
        {loading && (
          <Alert>
            <RefreshCw className="h-4 w-4 animate-spin" />
            <AlertDescription>
              Loading documents...
            </AlertDescription>
          </Alert>
        )}

        {/* Empty State Alert */}
        {!loading && documents.length === 0 && (
          <Alert>
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              Welcome to Document Management! Start by creating your first quotation or invoice.
            </AlertDescription>
          </Alert>
        )}

        {/* Header Section */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="w-12 h-12 bg-gradient-to-r from-purple-600 to-orange-600 rounded-xl flex items-center justify-center">
              <FileText className="w-6 h-6 text-white" />
            </div>
            <div>
              <h1 className="text-3xl font-bold text-gray-900 font-heading">Document Management</h1>
              <div className="flex items-center space-x-2">
                <p className="text-gray-600">Create and manage quotations and invoices</p>
                <div className="flex items-center space-x-1">
                  {getSyncStatusIcon()}
                  <span className="text-xs text-gray-500">
                    {syncStatus === 'offline' ? 'Offline' :
                     syncStatus === 'syncing' ? 'Syncing...' :
                     syncStatus === 'error' ? 'Error' : 'Online'}
                  </span>
                </div>
              </div>
            </div>
          </div>
          <div className="flex items-center space-x-2">
            <CompanySettings
              companyInfo={companyInfo}
              onUpdate={handleCompanyInfoUpdate}
            />
            <Button onClick={refreshDocuments} variant="outline" size="sm">
              <RefreshCw className="w-4 h-4 mr-2" />
              Refresh
            </Button>
            <Button onClick={exportToCSV} variant="outline" className="hidden sm:flex">
              <Download className="w-4 h-4 mr-2" />
              Export CSV
            </Button>
            <Button onClick={handlePrint} variant="outline" className="hidden sm:flex">
              <Printer className="w-4 h-4 mr-2" />
              Print
            </Button>
            <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
              <DialogTrigger asChild>
                <Button onClick={resetForm} className="bg-purple-600 hover:bg-purple-700">
                  <Plus className="w-4 h-4 mr-2" />
                  Create Document
                </Button>
              </DialogTrigger>
              <DialogContent className="sm:max-w-[900px] max-h-[90vh] overflow-y-auto">
                <DialogHeader>
                  <DialogTitle>
                    {editingDocument ? `Edit ${editingDocument.type === 'quotation' ? 'Quotation' : 'Invoice'}` : 'Create New Document'}
                  </DialogTitle>
                  <DialogDescription>
                    {editingDocument ? 'Update document details and items.' : 'Create a new quotation or invoice for your construction project.'}
                  </DialogDescription>
                </DialogHeader>
                <form onSubmit={handleSubmit}>
                  <div className="grid gap-4 py-4">
                    {/* Document Type Selection */}
                    {!editingDocument && (
                      <div className="space-y-2">
                        <Label>Document Type</Label>
                        <div className="flex space-x-4">
                          <Button
                            type="button"
                            variant={documentType === 'quotation' ? 'default' : 'outline'}
                            onClick={() => setDocumentType('quotation')}
                            className="flex-1"
                          >
                            Quotation
                          </Button>
                          <Button
                            type="button"
                            variant={documentType === 'invoice' ? 'default' : 'outline'}
                            onClick={() => setDocumentType('invoice')}
                            className="flex-1"
                          >
                            Invoice
                          </Button>
                        </div>
                      </div>
                    )}

                    {/* Basic Information */}
                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="title">Title</Label>
                        <Input
                          id="title"
                          value={formData.title}
                          onChange={(e) => setFormData({...formData, title: e.target.value})}
                          placeholder={`${documentType === 'quotation' ? 'Quotation' : 'Invoice'} title`}
                          required
                        />
                        {formErrors.title && (
                          <span className="text-sm text-red-500">{formErrors.title}</span>
                        )}
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="project_name">Project Name</Label>
                        <Input
                          id="project_name"
                          value={formData.project_name}
                          onChange={(e) => setFormData({...formData, project_name: e.target.value})}
                          required
                        />
                        {formErrors.project_name && (
                          <span className="text-sm text-red-500">{formErrors.project_name}</span>
                        )}
                      </div>
                    </div>

                    {/* Client Information */}
                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="client_name">Client Name</Label>
                        <Input
                          id="client_name"
                          value={formData.client_name}
                          onChange={(e) => setFormData({...formData, client_name: e.target.value})}
                          required
                        />
                        {formErrors.client_name && (
                          <span className="text-sm text-red-500">{formErrors.client_name}</span>
                        )}
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="client_email">Client Email</Label>
                        <Input
                          id="client_email"
                          type="email"
                          value={formData.client_email}
                          onChange={(e) => setFormData({...formData, client_email: e.target.value})}
                          required
                        />
                        {formErrors.client_email && (
                          <span className="text-sm text-red-500">{formErrors.client_email}</span>
                        )}
                      </div>
                    </div>

                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="client_phone">Client Phone</Label>
                        <Input
                          id="client_phone"
                          value={formData.client_phone}
                          onChange={(e) => setFormData({...formData, client_phone: e.target.value})}
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="client_address">Client Address</Label>
                        <Textarea
                          id="client_address"
                          value={formData.client_address}
                          onChange={(e) => setFormData({...formData, client_address: e.target.value})}
                          rows={2}
                        />
                      </div>
                    </div>

                    {/* Dates and Status */}
                    <div className="grid grid-cols-3 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="issue_date">Issue Date</Label>
                        <Input
                          id="issue_date"
                          type="date"
                          value={formData.issue_date}
                          onChange={(e) => setFormData({...formData, issue_date: e.target.value})}
                          required
                        />
                        {formErrors.issue_date && (
                          <span className="text-sm text-red-500">{formErrors.issue_date}</span>
                        )}
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="due_date">Due Date</Label>
                        <Input
                          id="due_date"
                          type="date"
                          value={formData.due_date}
                          onChange={(e) => setFormData({...formData, due_date: e.target.value})}
                          required
                        />
                        {formErrors.due_date && (
                          <span className="text-sm text-red-500">{formErrors.due_date}</span>
                        )}
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="status">Status</Label>
                        <Select value={formData.status} onValueChange={(value) => setFormData({...formData, status: value})}>
                          <SelectTrigger>
                            <SelectValue placeholder="Select status" />
                          </SelectTrigger>
                          <SelectContent>
                            {DOCUMENT_STATUSES[documentType].map((status) => (
                              <SelectItem key={status} value={status}>
                                {status}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>
                    </div>

                    {/* Items Section */}
                    <div className="space-y-4">
                      <div className="flex items-center justify-between">
                        <Label className="text-base font-semibold">Items</Label>
                        <Button type="button" onClick={addItem} variant="outline" size="sm">
                          <Plus className="w-4 h-4 mr-2" />
                          Add Item
                        </Button>
                      </div>

                      <div className="space-y-3">
                        {items.map((item, index) => (
                          <div key={item.id} className="grid grid-cols-12 gap-2 items-end p-3 border rounded-lg">
                            <div className="col-span-4">
                              <Label className="text-xs">Description</Label>
                              <Input
                                value={item.description}
                                onChange={(e) => {
                                  const updatedItems = [...items];
                                  updatedItems[index].description = e.target.value;
                                  setItems(updatedItems);
                                }}
                                placeholder="Item description"
                                className="text-sm"
                              />
                            </div>
                            <div className="col-span-2">
                              <Label className="text-xs">Quantity</Label>
                              <Input
                                type="number"
                                value={item.quantity}
                                onChange={(e) => updateItemTotal(index, 'quantity', parseFloat(e.target.value) || 0)}
                                min="0"
                                step="0.01"
                                className="text-sm"
                              />
                            </div>
                            <div className="col-span-1">
                              <Label className="text-xs">Unit</Label>
                              <Select value={item.unit} onValueChange={(value) => {
                                const updatedItems = [...items];
                                updatedItems[index].unit = value;
                                setItems(updatedItems);
                              }}>
                                <SelectTrigger className="text-sm">
                                  <SelectValue />
                                </SelectTrigger>
                                <SelectContent>
                                  {COMMON_UNITS.map((unit) => (
                                    <SelectItem key={unit} value={unit}>
                                      {unit}
                                    </SelectItem>
                                  ))}
                                </SelectContent>
                              </Select>
                            </div>
                            <div className="col-span-2">
                              <Label className="text-xs">Unit Price</Label>
                              <Input
                                type="number"
                                value={item.unit_price}
                                onChange={(e) => updateItemTotal(index, 'unit_price', parseFloat(e.target.value) || 0)}
                                min="0"
                                step="0.01"
                                className="text-sm"
                              />
                            </div>
                            <div className="col-span-2">
                              <Label className="text-xs">Total</Label>
                              <Input
                                value={item.total.toFixed(2)}
                                readOnly
                                className="text-sm bg-gray-50"
                              />
                            </div>
                            <div className="col-span-1">
                              <Button
                                type="button"
                                onClick={() => removeItem(index)}
                                variant="outline"
                                size="sm"
                                className="text-red-600 hover:text-red-700"
                                disabled={items.length === 1}
                              >
                                <Trash2 className="w-4 h-4" />
                              </Button>
                            </div>
                          </div>
                        ))}
                      </div>

                      {formErrors.items && (
                        <span className="text-sm text-red-500">{formErrors.items}</span>
                      )}
                    </div>

                    {/* Totals Section */}
                    <div className="space-y-4 p-4 bg-gray-50 rounded-lg">
                      <div className="grid grid-cols-2 gap-4">
                        <div className="space-y-2">
                          <Label htmlFor="tax_rate">Tax Rate (%)</Label>
                          <Input
                            id="tax_rate"
                            type="number"
                            value={formData.tax_rate}
                            onChange={(e) => setFormData({...formData, tax_rate: e.target.value})}
                            min="0"
                            max="100"
                            step="0.01"
                          />
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="discount_rate">Discount Rate (%)</Label>
                          <Input
                            id="discount_rate"
                            type="number"
                            value={formData.discount_rate}
                            onChange={(e) => setFormData({...formData, discount_rate: e.target.value})}
                            min="0"
                            max="100"
                            step="0.01"
                          />
                        </div>
                      </div>

                      <div className="space-y-2 text-sm">
                        <div className="flex justify-between">
                          <span>Subtotal:</span>
                          <span>${calculateTotals().subtotal.toFixed(2)}</span>
                        </div>
                        <div className="flex justify-between">
                          <span>Discount:</span>
                          <span>-${calculateTotals().discountAmount.toFixed(2)}</span>
                        </div>
                        <div className="flex justify-between">
                          <span>Tax:</span>
                          <span>${calculateTotals().taxAmount.toFixed(2)}</span>
                        </div>
                        <div className="flex justify-between font-semibold text-base border-t pt-2">
                          <span>Total:</span>
                          <span>${calculateTotals().totalAmount.toFixed(2)}</span>
                        </div>
                      </div>
                    </div>

                    {/* Notes and Terms */}
                    <div className="grid grid-cols-1 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="notes">Notes</Label>
                        <Textarea
                          id="notes"
                          value={formData.notes}
                          onChange={(e) => setFormData({...formData, notes: e.target.value})}
                          rows={3}
                          placeholder="Additional notes for the client..."
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="terms_conditions">Terms & Conditions</Label>
                        <Textarea
                          id="terms_conditions"
                          value={formData.terms_conditions}
                          onChange={(e) => setFormData({...formData, terms_conditions: e.target.value})}
                          rows={3}
                          placeholder="Payment terms and conditions..."
                        />
                      </div>
                    </div>
                  </div>
                  <DialogFooter>
                    <Button type="submit">
                      {editingDocument ? `Update ${documentType === 'quotation' ? 'Quotation' : 'Invoice'}` : `Create ${documentType === 'quotation' ? 'Quotation' : 'Invoice'}`}
                    </Button>
                  </DialogFooter>
                </form>
              </DialogContent>
            </Dialog>
          </div>
        </div>

        {/* Search and Filter Section */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Search className="w-5 h-5" />
              <span>Search & Filter</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <Input
                  placeholder="Search documents, clients, projects..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
              <Select value={selectedType} onValueChange={setSelectedType}>
                <SelectTrigger>
                  <SelectValue placeholder="Filter by type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Types</SelectItem>
                  <SelectItem value="quotation">Quotations</SelectItem>
                  <SelectItem value="invoice">Invoices</SelectItem>
                </SelectContent>
              </Select>
              <Select value={selectedStatus} onValueChange={setSelectedStatus}>
                <SelectTrigger>
                  <SelectValue placeholder="Filter by status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Statuses</SelectItem>
                  <SelectItem value="Draft">Draft</SelectItem>
                  <SelectItem value="Sent">Sent</SelectItem>
                  <SelectItem value="Accepted">Accepted</SelectItem>
                  <SelectItem value="Paid">Paid</SelectItem>
                  <SelectItem value="Overdue">Overdue</SelectItem>
                </SelectContent>
              </Select>
              <Button onClick={clearFilters} variant="outline" className="w-full">
                <Filter className="w-4 h-4 mr-2" />
                Clear Filters
              </Button>
            </div>
            {(searchTerm || (selectedType && selectedType !== 'all') || (selectedStatus && selectedStatus !== 'all')) && (
              <div className="mt-4 p-3 bg-blue-50 rounded-lg">
                <p className="text-sm text-blue-700">
                  Showing {filteredDocuments.length} of {documents.length} documents
                  {searchTerm && ` matching "${searchTerm}"`}
                  {selectedType && selectedType !== 'all' && ` of type "${selectedType}"`}
                  {selectedStatus && selectedStatus !== 'all' && ` with status "${selectedStatus}"`}
                </p>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Summary Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Documents</CardTitle>
              <FileText className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{documents.length}</div>
              <p className="text-xs text-muted-foreground">
                All documents
              </p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Quotations</CardTitle>
              <FileText className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-purple-600">{quotations.length}</div>
              <p className="text-xs text-muted-foreground">
                Active quotations
              </p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Invoices</CardTitle>
              <FileText className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-orange-600">{invoices.length}</div>
              <p className="text-xs text-muted-foreground">
                Active invoices
              </p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Value</CardTitle>
              <DollarSign className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">${totalValue.toLocaleString()}</div>
              <p className="text-xs text-muted-foreground">
                Combined value (filtered)
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Documents Table */}
        <Card>
          <CardHeader>
            <CardTitle>Documents</CardTitle>
            <CardDescription>
              Manage quotations and invoices for your construction projects
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Document</TableHead>
                    <TableHead>Type</TableHead>
                    <TableHead>Client</TableHead>
                    <TableHead>Project</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Created By</TableHead>
                    <TableHead>Issue Date</TableHead>
                    <TableHead className="text-right">Amount</TableHead>
                    <TableHead className="text-center">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredDocuments.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={9} className="text-center py-8 text-gray-500">
                        {documents.length === 0
                          ? "No documents created yet. Click \"Create Document\" to get started."
                          : "No documents match the current filters. Try adjusting your search criteria."
                        }
                      </TableCell>
                    </TableRow>
                  ) : (
                    filteredDocuments.map((document) => (
                      <TableRow key={document.id}>
                        <TableCell className="font-medium">
                          <div>
                            <div className="font-semibold">{document.title}</div>
                            <div className="text-sm text-gray-500">
                              {document.document_number}
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <Badge className={getTypeColor(document.type)}>
                            {document.type === 'quotation' ? 'Quotation' : 'Invoice'}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <div>
                            <div className="font-medium">{document.client_name}</div>
                            <div className="text-sm text-gray-500">{document.client_email}</div>
                          </div>
                        </TableCell>
                        <TableCell>{document.project_name}</TableCell>
                        <TableCell>
                          <Badge className={getStatusColor(document.status, document.type)}>
                            {document.status}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <CreatorInfo
                            createdByName={document.created_by_name}
                            createdByAvatar={document.created_by_avatar}
                            createdAt={document.created_at}
                            variant="compact"
                            showLabel={false}
                            showDate={false}
                          />
                        </TableCell>
                        <TableCell>
                          <div className="text-sm">
                            <div className="flex items-center text-gray-600">
                              <Calendar className="w-3 h-3 mr-1" />
                              {new Date(document.issue_date).toLocaleDateString()}
                            </div>
                            <div className="text-gray-500">
                              Due: {new Date(document.due_date).toLocaleDateString()}
                            </div>
                          </div>
                        </TableCell>
                        <TableCell className="text-right font-semibold">
                          ${Number(document.total_amount).toLocaleString()}
                        </TableCell>
                        <TableCell className="text-center">
                          <div className="flex justify-center space-x-1">
                            <DocumentPreview document={document} />
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => handleDownloadPDF(document)}
                              title="Download PDF"
                            >
                              <Download className="w-4 h-4" />
                            </Button>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => handleEdit(document)}
                              title="Edit"
                            >
                              <Edit className="w-4 h-4" />
                            </Button>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => handleDelete(document.id)}
                              className="text-red-600 hover:text-red-700"
                              title="Delete"
                            >
                              <Trash2 className="w-4 h-4" />
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>
            </div>

            {/* Summary Row */}
            {filteredDocuments.length > 0 && (
              <div className="mt-4 p-4 bg-gray-50 rounded-lg">
                <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                  <div className="text-center">
                    <div className="text-lg font-semibold text-gray-900">
                      {filteredDocuments.length} Documents
                    </div>
                    <div className="text-sm text-gray-600">
                      {filteredDocuments.length !== documents.length ? `of ${documents.length} total` : 'total'}
                    </div>
                  </div>
                  <div className="text-center">
                    <div className="text-lg font-semibold text-purple-600">
                      {quotations.length} Quotations
                    </div>
                    <div className="text-sm text-gray-600">
                      ${quotations.reduce((sum, q) => sum + q.total_amount, 0).toLocaleString()}
                    </div>
                  </div>
                  <div className="text-center">
                    <div className="text-lg font-semibold text-orange-600">
                      {invoices.length} Invoices
                    </div>
                    <div className="text-sm text-gray-600">
                      ${invoices.reduce((sum, i) => sum + i.total_amount, 0).toLocaleString()}
                    </div>
                  </div>
                  <div className="text-center">
                    <div className="text-lg font-semibold text-green-600">
                      ${totalValue.toLocaleString()}
                    </div>
                    <div className="text-sm text-gray-600">Total Value</div>
                  </div>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </Layout>
  );
};

export default Documents;
