-- Fix the remaining orphaned user: <EMAIL>

DO $$
DECLARE
    fix_result jsonb;
BEGIN
    RAISE NOTICE '';
    RAISE NOTICE '=== FIXING <EMAIL> ===';
    
    -- Use the same function we created earlier
    SELECT public.fix_orphaned_profile('<EMAIL>') INTO fix_result;
    
    IF (fix_result->>'success')::boolean THEN
        RAISE NOTICE '✅ SUCCESS: %', fix_result->>'message';
        RAISE NOTICE '📧 Email: %', fix_result->>'email';
        RAISE NOTICE '🔑 Password: %', fix_result->>'password';
        RAISE NOTICE '🆔 Auth ID: %', fix_result->>'auth_user_id';
    ELSE
        RAISE NOTICE '❌ FAILED: %', fix_result->>'error';
    END IF;
    
    RAISE NOTICE '';
END $$;

-- Verify all users are now fixed
SELECT 
    'FINAL_STATUS' as type,
    up.email,
    up.first_name,
    up.last_name,
    CASE 
        WHEN au.id IS NOT NULL THEN 'HAS_AUTH' 
        ELSE 'ORPHANED' 
    END as auth_status
FROM public.user_profiles up
LEFT JOIN auth.users au ON up.user_id = au.id
ORDER BY up.email;

-- Show summary
SELECT 
    'SUMMARY' as type,
    (SELECT COUNT(*) FROM public.user_profiles) as total_profiles,
    (SELECT COUNT(*) FROM auth.users) as total_auth_users,
    (SELECT COUNT(*) FROM public.user_profiles up LEFT JOIN auth.users au ON up.user_id = au.id WHERE au.id IS NULL) as orphaned_count;
