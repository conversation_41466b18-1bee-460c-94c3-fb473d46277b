import React from 'react';
import Layout from '@/components/layout/Layout';
import CompanyManagement from '@/components/company/CompanyManagement';
import { useAuth } from '@/contexts/AuthContext';
import { hasPermission } from '@/lib/permissions';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { AlertTriangle, Building2 } from 'lucide-react';

const Company: React.FC = () => {
  const { user, profile } = useAuth();

  // Check if user has permission to view company management
  const canViewCompany = hasPermission(profile?.role?.role_name || '', 'VIEW_ALL_USERS') || 
                        hasPermission(profile?.role?.role_name || '', 'MANAGE_SYSTEM_SETTINGS');

  if (!canViewCompany) {
    return (
      <Layout>
        <div className="container mx-auto px-4 py-8">
          <Card className="max-w-md mx-auto">
            <CardHeader>
              <CardTitle className="flex items-center text-orange-600">
                <AlertTriangle className="w-5 h-5 mr-2" />
                Access Restricted
              </CardTitle>
              <CardDescription>
                You don't have permission to access company management features.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                Contact your administrator if you need access to company management, team members, 
                activity logs, or permission settings.
              </p>
            </CardContent>
          </Card>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="container mx-auto px-4 py-8">
        <CompanyManagement />
      </div>
    </Layout>
  );
};

export default Company;
