-- Test Message Notifications System
-- This script helps debug why notifications aren't showing to other accounts

-- 1. Check if notifications are being created
DO $$
DECLARE
    notification_count INTEGER;
    message_count INTEGER;
    user_count INTEGER;
    recent_notifications RECORD;
BEGIN
    RAISE NOTICE '';
    RAISE NOTICE '=== TESTING MESSAGE NOTIFICATIONS ===';
    
    -- Count total notifications
    SELECT COUNT(*) INTO notification_count FROM public.notifications;
    RAISE NOTICE 'Total notifications in database: %', notification_count;
    
    -- Count total messages
    SELECT COUNT(*) INTO message_count FROM public.messages;
    RAISE NOTICE 'Total messages in database: %', message_count;
    
    -- Count total users
    SELECT COUNT(*) INTO user_count FROM auth.users;
    RAISE NOTICE 'Total users in database: %', user_count;
    
    -- Show recent notifications
    RAISE NOTICE '';
    RAISE NOTICE '=== RECENT NOTIFICATIONS ===';
    FOR recent_notifications IN
        SELECT 
            n.id,
            n.category,
            n.title,
            n.message,
            n.created_at,
            u.email as user_email
        FROM public.notifications n
        LEFT JOIN auth.users u ON n.user_id = u.id
        ORDER BY n.created_at DESC
        LIMIT 5
    LOOP
        RAISE NOTICE 'Notification: % | User: % | Title: % | Created: %', 
            recent_notifications.id, 
            recent_notifications.user_email, 
            recent_notifications.title, 
            recent_notifications.created_at;
    END LOOP;
    
    -- Show recent messages
    RAISE NOTICE '';
    RAISE NOTICE '=== RECENT MESSAGES ===';
    FOR recent_notifications IN
        SELECT 
            m.id,
            m.sender_name,
            m.message_content,
            m.created_at,
            m.created_by_user_id,
            c.name as channel_name
        FROM public.messages m
        LEFT JOIN public.message_channels c ON m.channel_id = c.id
        ORDER BY m.created_at DESC
        LIMIT 5
    LOOP
        RAISE NOTICE 'Message: % | Sender: % | Channel: % | Content: % | Created: %', 
            recent_notifications.id, 
            recent_notifications.sender_name, 
            recent_notifications.channel_name,
            LEFT(recent_notifications.message_content, 50),
            recent_notifications.created_at;
    END LOOP;
    
    RAISE NOTICE '';
END $$;

-- 2. Check if the trigger function exists and is working
DO $$
DECLARE
    trigger_exists BOOLEAN;
    function_exists BOOLEAN;
BEGIN
    RAISE NOTICE '=== CHECKING TRIGGER STATUS ===';
    
    -- Check if trigger exists
    SELECT EXISTS (
        SELECT 1 FROM information_schema.triggers 
        WHERE trigger_name = 'trigger_create_message_notifications'
        AND event_object_table = 'messages'
    ) INTO trigger_exists;
    
    -- Check if function exists
    SELECT EXISTS (
        SELECT 1 FROM information_schema.routines 
        WHERE routine_name = 'create_message_notifications'
        AND routine_type = 'FUNCTION'
    ) INTO function_exists;
    
    RAISE NOTICE 'Trigger exists: %', trigger_exists;
    RAISE NOTICE 'Function exists: %', function_exists;
    
    IF NOT trigger_exists OR NOT function_exists THEN
        RAISE NOTICE '⚠ Missing trigger or function - notifications may not be created automatically';
    ELSE
        RAISE NOTICE '✓ Trigger and function are properly set up';
    END IF;
    
    RAISE NOTICE '';
END $$;

-- 3. Test notification creation manually
DO $$
DECLARE
    test_user_id UUID;
    test_message_id UUID;
    notification_id UUID;
BEGIN
    RAISE NOTICE '=== TESTING MANUAL NOTIFICATION CREATION ===';
    
    -- Get a test user
    SELECT id INTO test_user_id FROM auth.users LIMIT 1;
    
    IF test_user_id IS NOT NULL THEN
        -- Create a test notification
        INSERT INTO public.notifications (
            user_id,
            category,
            type,
            title,
            message,
            action_url,
            action_label,
            priority,
            related_entity_type,
            metadata,
            created_by
        ) VALUES (
            test_user_id,
            'message',
            'info',
            'Test Notification',
            'This is a test notification to verify the system is working.',
            '/messages',
            'View Messages',
            'medium',
            'test',
            jsonb_build_object('test', true),
            test_user_id
        ) RETURNING id INTO notification_id;
        
        RAISE NOTICE '✓ Created test notification with ID: %', notification_id;
        RAISE NOTICE '✓ Check the notification center to see if it appears';
    ELSE
        RAISE NOTICE '⚠ No users found - cannot create test notification';
    END IF;
    
    RAISE NOTICE '';
END $$;

-- 4. Check user profiles and their relationship to auth.users
DO $$
DECLARE
    auth_users_count INTEGER;
    profile_users_count INTEGER;
    orphaned_profiles INTEGER;
    missing_profiles INTEGER;
BEGIN
    RAISE NOTICE '=== CHECKING USER PROFILE RELATIONSHIPS ===';
    
    -- Count auth users
    SELECT COUNT(*) INTO auth_users_count FROM auth.users;
    
    -- Count profile users
    SELECT COUNT(*) INTO profile_users_count FROM public.user_profiles;
    
    -- Count orphaned profiles (profiles without auth users)
    SELECT COUNT(*) INTO orphaned_profiles 
    FROM public.user_profiles p
    LEFT JOIN auth.users u ON p.user_id = u.id
    WHERE u.id IS NULL;
    
    -- Count missing profiles (auth users without profiles)
    SELECT COUNT(*) INTO missing_profiles 
    FROM auth.users u
    LEFT JOIN public.user_profiles p ON u.id = p.user_id
    WHERE p.user_id IS NULL;
    
    RAISE NOTICE 'Auth users: %', auth_users_count;
    RAISE NOTICE 'Profile users: %', profile_users_count;
    RAISE NOTICE 'Orphaned profiles: %', orphaned_profiles;
    RAISE NOTICE 'Missing profiles: %', missing_profiles;
    
    IF missing_profiles > 0 THEN
        RAISE NOTICE '⚠ Some auth users are missing profiles - this may affect notifications';
    END IF;
    
    RAISE NOTICE '';
END $$;

-- 5. Show notification permissions and policies
DO $$
DECLARE
    policy_record RECORD;
BEGIN
    RAISE NOTICE '=== CHECKING NOTIFICATION PERMISSIONS ===';
    
    -- Check if RLS is enabled
    IF EXISTS (
        SELECT 1 FROM pg_class c
        JOIN pg_namespace n ON n.oid = c.relnamespace
        WHERE c.relname = 'notifications' 
        AND n.nspname = 'public'
        AND c.relrowsecurity = true
    ) THEN
        RAISE NOTICE 'Row Level Security is ENABLED on notifications table';
        
        -- Show policies
        FOR policy_record IN
            SELECT policyname, permissive, roles, cmd, qual
            FROM pg_policies 
            WHERE tablename = 'notifications'
        LOOP
            RAISE NOTICE 'Policy: % | Command: % | Roles: %', 
                policy_record.policyname, 
                policy_record.cmd, 
                policy_record.roles;
        END LOOP;
    ELSE
        RAISE NOTICE 'Row Level Security is DISABLED on notifications table';
    END IF;
    
    RAISE NOTICE '';
END $$;

-- 6. Create a simple test to verify the trigger works
DO $$
DECLARE
    test_channel_id UUID;
    test_message_id UUID;
    notifications_before INTEGER;
    notifications_after INTEGER;
    current_user_id UUID;
BEGIN
    RAISE NOTICE '=== TESTING MESSAGE TRIGGER ===';
    
    -- Get current user
    current_user_id := auth.uid();
    
    -- Get a test channel
    SELECT id INTO test_channel_id FROM public.message_channels LIMIT 1;
    
    IF test_channel_id IS NULL THEN
        -- Create a test channel
        INSERT INTO public.message_channels (name, description, channel_type, is_private)
        VALUES ('Test Channel', 'Test channel for notifications', 'general', false)
        RETURNING id INTO test_channel_id;
        RAISE NOTICE '✓ Created test channel: %', test_channel_id;
    END IF;
    
    -- Count notifications before
    SELECT COUNT(*) INTO notifications_before FROM public.notifications;
    
    -- Insert a test message (this should trigger notification creation)
    INSERT INTO public.messages (
        channel_id,
        sender_name,
        sender_email,
        message_content,
        created_by_user_id,
        created_by_name
    ) VALUES (
        test_channel_id,
        'Test User',
        '<EMAIL>',
        'This is a test message to verify notifications work.',
        COALESCE(current_user_id, gen_random_uuid()),
        'Test User'
    ) RETURNING id INTO test_message_id;
    
    -- Wait a moment for trigger to execute
    PERFORM pg_sleep(1);
    
    -- Count notifications after
    SELECT COUNT(*) INTO notifications_after FROM public.notifications;
    
    RAISE NOTICE 'Notifications before: %', notifications_before;
    RAISE NOTICE 'Notifications after: %', notifications_after;
    RAISE NOTICE 'New notifications created: %', (notifications_after - notifications_before);
    
    IF notifications_after > notifications_before THEN
        RAISE NOTICE '✓ Trigger is working! Notifications are being created automatically.';
    ELSE
        RAISE NOTICE '⚠ Trigger may not be working - no new notifications were created.';
    END IF;
    
    RAISE NOTICE '';
    RAISE NOTICE '=== TEST COMPLETE ===';
    RAISE NOTICE 'If notifications are being created but not showing in the UI:';
    RAISE NOTICE '1. Check that users are logged in with different accounts';
    RAISE NOTICE '2. Wait 30 seconds for polling to refresh notifications';
    RAISE NOTICE '3. Check browser console for any JavaScript errors';
    RAISE NOTICE '4. Verify that the NotificationCenter component is loading properly';
    RAISE NOTICE '';
END $$;
