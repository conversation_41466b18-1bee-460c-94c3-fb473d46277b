-- Permanent Registration Fix - Prevents Future Profile Creation Issues
-- This ensures all future registrations work correctly

-- 1. Create a robust registration function that handles all edge cases
CREATE OR REPLACE FUNCTION public.complete_user_registration(
    p_email VARCHAR(255),
    p_auth_user_id UUID,
    p_first_name <PERSON><PERSON><PERSON><PERSON>(100),
    p_last_name <PERSON><PERSON><PERSON><PERSON>(100),
    p_role_name VA<PERSON>HAR(50) DEFAULT 'client'
)
RETURNS jsonb AS $$
DECLARE
    v_role_id UUID;
    v_profile_id UUID;
    v_existing_profile RECORD;
    v_auth_user RECORD;
BEGIN
    -- Check if auth user exists
    SELECT * INTO v_auth_user
    FROM auth.users
    WHERE id = p_auth_user_id;
    
    IF v_auth_user.id IS NULL THEN
        RETURN jsonb_build_object(
            'success', false,
            'error', 'Auth user not found',
            'code', 'AUTH_USER_NOT_FOUND'
        );
    END IF;
    
    -- Check if profile already exists
    SELECT * INTO v_existing_profile
    FROM public.user_profiles
    WHERE email = p_email OR user_id = p_auth_user_id;
    
    IF v_existing_profile.id IS NOT NULL THEN
        -- Update existing profile instead of creating new one
        UPDATE public.user_profiles
        SET 
            user_id = p_auth_user_id,
            first_name = p_first_name,
            last_name = p_last_name,
            is_verified = true,
            is_active = true,
            email_status = 'registration_completed',
            updated_at = NOW()
        WHERE id = v_existing_profile.id
        RETURNING id INTO v_profile_id;
        
        RETURN jsonb_build_object(
            'success', true,
            'message', 'Profile updated successfully',
            'profile_id', v_profile_id,
            'auth_user_id', p_auth_user_id,
            'action', 'updated'
        );
    END IF;
    
    -- Get role ID
    SELECT id INTO v_role_id
    FROM public.user_roles
    WHERE role_name = p_role_name AND is_active = true;
    
    IF v_role_id IS NULL THEN
        -- Default to client role if specified role not found
        SELECT id INTO v_role_id
        FROM public.user_roles
        WHERE role_name = 'client' AND is_active = true
        LIMIT 1;
    END IF;
    
    IF v_role_id IS NULL THEN
        -- If still no role, get any active role
        SELECT id INTO v_role_id
        FROM public.user_roles
        WHERE is_active = true
        LIMIT 1;
    END IF;
    
    IF v_role_id IS NULL THEN
        RETURN jsonb_build_object(
            'success', false,
            'error', 'No active roles found in system',
            'code', 'NO_ROLES_AVAILABLE'
        );
    END IF;
    
    -- Create new profile
    INSERT INTO public.user_profiles (
        user_id,
        email,
        first_name,
        last_name,
        role_id,
        is_verified,
        is_active,
        email_status,
        created_at,
        updated_at
    ) VALUES (
        p_auth_user_id,
        p_email,
        p_first_name,
        p_last_name,
        v_role_id,
        true,
        true,
        'registration_completed',
        NOW(),
        NOW()
    )
    RETURNING id INTO v_profile_id;
    
    RETURN jsonb_build_object(
        'success', true,
        'message', 'Profile created successfully',
        'profile_id', v_profile_id,
        'auth_user_id', p_auth_user_id,
        'role_id', v_role_id,
        'action', 'created'
    );
    
EXCEPTION
    WHEN OTHERS THEN
        RETURN jsonb_build_object(
            'success', false,
            'error', 'Profile creation failed: ' || SQLERRM,
            'code', 'PROFILE_CREATION_ERROR'
        );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 2. Create a function to fix orphaned auth users (users without profiles)
CREATE OR REPLACE FUNCTION public.fix_orphaned_auth_users()
RETURNS jsonb AS $$
DECLARE
    v_orphaned_user RECORD;
    v_fixed_count INTEGER := 0;
    v_role_id UUID;
    v_results jsonb := '[]'::jsonb;
BEGIN
    -- Get default client role
    SELECT id INTO v_role_id
    FROM public.user_roles
    WHERE role_name = 'client' AND is_active = true
    LIMIT 1;
    
    IF v_role_id IS NULL THEN
        SELECT id INTO v_role_id
        FROM public.user_roles
        WHERE is_active = true
        LIMIT 1;
    END IF;
    
    -- Find auth users without profiles
    FOR v_orphaned_user IN
        SELECT au.*
        FROM auth.users au
        LEFT JOIN public.user_profiles up ON au.id = up.user_id
        WHERE up.user_id IS NULL
        AND au.email IS NOT NULL
    LOOP
        -- Check if profile exists with this email but different user_id
        IF EXISTS (SELECT 1 FROM public.user_profiles WHERE email = v_orphaned_user.email) THEN
            -- Update existing profile to link to this auth user
            UPDATE public.user_profiles
            SET
                user_id = v_orphaned_user.id,
                is_verified = true,
                is_active = true,
                email_status = 'profile_linked_to_auth',
                updated_at = NOW()
            WHERE email = v_orphaned_user.email
            AND user_id IS NULL;

            v_fixed_count := v_fixed_count + 1;
            v_results := v_results || jsonb_build_object(
                'email', v_orphaned_user.email,
                'auth_user_id', v_orphaned_user.id,
                'status', 'linked_existing_profile'
            );
        ELSE
            -- Create new profile for orphaned user
            INSERT INTO public.user_profiles (
                user_id,
                email,
                first_name,
                last_name,
                role_id,
                is_verified,
                is_active,
                email_status,
                created_at,
                updated_at
            ) VALUES (
                v_orphaned_user.id,
                v_orphaned_user.email,
                COALESCE(v_orphaned_user.raw_user_meta_data->>'first_name', 'User'),
                COALESCE(v_orphaned_user.raw_user_meta_data->>'last_name', ''),
                v_role_id,
                true,
                true,
                'profile_auto_created',
                NOW(),
                NOW()
            );

            v_fixed_count := v_fixed_count + 1;
            v_results := v_results || jsonb_build_object(
                'email', v_orphaned_user.email,
                'auth_user_id', v_orphaned_user.id,
                'status', 'created_new_profile'
            );
        END IF;
    END LOOP;
    
    RETURN jsonb_build_object(
        'success', true,
        'fixed_count', v_fixed_count,
        'fixed_users', v_results
    );
    
EXCEPTION
    WHEN OTHERS THEN
        RETURN jsonb_build_object(
            'success', false,
            'error', 'Failed to fix orphaned users: ' || SQLERRM
        );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 3. Grant permissions
GRANT EXECUTE ON FUNCTION public.complete_user_registration(VARCHAR, UUID, VARCHAR, VARCHAR, VARCHAR) TO anon, authenticated;
GRANT EXECUTE ON FUNCTION public.fix_orphaned_auth_users() TO authenticated;

-- 4. Fix any existing orphaned users
SELECT public.fix_orphaned_auth_users() as orphaned_users_fix;

-- 5. Test the new registration function
DO $$
DECLARE
    test_result jsonb;
BEGIN
    RAISE NOTICE '';
    RAISE NOTICE '=== PERMANENT REGISTRATION FIX INSTALLED ===';
    RAISE NOTICE '';
    RAISE NOTICE 'New functions created:';
    RAISE NOTICE '1. complete_user_registration() - Robust registration with error handling';
    RAISE NOTICE '2. fix_orphaned_auth_users() - Fixes users without profiles';
    RAISE NOTICE '';
    RAISE NOTICE 'Benefits for future registrations:';
    RAISE NOTICE '✓ Handles auth user verification';
    RAISE NOTICE '✓ Updates existing profiles instead of failing';
    RAISE NOTICE '✓ Automatic role assignment';
    RAISE NOTICE '✓ Comprehensive error handling';
    RAISE NOTICE '✓ No more orphaned auth users';
    RAISE NOTICE '';
    RAISE NOTICE 'The frontend should now use complete_user_registration()';
    RAISE NOTICE 'instead of direct profile updates.';
    RAISE NOTICE '';
END $$;
