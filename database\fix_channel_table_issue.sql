-- Fix Channel Table Issue
-- This ensures the correct channel table exists and has data

-- 1. Check what channel tables exist
DO $$
DECLARE
    message_channels_exists BOOLEAN;
    channels_exists BOOLEAN;
BEGIN
    RAISE NOTICE '';
    RAISE NOTICE '=== CHECKING CHANNEL TABLES ===';
    
    -- Check if message_channels exists
    SELECT EXISTS (
        SELECT 1 FROM information_schema.tables 
        WHERE table_name = 'message_channels' AND table_schema = 'public'
    ) INTO message_channels_exists;
    
    -- Check if channels exists
    SELECT EXISTS (
        SELECT 1 FROM information_schema.tables 
        WHERE table_name = 'channels' AND table_schema = 'public'
    ) INTO channels_exists;
    
    RAISE NOTICE 'message_channels table exists: %', message_channels_exists;
    RAISE NOTICE 'channels table exists: %', channels_exists;
    
    -- If neither exists, create message_channels
    IF NOT message_channels_exists AND NOT channels_exists THEN
        RAISE NOTICE 'Creating message_channels table...';
    ELSIF channels_exists AND NOT message_channels_exists THEN
        RAISE NOTICE 'Only channels table exists - will create message_channels and copy data';
    ELSIF message_channels_exists AND NOT channels_exists THEN
        RAISE NOTICE 'Only message_channels exists - will create channels as alias';
    ELSE
        RAISE NOTICE 'Both tables exist - will ensure they have the same data';
    END IF;
    
    RAISE NOTICE '';
END $$;

-- 2. Create message_channels table if it doesn't exist
CREATE TABLE IF NOT EXISTS public.message_channels (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    channel_type VARCHAR(20) DEFAULT 'general' CHECK (channel_type IN ('general', 'project', 'team', 'direct', 'announcement')),
    project_id UUID,
    is_private BOOLEAN DEFAULT false,
    created_by VARCHAR(255),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 3. Create channels table if it doesn't exist (for compatibility)
CREATE TABLE IF NOT EXISTS public.channels (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    type VARCHAR(20) DEFAULT 'public',
    created_by UUID,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 4. Copy data from channels to message_channels if needed
DO $$
DECLARE
    channels_count INTEGER;
    message_channels_count INTEGER;
BEGIN
    -- Count records in each table
    SELECT COUNT(*) INTO channels_count FROM public.channels;
    SELECT COUNT(*) INTO message_channels_count FROM public.message_channels;
    
    RAISE NOTICE 'channels table has % records', channels_count;
    RAISE NOTICE 'message_channels table has % records', message_channels_count;
    
    -- If channels has data but message_channels doesn't, copy it
    IF channels_count > 0 AND message_channels_count = 0 THEN
        INSERT INTO public.message_channels (id, name, description, channel_type, is_private, created_by, created_at, updated_at)
        SELECT 
            id, 
            name, 
            description, 
            CASE 
                WHEN type = 'public' THEN 'general'
                WHEN type = 'private' THEN 'team'
                ELSE 'general'
            END as channel_type,
            CASE WHEN type = 'private' THEN true ELSE false END as is_private,
            created_by::text,
            created_at,
            updated_at
        FROM public.channels
        ON CONFLICT (id) DO NOTHING;
        
        RAISE NOTICE '✓ Copied % records from channels to message_channels', channels_count;
    END IF;
    
    -- If message_channels has data but channels doesn't, copy it
    IF message_channels_count > 0 AND channels_count = 0 THEN
        INSERT INTO public.channels (id, name, description, type, created_by, created_at, updated_at)
        SELECT 
            id, 
            name, 
            description, 
            CASE 
                WHEN is_private THEN 'private'
                ELSE 'public'
            END as type,
            created_by::uuid,
            created_at,
            updated_at
        FROM public.message_channels
        ON CONFLICT (id) DO NOTHING;
        
        RAISE NOTICE '✓ Copied % records from message_channels to channels', message_channels_count;
    END IF;
END $$;

-- 5. Insert default channels if both tables are empty
DO $$
DECLARE
    total_channels INTEGER;
BEGIN
    SELECT COUNT(*) INTO total_channels FROM public.message_channels;
    
    IF total_channels = 0 THEN
        INSERT INTO public.message_channels (name, description, channel_type, is_private) VALUES
        ('General', 'General team discussions and announcements', 'general', false),
        ('Project Updates', 'Project status updates and coordination', 'project', false),
        ('Team Chat', 'Casual team conversations', 'team', false),
        ('Announcements', 'Important company announcements', 'announcement', false),
        ('Technical Discussion', 'Technical discussions and problem solving', 'general', false)
        ON CONFLICT DO NOTHING;
        
        RAISE NOTICE '✓ Created default channels in message_channels';
        
        -- Also add to channels table for compatibility
        INSERT INTO public.channels (name, description, type) 
        SELECT name, description, 'public' as type 
        FROM public.message_channels
        ON CONFLICT DO NOTHING;
        
        RAISE NOTICE '✓ Synced default channels to channels table';
    END IF;
END $$;

-- 6. Update the notification trigger to use message_channels
CREATE OR REPLACE FUNCTION create_message_notifications()
RETURNS TRIGGER AS $$
DECLARE
    user_record RECORD;
    notification_count INTEGER := 0;
    sender_id UUID;
    sender_name TEXT;
    channel_name TEXT := 'General';
BEGIN
    -- Only create notifications for new messages
    IF TG_OP = 'INSERT' AND COALESCE(NEW.is_deleted, FALSE) = FALSE THEN
        
        -- Get sender ID from available fields
        sender_id := COALESCE(NEW.created_by_user_id, auth.uid());
        
        -- Get sender name from available fields
        sender_name := COALESCE(NEW.created_by_name, NEW.sender_name, 'User');
        
        -- Try to get channel name from message_channels first, then channels
        BEGIN
            SELECT name INTO channel_name 
            FROM public.message_channels 
            WHERE id = NEW.channel_id;
            
            -- If not found in message_channels, try channels
            IF channel_name IS NULL THEN
                SELECT name INTO channel_name 
                FROM public.channels 
                WHERE id = NEW.channel_id;
            END IF;
        EXCEPTION WHEN OTHERS THEN
            channel_name := 'General';
        END;
        
        -- Default if no channel found
        IF channel_name IS NULL THEN
            channel_name := 'General';
        END IF;
        
        -- Create notifications for other users (only if notifications table exists)
        IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'notifications') THEN
            BEGIN
                -- Try user_profiles first
                FOR user_record IN 
                    SELECT user_id 
                    FROM public.user_profiles 
                    WHERE user_id != sender_id
                    AND user_id IS NOT NULL
                    LIMIT 50  -- Limit to prevent too many notifications
                LOOP
                    BEGIN
                        INSERT INTO public.notifications (
                            user_id,
                            category,
                            type,
                            title,
                            message,
                            action_url,
                            action_label,
                            priority,
                            related_entity_type,
                            related_entity_id,
                            metadata,
                            created_by
                        ) VALUES (
                            user_record.user_id,
                            'message',
                            'info',
                            'New message in ' || channel_name,
                            sender_name || ': ' || LEFT(COALESCE(NEW.message_content, NEW.content, 'New message'), 100),
                            '/messages?channel=' || NEW.channel_id,
                            'View Message',
                            'medium',
                            'message',
                            NEW.id,
                            jsonb_build_object(
                                'channel_name', channel_name,
                                'sender_name', sender_name,
                                'message_id', NEW.id,
                                'channel_id', NEW.channel_id
                            ),
                            sender_id
                        );
                        notification_count := notification_count + 1;
                    EXCEPTION WHEN OTHERS THEN
                        -- Skip this notification if it fails
                        CONTINUE;
                    END;
                END LOOP;
            EXCEPTION WHEN OTHERS THEN
                -- If user_profiles doesn't exist, try auth.users
                FOR user_record IN 
                    SELECT id as user_id 
                    FROM auth.users 
                    WHERE id != sender_id
                    LIMIT 50
                LOOP
                    BEGIN
                        INSERT INTO public.notifications (
                            user_id,
                            category,
                            type,
                            title,
                            message,
                            action_url,
                            action_label,
                            priority,
                            related_entity_type,
                            related_entity_id,
                            metadata,
                            created_by
                        ) VALUES (
                            user_record.user_id,
                            'message',
                            'info',
                            'New message in ' || channel_name,
                            sender_name || ': ' || LEFT(COALESCE(NEW.message_content, NEW.content, 'New message'), 100),
                            '/messages?channel=' || NEW.channel_id,
                            'View Message',
                            'medium',
                            'message',
                            NEW.id,
                            jsonb_build_object(
                                'channel_name', channel_name,
                                'sender_name', sender_name,
                                'message_id', NEW.id,
                                'channel_id', NEW.channel_id
                            ),
                            sender_id
                        );
                        notification_count := notification_count + 1;
                    EXCEPTION WHEN OTHERS THEN
                        -- Skip this notification if it fails
                        CONTINUE;
                    END;
                END LOOP;
            END;
        END IF;
        
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 7. Recreate the trigger
DROP TRIGGER IF EXISTS trigger_create_message_notifications ON public.messages;
CREATE TRIGGER trigger_create_message_notifications
    AFTER INSERT ON public.messages
    FOR EACH ROW
    EXECUTE FUNCTION create_message_notifications();

-- 8. Set up RLS policies for both tables
ALTER TABLE public.message_channels ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.channels ENABLE ROW LEVEL SECURITY;

-- Drop existing policies
DROP POLICY IF EXISTS "Everyone can view message_channels" ON public.message_channels;
DROP POLICY IF EXISTS "Authenticated users can manage message_channels" ON public.message_channels;
DROP POLICY IF EXISTS "Everyone can view channels" ON public.channels;
DROP POLICY IF EXISTS "Authenticated users can manage channels" ON public.channels;

-- Create new policies
CREATE POLICY "Everyone can view message_channels" ON public.message_channels FOR SELECT USING (true);
CREATE POLICY "Authenticated users can manage message_channels" ON public.message_channels FOR ALL USING (auth.uid() IS NOT NULL);
CREATE POLICY "Everyone can view channels" ON public.channels FOR SELECT USING (true);
CREATE POLICY "Authenticated users can manage channels" ON public.channels FOR ALL USING (auth.uid() IS NOT NULL);

-- Grant permissions
GRANT SELECT, INSERT, UPDATE, DELETE ON public.message_channels TO authenticated;
GRANT SELECT, INSERT, UPDATE, DELETE ON public.channels TO authenticated;

-- 9. Final verification
DO $$
DECLARE
    message_channels_count INTEGER;
    channels_count INTEGER;
    trigger_exists BOOLEAN;
BEGIN
    RAISE NOTICE '';
    RAISE NOTICE '=== CHANNEL TABLE FIX COMPLETE ===';
    
    SELECT COUNT(*) INTO message_channels_count FROM public.message_channels;
    SELECT COUNT(*) INTO channels_count FROM public.channels;
    
    SELECT EXISTS (
        SELECT 1 FROM information_schema.triggers 
        WHERE trigger_name = 'trigger_create_message_notifications'
    ) INTO trigger_exists;
    
    RAISE NOTICE 'message_channels records: %', message_channels_count;
    RAISE NOTICE 'channels records: %', channels_count;
    RAISE NOTICE 'notification trigger exists: %', trigger_exists;
    
    IF message_channels_count > 0 AND channels_count > 0 AND trigger_exists THEN
        RAISE NOTICE '✓ Channel tables are properly set up!';
        RAISE NOTICE '✓ Message notifications should now work correctly';
        RAISE NOTICE '✓ Try sending a message to test the system';
    ELSE
        RAISE NOTICE '⚠ Some components may need attention';
    END IF;
    
    RAISE NOTICE '';
END $$;
