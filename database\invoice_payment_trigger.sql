-- Automatic Revenue Creation Trigger for Paid Invoices
-- Run this in your Supabase SQL Editor

-- Function to automatically create revenue entry when invoice is marked as paid
CREATE OR REPLACE FUNCTION create_revenue_from_paid_invoice()
RETURNS TRIGGER AS $$
BEGIN
  -- Only proceed if status changed to 'paid' (case insensitive) and it wasn't paid before
  IF LOWER(NEW.status) = 'paid' AND (OLD.status IS NULL OR LOWER(OLD.status) != 'paid') THEN
    
    -- Check if revenue entry already exists for this invoice
    IF NOT EXISTS (
      SELECT 1 FROM revenue_entries 
      WHERE invoice_number = NEW.document_number
    ) THEN
      
      -- Insert revenue entry for the paid invoice
      INSERT INTO revenue_entries (
        client_name,
        project_name,
        amount,
        revenue_date,
        payment_method,
        category,
        description,
        status,
        invoice_number,
        reference_number
      ) VALUES (
        NEW.client_name,
        NEW.project_name,
        NEW.total_amount,
        CURRENT_DATE, -- Use current date as payment date
        'Invoice Payment', -- Default payment method
        'Invoice Payment', -- Category
        'Payment received for invoice ' || NEW.document_number,
        'received', -- Status
        NEW.document_number, -- Invoice number
        'AUTO-' || NEW.document_number -- Auto-generated reference
      );
      
      -- Log the automatic revenue creation
      RAISE NOTICE 'Automatic revenue entry created for paid invoice: %', NEW.document_number;
    ELSE
      RAISE NOTICE 'Revenue entry already exists for invoice: %', NEW.document_number;
    END IF;
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for documents table (invoices)
DROP TRIGGER IF EXISTS invoice_payment_revenue_trigger ON documents;
CREATE TRIGGER invoice_payment_revenue_trigger
  AFTER UPDATE ON documents
  FOR EACH ROW
  WHEN (NEW.type = 'invoice')
  EXECUTE FUNCTION create_revenue_from_paid_invoice();

-- Create trigger for client_invoices table (if using this table)
DROP TRIGGER IF EXISTS client_invoice_payment_revenue_trigger ON client_invoices;
CREATE TRIGGER client_invoice_payment_revenue_trigger
  AFTER UPDATE ON client_invoices
  FOR EACH ROW
  EXECUTE FUNCTION create_revenue_from_paid_invoice();

-- Test the trigger (optional - remove in production)
-- UPDATE documents SET status = 'paid' WHERE document_number = 'TEST-001' AND type = 'invoice';

COMMENT ON FUNCTION create_revenue_from_paid_invoice() IS 'Automatically creates revenue entries when invoices are marked as paid';
COMMENT ON TRIGGER invoice_payment_revenue_trigger ON documents IS 'Triggers automatic revenue creation for paid invoices in documents table';
COMMENT ON TRIGGER client_invoice_payment_revenue_trigger ON client_invoices IS 'Triggers automatic revenue creation for paid invoices in client_invoices table';
