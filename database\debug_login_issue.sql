-- Debug Login Issue - Check User Registration Data
-- This helps identify why login is failing after successful registration

-- 1. Check user profiles for the registered email
SELECT 
    up.id as profile_id,
    up.email,
    up.first_name,
    up.last_name,
    up.user_id,
    up.is_verified,
    up.email_status,
    up.is_active,
    ur.role_name,
    up.created_at,
    up.updated_at
FROM public.user_profiles up
LEFT JOIN public.user_roles ur ON up.role_id = ur.id
WHERE up.email = '<EMAIL>'
ORDER BY up.created_at DESC;

-- 2. Check if there are auth users for this email
-- Note: This might not work if RLS is enabled on auth.users
SELECT 
    'Auth user check' as check_type,
    COUNT(*) as count
FROM auth.users 
WHERE email = '<EMAIL>';

-- 3. Check for duplicate profiles
SELECT 
    email,
    COUNT(*) as profile_count,
    array_agg(id) as profile_ids,
    array_agg(user_id) as auth_user_ids
FROM public.user_profiles 
WHERE email = '<EMAIL>'
GROUP BY email
HAVING COUNT(*) > 1;

-- 4. Check recent registrations (last 24 hours)
SELECT 
    up.email,
    up.first_name,
    up.last_name,
    up.user_id,
    up.is_verified,
    up.email_status,
    ur.role_name,
    up.created_at
FROM public.user_profiles up
LEFT JOIN public.user_roles ur ON up.role_id = ur.id
WHERE up.created_at > NOW() - INTERVAL '24 hours'
ORDER BY up.created_at DESC;

-- 5. Create a function to test login credentials
CREATE OR REPLACE FUNCTION public.debug_login_attempt(
    p_email VARCHAR(255)
)
RETURNS jsonb AS $$
DECLARE
    v_profile_record RECORD;
    v_auth_count INTEGER;
    result jsonb;
BEGIN
    -- Get profile information
    SELECT * INTO v_profile_record
    FROM public.user_profiles
    WHERE email = p_email;
    
    -- Count auth users (might fail due to RLS)
    BEGIN
        SELECT COUNT(*) INTO v_auth_count
        FROM auth.users
        WHERE email = p_email;
    EXCEPTION
        WHEN OTHERS THEN
            v_auth_count := -1; -- Indicates we couldn't check
    END;
    
    -- Build result
    result := jsonb_build_object(
        'email', p_email,
        'profile_exists', (v_profile_record.id IS NOT NULL),
        'profile_id', v_profile_record.id,
        'user_id', v_profile_record.user_id,
        'is_verified', v_profile_record.is_verified,
        'is_active', v_profile_record.is_active,
        'email_status', v_profile_record.email_status,
        'auth_user_count', v_auth_count,
        'diagnosis', CASE
            WHEN v_profile_record.id IS NULL THEN 'No profile found'
            WHEN v_profile_record.user_id IS NULL THEN 'Profile exists but no auth user linked'
            WHEN v_profile_record.is_verified = false THEN 'Profile not verified'
            WHEN v_profile_record.is_active = false THEN 'Profile not active'
            WHEN v_auth_count = 0 THEN 'No auth user found'
            WHEN v_auth_count > 1 THEN 'Multiple auth users found'
            ELSE 'Profile and auth user look correct'
        END
    );
    
    RETURN result;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant permissions
GRANT EXECUTE ON FUNCTION public.debug_login_attempt(VARCHAR) TO authenticated, anon;

-- 6. Test the debug function
SELECT public.debug_login_attempt('<EMAIL>') as login_debug;

-- 7. Provide diagnosis and recommendations
DO $$
DECLARE
    debug_result jsonb;
    diagnosis TEXT;
BEGIN
    RAISE NOTICE '';
    RAISE NOTICE '=== LOGIN ISSUE DIAGNOSIS ===';
    
    -- Get debug information
    SELECT public.debug_login_attempt('<EMAIL>') INTO debug_result;
    
    diagnosis := debug_result->>'diagnosis';
    
    RAISE NOTICE 'Email: %', debug_result->>'email';
    RAISE NOTICE 'Profile exists: %', debug_result->>'profile_exists';
    RAISE NOTICE 'User ID linked: %', debug_result->>'user_id';
    RAISE NOTICE 'Is verified: %', debug_result->>'is_verified';
    RAISE NOTICE 'Is active: %', debug_result->>'is_active';
    RAISE NOTICE 'Email status: %', debug_result->>'email_status';
    RAISE NOTICE 'Auth user count: %', debug_result->>'auth_user_count';
    RAISE NOTICE '';
    RAISE NOTICE 'DIAGNOSIS: %', diagnosis;
    RAISE NOTICE '';
    
    -- Provide specific recommendations
    CASE diagnosis
        WHEN 'No profile found' THEN
            RAISE NOTICE 'SOLUTION: User needs to be created in admin panel first';
        WHEN 'Profile exists but no auth user linked' THEN
            RAISE NOTICE 'SOLUTION: Registration process failed - try registration again';
        WHEN 'Profile not verified' THEN
            RAISE NOTICE 'SOLUTION: Set is_verified = true in user profile';
        WHEN 'Profile not active' THEN
            RAISE NOTICE 'SOLUTION: Set is_active = true in user profile';
        WHEN 'No auth user found' THEN
            RAISE NOTICE 'SOLUTION: Auth user creation failed - try registration again';
        WHEN 'Multiple auth users found' THEN
            RAISE NOTICE 'SOLUTION: Clean up duplicate auth users';
        ELSE
            RAISE NOTICE 'SOLUTION: Check password or try password reset';
    END CASE;
    
    RAISE NOTICE '';
    RAISE NOTICE '=== END DIAGNOSIS ===';
END $$;
