import React, { useState, useEffect } from 'react';
import Layout from '@/components/layout/Layout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useToast } from '@/components/ui/use-toast';
import { useAuth } from '@/contexts/AuthContext';
import { useRoleAccess } from '@/hooks/useRoleAccess';
import { TimeTrackingService, TimeEntry, Site } from '@/lib/timeTrackingService';
import { initializeTimeTracking, createSampleSites } from '@/lib/initializeTimeTracking';
import ClockInOut from '@/components/time-tracking/ClockInOut';
import TimeEntryHistory from '@/components/time-tracking/TimeEntryHistory';
import SiteAttendance from '@/components/time-tracking/SiteAttendance';
import TimeTrackingStats from '@/components/time-tracking/TimeTrackingStats';
import SiteManagement from '@/components/time-tracking/SiteManagement';
import {
  Clock,
  MapPin,
  Users,
  BarChart3,
  Calendar,
  Timer,
  PlayCircle,
  StopCircle,
  PauseCircle
} from 'lucide-react';

const TimeTracking = () => {
  const { toast } = useToast();
  const { user } = useAuth();
  const { userRole, permissions } = useRoleAccess();
  const [loading, setLoading] = useState(true);
  const [activeTimeEntry, setActiveTimeEntry] = useState<TimeEntry | null>(null);
  const [sites, setSites] = useState<Site[]>([]);
  const [refreshTrigger, setRefreshTrigger] = useState(0);

  // Load initial data
  useEffect(() => {
    loadInitialData();
  }, [user, refreshTrigger]);

  const loadInitialData = async () => {
    if (!user) return;

    setLoading(true);
    try {
      // Initialize and check time tracking setup
      const initResult = await initializeTimeTracking();

      if (!initResult.success) {
        toast({
          title: "Database Setup Required",
          description: initResult.error,
          variant: "destructive",
        });
        setLoading(false);
        return;
      }

      // Load sites
      const sitesData = await TimeTrackingService.getSites();
      setSites(sitesData);

      // Create sample sites if none exist
      if (sitesData.length === 0) {
        const sampleResult = await createSampleSites();
        if (sampleResult.success) {
          const updatedSites = await TimeTrackingService.getSites();
          setSites(updatedSites);
          toast({
            title: "Sample Sites Created",
            description: "Created sample work sites to get you started",
          });
        }
      }

      // Load active time entry for current user (non-clients only)
      if (userRole !== 'client') {
        const activeEntry = await TimeTrackingService.getActiveTimeEntry(user.id);
        setActiveTimeEntry(activeEntry);
      }
    } catch (error) {
      console.error('Error loading time tracking data:', error);
      toast({
        title: "Error",
        description: "Failed to load time tracking data. Please ensure the database schema is installed.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const handleClockAction = () => {
    setRefreshTrigger(prev => prev + 1);
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'active':
        return <Badge className="bg-green-100 text-green-800"><PlayCircle className="w-3 h-3 mr-1" />Active</Badge>;
      case 'break':
        return <Badge className="bg-yellow-100 text-yellow-800"><PauseCircle className="w-3 h-3 mr-1" />On Break</Badge>;
      case 'completed':
        return <Badge className="bg-gray-100 text-gray-800"><StopCircle className="w-3 h-3 mr-1" />Completed</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  if (loading) {
    return (
      <Layout>
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <Timer className="h-12 w-12 animate-spin text-blue-600 mx-auto mb-4" />
            <p className="text-gray-600">Loading time tracking...</p>
          </div>
        </div>
      </Layout>
    );
  }

  // Client view - restricted access
  if (userRole === 'client') {
    return (
      <Layout>
        <div className="container mx-auto p-6">
          <div className="text-center py-12">
            <Clock className="h-16 w-16 text-gray-400 mx-auto mb-4" />
            <h2 className="text-xl font-semibold text-gray-600 mb-2">Time Tracking</h2>
            <p className="text-gray-500">
              Time tracking is not available for client accounts. 
              Contact your project manager for work progress updates.
            </p>
          </div>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="container mx-auto p-6 space-y-6">
        {/* Header */}
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold">Time Tracking</h1>
            <p className="text-muted-foreground">
              Track work hours, manage sites, and monitor attendance
            </p>
          </div>
          <div className="flex items-center gap-2">
            <Badge variant="outline" className="text-sm">
              Role: {userRole.charAt(0).toUpperCase() + userRole.slice(1)}
            </Badge>
            {activeTimeEntry && getStatusBadge(activeTimeEntry.status)}
          </div>
        </div>

        {/* Quick Status Card */}
        {activeTimeEntry && (
          <Card className="border-green-200 bg-green-50">
            <CardHeader className="pb-3">
              <CardTitle className="flex items-center gap-2 text-green-800">
                <PlayCircle className="h-5 w-5" />
                Currently Working
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                <div>
                  <p className="text-green-600 font-medium">Site</p>
                  <p className="text-green-800">{activeTimeEntry.site?.name || 'Unknown Site'}</p>
                </div>
                <div>
                  <p className="text-green-600 font-medium">Started</p>
                  <p className="text-green-800">
                    {new Date(activeTimeEntry.clock_in_time).toLocaleTimeString()}
                  </p>
                </div>
                <div>
                  <p className="text-green-600 font-medium">Duration</p>
                  <p className="text-green-800">
                    {Math.round((Date.now() - new Date(activeTimeEntry.clock_in_time).getTime()) / (1000 * 60))} minutes
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Main Content Tabs */}
        <Tabs defaultValue="clock" className="space-y-6">
          <TabsList className="grid w-full grid-cols-5">
            <TabsTrigger value="clock" className="flex items-center gap-2">
              <Clock className="w-4 h-4" />
              <span className="hidden sm:inline">Clock In/Out</span>
            </TabsTrigger>
            <TabsTrigger value="history" className="flex items-center gap-2">
              <Calendar className="w-4 h-4" />
              <span className="hidden sm:inline">History</span>
            </TabsTrigger>
            {(permissions.canViewAllUsers || userRole === 'admin' || userRole === 'management') && (
              <>
                <TabsTrigger value="attendance" className="flex items-center gap-2">
                  <Users className="w-4 h-4" />
                  <span className="hidden sm:inline">Attendance</span>
                </TabsTrigger>
                <TabsTrigger value="analytics" className="flex items-center gap-2">
                  <BarChart3 className="w-4 h-4" />
                  <span className="hidden sm:inline">Analytics</span>
                </TabsTrigger>
                <TabsTrigger value="sites" className="flex items-center gap-2">
                  <MapPin className="w-4 h-4" />
                  <span className="hidden sm:inline">Sites</span>
                </TabsTrigger>
              </>
            )}
          </TabsList>

          {/* Clock In/Out Tab */}
          <TabsContent value="clock">
            <ClockInOut
              activeTimeEntry={activeTimeEntry}
              sites={sites}
              onClockAction={handleClockAction}
            />
          </TabsContent>

          {/* History Tab */}
          <TabsContent value="history">
            <TimeEntryHistory userId={user?.id} />
          </TabsContent>

          {/* Attendance Tab (Management/Admin only) */}
          {(permissions.canViewAllUsers || userRole === 'admin' || userRole === 'management') && (
            <TabsContent value="attendance">
              <SiteAttendance sites={sites} />
            </TabsContent>
          )}

          {/* Analytics Tab (Management/Admin only) */}
          {(permissions.canViewAllUsers || userRole === 'admin' || userRole === 'management') && (
            <TabsContent value="analytics">
              <TimeTrackingStats />
            </TabsContent>
          )}

          {/* Sites Tab (Management/Admin only) */}
          {(permissions.canViewAllUsers || userRole === 'admin' || userRole === 'management') && (
            <TabsContent value="sites">
              <SiteManagement
                sites={sites}
                onSiteUpdate={() => setRefreshTrigger(prev => prev + 1)}
              />
            </TabsContent>
          )}
        </Tabs>
      </div>
    </Layout>
  );
};

export default TimeTracking;
