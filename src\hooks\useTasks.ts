import { useState, useEffect, useCallback } from 'react'
import { TaskService, Task } from '@/lib/tasks'

interface UseTasksReturn {
  tasks: Task[]
  loading: boolean
  error: string | null
  addTask: (task: Omit<Task, 'id' | 'created_at' | 'updated_at'>) => Promise<void>
  updateTask: (id: string, updates: Partial<Omit<Task, 'id' | 'created_at'>>) => Promise<void>
  deleteTask: (id: string) => Promise<void>
  refreshTasks: () => Promise<void>
  getTasksByProject: (projectId: string) => Task[]
  getTaskStats: (projectId?: string) => Promise<any>
  isOnline: boolean
  syncStatus: 'idle' | 'syncing' | 'synced' | 'error' | 'offline'
}

export const useTasks = (): UseTasksReturn => {
  // Start with empty array and load from database
  const [tasks, setTasks] = useState<Task[]>([])
  const [loading, setLoading] = useState(true) // Start as true to show loading state
  const [error, setError] = useState<string | null>(null)
  const [isOnline, setIsOnline] = useState(navigator.onLine)
  const [syncStatus, setSyncStatus] = useState<'idle' | 'syncing' | 'synced' | 'error' | 'offline'>('synced') // Start as synced to prevent loops

  // Monitor online status
  useEffect(() => {
    const handleOnline = () => {
      setIsOnline(true)
      setSyncStatus('idle')
      // Don't auto-refresh to prevent loops - user can manually refresh
      console.log('Tasks: Connection restored')
    }
    
    const handleOffline = () => {
      setIsOnline(false)
      setSyncStatus('offline')
    }

    window.addEventListener('online', handleOnline)
    window.addEventListener('offline', handleOffline)

    return () => {
      window.removeEventListener('online', handleOnline)
      window.removeEventListener('offline', handleOffline)
    }
  }, [])

  // Load tasks from Supabase
  const loadTasks = useCallback(async () => {
    try {
      setSyncStatus('syncing')
      setError(null)
      
      const data = await TaskService.getTasks()
      setTasks(data)
      setSyncStatus('synced')
      
      console.log('Tasks loaded successfully:', data.length)
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to load tasks'
      console.error('Error loading tasks:', err)
      setError(errorMessage)
      setSyncStatus('error')
      
      // Try to load from localStorage as fallback
      try {
        const localTasks = localStorage.getItem('tasks')
        if (localTasks) {
          setTasks(JSON.parse(localTasks))
          console.log('Loaded tasks from localStorage fallback')
        }
      } catch (localError) {
        console.error('Failed to load from localStorage:', localError)
      }
    } finally {
      setLoading(false)
    }
  }, [])

  // Initial load (re-enabled for Kanban functionality)
  useEffect(() => {
    console.log('Tasks: Loading tasks for Kanban functionality')
    loadTasks()
  }, [loadTasks])

  // Save to localStorage for offline access
  useEffect(() => {
    if (tasks.length > 0) {
      localStorage.setItem('tasks', JSON.stringify(tasks))
    }
  }, [tasks])

  // Add task
  const addTask = useCallback(async (taskData: Omit<Task, 'id' | 'created_at' | 'updated_at'>) => {
    try {
      setSyncStatus('syncing')
      
      if (!isOnline) {
        // Offline mode - add to local state with temporary ID
        const tempTask: Task = {
          ...taskData,
          id: `temp_${Date.now()}`,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        }
        setTasks(prev => [tempTask, ...prev])
        setSyncStatus('offline')
        return
      }

      const newTask = await TaskService.addTask(taskData)
      setTasks(prev => [newTask, ...prev])
      setSyncStatus('synced')
      
      console.log('Task added successfully:', newTask)
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to add task'
      console.error('Error adding task:', err)
      setError(errorMessage)
      setSyncStatus('error')
      throw err
    }
  }, [isOnline])

  // Update task
  const updateTask = useCallback(async (id: string, updates: Partial<Omit<Task, 'id' | 'created_at'>>) => {
    try {
      setSyncStatus('syncing')
      
      // Update local state immediately for better UX
      setTasks(prev => prev.map(task => 
        task.id === id 
          ? { ...task, ...updates, updated_at: new Date().toISOString() }
          : task
      ))

      if (!isOnline) {
        setSyncStatus('offline')
        return
      }

      const updatedTask = await TaskService.updateTask(id, updates)
      
      // Update with server response
      setTasks(prev => prev.map(task => 
        task.id === id ? updatedTask : task
      ))
      
      setSyncStatus('synced')
      console.log('Task updated successfully:', updatedTask)
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to update task'
      console.error('Error updating task:', err)
      setError(errorMessage)
      setSyncStatus('error')
      
      // Revert local changes on error
      loadTasks()
      throw err
    }
  }, [isOnline]) // Remove loadTasks dependency

  // Delete task
  const deleteTask = useCallback(async (id: string) => {
    try {
      setSyncStatus('syncing')
      
      // Remove from local state immediately
      const taskToDelete = tasks.find(task => task.id === id)
      setTasks(prev => prev.filter(task => task.id !== id))

      if (!isOnline) {
        setSyncStatus('offline')
        return
      }

      await TaskService.deleteTask(id)
      setSyncStatus('synced')
      
      console.log('Task deleted successfully:', id)
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to delete task'
      console.error('Error deleting task:', err)
      setError(errorMessage)
      setSyncStatus('error')
      
      // Revert local changes on error
      loadTasks()
      throw err
    }
  }, [isOnline, tasks]) // Remove loadTasks dependency

  // Refresh tasks
  const refreshTasks = useCallback(async () => {
    setLoading(true)
    await loadTasks()
  }, []) // Remove loadTasks dependency

  // Get tasks by project
  const getTasksByProject = useCallback((projectId: string): Task[] => {
    return tasks.filter(task => task.project_id === projectId)
  }, [tasks])

  // Get task statistics
  const getTaskStats = useCallback(async (projectId?: string) => {
    try {
      if (!isOnline) {
        // Calculate stats from local data
        const filteredTasks = projectId 
          ? tasks.filter(task => task.project_id === projectId)
          : tasks

        const totalTasks = filteredTasks.length
        const completedTasks = filteredTasks.filter(task => task.status === 'Completed').length
        const inProgressTasks = filteredTasks.filter(task => task.status === 'In Progress').length
        const overdueTasks = filteredTasks.filter(task => 
          new Date(task.end_date) < new Date() && task.status !== 'Completed'
        ).length

        const averageProgress = totalTasks > 0 
          ? filteredTasks.reduce((sum, task) => sum + task.progress, 0) / totalTasks 
          : 0

        return {
          totalTasks,
          completedTasks,
          inProgressTasks,
          overdueTasks,
          averageProgress,
          completionRate: totalTasks > 0 ? (completedTasks / totalTasks) * 100 : 0
        }
      }

      return await TaskService.getTaskStats(projectId)
    } catch (err) {
      console.error('Error getting task stats:', err)
      throw err
    }
  }, [isOnline, tasks])

  // Set up real-time subscription (temporarily disabled to prevent loops)
  useEffect(() => {
    // Temporarily disabled real-time subscriptions
    console.log('Real-time subscriptions disabled to prevent refresh loops')
    return () => {
      // No cleanup needed
    }
  }, [])

  return {
    tasks,
    loading,
    error,
    addTask,
    updateTask,
    deleteTask,
    refreshTasks,
    getTasksByProject,
    getTaskStats,
    isOnline,
    syncStatus
  }
}
