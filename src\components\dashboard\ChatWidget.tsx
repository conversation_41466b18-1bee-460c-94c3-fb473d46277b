
import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON>nt, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Send, MessageCircle, User, Bot } from 'lucide-react';

interface Message {
  id: string;
  content: string;
  sender: 'user' | 'ai' | 'system';
  timestamp: Date;
}

const ChatWidget = () => {
  const [messages, setMessages] = useState<Message[]>([
    {
      id: '1',
      content: 'Welcome to Martcosy Construction Management System! How can I assist you today?',
      sender: 'ai',
      timestamp: new Date()
    },
    {
      id: '2',
      content: 'System notification: Daily attendance has been updated for all active projects.',
      sender: 'system',
      timestamp: new Date()
    }
  ]);
  const [newMessage, setNewMessage] = useState('');

  const handleSendMessage = () => {
    if (newMessage.trim()) {
      const message: Message = {
        id: Date.now().toString(),
        content: newMessage,
        sender: 'user',
        timestamp: new Date()
      };
      setMessages(prev => [...prev, message]);
      setNewMessage('');
      
      // Simulate AI response
      setTimeout(() => {
        const aiResponse: Message = {
          id: (Date.now() + 1).toString(),
          content: 'I understand your request. Let me help you with that information.',
          sender: 'ai',
          timestamp: new Date()
        };
        setMessages(prev => [...prev, aiResponse]);
      }, 1000);
    }
  };

  const getMessageIcon = (sender: string) => {
    switch (sender) {
      case 'user':
        return <User className="w-4 h-4" />;
      case 'ai':
        return <Bot className="w-4 h-4" />;
      default:
        return <MessageCircle className="w-4 h-4" />;
    }
  };

  const getMessageStyle = (sender: string) => {
    switch (sender) {
      case 'user':
        return 'bg-blue-500 text-white ml-auto';
      case 'ai':
        return 'bg-gray-100 text-gray-900';
      case 'system':
        return 'bg-orange-100 text-orange-900 border border-orange-200';
      default:
        return 'bg-gray-100 text-gray-900';
    }
  };

  return (
    <Card className="shadow-lg border-0 bg-gradient-to-br from-white to-gray-50 h-96">
      <CardHeader className="pb-3">
        <CardTitle className="font-heading text-lg flex items-center">
          <MessageCircle className="w-5 h-5 mr-2 text-blue-600" />
          AI Assistant & Chat
        </CardTitle>
      </CardHeader>
      <CardContent className="flex flex-col h-80">
        <div className="flex-1 overflow-y-auto space-y-3 mb-4">
          {messages.map((message) => (
            <div key={message.id} className={`flex items-start space-x-2 ${message.sender === 'user' ? 'justify-end' : ''}`}>
              {message.sender !== 'user' && (
                <div className="w-6 h-6 bg-blue-600 rounded-full flex items-center justify-center text-white">
                  {getMessageIcon(message.sender)}
                </div>
              )}
              <div className={`max-w-xs px-3 py-2 rounded-lg text-sm ${getMessageStyle(message.sender)}`}>
                {message.content}
              </div>
              {message.sender === 'user' && (
                <div className="w-6 h-6 bg-purple-600 rounded-full flex items-center justify-center text-white">
                  {getMessageIcon(message.sender)}
                </div>
              )}
            </div>
          ))}
        </div>
        <div className="flex space-x-2">
          <Input
            value={newMessage}
            onChange={(e) => setNewMessage(e.target.value)}
            placeholder="Type your message..."
            onKeyPress={(e) => e.key === 'Enter' && handleSendMessage()}
            className="flex-1"
          />
          <Button onClick={handleSendMessage} size="sm" className="bg-blue-600 hover:bg-blue-700">
            <Send className="w-4 h-4" />
          </Button>
        </div>
      </CardContent>
    </Card>
  );
};

export default ChatWidget;
