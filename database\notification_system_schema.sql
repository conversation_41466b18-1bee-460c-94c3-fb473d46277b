-- Comprehensive Notification System Database Schema
-- Run this script in your Supabase SQL Editor

-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create notifications table
CREATE TABLE IF NOT EXISTS public.notifications (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  type VARCHAR(50) NOT NULL CHECK (type IN ('info', 'warning', 'success', 'error', 'reminder')),
  category VARCHAR(50) NOT NULL CHECK (category IN ('project', 'financial', 'system', 'message', 'user', 'invoice', 'time_tracking', 'asset')),
  priority VARCHAR(20) NOT NULL DEFAULT 'medium' CHECK (priority IN ('low', 'medium', 'high', 'urgent')),
  title VARCHAR(255) NOT NULL,
  message TEXT NOT NULL,
  action_url TEXT,
  action_label VARCHAR(100),
  read BOOLEAN DEFAULT FALSE,
  email_sent BOOLEAN DEFAULT FALSE,
  email_sent_at TIMESTAMP WITH TIME ZONE,
  related_entity_type VARCHAR(50), -- 'project', 'invoice', 'message', etc.
  related_entity_id UUID,
  metadata JSONB DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  expires_at TIMESTAMP WITH TIME ZONE,
  created_by UUID REFERENCES auth.users(id)
);

-- Create notification templates table
CREATE TABLE IF NOT EXISTS public.notification_templates (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name VARCHAR(100) UNIQUE NOT NULL,
  category VARCHAR(50) NOT NULL,
  type VARCHAR(50) NOT NULL,
  priority VARCHAR(20) DEFAULT 'medium',
  title_template TEXT NOT NULL,
  message_template TEXT NOT NULL,
  email_subject_template TEXT,
  email_body_template TEXT,
  action_url_template TEXT,
  action_label VARCHAR(100),
  variables JSONB DEFAULT '[]', -- Array of variable names used in templates
  is_active BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create email queue table for batched email sending
CREATE TABLE IF NOT EXISTS public.email_queue (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  notification_id UUID REFERENCES public.notifications(id) ON DELETE CASCADE,
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  to_email VARCHAR(255) NOT NULL,
  subject VARCHAR(255) NOT NULL,
  html_content TEXT NOT NULL,
  text_content TEXT NOT NULL,
  status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'sending', 'sent', 'failed', 'cancelled')),
  attempts INTEGER DEFAULT 0,
  max_attempts INTEGER DEFAULT 3,
  scheduled_for TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  sent_at TIMESTAMP WITH TIME ZONE,
  error_message TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create notification preferences table (enhanced version)
CREATE TABLE IF NOT EXISTS public.user_notification_preferences (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID UNIQUE NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  
  -- General notification settings
  email_notifications BOOLEAN DEFAULT TRUE,
  push_notifications BOOLEAN DEFAULT TRUE,
  sms_notifications BOOLEAN DEFAULT FALSE,
  
  -- Category-specific settings
  project_updates BOOLEAN DEFAULT TRUE,
  financial_alerts BOOLEAN DEFAULT TRUE,
  time_tracking_reminders BOOLEAN DEFAULT TRUE,
  team_messages BOOLEAN DEFAULT TRUE,
  system_announcements BOOLEAN DEFAULT TRUE,
  invoice_notifications BOOLEAN DEFAULT TRUE,
  deadline_reminders BOOLEAN DEFAULT TRUE,
  asset_notifications BOOLEAN DEFAULT TRUE,
  user_management_notifications BOOLEAN DEFAULT TRUE,
  
  -- Email-specific settings
  email_project_updates BOOLEAN DEFAULT TRUE,
  email_financial_alerts BOOLEAN DEFAULT TRUE,
  email_time_tracking_reminders BOOLEAN DEFAULT FALSE,
  email_team_messages BOOLEAN DEFAULT FALSE,
  email_system_announcements BOOLEAN DEFAULT TRUE,
  email_invoice_notifications BOOLEAN DEFAULT TRUE,
  email_deadline_reminders BOOLEAN DEFAULT TRUE,
  email_asset_notifications BOOLEAN DEFAULT TRUE,
  email_user_management_notifications BOOLEAN DEFAULT TRUE,
  
  -- Frequency and timing
  notification_frequency VARCHAR(20) DEFAULT 'immediate' CHECK (notification_frequency IN ('immediate', 'hourly', 'daily', 'weekly')),
  email_digest_frequency VARCHAR(20) DEFAULT 'daily' CHECK (email_digest_frequency IN ('immediate', 'hourly', 'daily', 'weekly', 'never')),
  quiet_hours_enabled BOOLEAN DEFAULT FALSE,
  quiet_hours_start TIME DEFAULT '22:00',
  quiet_hours_end TIME DEFAULT '08:00',
  timezone VARCHAR(50) DEFAULT 'UTC',
  
  -- Security settings
  two_factor_enabled BOOLEAN DEFAULT FALSE,
  login_alerts BOOLEAN DEFAULT TRUE,
  session_timeout INTEGER DEFAULT 30, -- minutes
  
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_notifications_user_id ON public.notifications(user_id);
CREATE INDEX IF NOT EXISTS idx_notifications_read ON public.notifications(read);
CREATE INDEX IF NOT EXISTS idx_notifications_category ON public.notifications(category);
CREATE INDEX IF NOT EXISTS idx_notifications_created_at ON public.notifications(created_at);
CREATE INDEX IF NOT EXISTS idx_notifications_expires_at ON public.notifications(expires_at);
CREATE INDEX IF NOT EXISTS idx_email_queue_status ON public.email_queue(status);
CREATE INDEX IF NOT EXISTS idx_email_queue_scheduled_for ON public.email_queue(scheduled_for);
CREATE INDEX IF NOT EXISTS idx_email_queue_user_id ON public.email_queue(user_id);

-- Create updated_at trigger function
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create triggers for updated_at
DROP TRIGGER IF EXISTS update_notifications_updated_at ON public.notifications;
CREATE TRIGGER update_notifications_updated_at
  BEFORE UPDATE ON public.notifications
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_notification_templates_updated_at ON public.notification_templates;
CREATE TRIGGER update_notification_templates_updated_at
  BEFORE UPDATE ON public.notification_templates
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_email_queue_updated_at ON public.email_queue;
CREATE TRIGGER update_email_queue_updated_at
  BEFORE UPDATE ON public.email_queue
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_user_notification_preferences_updated_at ON public.user_notification_preferences;
CREATE TRIGGER update_user_notification_preferences_updated_at
  BEFORE UPDATE ON public.user_notification_preferences
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

-- Insert default notification templates
INSERT INTO public.notification_templates (name, category, type, priority, title_template, message_template, email_subject_template, email_body_template, action_url_template, action_label, variables) VALUES
-- Project notifications
('project_created', 'project', 'success', 'medium', 'New Project Created', 'Project "{{project_name}}" has been created successfully.', 'New Project: {{project_name}}', 'A new project "{{project_name}}" has been created and assigned to you.\n\nProject Details:\n- Name: {{project_name}}\n- Description: {{project_description}}\n- Start Date: {{start_date}}\n- End Date: {{end_date}}\n\nClick the link below to view the project details.', '/projects/{{project_id}}', 'View Project', '["project_name", "project_description", "project_id", "start_date", "end_date"]'),

('project_updated', 'project', 'info', 'medium', 'Project Updated', 'Project "{{project_name}}" has been updated.', 'Project Update: {{project_name}}', 'Project "{{project_name}}" has been updated.\n\nWhat changed:\n{{changes}}\n\nClick the link below to view the updated project.', '/projects/{{project_id}}', 'View Project', '["project_name", "project_id", "changes"]'),

('project_deadline_approaching', 'project', 'warning', 'high', 'Project Deadline Approaching', 'Project "{{project_name}}" deadline is in {{days_remaining}} days.', 'Deadline Alert: {{project_name}}', 'This is a reminder that project "{{project_name}}" is due in {{days_remaining}} days.\n\nDeadline: {{deadline_date}}\n\nPlease ensure all tasks are completed on time.', '/projects/{{project_id}}', 'View Project', '["project_name", "project_id", "days_remaining", "deadline_date"]'),

-- Financial notifications
('invoice_created', 'financial', 'success', 'medium', 'New Invoice Created', 'Invoice #{{invoice_number}} for {{client_name}} has been created.', 'New Invoice: #{{invoice_number}}', 'A new invoice has been created:\n\nInvoice #{{invoice_number}}\nClient: {{client_name}}\nAmount: ${{amount}}\nDue Date: {{due_date}}\n\nClick the link below to view the invoice.', '/invoices/{{invoice_id}}', 'View Invoice', '["invoice_number", "client_name", "amount", "due_date", "invoice_id"]'),

('payment_received', 'financial', 'success', 'high', 'Payment Received', 'Payment of ${{amount}} received for invoice #{{invoice_number}}.', 'Payment Received: ${{amount}}', 'Great news! We have received a payment:\n\nAmount: ${{amount}}\nInvoice: #{{invoice_number}}\nClient: {{client_name}}\nPayment Date: {{payment_date}}\n\nThe invoice has been marked as paid.', '/invoices/{{invoice_id}}', 'View Invoice', '["amount", "invoice_number", "client_name", "payment_date", "invoice_id"]'),

-- Message notifications
('new_message', 'message', 'info', 'medium', 'New Message', 'You have a new message from {{sender_name}} in {{channel_name}}.', 'New Message from {{sender_name}}', 'You have received a new message:\n\nFrom: {{sender_name}}\nChannel: {{channel_name}}\nMessage: {{message_preview}}\n\nClick the link below to view the full conversation.', '/messages?channel={{channel_id}}', 'View Message', '["sender_name", "channel_name", "message_preview", "channel_id"]'),

-- System notifications
('user_created', 'user', 'info', 'low', 'New User Account', 'A new user account has been created for {{user_name}}.', 'Welcome to the System', 'Welcome {{user_name}}!\n\nYour account has been successfully created. You can now access the construction management system.\n\nClick the link below to complete your profile setup.', '/settings/profile', 'Complete Profile', '["user_name"]'),

('system_maintenance', 'system', 'warning', 'high', 'System Maintenance', 'Scheduled maintenance on {{maintenance_date}} from {{start_time}} to {{end_time}}.', 'Scheduled Maintenance Notice', 'This is to inform you about scheduled system maintenance:\n\nDate: {{maintenance_date}}\nTime: {{start_time}} to {{end_time}}\nDuration: {{duration}}\n\nThe system will be temporarily unavailable during this time.', null, null, '["maintenance_date", "start_time", "end_time", "duration"]')

ON CONFLICT (name) DO UPDATE SET
  title_template = EXCLUDED.title_template,
  message_template = EXCLUDED.message_template,
  email_subject_template = EXCLUDED.email_subject_template,
  email_body_template = EXCLUDED.email_body_template,
  action_url_template = EXCLUDED.action_url_template,
  action_label = EXCLUDED.action_label,
  variables = EXCLUDED.variables,
  updated_at = NOW();

-- Function to create default notification preferences for new users
CREATE OR REPLACE FUNCTION create_default_notification_preferences()
RETURNS TRIGGER AS $$
BEGIN
  INSERT INTO public.user_notification_preferences (user_id)
  VALUES (NEW.id)
  ON CONFLICT (user_id) DO NOTHING;
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Trigger to create default preferences for new users
DROP TRIGGER IF EXISTS create_user_notification_preferences ON auth.users;
CREATE TRIGGER create_user_notification_preferences
  AFTER INSERT ON auth.users
  FOR EACH ROW
  EXECUTE FUNCTION create_default_notification_preferences();

-- Function to clean up expired notifications
CREATE OR REPLACE FUNCTION cleanup_expired_notifications()
RETURNS INTEGER AS $$
DECLARE
  deleted_count INTEGER;
BEGIN
  DELETE FROM public.notifications 
  WHERE expires_at IS NOT NULL AND expires_at < NOW();
  
  GET DIAGNOSTICS deleted_count = ROW_COUNT;
  RETURN deleted_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get user notification preferences with defaults
CREATE OR REPLACE FUNCTION get_user_notification_preferences(p_user_id UUID)
RETURNS TABLE (
  email_notifications BOOLEAN,
  category_enabled BOOLEAN,
  email_category_enabled BOOLEAN,
  frequency VARCHAR(20),
  email_frequency VARCHAR(20),
  quiet_hours_active BOOLEAN
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    COALESCE(unp.email_notifications, TRUE) as email_notifications,
    TRUE as category_enabled, -- Will be determined by specific category in application
    TRUE as email_category_enabled, -- Will be determined by specific category in application
    COALESCE(unp.notification_frequency, 'immediate') as frequency,
    COALESCE(unp.email_digest_frequency, 'daily') as email_frequency,
    CASE 
      WHEN unp.quiet_hours_enabled AND 
           CURRENT_TIME BETWEEN unp.quiet_hours_start AND unp.quiet_hours_end
      THEN TRUE
      ELSE FALSE
    END as quiet_hours_active
  FROM public.user_notification_preferences unp
  WHERE unp.user_id = p_user_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant necessary permissions
GRANT ALL ON public.notifications TO authenticated;
GRANT ALL ON public.notification_templates TO authenticated;
GRANT ALL ON public.email_queue TO authenticated;
GRANT ALL ON public.user_notification_preferences TO authenticated;

-- Enable Row Level Security
ALTER TABLE public.notifications ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.email_queue ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.user_notification_preferences ENABLE ROW LEVEL SECURITY;

-- Create RLS policies for notifications
CREATE POLICY "Users can view their own notifications" ON public.notifications
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can update their own notifications" ON public.notifications
  FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "System can insert notifications" ON public.notifications
  FOR INSERT WITH CHECK (true);

-- Create RLS policies for email queue
CREATE POLICY "Users can view their own email queue" ON public.email_queue
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "System can manage email queue" ON public.email_queue
  FOR ALL WITH CHECK (true);

-- Create RLS policies for notification preferences
CREATE POLICY "Users can view their own preferences" ON public.user_notification_preferences
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can update their own preferences" ON public.user_notification_preferences
  FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "System can insert preferences" ON public.user_notification_preferences
  FOR INSERT WITH CHECK (true);

-- Success message
DO $$
BEGIN
  RAISE NOTICE '✅ Notification system database schema created successfully!';
  RAISE NOTICE '📋 Tables created: notifications, notification_templates, email_queue, user_notification_preferences';
  RAISE NOTICE '🔧 Functions created: cleanup_expired_notifications, get_user_notification_preferences';
  RAISE NOTICE '🛡️ RLS policies enabled for data security';
  RAISE NOTICE '📝 Default notification templates inserted';
  RAISE NOTICE '';
  RAISE NOTICE '🚀 Next steps:';
  RAISE NOTICE '1. Create the NotificationService in your application';
  RAISE NOTICE '2. Integrate notification triggers in your components';
  RAISE NOTICE '3. Set up email sending configuration';
  RAISE NOTICE '4. Test the notification system';
END $$;
