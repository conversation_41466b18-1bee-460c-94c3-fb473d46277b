-- Simple Tasks Table Setup for Supabase
-- Copy and paste this entire script into your Supabase SQL Editor and run it

-- Step 1: Create the tasks table
CREATE TABLE IF NOT EXISTS public.tasks (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    name TEXT NOT NULL,
    description TEXT,
    status TEXT NOT NULL DEFAULT 'Not Started',
    priority TEXT NOT NULL DEFAULT 'Medium',
    start_date DATE,
    end_date DATE,
    progress INTEGER DEFAULT 0,
    assignee TEXT,
    project_id UUID NOT NULL,
    estimated_hours DECIMAL(10,2) DEFAULT 0,
    actual_hours DECIMAL(10,2) DEFAULT 0,
    dependencies JSONB DEFAULT '[]'::jsonb,
    tags JSONB DEFAULT '[]'::jsonb,
    parent_task_id UUID,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL
);

-- Step 2: Add basic constraints
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints
        WHERE constraint_name = 'chk_tasks_progress'
        AND table_name = 'tasks'
        AND table_schema = 'public'
    ) THEN
        ALTER TABLE public.tasks
        ADD CONSTRAINT chk_tasks_progress
        CHECK (progress >= 0 AND progress <= 100);
    END IF;
END $$;

-- Step 3: Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_tasks_project_id ON public.tasks(project_id);
CREATE INDEX IF NOT EXISTS idx_tasks_status ON public.tasks(status);
CREATE INDEX IF NOT EXISTS idx_tasks_created_at ON public.tasks(created_at);

-- Step 4: Create update trigger function
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = timezone('utc'::text, now());
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Step 5: Create the trigger
DROP TRIGGER IF EXISTS update_tasks_updated_at ON public.tasks;
CREATE TRIGGER update_tasks_updated_at
    BEFORE UPDATE ON public.tasks
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Step 6: Enable Row Level Security
ALTER TABLE public.tasks ENABLE ROW LEVEL SECURITY;

-- Step 7: Create simple RLS policy (allows all operations for now)
DROP POLICY IF EXISTS "Allow all operations for authenticated users" ON public.tasks;
CREATE POLICY "Allow all operations for authenticated users" ON public.tasks
    FOR ALL USING (true);

-- Step 8: Grant permissions
GRANT ALL ON public.tasks TO authenticated;
GRANT ALL ON public.tasks TO service_role;
GRANT ALL ON public.tasks TO anon;

-- Step 9: Insert a test task to verify everything works
DO $$
DECLARE
    test_project_id UUID;
BEGIN
    -- Get a project ID for testing (or create a dummy one)
    SELECT id INTO test_project_id FROM public.projects LIMIT 1;
    
    IF test_project_id IS NULL THEN
        -- Create a test project if none exists
        INSERT INTO public.projects (name, description, status, priority, start_date, end_date, budget, spent, progress, client_name, project_manager, location)
        VALUES ('Test Project', 'Test project for task setup', 'Planning', 'Medium', CURRENT_DATE, CURRENT_DATE + INTERVAL '30 days', 10000, 0, 0, 'Test Client', 'Test Manager', 'Test Location')
        RETURNING id INTO test_project_id;
    END IF;
    
    -- Insert a test task
    INSERT INTO public.tasks (name, description, status, priority, start_date, end_date, progress, assignee, project_id, estimated_hours, actual_hours)
    VALUES ('Setup Test Task', 'This task verifies the tasks table is working correctly', 'Completed', 'Low', CURRENT_DATE, CURRENT_DATE + INTERVAL '1 day', 100, 'System', test_project_id, 1, 1);
    
    RAISE NOTICE 'Tasks table setup completed successfully! Test task created.';
EXCEPTION
    WHEN OTHERS THEN
        RAISE NOTICE 'Error during setup: %', SQLERRM;
END $$;

-- Step 10: Verify the setup
SELECT 
    'Tasks table setup verification:' as status,
    COUNT(*) as total_tasks,
    COUNT(*) FILTER (WHERE name = 'Setup Test Task') as test_tasks
FROM public.tasks;

-- Show table structure
SELECT 
    column_name,
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_schema = 'public' 
AND table_name = 'tasks'
ORDER BY ordinal_position;
