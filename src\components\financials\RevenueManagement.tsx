import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Plus, DollarSign, Calendar, CreditCard, TrendingUp, Receipt, Building } from 'lucide-react';
import { useToast } from '@/components/ui/use-toast';
import { FinancialService } from '@/lib/financials';

interface RevenueEntry {
  id: string;
  client_name: string;
  project_name: string;
  amount: number;
  revenue_date: string;
  payment_method: string;
  category: string;
  description: string;
  status: 'received' | 'pending' | 'invoiced';
  invoice_number?: string;
  reference_number?: string;
}

interface RevenueManagementProps {
  onRevenueCreated: (revenue: RevenueEntry) => Promise<void>;
}

const RevenueManagement: React.FC<RevenueManagementProps> = ({ onRevenueCreated }) => {
  const { toast } = useToast();
  const [isOpen, setIsOpen] = useState(false);
  const [loading, setLoading] = useState(false);
  const [revenueEntries, setRevenueEntries] = useState<RevenueEntry[]>([]);

  // Load revenue entries on component mount
  useEffect(() => {
    loadRevenueEntries();
  }, []);

  const loadRevenueEntries = async () => {
    try {
      setLoading(true);
      const entries = await FinancialService.getRevenueEntries();
      setRevenueEntries(entries);
    } catch (error) {
      console.error('Error loading revenue entries:', error);
    } finally {
      setLoading(false);
    }
  };
  const [formData, setFormData] = useState({
    client_name: '',
    project_name: '',
    amount: '',
    revenue_date: new Date().toISOString().split('T')[0],
    payment_method: '',
    category: '',
    description: '',
    status: 'received' as const,
    invoice_number: '',
    reference_number: ''
  });

  const paymentMethods = [
    'Bank Transfer',
    'Cash',
    'Check',
    'Credit Card',
    'Online Payment',
    'Wire Transfer'
  ];

  const revenueCategories = [
    'Project Payment',
    'Consulting Fee',
    'Equipment Rental',
    'Material Sales',
    'Subcontractor Payment',
    'Maintenance Service',
    'Design Fee',
    'Other Revenue'
  ];

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.client_name || !formData.amount || !formData.category) {
      toast({
        title: "Validation Error",
        description: "Please fill in all required fields",
        variant: "destructive",
      });
      return;
    }

    setLoading(true);
    
    try {
      // Create revenue entry using the service
      const revenueData = {
        client_name: formData.client_name,
        project_name: formData.project_name,
        amount: parseFloat(formData.amount),
        revenue_date: formData.revenue_date,
        payment_method: formData.payment_method,
        category: formData.category,
        description: formData.description,
        status: formData.status,
        invoice_number: formData.invoice_number,
        reference_number: formData.reference_number
      };

      console.log('Creating revenue entry via service:', revenueData);

      // Use the financial service to create revenue entry
      const revenueEntry = await FinancialService.createRevenueEntry(revenueData);
      console.log('✅ Revenue entry created in database:', revenueEntry);

      // Reload revenue entries from database to get fresh data
      await loadRevenueEntries();

      // Call parent callback to refresh financial data
      await onRevenueCreated(revenueEntry as RevenueEntry);
      
      toast({
        title: "Success",
        description: `Revenue entry created: ${formData.category} - $${formData.amount}`,
      });

      // Reset form
      setFormData({
        client_name: '',
        project_name: '',
        amount: '',
        revenue_date: new Date().toISOString().split('T')[0],
        payment_method: '',
        category: '',
        description: '',
        status: 'received',
        invoice_number: '',
        reference_number: ''
      });
      
      setIsOpen(false);
    } catch (error) {
      console.error('Error creating revenue entry:', error);
      toast({
        title: "Error",
        description: "Failed to create revenue entry",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'received':
        return 'bg-green-100 text-green-800';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'invoiced':
        return 'bg-blue-100 text-blue-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold">Revenue Management</h2>
          <p className="text-gray-600">Track income and cash inflows</p>
        </div>
        <Dialog open={isOpen} onOpenChange={setIsOpen}>
          <DialogTrigger asChild>
            <Button>
              <Plus className="w-4 h-4 mr-2" />
              Add Revenue
            </Button>
          </DialogTrigger>
          <DialogContent className="sm:max-w-[600px]">
            <DialogHeader>
              <DialogTitle className="flex items-center space-x-2">
                <TrendingUp className="w-5 h-5" />
                <span>Add Revenue Entry</span>
              </DialogTitle>
              <DialogDescription>
                Record income from projects, payments, or other revenue sources.
              </DialogDescription>
            </DialogHeader>
            
            <form onSubmit={handleSubmit} className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="client_name">Client Name *</Label>
                  <Input
                    id="client_name"
                    value={formData.client_name}
                    onChange={(e) => setFormData({...formData, client_name: e.target.value})}
                    placeholder="ABC Construction Ltd"
                    required
                  />
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="project_name">Project Name</Label>
                  <Input
                    id="project_name"
                    value={formData.project_name}
                    onChange={(e) => setFormData({...formData, project_name: e.target.value})}
                    placeholder="Office Building Project"
                  />
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="category">Revenue Category *</Label>
                  <Select value={formData.category} onValueChange={(value) => setFormData({...formData, category: value})}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select category" />
                    </SelectTrigger>
                    <SelectContent>
                      {revenueCategories.map((category) => (
                        <SelectItem key={category} value={category}>
                          {category}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="amount">Amount *</Label>
                  <div className="relative">
                    <DollarSign className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                    <Input
                      id="amount"
                      type="number"
                      step="0.01"
                      min="0"
                      value={formData.amount}
                      onChange={(e) => setFormData({...formData, amount: e.target.value})}
                      placeholder="0.00"
                      className="pl-10"
                      required
                    />
                  </div>
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="description">Description *</Label>
                <Textarea
                  id="description"
                  value={formData.description}
                  onChange={(e) => setFormData({...formData, description: e.target.value})}
                  placeholder="Describe the revenue source..."
                  rows={2}
                  required
                />
              </div>

              <div className="grid grid-cols-3 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="revenue_date">Date *</Label>
                  <div className="relative">
                    <Calendar className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                    <Input
                      id="revenue_date"
                      type="date"
                      value={formData.revenue_date}
                      onChange={(e) => setFormData({...formData, revenue_date: e.target.value})}
                      className="pl-10"
                      required
                    />
                  </div>
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="payment_method">Payment Method</Label>
                  <Select value={formData.payment_method} onValueChange={(value) => setFormData({...formData, payment_method: value})}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select method" />
                    </SelectTrigger>
                    <SelectContent>
                      {paymentMethods.map((method) => (
                        <SelectItem key={method} value={method}>
                          {method}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="status">Status</Label>
                  <Select value={formData.status} onValueChange={(value) => setFormData({...formData, status: value as any})}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="received">Received</SelectItem>
                      <SelectItem value="pending">Pending</SelectItem>
                      <SelectItem value="invoiced">Invoiced</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="invoice_number">Invoice Number</Label>
                  <Input
                    id="invoice_number"
                    value={formData.invoice_number}
                    onChange={(e) => setFormData({...formData, invoice_number: e.target.value})}
                    placeholder="INV-2024-001"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="reference_number">Reference Number</Label>
                  <Input
                    id="reference_number"
                    value={formData.reference_number}
                    onChange={(e) => setFormData({...formData, reference_number: e.target.value})}
                    placeholder="Transaction ID, check number, etc."
                  />
                </div>
              </div>

              <DialogFooter>
                <Button type="button" variant="outline" onClick={() => setIsOpen(false)}>
                  Cancel
                </Button>
                <Button type="submit" disabled={loading}>
                  {loading ? 'Creating...' : 'Create Revenue Entry'}
                </Button>
              </DialogFooter>
            </form>
          </DialogContent>
        </Dialog>
      </div>

      {/* Revenue Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Revenue</CardTitle>
            <TrendingUp className="h-4 w-4 text-green-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">
              ${revenueEntries.reduce((sum, entry) => sum + entry.amount, 0).toLocaleString()}
            </div>
            <p className="text-xs text-muted-foreground">
              {revenueEntries.length} entries
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Received</CardTitle>
            <CreditCard className="h-4 w-4 text-blue-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-600">
              ${revenueEntries.filter(e => e.status === 'received').reduce((sum, entry) => sum + entry.amount, 0).toLocaleString()}
            </div>
            <p className="text-xs text-muted-foreground">
              Cash received
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Pending</CardTitle>
            <Receipt className="h-4 w-4 text-yellow-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-yellow-600">
              ${revenueEntries.filter(e => e.status === 'pending').reduce((sum, entry) => sum + entry.amount, 0).toLocaleString()}
            </div>
            <p className="text-xs text-muted-foreground">
              Awaiting payment
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Clients</CardTitle>
            <Building className="h-4 w-4 text-purple-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-purple-600">
              {new Set(revenueEntries.map(e => e.client_name)).size}
            </div>
            <p className="text-xs text-muted-foreground">
              Active clients
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Recent Revenue Entries */}
      <Card>
        <CardHeader>
          <CardTitle>Recent Revenue Entries</CardTitle>
          <CardDescription>Latest income and cash inflows</CardDescription>
        </CardHeader>
        <CardContent>
          {revenueEntries.length > 0 ? (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Client</TableHead>
                  <TableHead>Category</TableHead>
                  <TableHead>Amount</TableHead>
                  <TableHead>Date</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Payment Method</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {revenueEntries.slice(0, 10).map((entry) => (
                  <TableRow key={entry.id}>
                    <TableCell>
                      <div>
                        <p className="font-medium">{entry.client_name}</p>
                        {entry.project_name && (
                          <p className="text-sm text-gray-500">{entry.project_name}</p>
                        )}
                      </div>
                    </TableCell>
                    <TableCell>{entry.category}</TableCell>
                    <TableCell className="font-semibold text-green-600">
                      ${entry.amount.toLocaleString()}
                    </TableCell>
                    <TableCell>{new Date(entry.revenue_date).toLocaleDateString()}</TableCell>
                    <TableCell>
                      <Badge className={getStatusColor(entry.status)}>
                        {entry.status}
                      </Badge>
                    </TableCell>
                    <TableCell>{entry.payment_method || 'N/A'}</TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          ) : loading ? (
            <div className="text-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
              <p className="text-gray-500">Loading revenue entries...</p>
            </div>
          ) : (
            <div className="text-center py-8">
              <TrendingUp className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-500 mb-4">No revenue entries yet</p>
              <Button onClick={() => setIsOpen(true)}>
                <Plus className="w-4 h-4 mr-2" />
                Add Your First Revenue Entry
              </Button>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default RevenueManagement;
