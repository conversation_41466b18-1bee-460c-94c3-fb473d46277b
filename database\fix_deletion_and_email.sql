-- Fix User Deletion and Email Setup Issues
-- Run this AFTER running working_registration_fix.sql

-- 1. Create function to safely delete user profiles
CREATE OR REPLACE FUNCTION public.delete_user_profile(user_profile_id UUID)
RETURNS BOOLEAN AS $$
DECLARE
    v_user_id UUID;
    v_email VARCHAR(255);
BEGIN
    -- Get user details before deletion
    SELECT user_id, email INTO v_user_id, v_email
    FROM public.user_profiles 
    WHERE id = user_profile_id;
    
    IF NOT FOUND THEN
        RAISE NOTICE 'User profile not found: %', user_profile_id;
        RETURN FALSE;
    END IF;
    
    RAISE NOTICE 'Deleting user profile: % (%)', v_email, user_profile_id;
    
    -- Delete from user_profiles
    DELETE FROM public.user_profiles WHERE id = user_profile_id;
    
    -- Log the deletion
    INSERT INTO public.audit_logs (
        user_id, 
        action, 
        table_name, 
        record_id, 
        details
    ) VALUES (
        v_user_id,
        'DELETE',
        'user_profiles',
        user_profile_id,
        jsonb_build_object('email', v_email, 'deleted_at', NOW())
    );
    
    RAISE NOTICE 'User profile deleted successfully: %', v_email;
    RETURN TRUE;
    
EXCEPTION
    WHEN OTHERS THEN
        RAISE NOTICE 'Error deleting user profile: %', SQLERRM;
        RETURN FALSE;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 2. Create function to update user setup token (for email resend)
CREATE OR REPLACE FUNCTION public.update_user_setup_token(user_profile_id UUID)
RETURNS BOOLEAN AS $$
DECLARE
    v_token VARCHAR(255);
    v_expires_at TIMESTAMP WITH TIME ZONE;
BEGIN
    -- Generate a simple token
    v_token := 'setup_' || EXTRACT(EPOCH FROM NOW())::TEXT || '_' || user_profile_id::TEXT;
    v_expires_at := NOW() + INTERVAL '24 hours';
    
    -- Update the user profile
    UPDATE public.user_profiles 
    SET 
        password_setup_token = v_token,
        password_setup_expires_at = v_expires_at,
        updated_at = NOW()
    WHERE id = user_profile_id;
    
    IF FOUND THEN
        RAISE NOTICE 'Setup token updated for user: %', user_profile_id;
        RETURN TRUE;
    ELSE
        RAISE NOTICE 'User not found for token update: %', user_profile_id;
        RETURN FALSE;
    END IF;
    
EXCEPTION
    WHEN OTHERS THEN
        RAISE NOTICE 'Error updating setup token: %', SQLERRM;
        RETURN FALSE;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 3. Create function to check if user can be deleted
CREATE OR REPLACE FUNCTION public.can_delete_user(user_profile_id UUID, requesting_user_id UUID)
RETURNS BOOLEAN AS $$
DECLARE
    v_requesting_role VARCHAR(50);
    v_target_role VARCHAR(50);
BEGIN
    -- Get requesting user's role
    SELECT ur.role_name INTO v_requesting_role
    FROM public.user_profiles up
    JOIN public.user_roles ur ON up.role_id = ur.id
    WHERE up.user_id = requesting_user_id;
    
    -- Get target user's role
    SELECT ur.role_name INTO v_target_role
    FROM public.user_profiles up
    JOIN public.user_roles ur ON up.role_id = ur.id
    WHERE up.id = user_profile_id;
    
    -- Only admins can delete users
    IF v_requesting_role != 'admin' THEN
        RAISE NOTICE 'Permission denied: Only admins can delete users';
        RETURN FALSE;
    END IF;
    
    -- Cannot delete yourself
    IF EXISTS (SELECT 1 FROM public.user_profiles WHERE id = user_profile_id AND user_id = requesting_user_id) THEN
        RAISE NOTICE 'Cannot delete your own account';
        RETURN FALSE;
    END IF;
    
    RETURN TRUE;
    
EXCEPTION
    WHEN OTHERS THEN
        RAISE NOTICE 'Error checking delete permissions: %', SQLERRM;
        RETURN FALSE;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 4. Add missing columns if they don't exist
DO $$
BEGIN
    -- Add password_setup_token column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'user_profiles' 
                   AND column_name = 'password_setup_token') THEN
        ALTER TABLE public.user_profiles ADD COLUMN password_setup_token VARCHAR(255);
        RAISE NOTICE 'Added password_setup_token column';
    END IF;
    
    -- Add password_setup_expires_at column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'user_profiles' 
                   AND column_name = 'password_setup_expires_at') THEN
        ALTER TABLE public.user_profiles ADD COLUMN password_setup_expires_at TIMESTAMP WITH TIME ZONE;
        RAISE NOTICE 'Added password_setup_expires_at column';
    END IF;
    
    -- Add requires_password_setup column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'user_profiles' 
                   AND column_name = 'requires_password_setup') THEN
        ALTER TABLE public.user_profiles ADD COLUMN requires_password_setup BOOLEAN DEFAULT TRUE;
        RAISE NOTICE 'Added requires_password_setup column';
    END IF;
END $$;

-- 5. Create RPC functions for the frontend to call
CREATE OR REPLACE FUNCTION public.rpc_delete_user(user_profile_id UUID)
RETURNS jsonb AS $$
DECLARE
    v_requesting_user_id UUID;
    v_result BOOLEAN;
BEGIN
    -- Get the requesting user ID from the current session
    v_requesting_user_id := auth.uid();
    
    IF v_requesting_user_id IS NULL THEN
        RETURN jsonb_build_object('success', false, 'error', 'Not authenticated');
    END IF;
    
    -- Check permissions
    IF NOT public.can_delete_user(user_profile_id, v_requesting_user_id) THEN
        RETURN jsonb_build_object('success', false, 'error', 'Permission denied');
    END IF;
    
    -- Delete the user
    v_result := public.delete_user_profile(user_profile_id);
    
    IF v_result THEN
        RETURN jsonb_build_object('success', true, 'message', 'User deleted successfully');
    ELSE
        RETURN jsonb_build_object('success', false, 'error', 'Failed to delete user');
    END IF;
    
EXCEPTION
    WHEN OTHERS THEN
        RETURN jsonb_build_object('success', false, 'error', SQLERRM);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 6. Create RPC function for resending setup email
CREATE OR REPLACE FUNCTION public.rpc_resend_setup_email(user_profile_id UUID)
RETURNS jsonb AS $$
DECLARE
    v_requesting_user_id UUID;
    v_requesting_role VARCHAR(50);
    v_result BOOLEAN;
BEGIN
    -- Get the requesting user ID from the current session
    v_requesting_user_id := auth.uid();
    
    IF v_requesting_user_id IS NULL THEN
        RETURN jsonb_build_object('success', false, 'error', 'Not authenticated');
    END IF;
    
    -- Check if requesting user is admin
    SELECT ur.role_name INTO v_requesting_role
    FROM public.user_profiles up
    JOIN public.user_roles ur ON up.role_id = ur.id
    WHERE up.user_id = v_requesting_user_id;
    
    IF v_requesting_role != 'admin' THEN
        RETURN jsonb_build_object('success', false, 'error', 'Only admins can resend setup emails');
    END IF;
    
    -- Update the setup token
    v_result := public.update_user_setup_token(user_profile_id);
    
    IF v_result THEN
        RETURN jsonb_build_object('success', true, 'message', 'Setup email token updated');
    ELSE
        RETURN jsonb_build_object('success', false, 'error', 'Failed to update setup token');
    END IF;
    
EXCEPTION
    WHEN OTHERS THEN
        RETURN jsonb_build_object('success', false, 'error', SQLERRM);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 7. Grant necessary permissions
GRANT EXECUTE ON FUNCTION public.delete_user_profile(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION public.update_user_setup_token(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION public.can_delete_user(UUID, UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION public.rpc_delete_user(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION public.rpc_resend_setup_email(UUID) TO authenticated;

-- 8. Test the functions
DO $$
DECLARE
    test_result jsonb;
BEGIN
    RAISE NOTICE '=== TESTING DELETION AND EMAIL FUNCTIONS ===';
    
    -- Test if functions exist
    IF EXISTS (SELECT 1 FROM pg_proc WHERE proname = 'delete_user_profile') THEN
        RAISE NOTICE '✓ delete_user_profile function created';
    END IF;
    
    IF EXISTS (SELECT 1 FROM pg_proc WHERE proname = 'update_user_setup_token') THEN
        RAISE NOTICE '✓ update_user_setup_token function created';
    END IF;
    
    IF EXISTS (SELECT 1 FROM pg_proc WHERE proname = 'rpc_delete_user') THEN
        RAISE NOTICE '✓ rpc_delete_user function created';
    END IF;
    
    IF EXISTS (SELECT 1 FROM pg_proc WHERE proname = 'rpc_resend_setup_email') THEN
        RAISE NOTICE '✓ rpc_resend_setup_email function created';
    END IF;
    
    RAISE NOTICE '';
    RAISE NOTICE 'Functions created successfully!';
    RAISE NOTICE 'User deletion and email setup should now work properly.';
END $$;
