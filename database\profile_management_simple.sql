-- Simple Profile Management System with Picture Upload Support
-- Compatible with existing database structure
-- Run this in your Supabase SQL Editor

-- 1. Create storage bucket for profile pictures
INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
VALUES (
  'profile-pictures',
  'profile-pictures',
  true,
  5242880, -- 5MB limit
  ARRAY['image/jpeg', 'image/png', 'image/webp', 'image/gif']
) ON CONFLICT (id) DO UPDATE SET
  public = true,
  file_size_limit = 5242880,
  allowed_mime_types = ARRAY['image/jpeg', 'image/png', 'image/webp', 'image/gif'];

-- 2. Create storage policies for profile pictures
DROP POLICY IF EXISTS "Users can view all profile pictures" ON storage.objects;
DROP POLICY IF EXISTS "Users can upload their own profile picture" ON storage.objects;
DROP POLICY IF EXISTS "Users can update their own profile picture" ON storage.objects;
DROP POLICY IF EXISTS "Users can delete their own profile picture" ON storage.objects;

CREATE POLICY "Users can view all profile pictures" ON storage.objects
FOR SELECT USING (bucket_id = 'profile-pictures');

CREATE POLICY "Users can upload their own profile picture" ON storage.objects
FOR INSERT WITH CHECK (
  bucket_id = 'profile-pictures' 
  AND auth.uid()::text = (storage.foldername(name))[1]
);

CREATE POLICY "Users can update their own profile picture" ON storage.objects
FOR UPDATE USING (
  bucket_id = 'profile-pictures' 
  AND auth.uid()::text = (storage.foldername(name))[1]
);

CREATE POLICY "Users can delete their own profile picture" ON storage.objects
FOR DELETE USING (
  bucket_id = 'profile-pictures' 
  AND auth.uid()::text = (storage.foldername(name))[1]
);

-- 3. Check if user_profiles table exists and add columns safely
DO $$
BEGIN
  -- Check if user_profiles table exists
  IF EXISTS (SELECT FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'user_profiles') THEN
    -- Add columns if they don't exist
    IF NOT EXISTS (SELECT FROM information_schema.columns WHERE table_schema = 'public' AND table_name = 'user_profiles' AND column_name = 'profile_picture_url') THEN
      ALTER TABLE public.user_profiles ADD COLUMN profile_picture_url TEXT;
    END IF;
    
    IF NOT EXISTS (SELECT FROM information_schema.columns WHERE table_schema = 'public' AND table_name = 'user_profiles' AND column_name = 'bio') THEN
      ALTER TABLE public.user_profiles ADD COLUMN bio TEXT;
    END IF;
    
    IF NOT EXISTS (SELECT FROM information_schema.columns WHERE table_schema = 'public' AND table_name = 'user_profiles' AND column_name = 'date_of_birth') THEN
      ALTER TABLE public.user_profiles ADD COLUMN date_of_birth DATE;
    END IF;
    
    IF NOT EXISTS (SELECT FROM information_schema.columns WHERE table_schema = 'public' AND table_name = 'user_profiles' AND column_name = 'location') THEN
      ALTER TABLE public.user_profiles ADD COLUMN location TEXT;
    END IF;
    
    IF NOT EXISTS (SELECT FROM information_schema.columns WHERE table_schema = 'public' AND table_name = 'user_profiles' AND column_name = 'emergency_contact_name') THEN
      ALTER TABLE public.user_profiles ADD COLUMN emergency_contact_name TEXT;
    END IF;
    
    IF NOT EXISTS (SELECT FROM information_schema.columns WHERE table_schema = 'public' AND table_name = 'user_profiles' AND column_name = 'emergency_contact_phone') THEN
      ALTER TABLE public.user_profiles ADD COLUMN emergency_contact_phone TEXT;
    END IF;
    
    IF NOT EXISTS (SELECT FROM information_schema.columns WHERE table_schema = 'public' AND table_name = 'user_profiles' AND column_name = 'emergency_contact_relationship') THEN
      ALTER TABLE public.user_profiles ADD COLUMN emergency_contact_relationship TEXT;
    END IF;
    
    RAISE NOTICE 'user_profiles table updated with new columns';
  ELSE
    RAISE NOTICE 'user_profiles table does not exist - skipping column additions';
  END IF;
END $$;

-- 4. Create profile activity log table
CREATE TABLE IF NOT EXISTS public.profile_activity_log (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  action TEXT NOT NULL,
  field_changed TEXT,
  old_value TEXT,
  new_value TEXT,
  changed_by UUID REFERENCES auth.users(id),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 5. Create function to handle profile picture upload
CREATE OR REPLACE FUNCTION handle_profile_picture_upload(
  user_id UUID,
  file_path TEXT
)
RETURNS TEXT AS $$
DECLARE
  old_picture_url TEXT;
  new_picture_url TEXT;
BEGIN
  -- Generate the new picture URL
  new_picture_url := 'https://ygdaucsngasdutbvmevs.supabase.co/storage/v1/object/public/profile-pictures/' || file_path;
  
  -- Check if user_profiles table exists and has the column
  IF EXISTS (
    SELECT FROM information_schema.columns 
    WHERE table_schema = 'public' 
    AND table_name = 'user_profiles' 
    AND column_name = 'profile_picture_url'
  ) THEN
    -- Get the old picture URL
    SELECT profile_picture_url INTO old_picture_url
    FROM public.user_profiles
    WHERE user_id = handle_profile_picture_upload.user_id OR id = handle_profile_picture_upload.user_id;
    
    -- Update the user profile with new picture URL
    UPDATE public.user_profiles
    SET 
      profile_picture_url = new_picture_url,
      updated_at = NOW()
    WHERE user_id = handle_profile_picture_upload.user_id OR id = handle_profile_picture_upload.user_id;
    
    -- Log the activity
    INSERT INTO public.profile_activity_log (
      user_id,
      action,
      field_changed,
      old_value,
      new_value,
      changed_by
    ) VALUES (
      handle_profile_picture_upload.user_id,
      'profile_picture_updated',
      'profile_picture_url',
      old_picture_url,
      new_picture_url,
      handle_profile_picture_upload.user_id
    );
  ELSE
    RAISE NOTICE 'user_profiles table or profile_picture_url column does not exist';
  END IF;
  
  RETURN new_picture_url;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 6. Create function to update profile information
CREATE OR REPLACE FUNCTION update_user_profile(
  p_user_id UUID,
  p_profile_data JSONB
)
RETURNS UUID AS $$
DECLARE
  v_field TEXT;
  v_new_value TEXT;
  v_sql TEXT;
  v_set_clauses TEXT[] := ARRAY[]::TEXT[];
BEGIN
  -- Check if user_profiles table exists
  IF NOT EXISTS (
    SELECT FROM information_schema.tables 
    WHERE table_schema = 'public' 
    AND table_name = 'user_profiles'
  ) THEN
    RAISE NOTICE 'user_profiles table does not exist';
    RETURN p_user_id;
  END IF;

  -- Build dynamic update query based on existing columns
  FOR v_field IN SELECT jsonb_object_keys(p_profile_data)
  LOOP
    -- Check if column exists
    IF EXISTS (
      SELECT FROM information_schema.columns 
      WHERE table_schema = 'public' 
      AND table_name = 'user_profiles' 
      AND column_name = v_field
    ) THEN
      v_new_value := p_profile_data ->> v_field;
      v_set_clauses := array_append(v_set_clauses, format('%I = %L', v_field, v_new_value));
      
      -- Log the activity
      INSERT INTO public.profile_activity_log (
        user_id,
        action,
        field_changed,
        new_value,
        changed_by
      ) VALUES (
        p_user_id,
        'profile_updated',
        v_field,
        v_new_value,
        p_user_id
      );
    END IF;
  END LOOP;
  
  -- Execute update if we have fields to update
  IF array_length(v_set_clauses, 1) > 0 THEN
    v_set_clauses := array_append(v_set_clauses, 'updated_at = NOW()');
    v_sql := format('UPDATE public.user_profiles SET %s WHERE user_id = %L OR id = %L', 
                    array_to_string(v_set_clauses, ', '), p_user_id, p_user_id);
    EXECUTE v_sql;
  END IF;
  
  RETURN p_user_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 7. Grant permissions
GRANT ALL ON public.profile_activity_log TO authenticated;
GRANT EXECUTE ON FUNCTION handle_profile_picture_upload(UUID, TEXT) TO authenticated;
GRANT EXECUTE ON FUNCTION update_user_profile(UUID, JSONB) TO authenticated;

-- 8. Enable RLS on new table
ALTER TABLE public.profile_activity_log ENABLE ROW LEVEL SECURITY;

-- 9. Create RLS policies for activity log
DROP POLICY IF EXISTS "Users can view their own activity log" ON public.profile_activity_log;
DROP POLICY IF EXISTS "System can insert activity log" ON public.profile_activity_log;

CREATE POLICY "Users can view their own activity log" ON public.profile_activity_log
FOR SELECT USING (user_id = auth.uid());

CREATE POLICY "System can insert activity log" ON public.profile_activity_log
FOR INSERT WITH CHECK (true);

-- 10. Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_profile_activity_log_user_id ON public.profile_activity_log(user_id);
CREATE INDEX IF NOT EXISTS idx_profile_activity_log_created_at ON public.profile_activity_log(created_at DESC);

-- 11. Test the setup
DO $$
DECLARE
  test_user_id UUID;
  test_result TEXT;
BEGIN
  -- Get a test user ID (first user in auth.users)
  SELECT id INTO test_user_id FROM auth.users LIMIT 1;
  
  IF test_user_id IS NOT NULL THEN
    -- Test the profile picture function
    SELECT handle_profile_picture_upload(test_user_id, 'test/test-image.jpg') INTO test_result;
    RAISE NOTICE 'Profile picture function test: %', test_result;
    
    -- Test the profile update function
    PERFORM update_user_profile(test_user_id, '{"bio": "Test bio update"}'::jsonb);
    RAISE NOTICE 'Profile update function test completed';
  ELSE
    RAISE NOTICE 'No users found for testing';
  END IF;
  
  RAISE NOTICE 'Profile management system setup complete!';
  RAISE NOTICE 'Features enabled:';
  RAISE NOTICE '- Profile picture upload with 5MB limit';
  RAISE NOTICE '- Extended profile fields (bio, emergency contacts, etc.)';
  RAISE NOTICE '- Activity logging for profile changes';
  RAISE NOTICE '- Secure file storage with proper permissions';
  RAISE NOTICE '- Compatible with existing database structure';
END $$;
