import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { useToast } from '@/components/ui/use-toast';
import { TimeTrackingService } from '@/lib/timeTrackingService';
import {
  BarChart3,
  Clock,
  Users,
  TrendingUp,
  Calendar,
  RefreshCw,
  Award,
  AlertTriangle
} from 'lucide-react';

const TimeTrackingStats = () => {
  const { toast } = useToast();
  const [loading, setLoading] = useState(false);
  const [stats, setStats] = useState({
    totalHours: 0,
    totalOvertimeHours: 0,
    totalEntries: 0,
    activeEntries: 0,
    averageHoursPerDay: 0
  });
  const [startDate, setStartDate] = useState<string>(
    new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0]
  );
  const [endDate, setEndDate] = useState<string>(
    new Date().toISOString().split('T')[0]
  );

  useEffect(() => {
    loadStats();
  }, [startDate, endDate]);

  const loadStats = async () => {
    setLoading(true);
    try {
      const statsData = await TimeTrackingService.getTimeTrackingStats(
        startDate,
        endDate + 'T23:59:59'
      );
      setStats(statsData);
    } catch (error) {
      console.error('Error loading stats:', error);
      toast({
        title: "Error",
        description: "Failed to load time tracking statistics",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const formatDuration = (hours: number) => {
    const h = Math.floor(hours);
    const m = Math.round((hours - h) * 60);
    return `${h}h ${m}m`;
  };

  const getOvertimePercentage = () => {
    if (stats.totalHours === 0) return 0;
    return ((stats.totalOvertimeHours / stats.totalHours) * 100).toFixed(1);
  };

  const getEfficiencyScore = () => {
    // Simple efficiency calculation based on average hours per day
    const targetHoursPerDay = 8;
    const efficiency = Math.min((stats.averageHoursPerDay / targetHoursPerDay) * 100, 100);
    return efficiency.toFixed(1);
  };

  return (
    <div className="space-y-6">
      {/* Date Range Selector */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <BarChart3 className="h-5 w-5" />
            Time Tracking Analytics
          </CardTitle>
          <CardDescription>
            Comprehensive statistics and insights for time tracking data
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col sm:flex-row gap-4 items-end">
            <div className="flex-1">
              <Label htmlFor="stats-start-date">Start Date</Label>
              <Input
                id="stats-start-date"
                type="date"
                value={startDate}
                onChange={(e) => setStartDate(e.target.value)}
              />
            </div>
            
            <div className="flex-1">
              <Label htmlFor="stats-end-date">End Date</Label>
              <Input
                id="stats-end-date"
                type="date"
                value={endDate}
                onChange={(e) => setEndDate(e.target.value)}
              />
            </div>

            <Button onClick={loadStats} disabled={loading}>
              {loading ? (
                <RefreshCw className="h-4 w-4 animate-spin" />
              ) : (
                <RefreshCw className="h-4 w-4" />
              )}
              Refresh
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center gap-3">
              <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                <Clock className="h-6 w-6 text-blue-600" />
              </div>
              <div>
                <p className="text-sm text-gray-600">Total Hours</p>
                <p className="text-2xl font-bold">{formatDuration(stats.totalHours)}</p>
                <p className="text-xs text-gray-500">{stats.totalEntries} entries</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center gap-3">
              <div className="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center">
                <TrendingUp className="h-6 w-6 text-orange-600" />
              </div>
              <div>
                <p className="text-sm text-gray-600">Overtime Hours</p>
                <p className="text-2xl font-bold">{formatDuration(stats.totalOvertimeHours)}</p>
                <p className="text-xs text-gray-500">{getOvertimePercentage()}% of total</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center gap-3">
              <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                <Users className="h-6 w-6 text-green-600" />
              </div>
              <div>
                <p className="text-sm text-gray-600">Active Workers</p>
                <p className="text-2xl font-bold">{stats.activeEntries}</p>
                <p className="text-xs text-gray-500">Currently working</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center gap-3">
              <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                <Calendar className="h-6 w-6 text-purple-600" />
              </div>
              <div>
                <p className="text-sm text-gray-600">Avg Hours/Day</p>
                <p className="text-2xl font-bold">{formatDuration(stats.averageHoursPerDay)}</p>
                <p className="text-xs text-gray-500">Per work day</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Performance Insights */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Award className="h-5 w-5" />
              Performance Insights
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between p-4 bg-green-50 rounded-lg">
              <div>
                <p className="font-medium text-green-800">Work Efficiency</p>
                <p className="text-sm text-green-600">Based on 8-hour target</p>
              </div>
              <div className="text-right">
                <p className="text-2xl font-bold text-green-700">{getEfficiencyScore()}%</p>
              </div>
            </div>

            <div className="flex items-center justify-between p-4 bg-blue-50 rounded-lg">
              <div>
                <p className="font-medium text-blue-800">Total Work Days</p>
                <p className="text-sm text-blue-600">Completed sessions</p>
              </div>
              <div className="text-right">
                <p className="text-2xl font-bold text-blue-700">
                  {stats.totalEntries - stats.activeEntries}
                </p>
              </div>
            </div>

            <div className="flex items-center justify-between p-4 bg-orange-50 rounded-lg">
              <div>
                <p className="font-medium text-orange-800">Overtime Rate</p>
                <p className="text-sm text-orange-600">Percentage of total hours</p>
              </div>
              <div className="text-right">
                <p className="text-2xl font-bold text-orange-700">{getOvertimePercentage()}%</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <AlertTriangle className="h-5 w-5" />
              Alerts & Recommendations
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {stats.activeEntries > 0 && (
              <div className="p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                <div className="flex items-center gap-2 text-yellow-800 mb-1">
                  <AlertTriangle className="h-4 w-4" />
                  <span className="font-medium">Active Sessions</span>
                </div>
                <p className="text-sm text-yellow-700">
                  {stats.activeEntries} worker(s) currently have active time entries.
                </p>
              </div>
            )}

            {parseFloat(getOvertimePercentage()) > 20 && (
              <div className="p-4 bg-orange-50 border border-orange-200 rounded-lg">
                <div className="flex items-center gap-2 text-orange-800 mb-1">
                  <TrendingUp className="h-4 w-4" />
                  <span className="font-medium">High Overtime</span>
                </div>
                <p className="text-sm text-orange-700">
                  Overtime hours are {getOvertimePercentage()}% of total hours. Consider workload review.
                </p>
              </div>
            )}

            {stats.averageHoursPerDay < 6 && stats.totalEntries > 5 && (
              <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
                <div className="flex items-center gap-2 text-blue-800 mb-1">
                  <Clock className="h-4 w-4" />
                  <span className="font-medium">Low Average Hours</span>
                </div>
                <p className="text-sm text-blue-700">
                  Average daily hours ({formatDuration(stats.averageHoursPerDay)}) is below typical full-time.
                </p>
              </div>
            )}

            {stats.totalEntries === 0 && (
              <div className="p-4 bg-gray-50 border border-gray-200 rounded-lg">
                <div className="flex items-center gap-2 text-gray-800 mb-1">
                  <Calendar className="h-4 w-4" />
                  <span className="font-medium">No Data</span>
                </div>
                <p className="text-sm text-gray-700">
                  No time entries found for the selected date range.
                </p>
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Summary Report */}
      <Card>
        <CardHeader>
          <CardTitle>Summary Report</CardTitle>
          <CardDescription>
            Period: {new Date(startDate).toLocaleDateString()} - {new Date(endDate).toLocaleDateString()}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="prose max-w-none">
            <p className="text-gray-700">
              During the selected period, a total of <strong>{formatDuration(stats.totalHours)}</strong> were 
              logged across <strong>{stats.totalEntries}</strong> time entries. 
              {stats.totalOvertimeHours > 0 && (
                <>
                  {' '}This includes <strong>{formatDuration(stats.totalOvertimeHours)}</strong> of overtime work, 
                  representing <strong>{getOvertimePercentage()}%</strong> of total hours.
                </>
              )}
            </p>
            
            <p className="text-gray-700">
              The average work session was <strong>{formatDuration(stats.averageHoursPerDay)}</strong>, 
              with <strong>{stats.activeEntries}</strong> workers currently having active time entries.
            </p>

            {parseFloat(getEfficiencyScore()) >= 80 && (
              <p className="text-green-700">
                ✅ Work efficiency is excellent at <strong>{getEfficiencyScore()}%</strong> of target hours.
              </p>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default TimeTrackingStats;
