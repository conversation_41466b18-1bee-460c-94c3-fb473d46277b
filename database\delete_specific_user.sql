-- Delete Specific User - <EMAIL>
-- Run this after the nuclear script if user still exists

-- 1. Show current user details
DO $$
DECLARE
    user_record RECORD;
BEGIN
    RAISE NOTICE '=== CURRENT USER DETAILS ===';
    
    SELECT * INTO user_record 
    FROM public.user_profiles 
    WHERE email = '<EMAIL>';
    
    IF FOUND THEN
        RAISE NOTICE 'Found user:';
        RAISE NOTICE '  ID: %', user_record.id;
        RAISE NOTICE '  Email: %', user_record.email;
        RAISE NOTICE '  Name: % %', user_record.first_name, user_record.last_name;
        RAISE NOTICE '  Role ID: %', user_record.role_id;
        RAISE NOTICE '  User ID: %', user_record.user_id;
        RAISE NOTICE '  Active: %', user_record.is_active;
    ELSE
        RAISE NOTICE 'User <EMAIL> not found';
    END IF;
END $$;

-- 2. Try multiple deletion methods
DO $$
DECLARE
    deleted_count INTEGER;
    user_id_to_delete UUID;
BEGIN
    RAISE NOTICE '';
    RAISE NOTICE '=== ATTEMPTING MULTIPLE DELETION METHODS ===';
    
    -- Get the user ID
    SELECT id INTO user_id_to_delete 
    FROM public.user_profiles 
    WHERE email = '<EMAIL>';
    
    IF user_id_to_delete IS NULL THEN
        RAISE NOTICE 'User not found for deletion';
        RETURN;
    END IF;
    
    RAISE NOTICE 'Target user ID: %', user_id_to_delete;
    
    -- Method 1: Delete by email
    RAISE NOTICE 'Method 1: Delete by email';
    DELETE FROM public.user_profiles WHERE email = '<EMAIL>';
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    RAISE NOTICE 'Rows deleted by email: %', deleted_count;
    
    -- Check if still exists
    IF EXISTS (SELECT 1 FROM public.user_profiles WHERE email = '<EMAIL>') THEN
        RAISE NOTICE 'User still exists after email deletion';
        
        -- Method 2: Delete by ID
        RAISE NOTICE 'Method 2: Delete by ID';
        DELETE FROM public.user_profiles WHERE id = user_id_to_delete;
        GET DIAGNOSTICS deleted_count = ROW_COUNT;
        RAISE NOTICE 'Rows deleted by ID: %', deleted_count;
    END IF;
    
    -- Check if still exists
    IF EXISTS (SELECT 1 FROM public.user_profiles WHERE email = '<EMAIL>') THEN
        RAISE NOTICE 'User still exists after ID deletion';
        
        -- Method 3: Nuclear function
        RAISE NOTICE 'Method 3: Nuclear function';
        deleted_count := public.nuclear_delete_user('<EMAIL>');
        RAISE NOTICE 'Nuclear function result: %', deleted_count;
    END IF;
    
    -- Final check
    IF EXISTS (SELECT 1 FROM public.user_profiles WHERE email = '<EMAIL>') THEN
        RAISE NOTICE '✗ USER STILL EXISTS AFTER ALL DELETION ATTEMPTS';
        RAISE NOTICE 'This indicates a deeper database issue';
    ELSE
        RAISE NOTICE '✓ USER SUCCESSFULLY DELETED';
    END IF;
END $$;

-- 3. Show all remaining users
DO $$
DECLARE
    user_record RECORD;
    total_count INTEGER;
BEGIN
    SELECT COUNT(*) INTO total_count FROM public.user_profiles;
    
    RAISE NOTICE '';
    RAISE NOTICE '=== REMAINING USERS (Total: %) ===', total_count;
    
    FOR user_record IN 
        SELECT email, first_name, last_name, id, is_active
        FROM public.user_profiles 
        ORDER BY created_at DESC
    LOOP
        RAISE NOTICE '- %: % % (ID: %, Active: %)', 
            user_record.email,
            user_record.first_name, 
            user_record.last_name,
            SUBSTRING(user_record.id::TEXT, 1, 8),
            user_record.is_active;
    END LOOP;
END $$;

-- 4. Check for any constraints or triggers that might prevent deletion
DO $$
DECLARE
    constraint_record RECORD;
    trigger_record RECORD;
BEGIN
    RAISE NOTICE '';
    RAISE NOTICE '=== CHECKING CONSTRAINTS AND TRIGGERS ===';
    
    -- Check foreign key constraints
    FOR constraint_record IN 
        SELECT conname, confrelid::regclass as foreign_table
        FROM pg_constraint 
        WHERE conrelid = 'public.user_profiles'::regclass 
        AND contype = 'f'
    LOOP
        RAISE NOTICE 'Foreign key constraint: % -> %', 
            constraint_record.conname, 
            constraint_record.foreign_table;
    END LOOP;
    
    -- Check triggers
    FOR trigger_record IN 
        SELECT tgname, tgtype
        FROM pg_trigger 
        WHERE tgrelid = 'public.user_profiles'::regclass
    LOOP
        RAISE NOTICE 'Trigger: % (type: %)', 
            trigger_record.tgname, 
            trigger_record.tgtype;
    END LOOP;
    
    RAISE NOTICE 'Constraint/trigger check complete';
END $$;

-- 5. Create a super-nuclear delete function that ignores everything
CREATE OR REPLACE FUNCTION public.super_nuclear_delete(target_email TEXT)
RETURNS BOOLEAN AS $$
DECLARE
    result BOOLEAN := FALSE;
BEGIN
    -- Disable all triggers temporarily
    SET session_replication_role = replica;
    
    -- Force delete
    DELETE FROM public.user_profiles WHERE email = target_email;
    
    -- Re-enable triggers
    SET session_replication_role = DEFAULT;
    
    -- Check if deleted
    IF NOT EXISTS (SELECT 1 FROM public.user_profiles WHERE email = target_email) THEN
        result := TRUE;
    END IF;
    
    RETURN result;
    
EXCEPTION
    WHEN OTHERS THEN
        -- Re-enable triggers even if error
        SET session_replication_role = DEFAULT;
        RETURN FALSE;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 6. Test super nuclear delete
DO $$
DECLARE
    super_result BOOLEAN;
BEGIN
    RAISE NOTICE '';
    RAISE NOTICE '=== TESTING SUPER NUCLEAR DELETE ===';
    
    IF EXISTS (SELECT 1 FROM public.user_profiles WHERE email = '<EMAIL>') THEN
        RAISE NOTICE 'User still exists, trying super nuclear delete...';
        super_result := public.super_nuclear_delete('<EMAIL>');
        
        IF super_result THEN
            RAISE NOTICE '✓ SUPER NUCLEAR DELETE SUCCESSFUL!';
        ELSE
            RAISE NOTICE '✗ Even super nuclear delete failed';
        END IF;
    ELSE
        RAISE NOTICE 'User already deleted, no need for super nuclear';
    END IF;
END $$;
