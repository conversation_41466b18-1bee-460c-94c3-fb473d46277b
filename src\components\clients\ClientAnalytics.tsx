import React from 'react';
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { 
  TrendingUp, 
  Users, 
  DollarSign, 
  Building, 
  Calendar,
  Target,
  Award,
  BarChart3
} from 'lucide-react';
import { ClientAnalytics as ClientAnalyticsType } from '@/types/client';

interface ClientAnalyticsProps {
  analytics: ClientAnalyticsType | null;
}

export const ClientAnalytics: React.FC<ClientAnalyticsProps> = ({ analytics }) => {
  if (!analytics) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="text-center">
          <div className="w-8 h-8 border-4 border-blue-600 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-gray-600">Loading analytics...</p>
        </div>
      </div>
    );
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount);
  };

  const getIndustryColor = (index: number) => {
    const colors = [
      'bg-blue-100 text-blue-800',
      'bg-green-100 text-green-800',
      'bg-purple-100 text-purple-800',
      'bg-orange-100 text-orange-800',
      'bg-pink-100 text-pink-800'
    ];
    return colors[index % colors.length];
  };

  return (
    <div className="space-y-6">
      {/* Overview Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Clients</p>
                <p className="text-3xl font-bold text-blue-600">{analytics.total_clients}</p>
              </div>
              <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                <Users className="h-6 w-6 text-blue-600" />
              </div>
            </div>
            <div className="mt-2">
              <p className="text-sm text-gray-600">
                {analytics.active_clients} active ({Math.round((analytics.active_clients / analytics.total_clients) * 100)}%)
              </p>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Revenue</p>
                <p className="text-3xl font-bold text-green-600">
                  {formatCurrency(analytics.total_revenue)}
                </p>
              </div>
              <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                <DollarSign className="h-6 w-6 text-green-600" />
              </div>
            </div>
            <div className="mt-2">
              <p className="text-sm text-gray-600">
                Avg per client: {formatCurrency(analytics.total_revenue / analytics.total_clients)}
              </p>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Outstanding</p>
                <p className="text-3xl font-bold text-orange-600">
                  {formatCurrency(analytics.outstanding_revenue)}
                </p>
              </div>
              <div className="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center">
                <Calendar className="h-6 w-6 text-orange-600" />
              </div>
            </div>
            <div className="mt-2">
              <p className="text-sm text-gray-600">
                {Math.round((analytics.outstanding_revenue / analytics.total_revenue) * 100)}% of total revenue
              </p>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Avg Payment Time</p>
                <p className="text-3xl font-bold text-purple-600">{analytics.average_payment_time}d</p>
              </div>
              <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                <Target className="h-6 w-6 text-purple-600" />
              </div>
            </div>
            <div className="mt-2">
              <p className="text-sm text-gray-600">
                Industry standard: 35 days
              </p>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Top Clients */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Award className="h-5 w-5" />
            Top Clients by Revenue
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {analytics.top_clients.map((client, index) => (
              <div key={client.client_id} className="flex items-center justify-between p-4 border rounded-lg">
                <div className="flex items-center space-x-4">
                  <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                    <span className="font-bold text-blue-600">#{index + 1}</span>
                  </div>
                  <div>
                    <p className="font-medium text-gray-900">{client.client_name}</p>
                    <p className="text-sm text-gray-500">{client.project_count} projects</p>
                  </div>
                </div>
                <div className="text-right">
                  <p className="font-bold text-green-600">
                    {formatCurrency(client.total_revenue)}
                  </p>
                  <p className="text-sm text-gray-500">
                    {Math.round((client.total_revenue / analytics.total_revenue) * 100)}% of total
                  </p>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Payment Trends */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <TrendingUp className="h-5 w-5" />
            Payment Trends (Last 3 Months)
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-6">
            {analytics.payment_trends.map((trend, index) => (
              <div key={trend.month} className="space-y-3">
                <div className="flex items-center justify-between">
                  <h4 className="font-medium text-gray-900">
                    {new Date(trend.month + '-01').toLocaleDateString('en-US', { 
                      month: 'long', 
                      year: 'numeric' 
                    })}
                  </h4>
                  <div className="text-sm text-gray-600">
                    Collection Rate: {Math.round((trend.payments_received / trend.invoices_sent) * 100)}%
                  </div>
                </div>
                
                <div className="grid grid-cols-3 gap-4 text-sm">
                  <div>
                    <p className="text-gray-600">Payments Received</p>
                    <p className="font-medium text-green-600">
                      {formatCurrency(trend.payments_received)}
                    </p>
                  </div>
                  <div>
                    <p className="text-gray-600">Invoices Sent</p>
                    <p className="font-medium text-blue-600">
                      {formatCurrency(trend.invoices_sent)}
                    </p>
                  </div>
                  <div>
                    <p className="text-gray-600">Outstanding</p>
                    <p className="font-medium text-orange-600">
                      {formatCurrency(trend.outstanding)}
                    </p>
                  </div>
                </div>
                
                <Progress 
                  value={(trend.payments_received / trend.invoices_sent) * 100} 
                  className="h-2"
                />
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Industry Breakdown */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <BarChart3 className="h-5 w-5" />
            Industry Breakdown
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {analytics.industry_breakdown.map((industry, index) => (
              <div key={industry.industry} className="space-y-3">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div className="w-8 h-8 bg-gray-100 rounded-lg flex items-center justify-center">
                      <Building className="h-4 w-4 text-gray-600" />
                    </div>
                    <div>
                      <p className="font-medium text-gray-900">{industry.industry}</p>
                      <p className="text-sm text-gray-500">{industry.client_count} clients</p>
                    </div>
                  </div>
                  <Badge className={getIndustryColor(index)}>
                    {formatCurrency(industry.revenue)}
                  </Badge>
                </div>
                
                <div className="space-y-1">
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-600">Revenue Share</span>
                    <span className="font-medium">
                      {Math.round((industry.revenue / analytics.total_revenue) * 100)}%
                    </span>
                  </div>
                  <Progress 
                    value={(industry.revenue / analytics.total_revenue) * 100} 
                    className="h-2"
                  />
                </div>
                
                <div className="text-sm text-gray-600">
                  Avg per client: {formatCurrency(industry.revenue / industry.client_count)}
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Key Insights */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Target className="h-5 w-5" />
            Key Insights
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-4">
              <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
                <h4 className="font-medium text-green-800 mb-2">Strong Performance</h4>
                <ul className="text-sm text-green-700 space-y-1">
                  <li>• Payment time below industry average</li>
                  <li>• {Math.round((analytics.active_clients / analytics.total_clients) * 100)}% client retention rate</li>
                  <li>• Diversified industry portfolio</li>
                </ul>
              </div>
            </div>
            
            <div className="space-y-4">
              <div className="p-4 bg-orange-50 border border-orange-200 rounded-lg">
                <h4 className="font-medium text-orange-800 mb-2">Areas for Improvement</h4>
                <ul className="text-sm text-orange-700 space-y-1">
                  <li>• {formatCurrency(analytics.overdue_revenue)} in overdue payments</li>
                  <li>• Monitor top client concentration risk</li>
                  <li>• Optimize collection processes</li>
                </ul>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
