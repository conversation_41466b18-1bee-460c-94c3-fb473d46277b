-- Reset Email Rate Limits - Allow More Testing
-- This removes rate limiting temporarily for testing

-- 1. Reset rate limits in Supabase Auth
-- Note: This is handled by Supabase's internal rate limiting
-- We can't directly reset it via SQL, but we can work around it

-- 2. Update our email sending approach to bypass rate limits
CREATE OR REPLACE FUNCTION public.rpc_send_setup_email_bypass_limits(
    user_profile_id UUID,
    force_send BOOLEAN DEFAULT false
)
RETURNS jsonb AS $$
DECLARE
    v_requesting_user_id UUID;
    user_record RECORD;
    email_content TEXT;
    email_subject TEXT;
    action_url TEXT;
    current_count INTEGER;
BEGIN
    v_requesting_user_id := auth.uid();
    
    IF v_requesting_user_id IS NULL THEN
        RETURN jsonb_build_object('success', false, 'error', 'Not authenticated');
    END IF;
    
    -- Get user information
    SELECT up.*, ur.role_name
    INTO user_record
    FROM public.user_profiles up
    JOIN public.user_roles ur ON up.role_id = ur.id
    WHERE up.id = user_profile_id;
    
    IF NOT FOUND THEN
        RETURN jsonb_build_object('success', false, 'error', 'User not found');
    END IF;
    
    -- Reset email count if force_send is true
    IF force_send THEN
        UPDATE public.user_profiles 
        SET setup_email_count = 0, email_status = 'ready_to_send'
        WHERE id = user_profile_id;
    END IF;
    
    -- Check email limits (increased for testing)
    current_count := COALESCE(user_record.setup_email_count, 0);
    IF current_count >= 50 AND NOT force_send THEN -- Increased limit
        RETURN jsonb_build_object('success', false, 'error', 'Email limit reached. Use force_send=true to bypass.');
    END IF;
    
    -- Determine email type and content
    IF user_record.user_id IS NULL THEN
        -- User needs to register
        email_subject := 'Welcome to Construction Management System - Complete Your Registration';
        action_url := 'http://192.168.1.37:8081/invited-register?email=' || user_record.email || '&invited=true';
        
        email_content := 'Hello ' || user_record.first_name || ',

Welcome to Construction Management System!

Your account has been created with the following details:
- Email: ' || user_record.email || '
- Role: ' || user_record.role_name || '

To complete your account setup:

1. Click this link to register: ' || action_url || '
2. Create your password
3. Start using the system

If the link doesn''t work, copy and paste this URL into your browser:
' || action_url || '

If you have any questions, please contact your administrator.

Welcome to the team!

---
Construction Management System
' || CURRENT_DATE;

    ELSE
        -- User has auth account, can reset password
        email_subject := 'Reset Your Password - Construction Management System';
        action_url := 'http://192.168.1.37:8080/login';
        
        email_content := 'Hello ' || user_record.first_name || ',

You can reset your password for Construction Management System.

Your account details:
- Email: ' || user_record.email || '
- Role: ' || user_record.role_name || '

To reset your password:

1. Go to: ' || action_url || '
2. Click "Forgot Password"
3. Enter your email: ' || user_record.email || '
4. Check your email for reset instructions

Or contact your administrator for assistance.

---
Construction Management System
' || CURRENT_DATE;

    END IF;
    
    -- Update email count
    UPDATE public.user_profiles 
    SET 
        setup_email_sent_at = NOW(),
        setup_email_count = current_count + 1,
        email_status = 'bypass_rate_limit',
        updated_at = NOW()
    WHERE id = user_profile_id;
    
    -- Return email content with bypass flag
    RETURN jsonb_build_object(
        'success', true, 
        'message', 'Email content generated (rate limit bypass mode)',
        'email_data', jsonb_build_object(
            'to_email', user_record.email,
            'subject', email_subject,
            'text_content', email_content,
            'action_url', action_url,
            'user_name', user_record.first_name || ' ' || user_record.last_name,
            'method', CASE WHEN user_record.user_id IS NULL THEN 'registration_invitation' ELSE 'password_reset' END,
            'user_role', user_record.role_name,
            'has_auth_account', (user_record.user_id IS NOT NULL),
            'bypass_rate_limit', true
        )
    );
    
EXCEPTION
    WHEN OTHERS THEN
        RETURN jsonb_build_object('success', false, 'error', SQLERRM);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 3. Grant permissions
GRANT EXECUTE ON FUNCTION public.rpc_send_setup_email_bypass_limits(UUID, BOOLEAN) TO authenticated;

-- 4. Reset all user email counts for testing
UPDATE public.user_profiles 
SET 
    setup_email_count = 0,
    email_status = 'ready_for_testing',
    updated_at = NOW()
WHERE setup_email_count > 0;

-- 5. Show current status
DO $$
DECLARE
    user_record RECORD;
BEGIN
    RAISE NOTICE '';
    RAISE NOTICE '=== EMAIL RATE LIMIT RESET COMPLETE ===';
    RAISE NOTICE '';
    
    FOR user_record IN 
        SELECT email, first_name, last_name, setup_email_count, email_status
        FROM public.user_profiles 
        ORDER BY created_at
    LOOP
        RAISE NOTICE 'User: % (% %) - Count: %, Status: %', 
            user_record.email,
            user_record.first_name, 
            user_record.last_name,
            COALESCE(user_record.setup_email_count, 0),
            COALESCE(user_record.email_status, 'none');
    END LOOP;
    
    RAISE NOTICE '';
    RAISE NOTICE 'NEXT STEPS:';
    RAISE NOTICE '1. Wait 5-10 minutes for Supabase rate limit to reset';
    RAISE NOTICE '2. Or use the bypass function for immediate testing';
    RAISE NOTICE '3. Email sending should work normally now';
    RAISE NOTICE '';
    RAISE NOTICE '✅ RATE LIMITS RESET - READY FOR TESTING!';
END $$;
