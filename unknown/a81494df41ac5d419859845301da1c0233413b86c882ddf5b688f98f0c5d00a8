-- Reset email limits and update domain to localhost
-- Run this FIRST, then run the domain update script

-- 1. Reset email counts for all users
UPDATE public.user_profiles 
SET 
    setup_email_count = 0,
    email_status = 'pending'
WHERE setup_email_count > 0;

-- 2. Show current email counts
DO $$
DECLARE
    user_record RECORD;
    total_users INTEGER;
    users_with_emails INTEGER;
BEGIN
    RAISE NOTICE '=== EMAIL LIMIT RESET ===';
    
    SELECT COUNT(*) INTO total_users FROM public.user_profiles;
    SELECT COUNT(*) INTO users_with_emails FROM public.user_profiles WHERE setup_email_count > 0;
    
    RAISE NOTICE 'Total users: %', total_users;
    RAISE NOTICE 'Users with email counts > 0: %', users_with_emails;
    
    RAISE NOTICE '';
    RAISE NOTICE 'Current email counts:';
    
    FOR user_record IN 
        SELECT email, setup_email_count, email_status
        FROM public.user_profiles 
        ORDER BY email
    LOOP
        RAISE NOTICE '- %: count=%, status=%', 
            user_record.email,
            COALESCE(user_record.setup_email_count, 0),
            COALESCE(user_record.email_status, 'pending');
    END LOOP;
    
    RAISE NOTICE '';
    RAISE NOTICE '✓ Email limits reset successfully!';
    RAISE NOTICE 'You can now send setup emails again.';
END $$;
