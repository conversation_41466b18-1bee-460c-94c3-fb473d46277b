-- Update the clients table to make email optional
-- Run this if you already created the table with email as required

-- Remove the NOT NULL constraint from email column
ALTER TABLE public.clients ALTER COLUMN email DROP NOT NULL;

-- Verify the change
SELECT column_name, is_nullable, data_type 
FROM information_schema.columns 
WHERE table_name = 'clients' 
AND table_schema = 'public' 
AND column_name = 'email';
