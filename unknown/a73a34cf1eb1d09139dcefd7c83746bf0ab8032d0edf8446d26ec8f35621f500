import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { supabase } from '@/lib/supabase';
import { useAuth } from '@/contexts/AuthContext';
import { 
  Upload, 
  CheckCircle, 
  AlertTriangle, 
  XCircle, 
  Camera,
  Database,
  Image
} from 'lucide-react';

const ProfileUploadTest = () => {
  const { user } = useAuth();
  const [testResults, setTestResults] = useState<{
    storage: 'idle' | 'testing' | 'success' | 'error';
    function: 'idle' | 'testing' | 'success' | 'error';
    upload: 'idle' | 'testing' | 'success' | 'error';
  }>({
    storage: 'idle',
    function: 'idle',
    upload: 'idle'
  });
  const [messages, setMessages] = useState<string[]>([]);

  const addMessage = (message: string) => {
    setMessages(prev => [...prev, `${new Date().toLocaleTimeString()}: ${message}`]);
  };

  const testStorageBucket = async () => {
    setTestResults(prev => ({ ...prev, storage: 'testing' }));
    addMessage('Testing storage bucket access...');

    try {
      const { data, error } = await supabase.storage
        .from('profile-pictures')
        .list('', { limit: 1 });

      if (error) {
        addMessage(`Storage error: ${error.message}`);
        setTestResults(prev => ({ ...prev, storage: 'error' }));
        return false;
      }

      addMessage('Storage bucket accessible!');
      setTestResults(prev => ({ ...prev, storage: 'success' }));
      return true;
    } catch (error: any) {
      addMessage(`Storage test failed: ${error.message}`);
      setTestResults(prev => ({ ...prev, storage: 'error' }));
      return false;
    }
  };

  const testDatabaseFunction = async () => {
    if (!user) {
      addMessage('No user authenticated');
      setTestResults(prev => ({ ...prev, function: 'error' }));
      return false;
    }

    setTestResults(prev => ({ ...prev, function: 'testing' }));
    addMessage('Testing database function...');

    try {
      const { data, error } = await supabase.rpc('handle_profile_picture_upload', {
        user_id: user.id,
        file_path: 'test/test-image.jpg'
      });

      if (error) {
        addMessage(`Function error: ${error.message}`);
        setTestResults(prev => ({ ...prev, function: 'error' }));
        return false;
      }

      addMessage('Database function working!');
      setTestResults(prev => ({ ...prev, function: 'success' }));
      return true;
    } catch (error: any) {
      addMessage(`Function test failed: ${error.message}`);
      setTestResults(prev => ({ ...prev, function: 'error' }));
      return false;
    }
  };

  const testFileUpload = async () => {
    if (!user) {
      addMessage('No user authenticated');
      setTestResults(prev => ({ ...prev, upload: 'error' }));
      return false;
    }

    setTestResults(prev => ({ ...prev, upload: 'testing' }));
    addMessage('Testing file upload...');

    try {
      // Create a small test image (1x1 pixel PNG)
      const canvas = document.createElement('canvas');
      canvas.width = 1;
      canvas.height = 1;
      const ctx = canvas.getContext('2d');
      if (ctx) {
        ctx.fillStyle = '#FF0000';
        ctx.fillRect(0, 0, 1, 1);
      }

      canvas.toBlob(async (blob) => {
        if (!blob) {
          addMessage('Failed to create test image');
          setTestResults(prev => ({ ...prev, upload: 'error' }));
          return;
        }

        const fileName = `${user.id}/test-${Date.now()}.png`;

        const { data: uploadData, error: uploadError } = await supabase.storage
          .from('profile-pictures')
          .upload(fileName, blob, {
            cacheControl: '3600',
            upsert: true
          });

        if (uploadError) {
          addMessage(`Upload error: ${uploadError.message}`);
          setTestResults(prev => ({ ...prev, upload: 'error' }));
          return;
        }

        addMessage('File upload successful!');
        setTestResults(prev => ({ ...prev, upload: 'success' }));

        // Clean up test file
        await supabase.storage
          .from('profile-pictures')
          .remove([fileName]);
        addMessage('Test file cleaned up');
      }, 'image/png');

    } catch (error: any) {
      addMessage(`Upload test failed: ${error.message}`);
      setTestResults(prev => ({ ...prev, upload: 'error' }));
    }
  };

  const runAllTests = async () => {
    setMessages([]);
    addMessage('Starting profile upload system tests...');
    
    const storageOk = await testStorageBucket();
    if (storageOk) {
      const functionOk = await testDatabaseFunction();
      if (functionOk) {
        await testFileUpload();
      }
    }
    
    addMessage('Tests completed!');
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'success':
        return <CheckCircle className="h-4 w-4 text-green-600" />;
      case 'error':
        return <XCircle className="h-4 w-4 text-red-600" />;
      case 'testing':
        return <div className="h-4 w-4 border-2 border-blue-600 border-t-transparent rounded-full animate-spin" />;
      default:
        return <div className="h-4 w-4 border-2 border-gray-300 rounded-full" />;
    }
  };

  return (
    <Card className="border-blue-200 bg-blue-50">
      <CardHeader>
        <CardTitle className="flex items-center gap-2 text-blue-800">
          <Camera className="h-5 w-5" />
          Profile Picture Upload Test
        </CardTitle>
        <CardDescription className="text-blue-700">
          Test the profile picture upload functionality
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="space-y-2">
          <div className="flex items-center gap-3 p-2 rounded-md bg-white/50">
            {getStatusIcon(testResults.storage)}
            <span className="text-sm font-medium">Storage Bucket Access</span>
          </div>
          
          <div className="flex items-center gap-3 p-2 rounded-md bg-white/50">
            {getStatusIcon(testResults.function)}
            <span className="text-sm font-medium">Database Function</span>
          </div>
          
          <div className="flex items-center gap-3 p-2 rounded-md bg-white/50">
            {getStatusIcon(testResults.upload)}
            <span className="text-sm font-medium">File Upload</span>
          </div>
        </div>

        {messages.length > 0 && (
          <div className="bg-white/70 p-3 rounded-md max-h-32 overflow-y-auto">
            <h4 className="font-medium text-blue-900 mb-2">Test Log:</h4>
            <div className="space-y-1">
              {messages.map((message, index) => (
                <p key={index} className="text-xs text-blue-800 font-mono">
                  {message}
                </p>
              ))}
            </div>
          </div>
        )}

        <div className="flex gap-2">
          <Button onClick={runAllTests} size="sm" className="flex-1">
            <Upload className="h-4 w-4 mr-2" />
            Run Tests
          </Button>
          <Button 
            variant="outline" 
            size="sm" 
            onClick={() => setMessages([])}
          >
            Clear Log
          </Button>
        </div>

        <Alert>
          <Database className="h-4 w-4" />
          <AlertDescription>
            Run the <code>database/profile_management_complete.sql</code> script in Supabase first to enable all features.
          </AlertDescription>
        </Alert>
      </CardContent>
    </Card>
  );
};

export default ProfileUploadTest;
