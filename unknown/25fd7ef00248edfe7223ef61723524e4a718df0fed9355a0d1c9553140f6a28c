-- Fix Orphaned <NAME_EMAIL>
-- This creates the missing auth user for the existing profile

DO $$
DECLARE
    v_profile_record RECORD;
    v_auth_user_id UUID;
    v_role_name VARCHAR(50);
    v_password VARCHAR(255) := 'TempPass123!';
BEGIN
    RAISE NOTICE '';
    RAISE NOTICE '=== FIXING ORPHANED <NAME_EMAIL> ===';
    
    -- Get the existing profile information
    SELECT up.*, ur.role_name as role_name_from_table
    INTO v_profile_record
    FROM public.user_profiles up
    LEFT JOIN public.user_roles ur ON up.role_id = ur.id
    WHERE up.email = '<EMAIL>';
    
    IF v_profile_record.email IS NULL THEN
        RAISE NOTICE '❌ Profile not <NAME_EMAIL>';
        RETURN;
    END IF;
    
    RAISE NOTICE '✅ Found profile: % % (Role: %)', 
        v_profile_record.first_name, 
        v_profile_record.last_name,
        v_profile_record.role_name_from_table;
    
    -- Check if auth user already exists
    SELECT id INTO v_auth_user_id
    FROM auth.users
    WHERE email = '<EMAIL>';
    
    IF v_auth_user_id IS NOT NULL THEN
        RAISE NOTICE '✅ Auth user already exists with ID: %', v_auth_user_id;
        
        -- Update the profile to link to the existing auth user
        UPDATE public.user_profiles 
        SET user_id = v_auth_user_id
        WHERE email = '<EMAIL>';
        
        RAISE NOTICE '✅ Updated profile to link to existing auth user';
        RETURN;
    END IF;
    
    -- Create new auth user
    v_auth_user_id := gen_random_uuid();
    
    BEGIN
        INSERT INTO auth.users (
            id,
            instance_id,
            email,
            encrypted_password,
            email_confirmed_at,
            created_at,
            updated_at,
            raw_user_meta_data,
            raw_app_meta_data,
            is_super_admin,
            role
        ) VALUES (
            v_auth_user_id,
            '00000000-0000-0000-0000-000000000000',
            '<EMAIL>',
            crypt(v_password, gen_salt('bf')),
            NOW(), -- Auto-confirm email
            NOW(),
            NOW(),
            jsonb_build_object(
                'first_name', v_profile_record.first_name,
                'last_name', v_profile_record.last_name
            ),
            jsonb_build_object('provider', 'email', 'providers', ARRAY['email']),
            false,
            'authenticated'
        );
        
        RAISE NOTICE '✅ Created auth user with ID: %', v_auth_user_id;
        
        -- Update the profile to link to the new auth user
        UPDATE public.user_profiles 
        SET user_id = v_auth_user_id
        WHERE email = '<EMAIL>';
        
        RAISE NOTICE '✅ Updated profile to link to new auth user';
        
        -- Add role_name column if it doesn't exist and update it
        IF NOT EXISTS (
            SELECT 1 FROM information_schema.columns 
            WHERE table_name = 'user_profiles' AND column_name = 'role_name'
        ) THEN
            ALTER TABLE public.user_profiles ADD COLUMN role_name VARCHAR(50);
            RAISE NOTICE '✅ Added role_name column';
        END IF;
        
        UPDATE public.user_profiles 
        SET role_name = v_profile_record.role_name_from_table
        WHERE email = '<EMAIL>';
        
        RAISE NOTICE '✅ Updated role_name to: %', v_profile_record.role_name_from_table;
        
    EXCEPTION
        WHEN OTHERS THEN
            RAISE NOTICE '❌ Failed to create auth user: %', SQLERRM;
            RETURN;
    END;
    
    RAISE NOTICE '';
    RAISE NOTICE '=== SUCCESS ===';
    RAISE NOTICE '✅ Fixed orphaned <NAME_EMAIL>';
    RAISE NOTICE '✅ Login credentials:';
    RAISE NOTICE '   Email: <EMAIL>';
    RAISE NOTICE '   Password: %', v_password;
    RAISE NOTICE '';
    RAISE NOTICE 'You can now login with these credentials!';
    RAISE NOTICE '';
    
END $$;

-- Verify the fix
DO $$
DECLARE
    v_profile_exists BOOLEAN := FALSE;
    v_auth_exists BOOLEAN := FALSE;
    v_linked BOOLEAN := FALSE;
BEGIN
    RAISE NOTICE '';
    RAISE NOTICE '=== VERIFICATION ===';
    
    -- Check profile exists
    SELECT EXISTS(SELECT 1 FROM public.user_profiles WHERE email = '<EMAIL>')
    INTO v_profile_exists;
    
    -- Check auth user exists
    SELECT EXISTS(SELECT 1 FROM auth.users WHERE email = '<EMAIL>')
    INTO v_auth_exists;
    
    -- Check they are linked
    SELECT EXISTS(
        SELECT 1 FROM public.user_profiles up
        JOIN auth.users au ON up.user_id = au.id
        WHERE up.email = '<EMAIL>'
    ) INTO v_linked;
    
    RAISE NOTICE 'Profile exists: %', v_profile_exists;
    RAISE NOTICE 'Auth user exists: %', v_auth_exists;
    RAISE NOTICE 'Properly linked: %', v_linked;
    
    IF v_profile_exists AND v_auth_exists AND v_linked THEN
        RAISE NOTICE '✅ ALL GOOD! User should be able to login now.';
    ELSE
        RAISE NOTICE '❌ Something is still wrong. Check the logs above.';
    END IF;
    
    RAISE NOTICE '';
END $$;
