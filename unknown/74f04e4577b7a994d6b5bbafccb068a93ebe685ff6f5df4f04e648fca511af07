-- Database Structure Checker
-- Run this to see your current database structure before setting up profile management

-- 1. Check existing tables
SELECT 
  'TABLES' as type,
  table_name as name,
  table_type as details
FROM information_schema.tables 
WHERE table_schema = 'public'
ORDER BY table_name;

-- 2. Check user_profiles table structure if it exists
DO $$
BEGIN
  IF EXISTS (SELECT FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'user_profiles') THEN
    RAISE NOTICE '=== USER_PROFILES TABLE STRUCTURE ===';
    
    -- Show columns
    FOR rec IN 
      SELECT column_name, data_type, is_nullable, column_default
      FROM information_schema.columns 
      WHERE table_schema = 'public' AND table_name = 'user_profiles'
      ORDER BY ordinal_position
    LOOP
      RAISE NOTICE 'Column: % | Type: % | Nullable: % | Default: %', 
        rec.column_name, rec.data_type, rec.is_nullable, rec.column_default;
    END LOOP;
  ELSE
    RAISE NOTICE 'user_profiles table does not exist';
  END IF;
END $$;

-- 3. Check for roles/role-related tables
SELECT 
  'ROLE TABLES' as type,
  table_name as name,
  'Found' as details
FROM information_schema.tables 
WHERE table_schema = 'public' 
AND (table_name LIKE '%role%' OR table_name LIKE '%user%')
ORDER BY table_name;

-- 4. Check existing storage buckets
SELECT 
  'STORAGE BUCKETS' as type,
  name,
  CASE WHEN public THEN 'Public' ELSE 'Private' END as details
FROM storage.buckets
ORDER BY name;

-- 5. Check existing functions
SELECT 
  'FUNCTIONS' as type,
  routine_name as name,
  routine_type as details
FROM information_schema.routines 
WHERE routine_schema = 'public'
AND routine_name LIKE '%profile%'
ORDER BY routine_name;

-- 6. Sample data check
DO $$
DECLARE
  user_count INTEGER;
  profile_count INTEGER;
BEGIN
  -- Count users
  SELECT COUNT(*) INTO user_count FROM auth.users;
  RAISE NOTICE 'Total users in auth.users: %', user_count;
  
  -- Count profiles if table exists
  IF EXISTS (SELECT FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'user_profiles') THEN
    EXECUTE 'SELECT COUNT(*) FROM public.user_profiles' INTO profile_count;
    RAISE NOTICE 'Total profiles in user_profiles: %', profile_count;
  END IF;
END $$;

-- 7. Check for existing profile-related columns
DO $$
BEGIN
  RAISE NOTICE '=== CHECKING FOR PROFILE COLUMNS ===';
  
  IF EXISTS (SELECT FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'user_profiles') THEN
    -- Check for profile picture column
    IF EXISTS (SELECT FROM information_schema.columns WHERE table_schema = 'public' AND table_name = 'user_profiles' AND column_name = 'profile_picture_url') THEN
      RAISE NOTICE '✓ profile_picture_url column exists';
    ELSE
      RAISE NOTICE '✗ profile_picture_url column missing';
    END IF;
    
    -- Check for bio column
    IF EXISTS (SELECT FROM information_schema.columns WHERE table_schema = 'public' AND table_name = 'user_profiles' AND column_name = 'bio') THEN
      RAISE NOTICE '✓ bio column exists';
    ELSE
      RAISE NOTICE '✗ bio column missing';
    END IF;
    
    -- Check for emergency contact columns
    IF EXISTS (SELECT FROM information_schema.columns WHERE table_schema = 'public' AND table_name = 'user_profiles' AND column_name = 'emergency_contact_name') THEN
      RAISE NOTICE '✓ emergency_contact_name column exists';
    ELSE
      RAISE NOTICE '✗ emergency_contact_name column missing';
    END IF;
  END IF;
END $$;

-- 8. Final recommendations
DO $$
BEGIN
  RAISE NOTICE '=== SETUP RECOMMENDATIONS ===';
  
  IF NOT EXISTS (SELECT FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'user_profiles') THEN
    RAISE NOTICE '⚠️  user_profiles table does not exist - you may need to create it first';
  END IF;
  
  IF NOT EXISTS (SELECT FROM storage.buckets WHERE name = 'profile-pictures') THEN
    RAISE NOTICE '⚠️  profile-pictures storage bucket does not exist - will be created by setup script';
  END IF;
  
  RAISE NOTICE '✅ Run database/profile_management_simple.sql to set up profile management';
  RAISE NOTICE '✅ This script is compatible with your existing database structure';
END $$;
