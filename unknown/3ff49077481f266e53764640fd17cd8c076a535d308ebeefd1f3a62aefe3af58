import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Separator } from '@/components/ui/separator';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { useToast } from '@/components/ui/use-toast';
import { useAuth } from '@/contexts/AuthContext';
import { supabase } from '@/lib/supabase';
import {
  Monitor,
  Database,
  Shield,
  Clock,
  Mail,
  Globe,
  Server,
  Settings,
  AlertTriangle,
  CheckCircle,
  Loader2,
  Download,
  Upload,
  Trash2
} from 'lucide-react';

interface SystemConfig {
  company_name: string;
  company_email: string;
  company_phone: string;
  company_address: string;
  timezone: string;
  date_format: string;
  currency: string;
  session_timeout: number;
  max_file_size: number;
  backup_frequency: string;
  maintenance_mode: boolean;
  registration_enabled: boolean;
  email_verification_required: boolean;
  two_factor_required: boolean;
}

const SystemSettings = () => {
  const { toast } = useToast();
  const { user } = useAuth();
  const [loading, setLoading] = useState(false);
  const [config, setConfig] = useState<SystemConfig>({
    company_name: 'Construction Management System',
    company_email: '<EMAIL>',
    company_phone: '+****************',
    company_address: '123 Business Ave, City, State 12345',
    timezone: 'America/New_York',
    date_format: 'MM/DD/YYYY',
    currency: 'USD',
    session_timeout: 30,
    max_file_size: 10,
    backup_frequency: 'daily',
    maintenance_mode: false,
    registration_enabled: true,
    email_verification_required: true,
    two_factor_required: false
  });

  useEffect(() => {
    loadSystemConfig();
  }, []);

  const loadSystemConfig = async () => {
    try {
      const { data, error } = await supabase
        .from('system_config')
        .select('*')
        .single();

      if (error && error.code !== 'PGRST116') {
        console.warn('System config table not found:', error.message);
        return; // Use default config
      }

      if (data) {
        setConfig(data);
      }
    } catch (error) {
      console.warn('Error loading system config (database may not be set up):', error);
      // Continue with default config
    }
  };

  const handleConfigChange = (key: keyof SystemConfig, value: string | number | boolean) => {
    setConfig(prev => ({ ...prev, [key]: value }));
  };

  const saveSystemConfig = async () => {
    setLoading(true);
    try {
      const { error } = await supabase
        .from('system_config')
        .upsert({
          id: 1, // Single row config
          ...config,
          updated_at: new Date().toISOString(),
          updated_by: user?.id
        });

      if (error) {
        throw error;
      }

      toast({
        title: "System Configuration Updated",
        description: "System settings have been successfully saved",
      });

    } catch (error: any) {
      console.error('Error saving system config:', error);

      let errorMessage = "Failed to save system configuration";
      if (error?.message?.includes('relation') && error?.message?.includes('does not exist')) {
        errorMessage = "Configuration updated locally. Run database schema to enable persistence.";
      } else if (error.message) {
        errorMessage = error.message;
      }

      toast({
        title: error?.message?.includes('relation') ? "Database Not Set Up" : "Save Failed",
        description: errorMessage,
        variant: error?.message?.includes('relation') ? "default" : "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const performBackup = async () => {
    setLoading(true);
    try {
      // This would typically call a backup API endpoint
      await new Promise(resolve => setTimeout(resolve, 2000)); // Simulate backup
      
      toast({
        title: "Backup Completed",
        description: "System backup has been successfully created",
      });
    } catch (error) {
      toast({
        title: "Backup Failed",
        description: "Failed to create system backup",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const clearCache = async () => {
    try {
      // Clear browser cache and local storage
      if ('caches' in window) {
        const cacheNames = await caches.keys();
        await Promise.all(cacheNames.map(name => caches.delete(name)));
      }
      
      localStorage.clear();
      sessionStorage.clear();
      
      toast({
        title: "Cache Cleared",
        description: "System cache has been cleared successfully",
      });
    } catch (error) {
      toast({
        title: "Clear Cache Failed",
        description: "Failed to clear system cache",
        variant: "destructive",
      });
    }
  };

  return (
    <div className="space-y-6">
      {/* Company Information */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Globe className="h-5 w-5" />
            Company Information
          </CardTitle>
          <CardDescription>
            Configure your company details and branding
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="company-name">Company Name</Label>
              <Input
                id="company-name"
                value={config.company_name}
                onChange={(e) => handleConfigChange('company_name', e.target.value)}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="company-email">Company Email</Label>
              <Input
                id="company-email"
                type="email"
                value={config.company_email}
                onChange={(e) => handleConfigChange('company_email', e.target.value)}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="company-phone">Company Phone</Label>
              <Input
                id="company-phone"
                value={config.company_phone}
                onChange={(e) => handleConfigChange('company_phone', e.target.value)}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="timezone">Timezone</Label>
              <Select value={config.timezone} onValueChange={(value) => handleConfigChange('timezone', value)}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="America/New_York">Eastern Time</SelectItem>
                  <SelectItem value="America/Chicago">Central Time</SelectItem>
                  <SelectItem value="America/Denver">Mountain Time</SelectItem>
                  <SelectItem value="America/Los_Angeles">Pacific Time</SelectItem>
                  <SelectItem value="UTC">UTC</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
          <div className="space-y-2">
            <Label htmlFor="company-address">Company Address</Label>
            <Input
              id="company-address"
              value={config.company_address}
              onChange={(e) => handleConfigChange('company_address', e.target.value)}
            />
          </div>
        </CardContent>
      </Card>

      {/* System Preferences */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Settings className="h-5 w-5" />
            System Preferences
          </CardTitle>
          <CardDescription>
            Configure system-wide settings and preferences
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="space-y-2">
              <Label htmlFor="date-format">Date Format</Label>
              <Select value={config.date_format} onValueChange={(value) => handleConfigChange('date_format', value)}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="MM/DD/YYYY">MM/DD/YYYY</SelectItem>
                  <SelectItem value="DD/MM/YYYY">DD/MM/YYYY</SelectItem>
                  <SelectItem value="YYYY-MM-DD">YYYY-MM-DD</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <Label htmlFor="currency">Currency</Label>
              <Select value={config.currency} onValueChange={(value) => handleConfigChange('currency', value)}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="USD">USD ($)</SelectItem>
                  <SelectItem value="EUR">EUR (€)</SelectItem>
                  <SelectItem value="GBP">GBP (£)</SelectItem>
                  <SelectItem value="CAD">CAD (C$)</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <Label htmlFor="session-timeout">Session Timeout (minutes)</Label>
              <Input
                id="session-timeout"
                type="number"
                value={config.session_timeout}
                onChange={(e) => handleConfigChange('session_timeout', parseInt(e.target.value))}
                min="5"
                max="480"
              />
            </div>
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="max-file-size">Maximum File Upload Size (MB)</Label>
            <Input
              id="max-file-size"
              type="number"
              value={config.max_file_size}
              onChange={(e) => handleConfigChange('max_file_size', parseInt(e.target.value))}
              min="1"
              max="100"
              className="w-32"
            />
          </div>
        </CardContent>
      </Card>

      {/* Security Settings */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Shield className="h-5 w-5" />
            Security Settings
          </CardTitle>
          <CardDescription>
            Configure system security and access controls
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label>User Registration</Label>
              <p className="text-sm text-gray-500">Allow new users to register accounts</p>
            </div>
            <Switch 
              checked={config.registration_enabled}
              onCheckedChange={(checked) => handleConfigChange('registration_enabled', checked)}
            />
          </div>
          
          <Separator />
          
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label>Email Verification Required</Label>
              <p className="text-sm text-gray-500">Require email verification for new accounts</p>
            </div>
            <Switch 
              checked={config.email_verification_required}
              onCheckedChange={(checked) => handleConfigChange('email_verification_required', checked)}
            />
          </div>
          
          <Separator />
          
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label>Two-Factor Authentication Required</Label>
              <p className="text-sm text-gray-500">Require 2FA for all user accounts</p>
            </div>
            <Switch 
              checked={config.two_factor_required}
              onCheckedChange={(checked) => handleConfigChange('two_factor_required', checked)}
            />
          </div>
        </CardContent>
      </Card>

      {/* System Maintenance */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Server className="h-5 w-5" />
            System Maintenance
          </CardTitle>
          <CardDescription>
            Manage system maintenance and data operations
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label>Maintenance Mode</Label>
              <p className="text-sm text-gray-500">Enable maintenance mode to restrict access</p>
            </div>
            <Switch 
              checked={config.maintenance_mode}
              onCheckedChange={(checked) => handleConfigChange('maintenance_mode', checked)}
            />
          </div>
          
          {config.maintenance_mode && (
            <Alert>
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>
                Maintenance mode is enabled. Only administrators can access the system.
              </AlertDescription>
            </Alert>
          )}
          
          <Separator />
          
          <div className="space-y-4">
            <div className="space-y-2">
              <Label>Backup Frequency</Label>
              <Select value={config.backup_frequency} onValueChange={(value) => handleConfigChange('backup_frequency', value)}>
                <SelectTrigger className="w-48">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="hourly">Hourly</SelectItem>
                  <SelectItem value="daily">Daily</SelectItem>
                  <SelectItem value="weekly">Weekly</SelectItem>
                  <SelectItem value="monthly">Monthly</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <div className="flex gap-2">
              <Button onClick={performBackup} disabled={loading}>
                {loading ? (
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                ) : (
                  <Download className="h-4 w-4 mr-2" />
                )}
                Create Backup
              </Button>
              <Button variant="outline" onClick={clearCache}>
                <Trash2 className="h-4 w-4 mr-2" />
                Clear Cache
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Save Configuration */}
      <div className="flex justify-end">
        <Button onClick={saveSystemConfig} disabled={loading} size="lg">
          {loading ? (
            <Loader2 className="h-4 w-4 mr-2 animate-spin" />
          ) : (
            <CheckCircle className="h-4 w-4 mr-2" />
          )}
          Save System Configuration
        </Button>
      </div>
    </div>
  );
};

export default SystemSettings;
