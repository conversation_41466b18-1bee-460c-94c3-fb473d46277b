import React, { useState } from 'react';
import { supabase } from '@/lib/supabase';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { UserPlus, CheckCircle, XCircle, Loader2 } from 'lucide-react';

const StandaloneRegister = () => {
  const [formData, setFormData] = useState({
    email: '',
    firstName: '',
    lastName: '',
    role: 'admin', // Default to admin so you can get back into the system
    password: 'TempPass123!' // Fixed password for now
  });
  const [loading, setLoading] = useState(false);
  const [result, setResult] = useState<any>(null);
  const [roles, setRoles] = useState<any[]>([]);

  // Load roles on component mount
  React.useEffect(() => {
    loadRoles();
  }, []);

  const loadRoles = async () => {
    try {
      const { data, error } = await supabase
        .from('user_roles')
        .select('*')
        .order('role_name');
      
      if (error) throw error;
      setRoles(data || []);
    } catch (error) {
      console.error('Failed to load roles:', error);
    }
  };

  const createAccount = async () => {
    setLoading(true);
    setResult(null);

    try {
      console.log('🔍 Creating new account...');
      
      // Validate required fields
      if (!formData.email || !formData.firstName || !formData.lastName || !formData.role) {
        setResult({
          success: false,
          error: 'Please fill in all required fields'
        });
        return;
      }

      // Step 1: Create auth user
      console.log('Creating auth user...');
      const { data: authData, error: authError } = await supabase.auth.signUp({
        email: formData.email,
        password: formData.password,
        options: {
          data: {
            first_name: formData.firstName,
            last_name: formData.lastName
          }
        }
      });

      if (authError) {
        console.error('Auth creation failed:', authError);
        setResult({
          success: false,
          step: 'Auth User Creation',
          error: authError.message,
          details: authError
        });
        return;
      }

      console.log('✅ Auth user created:', authData.user?.id);

      // Step 2: Get role information
      const selectedRole = roles.find(r => r.role_name === formData.role);
      if (!selectedRole) {
        setResult({
          success: false,
          step: 'Role Validation',
          error: `Role '${formData.role}' not found`
        });
        return;
      }

      // Step 3: Create user profile
      console.log('Creating user profile...');
      const { data: profileData, error: profileError } = await supabase
        .from('user_profiles')
        .insert({
          user_id: authData.user?.id,
          email: formData.email,
          first_name: formData.firstName,
          last_name: formData.lastName,
          role_id: selectedRole.id,
          role_name: selectedRole.role_name,
          is_active: true
        })
        .select()
        .single();

      if (profileError) {
        console.error('Profile creation failed:', profileError);
        setResult({
          success: false,
          step: 'User Profile Creation',
          error: profileError.message,
          details: profileError
        });
        return;
      }

      console.log('✅ User profile created:', profileData.id);

      // Success!
      setResult({
        success: true,
        authUser: authData.user,
        profile: profileData,
        credentials: {
          email: formData.email,
          password: formData.password
        }
      });

      // Clear form
      setFormData({
        email: '',
        firstName: '',
        lastName: '',
        role: 'admin',
        password: 'TempPass123!'
      });

    } catch (error: any) {
      console.error('Account creation failed:', error);
      setResult({
        success: false,
        step: 'General Error',
        error: error.message,
        details: error
      });
    } finally {
      setLoading(false);
    }
  };

  const createAccountDirectly = async () => {
    setLoading(true);
    setResult(null);

    try {
      console.log('🔍 Creating account directly in database...');
      
      // Validate required fields
      if (!formData.email || !formData.firstName || !formData.lastName || !formData.role) {
        setResult({
          success: false,
          error: 'Please fill in all required fields'
        });
        return;
      }

      // Get role information
      const selectedRole = roles.find(r => r.role_name === formData.role);
      if (!selectedRole) {
        setResult({
          success: false,
          error: `Role '${formData.role}' not found`
        });
        return;
      }

      // Create using SQL function (bypasses Supabase Auth)
      const { data, error } = await supabase.rpc('create_user_directly', {
        p_email: formData.email,
        p_first_name: formData.firstName,
        p_last_name: formData.lastName,
        p_role_id: selectedRole.id,
        p_role_name: selectedRole.role_name,
        p_password: formData.password
      });

      if (error) {
        console.error('Direct creation failed:', error);
        setResult({
          success: false,
          step: 'Direct Database Creation',
          error: error.message,
          details: error
        });
        return;
      }

      console.log('✅ User created directly:', data);

      setResult({
        success: true,
        method: 'Direct Database Creation',
        result: data,
        credentials: {
          email: formData.email,
          password: formData.password
        }
      });

      // Clear form
      setFormData({
        email: '',
        firstName: '',
        lastName: '',
        role: 'admin',
        password: 'TempPass123!'
      });

    } catch (error: any) {
      console.error('Direct account creation failed:', error);
      setResult({
        success: false,
        step: 'Direct Creation Error',
        error: error.message,
        details: error
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
      <Card className="w-full max-w-md">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <UserPlus className="h-5 w-5" />
            Create New Account
          </CardTitle>
          <CardDescription>
            Create a new account without logging into the management system
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <Label htmlFor="email">Email *</Label>
            <Input
              id="email"
              type="email"
              value={formData.email}
              onChange={(e) => setFormData({ ...formData, email: e.target.value })}
              placeholder="<EMAIL>"
              required
            />
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="firstName">First Name *</Label>
              <Input
                id="firstName"
                value={formData.firstName}
                onChange={(e) => setFormData({ ...formData, firstName: e.target.value })}
                placeholder="John"
                required
              />
            </div>
            <div>
              <Label htmlFor="lastName">Last Name *</Label>
              <Input
                id="lastName"
                value={formData.lastName}
                onChange={(e) => setFormData({ ...formData, lastName: e.target.value })}
                placeholder="Doe"
                required
              />
            </div>
          </div>

          <div>
            <Label htmlFor="role">Role *</Label>
            <Select value={formData.role} onValueChange={(value) => setFormData({ ...formData, role: value })}>
              <SelectTrigger>
                <SelectValue placeholder="Select a role" />
              </SelectTrigger>
              <SelectContent>
                {roles.map((role) => (
                  <SelectItem key={role.id} value={role.role_name}>
                    {role.role_name.charAt(0).toUpperCase() + role.role_name.slice(1)}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div>
            <Label htmlFor="password">Password</Label>
            <Input
              id="password"
              type="password"
              value={formData.password}
              onChange={(e) => setFormData({ ...formData, password: e.target.value })}
              placeholder="Password"
            />
            <p className="text-xs text-gray-500 mt-1">Default: TempPass123!</p>
          </div>

          <div className="flex gap-2">
            <Button onClick={createAccount} disabled={loading} className="flex-1">
              {loading ? <Loader2 className="h-4 w-4 mr-2 animate-spin" /> : <UserPlus className="h-4 w-4 mr-2" />}
              Create Account
            </Button>
            <Button variant="outline" onClick={createAccountDirectly} disabled={loading} className="flex-1">
              {loading ? <Loader2 className="h-4 w-4 mr-2 animate-spin" /> : <UserPlus className="h-4 w-4 mr-2" />}
              Create Direct
            </Button>
          </div>

          {result && (
            <Alert className={result.success ? "border-green-200 bg-green-50" : "border-red-200 bg-red-50"}>
              {result.success ? <CheckCircle className="h-4 w-4" /> : <XCircle className="h-4 w-4" />}
              <AlertDescription>
                <div className="space-y-2">
                  <div><strong>Result:</strong> {result.success ? 'SUCCESS' : 'FAILED'}</div>
                  
                  {result.step && <div><strong>Step:</strong> {result.step}</div>}
                  {result.method && <div><strong>Method:</strong> {result.method}</div>}
                  {result.error && <div><strong>Error:</strong> {result.error}</div>}
                  
                  {result.success && result.credentials && (
                    <div className="mt-3 p-3 bg-blue-50 border border-blue-200 rounded">
                      <div><strong>🎉 Account Created Successfully!</strong></div>
                      <div className="mt-2">
                        <div><strong>Email:</strong> {result.credentials.email}</div>
                        <div><strong>Password:</strong> {result.credentials.password}</div>
                      </div>
                      <div className="mt-2 text-sm text-blue-600">
                        You can now try logging into the system with these credentials.
                      </div>
                    </div>
                  )}
                  
                  {result.details && (
                    <details className="mt-2">
                      <summary className="cursor-pointer text-sm font-medium">Error Details</summary>
                      <pre className="mt-1 text-xs bg-gray-100 p-2 rounded overflow-auto">
                        {JSON.stringify(result.details, null, 2)}
                      </pre>
                    </details>
                  )}
                </div>
              </AlertDescription>
            </Alert>
          )}

          <div className="text-center">
            <p className="text-sm text-gray-500">
              This page bypasses the main authentication system
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default StandaloneRegister;
