-- Fix for function type mismatch error
-- First, let's check the actual column types in the projects table
DO $$
DECLARE
  col_info RECORD;
BEGIN
  RAISE NOTICE 'Projects table column types:';
  FOR col_info IN
    SELECT column_name, data_type, character_maximum_length
    FROM information_schema.columns
    WHERE table_name = 'projects' AND table_schema = 'public'
    ORDER BY ordinal_position
  LOOP
    RAISE NOTICE '  %: % (%)', col_info.column_name, col_info.data_type, COALESCE(col_info.character_maximum_length::text, 'no limit');
  END LOOP;
END $$;

-- Drop and recreate the function with correct return types
DROP FUNCTION IF EXISTS get_user_accessible_projects(UUID);

-- Use SETOF projects to return the exact table structure
CREATE OR REPLACE FUNCTION get_user_accessible_projects(target_user_id UUID DEFAULT NULL)
RETURNS SETOF public.projects AS $$
DECLARE
  user_id_to_check UUID;
  user_role TEXT;
BEGIN
  -- Use provided user_id or current authenticated user
  user_id_to_check := COALESCE(target_user_id, auth.uid());

  -- Get user role
  SELECT role_name INTO user_role
  FROM public.user_profiles
  WHERE user_id = user_id_to_check;

  -- If user is admin, management, QS, or accountant, return all projects
  IF user_role IN ('admin', 'management', 'qs', 'accountant') THEN
    RETURN QUERY
    SELECT p.*
    FROM public.projects p
    ORDER BY p.date_added DESC;

  -- If user is client, return only their projects
  ELSIF user_role = 'client' THEN
    RETURN QUERY
    SELECT p.*
    FROM public.projects p
    INNER JOIN public.client_users cu ON p.client_id = cu.client_id
    WHERE cu.user_id = user_id_to_check
    ORDER BY p.date_added DESC;

  -- Default: return empty result for unknown roles
  ELSE
    RETURN;
  END IF;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Also fix the show_user_project_access function
DROP FUNCTION IF EXISTS show_user_project_access(TEXT);

CREATE OR REPLACE FUNCTION show_user_project_access(target_email TEXT DEFAULT NULL)
RETURNS TABLE (
  user_email VARCHAR(255),
  user_role VARCHAR(50),
  project_name VARCHAR(255),
  client_name VARCHAR(255),
  can_access BOOLEAN
) AS $$
DECLARE
  user_record RECORD;
BEGIN
  -- If specific email provided, check just that user
  IF target_email IS NOT NULL THEN
    SELECT user_id, email, role_name INTO user_record
    FROM public.user_profiles 
    WHERE email = target_email;
    
    IF user_record.user_id IS NOT NULL THEN
      RETURN QUERY
      SELECT 
        user_record.email::VARCHAR(255),
        user_record.role_name::VARCHAR(50),
        p.name,
        p.client_name,
        can_user_access_project(p.id, user_record.user_id) as can_access
      FROM public.projects p
      ORDER BY p.name;
    END IF;
  ELSE
    -- Show access for all client users
    FOR user_record IN 
      SELECT user_id, email, role_name 
      FROM public.user_profiles 
      WHERE role_name = 'client'
    LOOP
      RETURN QUERY
      SELECT 
        user_record.email::VARCHAR(255),
        user_record.role_name::VARCHAR(50),
        p.name,
        p.client_name,
        can_user_access_project(p.id, user_record.user_id) as can_access
      FROM public.projects p
      ORDER BY p.name;
    END LOOP;
  END IF;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Test the function
DO $$
BEGIN
  RAISE NOTICE '✅ Functions updated with correct types!';
  RAISE NOTICE 'Testing get_user_accessible_projects function...';
  
  -- Test if function works
  PERFORM get_user_accessible_projects();
  RAISE NOTICE '✅ Function test successful!';
  
EXCEPTION WHEN OTHERS THEN
  RAISE NOTICE '❌ Function test failed: %', SQLERRM;
END $$;

-- Show available functions
SELECT 
  routine_name,
  routine_type,
  data_type
FROM information_schema.routines 
WHERE routine_schema = 'public' 
AND routine_name IN ('get_user_accessible_projects', 'can_user_access_project', 'link_user_to_client')
ORDER BY routine_name;
