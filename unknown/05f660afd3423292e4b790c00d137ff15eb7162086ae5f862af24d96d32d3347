-- Fix Foreign Key Constraint Error
-- Run this in Supabase SQL Editor

-- 1. Disable the automatic trigger temporarily to avoid conflicts
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;

-- 2. Clean up any orphaned data
DO $$
DECLARE
    orphaned_profiles INTEGER;
    orphaned_auth_users INTEGER;
BEGIN
    -- Delete profiles that don't have corresponding auth users
    DELETE FROM public.user_profiles 
    WHERE user_id NOT IN (SELECT id FROM auth.users);
    
    GET DIAGNOSTICS orphaned_profiles = ROW_COUNT;
    RAISE NOTICE 'Deleted % orphaned user profiles', orphaned_profiles;
    
    -- Check for auth users without profiles (these are OK, profiles will be created manually)
    SELECT COUNT(*) INTO orphaned_auth_users
    FROM auth.users au
    LEFT JOIN public.user_profiles up ON au.id = up.user_id
    WHERE up.user_id IS NULL;
    
    RAISE NOTICE 'Found % auth users without profiles (will be handled by application)', orphaned_auth_users;
END $$;

-- 3. Create a safer trigger function that only runs when needed
CREATE OR REPLACE FUNCTION public.handle_new_user_safe()
RETURNS TRIGGER AS $$
BEGIN
    -- Only create profile if explicitly requested via metadata
    IF NEW.raw_user_meta_data ? 'create_profile' AND 
       (NEW.raw_user_meta_data->>'create_profile')::boolean = true THEN
        
        -- Double-check that profile doesn't exist
        IF NOT EXISTS (
            SELECT 1 FROM public.user_profiles 
            WHERE user_id = NEW.id OR email = NEW.email
        ) THEN
            INSERT INTO public.user_profiles (user_id, email, first_name, last_name, role_id)
            VALUES (
                NEW.id,
                NEW.email,
                COALESCE(NEW.raw_user_meta_data->>'first_name', 'User'),
                COALESCE(NEW.raw_user_meta_data->>'last_name', ''),
                (SELECT id FROM public.user_roles WHERE role_name = 'client' LIMIT 1)
            )
            ON CONFLICT (email) DO NOTHING;
        END IF;
    END IF;
    
    RETURN NEW;
EXCEPTION
    WHEN OTHERS THEN
        -- Log error but don't fail the user creation
        RAISE WARNING 'Failed to create user profile for %: %', NEW.email, SQLERRM;
        RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 4. Don't recreate the trigger - let the application handle profile creation manually

-- 5. Create a function to manually create profiles when needed
CREATE OR REPLACE FUNCTION public.create_user_profile(
    p_user_id UUID,
    p_email VARCHAR(255),
    p_first_name VARCHAR(100),
    p_last_name VARCHAR(100),
    p_role_name VARCHAR(50),
    p_phone VARCHAR(20) DEFAULT NULL,
    p_department VARCHAR(100) DEFAULT NULL,
    p_job_title VARCHAR(100) DEFAULT NULL
)
RETURNS public.user_profiles AS $$
DECLARE
    v_role_id UUID;
    v_profile public.user_profiles;
BEGIN
    -- Get role ID
    SELECT id INTO v_role_id 
    FROM public.user_roles 
    WHERE role_name = p_role_name;
    
    IF v_role_id IS NULL THEN
        RAISE EXCEPTION 'Invalid role: %', p_role_name;
    END IF;
    
    -- Verify the auth user exists
    IF NOT EXISTS (SELECT 1 FROM auth.users WHERE id = p_user_id) THEN
        RAISE EXCEPTION 'Auth user does not exist: %', p_user_id;
    END IF;
    
    -- Check if profile already exists
    SELECT * INTO v_profile 
    FROM public.user_profiles 
    WHERE user_id = p_user_id OR email = p_email;
    
    IF v_profile.id IS NOT NULL THEN
        -- Update existing profile
        UPDATE public.user_profiles 
        SET 
            first_name = p_first_name,
            last_name = p_last_name,
            role_id = v_role_id,
            phone = p_phone,
            department = p_department,
            job_title = p_job_title,
            updated_at = NOW()
        WHERE id = v_profile.id
        RETURNING * INTO v_profile;
    ELSE
        -- Create new profile
        INSERT INTO public.user_profiles (
            user_id, email, first_name, last_name, role_id,
            phone, department, job_title, is_active, is_verified
        ) VALUES (
            p_user_id, p_email, p_first_name, p_last_name, v_role_id,
            p_phone, p_department, p_job_title, true, false
        )
        RETURNING * INTO v_profile;
    END IF;
    
    RETURN v_profile;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 6. Grant necessary permissions
GRANT EXECUTE ON FUNCTION public.create_user_profile TO authenticated;
GRANT EXECUTE ON FUNCTION public.create_user_profile TO anon;

-- 7. Test the function works
DO $$
BEGIN
    RAISE NOTICE '=== FOREIGN KEY FIX COMPLETE ===';
    RAISE NOTICE 'Automatic trigger disabled - profiles will be created manually by application';
    RAISE NOTICE 'New function created: public.create_user_profile()';
    RAISE NOTICE 'This should resolve foreign key constraint errors';
    RAISE NOTICE '';
    RAISE NOTICE 'Next steps:';
    RAISE NOTICE '1. Try registering a new user';
    RAISE NOTICE '2. The application will handle profile creation manually';
    RAISE NOTICE '3. No more foreign key constraint errors should occur';
END $$;
