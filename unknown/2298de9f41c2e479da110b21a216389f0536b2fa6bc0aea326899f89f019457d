import React, { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Shield, AlertTriangle, ArrowLeft } from 'lucide-react';

const RegisterDisabled = () => {
  const navigate = useNavigate();

  useEffect(() => {
    // Auto-redirect to login after 5 seconds
    const timer = setTimeout(() => {
      navigate('/login');
    }, 5000);

    return () => clearTimeout(timer);
  }, [navigate]);

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <div className="flex justify-center mb-4">
            <Shield className="h-12 w-12 text-red-600" />
          </div>
          <CardTitle>Registration Disabled</CardTitle>
          <CardDescription>
            Self-registration is not available on this system
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <Alert>
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>
              For security reasons, self-registration has been disabled. All user accounts must be created by system administrators through the admin panel.
            </AlertDescription>
          </Alert>
          
          <div className="space-y-4">
            <div className="text-sm text-gray-600 space-y-2">
              <p><strong>Need an account?</strong></p>
              <ul className="list-disc list-inside space-y-1 ml-4">
                <li>Contact your system administrator</li>
                <li>Request account creation through proper channels</li>
                <li>Administrator will create your account and provide login credentials</li>
              </ul>
            </div>
            
            <div className="text-sm text-gray-600 space-y-2">
              <p><strong>Already have an account?</strong></p>
              <p>Use the login page to access the system.</p>
            </div>
          </div>

          <div className="space-y-2">
            <Button 
              onClick={() => navigate('/login')} 
              className="w-full"
            >
              <ArrowLeft className="mr-2 h-4 w-4" />
              Go to Login
            </Button>
            
            <p className="text-xs text-center text-gray-500">
              Redirecting to login in 5 seconds...
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default RegisterDisabled;
