-- Fix Profile Unlinking - Ensure all user_profiles are properly unlinked
-- This addresses the issue where profiles still show linked user_ids

-- STEP 1: Check current profile linking status
DO $$
DECLARE
    total_profiles INTEGER;
    linked_profiles INTEGER;
    orphaned_profiles INTEGER;
BEGIN
    RAISE NOTICE '';
    RAISE NOTICE '🔍 === CHECKING PROFILE LINKING STATUS === 🔍';
    RAISE NOTICE '';
    
    -- Count total profiles
    SELECT COUNT(*) INTO total_profiles FROM public.user_profiles;
    RAISE NOTICE 'Total user profiles: %', total_profiles;
    
    -- Count profiles with user_id set
    SELECT COUNT(*) INTO linked_profiles 
    FROM public.user_profiles 
    WHERE user_id IS NOT NULL;
    RAISE NOTICE 'Profiles with user_id set: %', linked_profiles;
    
    -- Count profiles with created_by_admin_id set
    SELECT COUNT(*) INTO orphaned_profiles 
    FROM public.user_profiles 
    WHERE created_by_admin_id IS NOT NULL;
    RAISE NOTICE 'Profiles with created_by_admin_id set: %', orphaned_profiles;
    
    RAISE NOTICE '';
END $$;

-- STEP 2: Show specific profiles that are still linked
DO $$
DECLARE
    profile_record RECORD;
    linked_count INTEGER := 0;
BEGIN
    RAISE NOTICE '=== PROFILES STILL LINKED ===';
    
    FOR profile_record IN 
        SELECT id, email, first_name, last_name, user_id, created_by_admin_id
        FROM public.user_profiles
        WHERE user_id IS NOT NULL OR created_by_admin_id IS NOT NULL
        ORDER BY email
    LOOP
        linked_count := linked_count + 1;
        RAISE NOTICE 'Profile %: % (% %) | user_id: % | created_by_admin_id: %', 
            linked_count,
            profile_record.email,
            profile_record.first_name,
            profile_record.last_name,
            COALESCE(profile_record.user_id::text, 'NULL'),
            COALESCE(profile_record.created_by_admin_id::text, 'NULL');
    END LOOP;
    
    IF linked_count = 0 THEN
        RAISE NOTICE '✅ No profiles are linked - all clean!';
    ELSE
        RAISE NOTICE '❌ Found % profiles still linked', linked_count;
    END IF;
    
    RAISE NOTICE '';
END $$;

-- STEP 3: Force unlink ALL profiles
DO $$
DECLARE
    update_count INTEGER;
BEGIN
    RAISE NOTICE '=== FORCE UNLINKING ALL PROFILES ===';
    
    -- Force set user_id to NULL for ALL profiles
    UPDATE public.user_profiles 
    SET user_id = NULL
    WHERE user_id IS NOT NULL;
    
    GET DIAGNOSTICS update_count = ROW_COUNT;
    RAISE NOTICE '✅ Unlinked user_id from % profiles', update_count;
    
    -- Force set created_by_admin_id to NULL for ALL profiles
    UPDATE public.user_profiles 
    SET created_by_admin_id = NULL
    WHERE created_by_admin_id IS NOT NULL;
    
    GET DIAGNOSTICS update_count = ROW_COUNT;
    RAISE NOTICE '✅ Unlinked created_by_admin_id from % profiles', update_count;
    
    RAISE NOTICE '';
END $$;

-- STEP 4: Verify all profiles are now unlinked
DO $$
DECLARE
    total_profiles INTEGER;
    linked_profiles INTEGER;
    admin_linked_profiles INTEGER;
BEGIN
    RAISE NOTICE '=== VERIFICATION AFTER UNLINKING ===';
    
    -- Count total profiles
    SELECT COUNT(*) INTO total_profiles FROM public.user_profiles;
    RAISE NOTICE 'Total user profiles: %', total_profiles;
    
    -- Count profiles still with user_id set
    SELECT COUNT(*) INTO linked_profiles 
    FROM public.user_profiles 
    WHERE user_id IS NOT NULL;
    RAISE NOTICE 'Profiles still with user_id: %', linked_profiles;
    
    -- Count profiles still with created_by_admin_id set
    SELECT COUNT(*) INTO admin_linked_profiles 
    FROM public.user_profiles 
    WHERE created_by_admin_id IS NOT NULL;
    RAISE NOTICE 'Profiles still with created_by_admin_id: %', admin_linked_profiles;
    
    IF linked_profiles = 0 AND admin_linked_profiles = 0 THEN
        RAISE NOTICE '✅ SUCCESS: All profiles are now properly unlinked!';
    ELSE
        RAISE NOTICE '❌ ISSUE: Some profiles are still linked';
    END IF;
    
    RAISE NOTICE '';
END $$;

-- STEP 5: Show all profiles after unlinking
DO $$
DECLARE
    profile_record RECORD;
    profile_count INTEGER := 0;
BEGIN
    RAISE NOTICE '=== ALL PROFILES STATUS ===';
    
    FOR profile_record IN 
        SELECT id, email, first_name, last_name, role_name, user_id, created_by_admin_id
        FROM public.user_profiles
        ORDER BY email
    LOOP
        profile_count := profile_count + 1;
        RAISE NOTICE 'Profile %: % (% %) | Role: % | user_id: % | admin_id: %', 
            profile_count,
            profile_record.email,
            profile_record.first_name,
            profile_record.last_name,
            profile_record.role_name,
            COALESCE(profile_record.user_id::text, 'NULL'),
            COALESCE(profile_record.created_by_admin_id::text, 'NULL');
    END LOOP;
    
    RAISE NOTICE '';
    RAISE NOTICE 'Total profiles: %', profile_count;
    RAISE NOTICE '';
END $$;

-- STEP 6: Check auth users count
DO $$
DECLARE
    auth_user_count INTEGER;
BEGIN
    RAISE NOTICE '=== AUTH USERS STATUS ===';
    
    SELECT COUNT(*) INTO auth_user_count FROM auth.users;
    RAISE NOTICE 'Remaining auth users: %', auth_user_count;
    
    IF auth_user_count = 0 THEN
        RAISE NOTICE '✅ Auth users table is empty - ready for fresh start!';
    ELSE
        RAISE NOTICE '❌ Still have % auth users - may need additional cleanup', auth_user_count;
    END IF;
    
    RAISE NOTICE '';
END $$;

-- STEP 7: Final status
DO $$
BEGIN
    RAISE NOTICE '🎯 === PROFILE UNLINKING COMPLETE === 🎯';
    RAISE NOTICE '';
    RAISE NOTICE '✅ All user profiles should now be unlinked from auth users';
    RAISE NOTICE '✅ All foreign key references have been removed';
    RAISE NOTICE '✅ Ready to create fresh auth users';
    RAISE NOTICE '';
    RAISE NOTICE '📋 NEXT STEPS:';
    RAISE NOTICE '1. Verify all profiles show user_id: NULL';
    RAISE NOTICE '2. Create new auth users using Supabase Dashboard';
    RAISE NOTICE '3. Link new auth users to existing profiles';
    RAISE NOTICE '4. Test login with new credentials';
    RAISE NOTICE '';
END $$;
