import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { TrendingUp, TrendingDown, Minus, AlertTriangle, CheckCircle, Info } from 'lucide-react';

export interface ReportCardProps {
  title: string;
  value: string | number;
  description?: string;
  change?: number;
  changeLabel?: string;
  trend?: 'up' | 'down' | 'neutral';
  type?: 'currency' | 'percentage' | 'number' | 'text';
  status?: 'positive' | 'negative' | 'neutral' | 'warning';
  icon?: React.ReactNode;
  className?: string;
}

const ReportCard: React.FC<ReportCardProps> = ({
  title,
  value,
  description,
  change,
  changeLabel,
  trend,
  type = 'number',
  status = 'neutral',
  icon,
  className = ''
}) => {
  const formatValue = (val: string | number): string => {
    if (typeof val === 'string') return val;
    
    switch (type) {
      case 'currency':
        return `$${val.toLocaleString()}`;
      case 'percentage':
        return `${val.toFixed(1)}%`;
      case 'number':
        return val.toLocaleString();
      default:
        return val.toString();
    }
  };

  const getStatusColor = () => {
    switch (status) {
      case 'positive':
        return 'text-green-600';
      case 'negative':
        return 'text-red-600';
      case 'warning':
        return 'text-yellow-600';
      default:
        return 'text-gray-900';
    }
  };

  const getStatusIcon = () => {
    switch (status) {
      case 'positive':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'negative':
        return <AlertTriangle className="h-4 w-4 text-red-500" />;
      case 'warning':
        return <AlertTriangle className="h-4 w-4 text-yellow-500" />;
      default:
        return <Info className="h-4 w-4 text-gray-500" />;
    }
  };

  const getTrendIcon = () => {
    switch (trend) {
      case 'up':
        return <TrendingUp className="h-3 w-3 text-green-500" />;
      case 'down':
        return <TrendingDown className="h-3 w-3 text-red-500" />;
      default:
        return <Minus className="h-3 w-3 text-gray-500" />;
    }
  };

  const getTrendColor = () => {
    switch (trend) {
      case 'up':
        return 'text-green-600';
      case 'down':
        return 'text-red-600';
      default:
        return 'text-gray-600';
    }
  };

  return (
    <Card className={`${className}`}>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium">{title}</CardTitle>
        {icon || getStatusIcon()}
      </CardHeader>
      <CardContent>
        <div className={`text-2xl font-bold ${getStatusColor()}`}>
          {formatValue(value)}
        </div>
        {description && (
          <p className="text-xs text-muted-foreground mt-1">
            {description}
          </p>
        )}
        {(change !== undefined || changeLabel) && (
          <div className="flex items-center space-x-1 mt-2">
            {change !== undefined && getTrendIcon()}
            <span className={`text-xs ${getTrendColor()}`}>
              {change !== undefined && (
                <>
                  {change > 0 ? '+' : ''}{change.toFixed(1)}%
                  {changeLabel && ` ${changeLabel}`}
                </>
              )}
              {!change && changeLabel && changeLabel}
            </span>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default ReportCard;
