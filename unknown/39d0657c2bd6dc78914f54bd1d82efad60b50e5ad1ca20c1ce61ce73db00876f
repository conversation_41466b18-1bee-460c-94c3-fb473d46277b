-- User Management & Authentication System Database Schema (FIXED VERSION)
-- Run this in your Supabase SQL Editor

-- 1. Create user roles table (defines available roles and their permissions)
CREATE TABLE IF NOT EXISTS public.user_roles (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    role_name VA<PERSON>HA<PERSON>(50) NOT NULL UNIQUE,
    role_display_name VA<PERSON>HA<PERSON>(100) NOT NULL,
    description TEXT,
    permissions JSONB NOT NULL DEFAULT '{}', -- Store permissions as JSON
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 2. Create user profiles table (extends Supabase auth.users)
CREATE TABLE IF NOT EXISTS public.user_profiles (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    email VARCHAR(255) NOT NULL UNIQUE,
    first_name <PERSON><PERSON><PERSON><PERSON>(100) NOT NULL,
    last_name VA<PERSON>HA<PERSON>(100) NOT NULL,
    phone VARCHAR(20),
    role_id UUID NOT NULL REFERENCES public.user_roles(id),
    
    -- Profile information
    avatar_url TEXT,
    bio TEXT,
    department VARCHAR(100),
    job_title VARCHAR(100),
    employee_id VARCHAR(50),
    
    -- Contact information
    address TEXT,
    city VARCHAR(100),
    state VARCHAR(100),
    postal_code VARCHAR(20),
    country VARCHAR(100) DEFAULT 'United States',
    
    -- Account status
    is_active BOOLEAN DEFAULT true,
    is_verified BOOLEAN DEFAULT false,
    last_login TIMESTAMP WITH TIME ZONE,
    
    -- Preferences
    preferences JSONB DEFAULT '{}',
    
    -- Audit trail
    created_by UUID REFERENCES public.user_profiles(id),
    updated_by UUID REFERENCES public.user_profiles(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 3. Create user sessions table (track active sessions)
CREATE TABLE IF NOT EXISTS public.user_sessions (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    session_token VARCHAR(255) NOT NULL UNIQUE,
    ip_address INET,
    user_agent TEXT,
    device_info JSONB,
    is_active BOOLEAN DEFAULT true,
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 4. Create audit log table (track user actions)
CREATE TABLE IF NOT EXISTS public.audit_logs (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE SET NULL,
    action VARCHAR(100) NOT NULL,
    resource_type VARCHAR(100),
    resource_id VARCHAR(255),
    old_values JSONB,
    new_values JSONB,
    ip_address INET,
    user_agent TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 5. Create password reset tokens table
CREATE TABLE IF NOT EXISTS public.password_reset_tokens (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    token VARCHAR(255) NOT NULL UNIQUE,
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    used_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_user_profiles_user_id ON public.user_profiles(user_id);
CREATE INDEX IF NOT EXISTS idx_user_profiles_email ON public.user_profiles(email);
CREATE INDEX IF NOT EXISTS idx_user_profiles_role_id ON public.user_profiles(role_id);
CREATE INDEX IF NOT EXISTS idx_user_profiles_employee_id ON public.user_profiles(employee_id);
CREATE INDEX IF NOT EXISTS idx_user_sessions_user_id ON public.user_sessions(user_id);
CREATE INDEX IF NOT EXISTS idx_user_sessions_token ON public.user_sessions(session_token);
CREATE INDEX IF NOT EXISTS idx_audit_logs_user_id ON public.audit_logs(user_id);
CREATE INDEX IF NOT EXISTS idx_audit_logs_created_at ON public.audit_logs(created_at);
CREATE INDEX IF NOT EXISTS idx_password_reset_tokens_user_id ON public.password_reset_tokens(user_id);
CREATE INDEX IF NOT EXISTS idx_password_reset_tokens_token ON public.password_reset_tokens(token);

-- Enable Row Level Security
ALTER TABLE public.user_roles ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.user_profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.user_sessions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.audit_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.password_reset_tokens ENABLE ROW LEVEL SECURITY;

-- Create RLS Policies (Simple policies to avoid recursion)
DROP POLICY IF EXISTS "Allow all operations on user roles" ON public.user_roles;
DROP POLICY IF EXISTS "Allow all operations on user profiles" ON public.user_profiles;
DROP POLICY IF EXISTS "Allow all operations on user sessions" ON public.user_sessions;
DROP POLICY IF EXISTS "Allow all operations on audit logs" ON public.audit_logs;
DROP POLICY IF EXISTS "Allow all operations on password reset tokens" ON public.password_reset_tokens;

CREATE POLICY "Enable all operations for user_roles" ON public.user_roles FOR ALL USING (true);
CREATE POLICY "Enable all operations for user_profiles" ON public.user_profiles FOR ALL USING (true);
CREATE POLICY "Enable all operations for user_sessions" ON public.user_sessions FOR ALL USING (true);
CREATE POLICY "Enable all operations for audit_logs" ON public.audit_logs FOR ALL USING (true);
CREATE POLICY "Enable all operations for password_reset_tokens" ON public.password_reset_tokens FOR ALL USING (true);

-- Create function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers for automatic timestamp updates
CREATE TRIGGER update_user_roles_updated_at BEFORE UPDATE ON public.user_roles FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_user_profiles_updated_at BEFORE UPDATE ON public.user_profiles FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Create function to log user actions
CREATE OR REPLACE FUNCTION public.log_user_action(
    action_name VARCHAR(100),
    resource_type VARCHAR(100) DEFAULT NULL,
    resource_id VARCHAR(255) DEFAULT NULL,
    old_values JSONB DEFAULT NULL,
    new_values JSONB DEFAULT NULL
)
RETURNS VOID AS $$
BEGIN
    INSERT INTO public.audit_logs (user_id, action, resource_type, resource_id, old_values, new_values)
    VALUES (auth.uid(), action_name, resource_type, resource_id, old_values, new_values);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Insert default user roles with comprehensive permissions
INSERT INTO public.user_roles (role_name, role_display_name, description, permissions) VALUES
('admin', 'Administrator', 'Full system access with all administrative privileges', '{
    "users": {"create": true, "read": true, "update": true, "delete": true},
    "projects": {"create": true, "read": true, "update": true, "delete": true},
    "financials": {"create": true, "read": true, "update": true, "delete": true},
    "assets": {"create": true, "read": true, "update": true, "delete": true},
    "messages": {"create": true, "read": true, "update": true, "delete": true},
    "reports": {"create": true, "read": true, "update": true, "delete": true},
    "settings": {"create": true, "read": true, "update": true, "delete": true},
    "audit": {"read": true}
}'),

('qs', 'Quantity Surveyor', 'Quantity surveying and cost management specialist', '{
    "users": {"read": true},
    "projects": {"create": true, "read": true, "update": true},
    "financials": {"create": true, "read": true, "update": true},
    "assets": {"read": true, "update": true},
    "messages": {"create": true, "read": true, "update": true},
    "reports": {"create": true, "read": true},
    "cost_analysis": {"create": true, "read": true, "update": true},
    "estimates": {"create": true, "read": true, "update": true}
}'),

('accountant', 'Accountant', 'Financial management and accounting specialist', '{
    "users": {"read": true},
    "projects": {"read": true},
    "financials": {"create": true, "read": true, "update": true, "delete": true},
    "assets": {"read": true, "update": true},
    "messages": {"create": true, "read": true, "update": true},
    "reports": {"create": true, "read": true},
    "invoicing": {"create": true, "read": true, "update": true, "delete": true},
    "payments": {"create": true, "read": true, "update": true}
}'),

('management', 'Management', 'Project and team management with oversight privileges', '{
    "users": {"read": true, "update": true},
    "projects": {"create": true, "read": true, "update": true, "delete": true},
    "financials": {"read": true, "update": true},
    "assets": {"create": true, "read": true, "update": true},
    "messages": {"create": true, "read": true, "update": true, "delete": true},
    "reports": {"create": true, "read": true},
    "team_management": {"read": true, "update": true},
    "project_oversight": {"read": true, "update": true}
}'),

('client', 'Client', 'Client access with limited project visibility', '{
    "projects": {"read": true},
    "financials": {"read": true},
    "messages": {"create": true, "read": true},
    "reports": {"read": true},
    "project_updates": {"read": true},
    "invoices": {"read": true}
}')
ON CONFLICT (role_name) DO NOTHING;

-- Create function to automatically create user profile when user signs up
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
    -- Only create profile if one doesn't already exist
    IF NOT EXISTS (SELECT 1 FROM public.user_profiles WHERE user_id = NEW.id) THEN
        INSERT INTO public.user_profiles (user_id, email, first_name, last_name, role_id)
        VALUES (
            NEW.id,
            NEW.email,
            COALESCE(NEW.raw_user_meta_data->>'first_name', 'User'),
            COALESCE(NEW.raw_user_meta_data->>'last_name', ''),
            (SELECT id FROM public.user_roles WHERE role_name = 'client' LIMIT 1)
        );
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger to automatically create profile for new users
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
CREATE TRIGGER on_auth_user_created
    AFTER INSERT ON auth.users
    FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

-- Comments for documentation
COMMENT ON TABLE public.user_roles IS 'Defines user roles and their permissions';
COMMENT ON TABLE public.user_profiles IS 'Extended user profile information';
COMMENT ON TABLE public.user_sessions IS 'Track active user sessions';
COMMENT ON TABLE public.audit_logs IS 'Audit trail of user actions';
COMMENT ON TABLE public.password_reset_tokens IS 'Password reset token management';
COMMENT ON FUNCTION public.handle_new_user() IS 'Automatically creates user profile when user signs up';
COMMENT ON FUNCTION public.log_user_action IS 'Logs user actions for audit trail';

-- Success message
DO $$
BEGIN
    RAISE NOTICE 'User management schema created successfully!';
    RAISE NOTICE 'You can now:';
    RAISE NOTICE '1. Navigate to /register to create your first admin account';
    RAISE NOTICE '2. Navigate to /login to sign in';
    RAISE NOTICE '3. Navigate to /profile to manage your profile';
    RAISE NOTICE '4. Navigate to /settings (as admin) to manage users';
END $$;
