-- Fix Auth Mismatch - Sync user_profiles with auth.users
-- This helps identify and fix authentication issues

-- 1. Check current state of users
DO $$
DECLARE
    profile_record RECORD;
    auth_user_record RECORD;
BEGIN
    RAISE NOTICE '';
    RAISE NOTICE '=== AUTHENTICATION MISMATCH DIAGNOSIS ===';
    RAISE NOTICE '';
    
    -- Show all profiles
    RAISE NOTICE 'USER PROFILES IN DATABASE:';
    FOR profile_record IN 
        SELECT id, email, first_name, last_name, user_id, created_at
        FROM public.user_profiles 
        ORDER BY created_at DESC
    LOOP
        RAISE NOTICE 'Profile: % % (%) - Auth ID: % - Created: %', 
            profile_record.first_name, 
            profile_record.last_name,
            profile_record.email,
            COALESCE(profile_record.user_id::text, 'NULL'),
            profile_record.created_at;
    END LOOP;
    
    RAISE NOTICE '';
    RAISE NOTICE 'AUTH USERS IN SUPABASE:';
    FOR auth_user_record IN 
        SELECT id, email, created_at, email_confirmed_at, last_sign_in_at
        FROM auth.users 
        ORDER BY created_at DESC
    LOOP
        RAISE NOTICE 'Auth User: % - ID: % - Confirmed: % - Last Login: %', 
            auth_user_record.email,
            auth_user_record.id,
            COALESCE(auth_user_record.email_confirmed_at::text, 'NOT CONFIRMED'),
            COALESCE(auth_user_record.last_sign_in_at::text, 'NEVER');
    END LOOP;
    
    RAISE NOTICE '';
    RAISE NOTICE 'MISMATCHED PROFILES (profiles without auth users):';
    FOR profile_record IN 
        SELECT up.id, up.email, up.first_name, up.last_name, up.user_id
        FROM public.user_profiles up
        LEFT JOIN auth.users au ON up.user_id = au.id
        WHERE au.id IS NULL
    LOOP
        RAISE NOTICE 'ORPHANED PROFILE: % % (%) - Missing Auth User', 
            profile_record.first_name, 
            profile_record.last_name,
            profile_record.email;
    END LOOP;
    
    RAISE NOTICE '';
    RAISE NOTICE 'MISMATCHED AUTH USERS (auth users without profiles):';
    FOR auth_user_record IN 
        SELECT au.id, au.email
        FROM auth.users au
        LEFT JOIN public.user_profiles up ON au.id = up.user_id
        WHERE up.id IS NULL
    LOOP
        RAISE NOTICE 'ORPHANED AUTH USER: % - ID: %', 
            auth_user_record.email,
            auth_user_record.id;
    END LOOP;
END $$;

-- 2. Function to manually confirm email for testing
CREATE OR REPLACE FUNCTION public.confirm_user_email_for_testing(
    user_email VARCHAR(255)
)
RETURNS jsonb AS $$
DECLARE
    auth_user_id UUID;
BEGIN
    -- Find the auth user
    SELECT id INTO auth_user_id
    FROM auth.users
    WHERE email = user_email;
    
    IF auth_user_id IS NULL THEN
        RETURN jsonb_build_object(
            'success', false,
            'error', 'Auth user not found for email: ' || user_email
        );
    END IF;
    
    -- Confirm the email (this requires admin privileges)
    UPDATE auth.users 
    SET 
        email_confirmed_at = NOW(),
        updated_at = NOW()
    WHERE id = auth_user_id;
    
    RETURN jsonb_build_object(
        'success', true,
        'message', 'Email confirmed for user: ' || user_email,
        'auth_user_id', auth_user_id
    );
    
EXCEPTION
    WHEN OTHERS THEN
        RETURN jsonb_build_object(
            'success', false,
            'error', 'Failed to confirm email: ' || SQLERRM
        );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 3. Function to reset user password for testing
CREATE OR REPLACE FUNCTION public.reset_user_password_for_testing(
    user_email VARCHAR(255),
    new_password VARCHAR(255) DEFAULT 'password123'
)
RETURNS jsonb AS $$
DECLARE
    auth_user_id UUID;
    encrypted_password TEXT;
BEGIN
    -- Find the auth user
    SELECT id INTO auth_user_id
    FROM auth.users
    WHERE email = user_email;
    
    IF auth_user_id IS NULL THEN
        RETURN jsonb_build_object(
            'success', false,
            'error', 'Auth user not found for email: ' || user_email
        );
    END IF;
    
    -- Generate encrypted password (this is a simplified version)
    -- In production, use proper password hashing
    encrypted_password := crypt(new_password, gen_salt('bf'));
    
    -- Update the password
    UPDATE auth.users 
    SET 
        encrypted_password = encrypted_password,
        updated_at = NOW()
    WHERE id = auth_user_id;
    
    RETURN jsonb_build_object(
        'success', true,
        'message', 'Password reset for user: ' || user_email,
        'new_password', new_password,
        'auth_user_id', auth_user_id
    );
    
EXCEPTION
    WHEN OTHERS THEN
        RETURN jsonb_build_object(
            'success', false,
            'error', 'Failed to reset password: ' || SQLERRM
        );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 4. Grant permissions
GRANT EXECUTE ON FUNCTION public.confirm_user_email_for_testing(VARCHAR) TO authenticated;
GRANT EXECUTE ON FUNCTION public.reset_user_password_for_testing(VARCHAR, VARCHAR) TO authenticated;

-- 5. Show instructions
DO $$
BEGIN
    RAISE NOTICE '';
    RAISE NOTICE '=== NEXT STEPS TO FIX LOGIN ISSUES ===';
    RAISE NOTICE '';
    RAISE NOTICE '1. Check the diagnosis above to see what''s wrong';
    RAISE NOTICE '2. If users have unconfirmed emails, run:';
    RAISE NOTICE '   SELECT public.confirm_user_email_for_testing(''<EMAIL>'');';
    RAISE NOTICE '';
    RAISE NOTICE '3. If users need password reset, run:';
    RAISE NOTICE '   SELECT public.reset_user_password_for_testing(''<EMAIL>'', ''newpassword123'');';
    RAISE NOTICE '';
    RAISE NOTICE '4. Check Supabase Auth settings:';
    RAISE NOTICE '   - Email confirmation might be required';
    RAISE NOTICE '   - Check if email provider is configured';
    RAISE NOTICE '   - Verify auth policies are correct';
    RAISE NOTICE '';
    RAISE NOTICE '✅ DIAGNOSIS COMPLETE - CHECK OUTPUT ABOVE';
END $$;
