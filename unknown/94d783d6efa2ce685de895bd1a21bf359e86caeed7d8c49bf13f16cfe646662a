-- Fix Row Level Security policies for notifications
-- Run this script in your Supabase SQL Editor to fix RLS issues

-- Drop existing restrictive policies
DROP POLICY IF EXISTS "Users can view their own notifications" ON public.notifications;
DROP POLICY IF EXISTS "Users can update their own notifications" ON public.notifications;
DROP POLICY IF EXISTS "System can insert notifications" ON public.notifications;

-- Create more permissive policies that allow system notifications
CREATE POLICY "Users can view their own notifications" ON public.notifications
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can update their own notifications" ON public.notifications
  FOR UPDATE USING (auth.uid() = user_id);

-- Allow system to insert notifications (more permissive policy)
CREATE POLICY "Allow notification creation" ON public.notifications
  FOR INSERT WITH CHECK (true);

-- Allow system to delete notifications if needed
CREATE POLICY "Allow notification deletion" ON public.notifications
  FOR DELETE USING (auth.uid() = user_id OR auth.uid() = created_by);

-- Success message
DO $$
BEGIN
  RAISE NOTICE '✅ Notification RLS policies updated successfully!';
  RAISE NOTICE '📝 System can now create notifications for users';
  RAISE NOTICE '🔒 Users can still only view/update their own notifications';
END $$;
