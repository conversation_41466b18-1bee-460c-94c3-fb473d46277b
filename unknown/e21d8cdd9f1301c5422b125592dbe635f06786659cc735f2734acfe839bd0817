import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { 
  Plus,
  MoreHorizontal,
  User,
  Calendar,
  DollarSign,
  Clock,
  AlertTriangle,
  CheckCircle,
  PlayCircle,
  PauseCircle,
  Target
} from 'lucide-react';

interface Project {
  id: string;
  name: string;
  description: string;
  status: 'Planning' | 'In Progress' | 'On Hold' | 'Completed' | 'Cancelled';
  priority: 'Low' | 'Medium' | 'High' | 'Critical';
  start_date: string;
  end_date: string;
  budget: number;
  spent: number;
  progress: number;
  client_name: string;
  project_manager: string;
  location: string;
}

interface ProjectKanbanProps {
  projects: Project[];
  onProjectUpdate: (projectId: string, updates: Partial<Project>) => void;
  onProjectCreate: () => void;
}

export const ProjectKanban: React.FC<ProjectKanbanProps> = ({
  projects,
  onProjectUpdate,
  onProjectCreate
}) => {
  const [draggedProject, setDraggedProject] = useState<string | null>(null);

  const columns = [
    {
      id: 'Planning',
      title: 'Planning',
      icon: <Target className="w-4 h-4" />,
      color: 'bg-purple-100 border-purple-200',
      headerColor: 'bg-purple-500'
    },
    {
      id: 'In Progress',
      title: 'In Progress',
      icon: <PlayCircle className="w-4 h-4" />,
      color: 'bg-blue-100 border-blue-200',
      headerColor: 'bg-blue-500'
    },
    {
      id: 'On Hold',
      title: 'On Hold',
      icon: <PauseCircle className="w-4 h-4" />,
      color: 'bg-yellow-100 border-yellow-200',
      headerColor: 'bg-yellow-500'
    },
    {
      id: 'Completed',
      title: 'Completed',
      icon: <CheckCircle className="w-4 h-4" />,
      color: 'bg-green-100 border-green-200',
      headerColor: 'bg-green-500'
    }
  ];

  // Get projects for a specific status
  const getProjectsByStatus = (status: string) => {
    return projects.filter(project => project.status === status);
  };

  // Get priority color
  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'Critical':
        return 'bg-red-100 text-red-800 border-red-200';
      case 'High':
        return 'bg-orange-100 text-orange-800 border-orange-200';
      case 'Medium':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'Low':
        return 'bg-green-100 text-green-800 border-green-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  // Format currency
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount);
  };

  // Format date
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric'
    });
  };

  // Calculate days remaining
  const getDaysRemaining = (endDate: string) => {
    const today = new Date();
    const end = new Date(endDate);
    const diffTime = end.getTime() - today.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays;
  };

  // Handle drag start
  const handleDragStart = (e: React.DragEvent, projectId: string) => {
    setDraggedProject(projectId);
    e.dataTransfer.effectAllowed = 'move';
  };

  // Handle drag over
  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    e.dataTransfer.dropEffect = 'move';
  };

  // Handle drop
  const handleDrop = (e: React.DragEvent, newStatus: string) => {
    e.preventDefault();
    
    if (draggedProject && draggedProject !== newStatus) {
      onProjectUpdate(draggedProject, { status: newStatus as Project['status'] });
    }
    
    setDraggedProject(null);
  };

  // Handle drag end
  const handleDragEnd = () => {
    setDraggedProject(null);
  };

  return (
    <div className="h-full">
      <div className="flex space-x-6 h-full overflow-x-auto pb-4">
        {columns.map((column) => {
          const columnProjects = getProjectsByStatus(column.id);
          
          return (
            <div
              key={column.id}
              className="flex-shrink-0 w-80"
              onDragOver={handleDragOver}
              onDrop={(e) => handleDrop(e, column.id)}
            >
              {/* Column Header */}
              <Card className={`${column.color} border-2 mb-4`}>
                <CardHeader className="pb-3">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <div className={`p-1 rounded ${column.headerColor} text-white`}>
                        {column.icon}
                      </div>
                      <CardTitle className="text-sm font-medium">
                        {column.title}
                      </CardTitle>
                      <Badge variant="secondary" className="text-xs">
                        {columnProjects.length}
                      </Badge>
                    </div>
                    {column.id === 'Planning' && (
                      <Button
                        size="sm"
                        variant="ghost"
                        onClick={onProjectCreate}
                        className="h-6 w-6 p-0"
                      >
                        <Plus className="w-4 h-4" />
                      </Button>
                    )}
                  </div>
                </CardHeader>
              </Card>

              {/* Project Cards */}
              <div className="space-y-3 max-h-[calc(100vh-200px)] overflow-y-auto">
                {columnProjects.map((project) => {
                  const daysRemaining = getDaysRemaining(project.end_date);
                  const isOverdue = daysRemaining < 0 && project.status !== 'Completed';
                  const budgetUtilization = (project.spent / project.budget) * 100;
                  
                  return (
                    <Card
                      key={project.id}
                      className={`cursor-move hover:shadow-lg transition-all duration-200 ${
                        draggedProject === project.id ? 'opacity-50 transform rotate-2' : ''
                      } ${isOverdue ? 'border-red-300 bg-red-50' : ''}`}
                      draggable
                      onDragStart={(e) => handleDragStart(e, project.id)}
                      onDragEnd={handleDragEnd}
                    >
                      <CardContent className="p-4">
                        {/* Project Header */}
                        <div className="flex items-start justify-between mb-3">
                          <div className="flex-1">
                            <h3 className="font-medium text-gray-900 text-sm leading-tight mb-1">
                              {project.name}
                            </h3>
                            <p className="text-xs text-gray-600 mb-2">
                              {project.client_name}
                            </p>
                          </div>
                          <Button variant="ghost" size="sm" className="h-6 w-6 p-0">
                            <MoreHorizontal className="w-4 h-4" />
                          </Button>
                        </div>

                        {/* Priority Badge */}
                        <div className="mb-3">
                          <Badge className={`text-xs ${getPriorityColor(project.priority)}`}>
                            {project.priority}
                          </Badge>
                        </div>

                        {/* Progress */}
                        <div className="mb-3">
                          <div className="flex items-center justify-between text-xs mb-1">
                            <span className="text-gray-600">Progress</span>
                            <span className="font-medium">{project.progress}%</span>
                          </div>
                          <Progress value={project.progress} className="h-1.5" />
                        </div>

                        {/* Budget */}
                        <div className="mb-3">
                          <div className="flex items-center justify-between text-xs mb-1">
                            <span className="text-gray-600">Budget</span>
                            <span className={`font-medium ${budgetUtilization > 100 ? 'text-red-600' : ''}`}>
                              {Math.round(budgetUtilization)}%
                            </span>
                          </div>
                          <div className="flex items-center space-x-1 text-xs text-gray-500">
                            <DollarSign className="w-3 h-3" />
                            <span>{formatCurrency(project.spent)} / {formatCurrency(project.budget)}</span>
                          </div>
                        </div>

                        {/* Project Manager */}
                        <div className="flex items-center space-x-1 text-xs text-gray-500 mb-3">
                          <User className="w-3 h-3" />
                          <span>{project.project_manager}</span>
                        </div>

                        {/* Timeline */}
                        <div className="flex items-center justify-between text-xs">
                          <div className="flex items-center space-x-1 text-gray-500">
                            <Calendar className="w-3 h-3" />
                            <span>{formatDate(project.start_date)} - {formatDate(project.end_date)}</span>
                          </div>
                          {project.status !== 'Completed' && (
                            <div className={`flex items-center space-x-1 ${
                              isOverdue ? 'text-red-600' : 
                              daysRemaining <= 7 ? 'text-orange-600' : 'text-gray-500'
                            }`}>
                              <Clock className="w-3 h-3" />
                              <span>
                                {isOverdue 
                                  ? `${Math.abs(daysRemaining)}d overdue`
                                  : `${daysRemaining}d left`
                                }
                              </span>
                            </div>
                          )}
                        </div>

                        {/* Risk Indicators */}
                        {(isOverdue || budgetUtilization > 90) && (
                          <div className="mt-3 pt-3 border-t border-gray-200">
                            <div className="flex items-center space-x-2">
                              <AlertTriangle className="w-4 h-4 text-red-500" />
                              <div className="text-xs text-red-600">
                                {isOverdue && <div>Behind schedule</div>}
                                {budgetUtilization > 90 && <div>Budget risk</div>}
                              </div>
                            </div>
                          </div>
                        )}
                      </CardContent>
                    </Card>
                  );
                })}

                {/* Empty State */}
                {columnProjects.length === 0 && (
                  <div className="text-center py-8 text-gray-400">
                    <div className={`w-12 h-12 mx-auto mb-3 rounded-full ${column.color} flex items-center justify-center`}>
                      {column.icon}
                    </div>
                    <p className="text-sm">No projects in {column.title.toLowerCase()}</p>
                    {column.id === 'Planning' && (
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={onProjectCreate}
                        className="mt-2 text-xs"
                      >
                        <Plus className="w-3 h-3 mr-1" />
                        Add Project
                      </Button>
                    )}
                  </div>
                )}
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
};
