import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Separator } from '@/components/ui/separator';
import { useToast } from '@/components/ui/use-toast';
import { useAuth } from '@/contexts/AuthContext';
import { supabase } from '@/lib/supabase';
import {
  Bell,
  Mail,
  Smartphone,
  MessageSquare,
  Calendar,
  DollarSign,
  Users,
  AlertTriangle,
  CheckCircle,
  Clock,
  Volume2,
  VolumeX
} from 'lucide-react';

interface NotificationPreferences {
  email_notifications: boolean;
  push_notifications: boolean;
  sms_notifications: boolean;
  project_updates: boolean;
  financial_alerts: boolean;
  time_tracking_reminders: boolean;
  team_messages: boolean;
  system_announcements: boolean;
  invoice_notifications: boolean;
  deadline_reminders: boolean;
  notification_frequency: 'immediate' | 'daily' | 'weekly';
  quiet_hours_enabled: boolean;
  quiet_hours_start: string;
  quiet_hours_end: string;
}

const NotificationSettings = () => {
  const { toast } = useToast();
  const { user } = useAuth();
  const [loading, setLoading] = useState(false);
  const [preferences, setPreferences] = useState<NotificationPreferences>({
    email_notifications: true,
    push_notifications: true,
    sms_notifications: false,
    project_updates: true,
    financial_alerts: true,
    time_tracking_reminders: true,
    team_messages: true,
    system_announcements: true,
    invoice_notifications: true,
    deadline_reminders: true,
    notification_frequency: 'immediate',
    quiet_hours_enabled: false,
    quiet_hours_start: '22:00',
    quiet_hours_end: '08:00'
  });

  useEffect(() => {
    loadNotificationPreferences();
  }, [user]);

  const loadNotificationPreferences = async () => {
    if (!user) return;

    try {
      const { data, error } = await supabase
        .from('user_notification_preferences')
        .select('*')
        .eq('user_id', user.id)
        .single();

      if (error && error.code !== 'PGRST116') { // PGRST116 = no rows returned
        console.warn('Notification preferences table not found:', error.message);
        return; // Use default preferences
      }

      if (data) {
        setPreferences(data);
      }
    } catch (error) {
      console.warn('Error loading notification preferences (database may not be set up):', error);
      // Continue with default preferences
    }
  };

  const handlePreferenceChange = async (key: keyof NotificationPreferences, value: boolean | string) => {
    const newPreferences = { ...preferences, [key]: value };
    setPreferences(newPreferences);

    if (!user) {
      toast({
        title: "Preference Updated",
        description: "Notification preference updated locally",
      });
      return;
    }

    // Save to database
    try {
      const { error } = await supabase
        .from('user_notification_preferences')
        .upsert({
          user_id: user.id,
          ...newPreferences,
          updated_at: new Date().toISOString()
        });

      if (error) {
        throw error;
      }

      toast({
        title: "Preference Updated",
        description: "Your notification preference has been saved to database",
      });

    } catch (error: any) {
      console.warn('Error saving notification preference (database may not be set up):', error);

      // Don't show error if it's just a missing table
      if (error?.message?.includes('relation') && error?.message?.includes('does not exist')) {
        toast({
          title: "Preference Updated",
          description: "Preference updated locally. Run the database schema to enable persistence.",
        });
      } else {
        toast({
          title: "Database Error",
          description: "Preference updated locally. Check database connection.",
          variant: "destructive",
        });
      }
    }
  };

  const testNotification = () => {
    toast({
      title: "Test Notification",
      description: "This is how notifications will appear to you",
    });
  };

  return (
    <div className="space-y-6">
      {/* Notification Channels */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Bell className="h-5 w-5" />
            Notification Channels
          </CardTitle>
          <CardDescription>
            Choose how you want to receive notifications
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <div className="flex items-center gap-2">
                <Mail className="h-4 w-4" />
                <Label>Email Notifications</Label>
              </div>
              <p className="text-sm text-gray-500">Receive notifications via email</p>
            </div>
            <Switch 
              checked={preferences.email_notifications}
              onCheckedChange={(checked) => handlePreferenceChange('email_notifications', checked)}
            />
          </div>
          
          <Separator />
          
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <div className="flex items-center gap-2">
                <Smartphone className="h-4 w-4" />
                <Label>Push Notifications</Label>
              </div>
              <p className="text-sm text-gray-500">Receive push notifications in your browser</p>
            </div>
            <Switch 
              checked={preferences.push_notifications}
              onCheckedChange={(checked) => handlePreferenceChange('push_notifications', checked)}
            />
          </div>
          
          <Separator />
          
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <div className="flex items-center gap-2">
                <MessageSquare className="h-4 w-4" />
                <Label>SMS Notifications</Label>
              </div>
              <p className="text-sm text-gray-500">Receive important alerts via SMS</p>
            </div>
            <Switch 
              checked={preferences.sms_notifications}
              onCheckedChange={(checked) => handlePreferenceChange('sms_notifications', checked)}
            />
          </div>
        </CardContent>
      </Card>

      {/* Notification Types */}
      <Card>
        <CardHeader>
          <CardTitle>Notification Types</CardTitle>
          <CardDescription>
            Control which types of notifications you receive
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <div className="flex items-center gap-2">
                <Calendar className="h-4 w-4" />
                <Label>Project Updates</Label>
              </div>
              <p className="text-sm text-gray-500">Updates on project progress and milestones</p>
            </div>
            <Switch 
              checked={preferences.project_updates}
              onCheckedChange={(checked) => handlePreferenceChange('project_updates', checked)}
            />
          </div>
          
          <Separator />
          
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <div className="flex items-center gap-2">
                <DollarSign className="h-4 w-4" />
                <Label>Financial Alerts</Label>
              </div>
              <p className="text-sm text-gray-500">Invoice payments, budget alerts, and financial updates</p>
            </div>
            <Switch 
              checked={preferences.financial_alerts}
              onCheckedChange={(checked) => handlePreferenceChange('financial_alerts', checked)}
            />
          </div>
          
          <Separator />
          
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <div className="flex items-center gap-2">
                <Clock className="h-4 w-4" />
                <Label>Time Tracking Reminders</Label>
              </div>
              <p className="text-sm text-gray-500">Reminders to clock in/out and submit timesheets</p>
            </div>
            <Switch 
              checked={preferences.time_tracking_reminders}
              onCheckedChange={(checked) => handlePreferenceChange('time_tracking_reminders', checked)}
            />
          </div>
          
          <Separator />
          
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <div className="flex items-center gap-2">
                <Users className="h-4 w-4" />
                <Label>Team Messages</Label>
              </div>
              <p className="text-sm text-gray-500">Messages from team members and collaborators</p>
            </div>
            <Switch 
              checked={preferences.team_messages}
              onCheckedChange={(checked) => handlePreferenceChange('team_messages', checked)}
            />
          </div>
          
          <Separator />
          
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <div className="flex items-center gap-2">
                <AlertTriangle className="h-4 w-4" />
                <Label>System Announcements</Label>
              </div>
              <p className="text-sm text-gray-500">Important system updates and maintenance notices</p>
            </div>
            <Switch 
              checked={preferences.system_announcements}
              onCheckedChange={(checked) => handlePreferenceChange('system_announcements', checked)}
            />
          </div>
          
          <Separator />
          
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <div className="flex items-center gap-2">
                <CheckCircle className="h-4 w-4" />
                <Label>Invoice Notifications</Label>
              </div>
              <p className="text-sm text-gray-500">Invoice creation, payment confirmations, and overdue notices</p>
            </div>
            <Switch 
              checked={preferences.invoice_notifications}
              onCheckedChange={(checked) => handlePreferenceChange('invoice_notifications', checked)}
            />
          </div>
          
          <Separator />
          
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <div className="flex items-center gap-2">
                <Calendar className="h-4 w-4" />
                <Label>Deadline Reminders</Label>
              </div>
              <p className="text-sm text-gray-500">Reminders for upcoming project deadlines and tasks</p>
            </div>
            <Switch 
              checked={preferences.deadline_reminders}
              onCheckedChange={(checked) => handlePreferenceChange('deadline_reminders', checked)}
            />
          </div>
        </CardContent>
      </Card>

      {/* Notification Frequency */}
      <Card>
        <CardHeader>
          <CardTitle>Notification Frequency</CardTitle>
          <CardDescription>
            Control how often you receive notifications
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label>Notification Frequency</Label>
            <Select 
              value={preferences.notification_frequency} 
              onValueChange={(value) => handlePreferenceChange('notification_frequency', value)}
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="immediate">Immediate</SelectItem>
                <SelectItem value="daily">Daily Digest</SelectItem>
                <SelectItem value="weekly">Weekly Summary</SelectItem>
              </SelectContent>
            </Select>
            <p className="text-sm text-gray-500">
              Choose how frequently you want to receive non-urgent notifications
            </p>
          </div>
        </CardContent>
      </Card>

      {/* Quiet Hours */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            {preferences.quiet_hours_enabled ? <VolumeX className="h-5 w-5" /> : <Volume2 className="h-5 w-5" />}
            Quiet Hours
          </CardTitle>
          <CardDescription>
            Set times when you don't want to receive notifications
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <Label>Enable Quiet Hours</Label>
            <Switch 
              checked={preferences.quiet_hours_enabled}
              onCheckedChange={(checked) => handlePreferenceChange('quiet_hours_enabled', checked)}
            />
          </div>
          
          {preferences.quiet_hours_enabled && (
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label>Start Time</Label>
                <input
                  type="time"
                  value={preferences.quiet_hours_start}
                  onChange={(e) => handlePreferenceChange('quiet_hours_start', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
              <div className="space-y-2">
                <Label>End Time</Label>
                <input
                  type="time"
                  value={preferences.quiet_hours_end}
                  onChange={(e) => handlePreferenceChange('quiet_hours_end', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Test Notifications */}
      <Card>
        <CardHeader>
          <CardTitle>Test Notifications</CardTitle>
          <CardDescription>
            Send a test notification to verify your settings
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Button onClick={testNotification}>
            <Bell className="h-4 w-4 mr-2" />
            Send Test Notification
          </Button>
        </CardContent>
      </Card>
    </div>
  );
};

export default NotificationSettings;
