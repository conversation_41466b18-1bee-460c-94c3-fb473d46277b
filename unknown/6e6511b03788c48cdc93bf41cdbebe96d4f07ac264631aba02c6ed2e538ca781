-- Fix All Orphaned Profiles - Create Missing Auth Users
-- This script creates Supabase Auth users for profiles that don't have them

-- 1. First, let's see what we're dealing with
DO $$
DECLARE
    profile_record RECORD;
    fix_count INTEGER := 0;
BEGIN
    RAISE NOTICE '';
    RAISE NOTICE '=== FIXING ORPHANED PROFILES ===';
    RAISE NOTICE '';
    
    -- Find all orphaned profiles (profiles without auth users)
    FOR profile_record IN 
        SELECT up.id, up.email, up.first_name, up.last_name, up.user_id
        FROM public.user_profiles up
        LEFT JOIN auth.users au ON up.user_id = au.id
        WHERE au.id IS NULL
        ORDER BY up.created_at
    LOOP
        RAISE NOTICE 'Found orphaned profile: % % (%) - Auth ID: %', 
            profile_record.first_name, 
            profile_record.last_name,
            profile_record.email,
            profile_record.user_id;
        fix_count := fix_count + 1;
    END LOOP;
    
    RAISE NOTICE '';
    RAISE NOTICE 'Total orphaned profiles found: %', fix_count;
    RAISE NOTICE '';
    
    IF fix_count = 0 THEN
        RAISE NOTICE '✅ No orphaned profiles found - all users have auth accounts!';
    ELSE
        RAISE NOTICE '⚠️  Found % orphaned profiles that need auth users created', fix_count;
    END IF;
END $$;

-- 2. Function to create missing auth user for a profile
CREATE OR REPLACE FUNCTION public.create_missing_auth_user(
    profile_email VARCHAR(255),
    temp_password VARCHAR(255) DEFAULT 'TempPass123!'
)
RETURNS jsonb AS $$
DECLARE
    profile_record RECORD;
    auth_exists BOOLEAN := FALSE;
    result jsonb;
BEGIN
    -- Get the profile information
    SELECT id, email, first_name, last_name, user_id, created_at
    INTO profile_record
    FROM public.user_profiles
    WHERE email = profile_email;
    
    IF profile_record.id IS NULL THEN
        RETURN jsonb_build_object(
            'success', false,
            'error', 'Profile not found for email: ' || profile_email
        );
    END IF;
    
    -- Check if auth user already exists
    SELECT EXISTS(
        SELECT 1 FROM auth.users WHERE id = profile_record.user_id
    ) INTO auth_exists;
    
    IF auth_exists THEN
        RETURN jsonb_build_object(
            'success', false,
            'error', 'Auth user already exists for this profile',
            'auth_user_id', profile_record.user_id
        );
    END IF;
    
    -- Create the missing auth user
    BEGIN
        INSERT INTO auth.users (
            id,
            instance_id,
            email,
            encrypted_password,
            email_confirmed_at,
            created_at,
            updated_at,
            raw_user_meta_data,
            raw_app_meta_data,
            is_super_admin,
            role
        ) VALUES (
            profile_record.user_id,
            '00000000-0000-0000-0000-000000000000',  -- Default instance
            profile_record.email,
            crypt(temp_password, gen_salt('bf')),
            NOW(),  -- Auto-confirm email for testing
            profile_record.created_at,
            NOW(),
            jsonb_build_object(
                'first_name', profile_record.first_name,
                'last_name', profile_record.last_name
            ),
            jsonb_build_object('provider', 'email', 'providers', ARRAY['email']),
            false,
            'authenticated'
        );
        
        RETURN jsonb_build_object(
            'success', true,
            'message', 'Auth user created successfully',
            'email', profile_record.email,
            'auth_user_id', profile_record.user_id,
            'temp_password', temp_password,
            'profile_id', profile_record.id
        );
        
    EXCEPTION
        WHEN unique_violation THEN
            RETURN jsonb_build_object(
                'success', false,
                'error', 'Auth user with this ID already exists',
                'auth_user_id', profile_record.user_id
            );
        WHEN OTHERS THEN
            RETURN jsonb_build_object(
                'success', false,
                'error', 'Failed to create auth user: ' || SQLERRM,
                'auth_user_id', profile_record.user_id
            );
    END;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 3. Function to fix ALL orphaned profiles at once
CREATE OR REPLACE FUNCTION public.fix_all_orphaned_profiles(
    temp_password VARCHAR(255) DEFAULT 'TempPass123!'
)
RETURNS jsonb AS $$
DECLARE
    profile_record RECORD;
    fix_result jsonb;
    success_count INTEGER := 0;
    error_count INTEGER := 0;
    results jsonb[] := '{}';
BEGIN
    -- Process each orphaned profile
    FOR profile_record IN 
        SELECT up.email
        FROM public.user_profiles up
        LEFT JOIN auth.users au ON up.user_id = au.id
        WHERE au.id IS NULL
        ORDER BY up.created_at
    LOOP
        -- Try to fix this profile
        SELECT public.create_missing_auth_user(profile_record.email, temp_password)
        INTO fix_result;
        
        -- Add to results
        results := results || fix_result;
        
        -- Count successes and errors
        IF (fix_result->>'success')::boolean THEN
            success_count := success_count + 1;
        ELSE
            error_count := error_count + 1;
        END IF;
    END LOOP;
    
    RETURN jsonb_build_object(
        'success', success_count > 0,
        'total_processed', success_count + error_count,
        'successful_fixes', success_count,
        'errors', error_count,
        'temp_password', temp_password,
        'details', results
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 4. Grant permissions
GRANT EXECUTE ON FUNCTION public.create_missing_auth_user(VARCHAR, VARCHAR) TO authenticated;
GRANT EXECUTE ON FUNCTION public.fix_all_orphaned_profiles(VARCHAR) TO authenticated;

-- 5. Instructions
DO $$
BEGIN
    RAISE NOTICE '';
    RAISE NOTICE '=== READY TO FIX ORPHANED PROFILES ===';
    RAISE NOTICE '';
    RAISE NOTICE 'To fix ALL orphaned profiles at once, run:';
    RAISE NOTICE '  SELECT public.fix_all_orphaned_profiles(''TempPass123!'');';
    RAISE NOTICE '';
    RAISE NOTICE 'To fix a single profile, run:';
    RAISE NOTICE '  SELECT public.create_missing_auth_user(''<EMAIL>'', ''TempPass123!'');';
    RAISE NOTICE '';
    RAISE NOTICE 'After fixing, users can login with:';
    RAISE NOTICE '  - Their email address';
    RAISE NOTICE '  - Password: TempPass123! (or whatever you set)';
    RAISE NOTICE '';
    RAISE NOTICE '⚠️  IMPORTANT: Tell users to change their password after first login!';
    RAISE NOTICE '';
END $$;
