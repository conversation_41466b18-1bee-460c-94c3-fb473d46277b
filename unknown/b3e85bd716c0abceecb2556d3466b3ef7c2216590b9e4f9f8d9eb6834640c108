import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { 
  TrendingUp, 
  TrendingDown, 
  AlertTriangle, 
  CheckCircle, 
  Info, 
  DollarSign,
  Users,
  BarChart3,
  CreditCard,
  X
} from 'lucide-react';
import { BusinessInsight } from '@/lib/analytics';

export interface InsightCardProps {
  insight: BusinessInsight;
  onDismiss?: (id: string) => void;
  onAction?: (id: string) => void;
  className?: string;
}

const InsightCard: React.FC<InsightCardProps> = ({
  insight,
  onDismiss,
  onAction,
  className = ''
}) => {
  const getTypeIcon = () => {
    switch (insight.type) {
      case 'positive':
        return <CheckCircle className="h-5 w-5 text-green-500" />;
      case 'negative':
        return <TrendingDown className="h-5 w-5 text-red-500" />;
      case 'warning':
        return <AlertTriangle className="h-5 w-5 text-yellow-500" />;
      default:
        return <Info className="h-5 w-5 text-blue-500" />;
    }
  };

  const getCategoryIcon = () => {
    switch (insight.category) {
      case 'revenue':
        return <DollarSign className="h-4 w-4" />;
      case 'expenses':
        return <CreditCard className="h-4 w-4" />;
      case 'cashflow':
        return <BarChart3 className="h-4 w-4" />;
      case 'clients':
        return <Users className="h-4 w-4" />;
      default:
        return <Info className="h-4 w-4" />;
    }
  };

  const getTypeColor = () => {
    switch (insight.type) {
      case 'positive':
        return 'border-green-200 bg-green-50';
      case 'negative':
        return 'border-red-200 bg-red-50';
      case 'warning':
        return 'border-yellow-200 bg-yellow-50';
      default:
        return 'border-blue-200 bg-blue-50';
    }
  };

  const getPriorityBadge = () => {
    const variants = {
      high: 'destructive',
      medium: 'default',
      low: 'secondary'
    } as const;

    return (
      <Badge variant={variants[insight.priority]} className="text-xs">
        {insight.priority.toUpperCase()}
      </Badge>
    );
  };

  const formatValue = (value: number) => {
    if (insight.category === 'revenue' || insight.category === 'expenses' || insight.category === 'cashflow') {
      return `$${Math.abs(value).toLocaleString()}`;
    }
    return value.toLocaleString();
  };

  return (
    <Card className={`${getTypeColor()} ${className}`}>
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="flex items-center space-x-3">
            {getTypeIcon()}
            <div className="flex-1">
              <div className="flex items-center space-x-2 mb-1">
                <CardTitle className="text-sm font-semibold">{insight.title}</CardTitle>
                {getPriorityBadge()}
              </div>
              <div className="flex items-center space-x-1 text-xs text-muted-foreground">
                {getCategoryIcon()}
                <span className="capitalize">{insight.category}</span>
              </div>
            </div>
          </div>
          {onDismiss && (
            <Button
              variant="ghost"
              size="sm"
              onClick={() => onDismiss(insight.id)}
              className="h-6 w-6 p-0 hover:bg-white/50"
            >
              <X className="h-3 w-3" />
            </Button>
          )}
        </div>
      </CardHeader>
      <CardContent className="pt-0">
        <p className="text-sm text-gray-700 mb-3">
          {insight.description}
        </p>
        
        {insight.value !== undefined && (
          <div className="flex items-center space-x-2 mb-3">
            <span className="text-xs text-muted-foreground">Value:</span>
            <span className="text-sm font-medium">
              {formatValue(insight.value)}
            </span>
            {insight.change !== undefined && (
              <span className={`text-xs flex items-center space-x-1 ${
                insight.change > 0 ? 'text-green-600' : insight.change < 0 ? 'text-red-600' : 'text-gray-600'
              }`}>
                {insight.change > 0 ? (
                  <TrendingUp className="h-3 w-3" />
                ) : insight.change < 0 ? (
                  <TrendingDown className="h-3 w-3" />
                ) : null}
                <span>{insight.change > 0 ? '+' : ''}{insight.change.toFixed(1)}%</span>
              </span>
            )}
          </div>
        )}

        {insight.recommendation && (
          <div className="bg-white/50 rounded-md p-3 mb-3">
            <p className="text-xs font-medium text-gray-800 mb-1">Recommendation:</p>
            <p className="text-xs text-gray-700">{insight.recommendation}</p>
          </div>
        )}

        {onAction && (
          <Button
            variant="outline"
            size="sm"
            onClick={() => onAction(insight.id)}
            className="w-full bg-white/50 hover:bg-white/80"
          >
            Take Action
          </Button>
        )}
      </CardContent>
    </Card>
  );
};

export default InsightCard;
