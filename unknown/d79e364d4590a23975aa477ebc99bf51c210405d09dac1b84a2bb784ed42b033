// Test utility for validating client access control
// This can be used in development to test the access control system

import { ProjectService } from '@/lib/projects';
import { supabase } from '@/lib/supabase';

export interface AccessTestResult {
  userId: string;
  userEmail: string;
  userRole: string;
  totalProjects: number;
  accessibleProjects: number;
  projectNames: string[];
  hasCorrectAccess: boolean;
  details: string;
}

export class ClientAccessTester {
  /**
   * Test access control for a specific user
   */
  static async testUserAccess(userId: string): Promise<AccessTestResult> {
    try {
      // Get user info
      const { data: userProfile } = await supabase
        .from('user_profiles')
        .select('email, role_name, full_name')
        .eq('user_id', userId)
        .single();

      if (!userProfile) {
        throw new Error('User not found');
      }

      // Get all projects (admin view)
      const allProjects = await ProjectService.getAllProjects();
      
      // Get user's accessible projects
      const accessibleProjects = await ProjectService.getProjects(userId);

      // Determine if access is correct based on role
      let hasCorrectAccess = false;
      let details = '';

      if (userProfile.role_name === 'client') {
        // Client should only see their projects
        if (accessibleProjects.length < allProjects.length) {
          hasCorrectAccess = true;
          details = `Client correctly sees ${accessibleProjects.length} of ${allProjects.length} total projects`;
        } else if (accessibleProjects.length === allProjects.length) {
          hasCorrectAccess = false;
          details = `ERROR: Client sees ALL projects (should be restricted)`;
        } else {
          hasCorrectAccess = false;
          details = `ERROR: Unexpected project count`;
        }
      } else if (['admin', 'management', 'qs', 'accountant'].includes(userProfile.role_name)) {
        // Non-client roles should see all projects
        if (accessibleProjects.length === allProjects.length) {
          hasCorrectAccess = true;
          details = `${userProfile.role_name} correctly sees all ${allProjects.length} projects`;
        } else {
          hasCorrectAccess = false;
          details = `ERROR: ${userProfile.role_name} should see all projects but only sees ${accessibleProjects.length}`;
        }
      }

      return {
        userId,
        userEmail: userProfile.email,
        userRole: userProfile.role_name,
        totalProjects: allProjects.length,
        accessibleProjects: accessibleProjects.length,
        projectNames: accessibleProjects.map(p => p.name),
        hasCorrectAccess,
        details
      };
    } catch (error) {
      console.error('Error testing user access:', error);
      throw error;
    }
  }

  /**
   * Test access control for all users
   */
  static async testAllUsersAccess(): Promise<AccessTestResult[]> {
    try {
      // Get all users
      const { data: users } = await supabase
        .from('user_profiles')
        .select('user_id, email, role_name')
        .order('role_name', { ascending: true });

      if (!users) {
        return [];
      }

      const results: AccessTestResult[] = [];

      for (const user of users) {
        try {
          const result = await this.testUserAccess(user.user_id);
          results.push(result);
        } catch (error) {
          console.error(`Error testing access for user ${user.email}:`, error);
          results.push({
            userId: user.user_id,
            userEmail: user.email,
            userRole: user.role_name,
            totalProjects: 0,
            accessibleProjects: 0,
            projectNames: [],
            hasCorrectAccess: false,
            details: `Error: ${error.message}`
          });
        }
      }

      return results;
    } catch (error) {
      console.error('Error testing all users access:', error);
      throw error;
    }
  }

  /**
   * Test client-user relationships
   */
  static async testClientUserRelationships(): Promise<any[]> {
    try {
      const { data: relationships } = await supabase
        .from('client_users')
        .select(`
          *,
          client:clients(name, company_name),
          user_profile:user_profiles(email, full_name, role_name)
        `);

      return relationships || [];
    } catch (error) {
      console.error('Error testing client-user relationships:', error);
      throw error;
    }
  }

  /**
   * Generate a comprehensive access control report
   */
  static async generateAccessReport(): Promise<{
    summary: {
      totalUsers: number;
      clientUsers: number;
      nonClientUsers: number;
      usersWithCorrectAccess: number;
      usersWithIncorrectAccess: number;
    };
    userResults: AccessTestResult[];
    relationships: any[];
    recommendations: string[];
  }> {
    try {
      const userResults = await this.testAllUsersAccess();
      const relationships = await this.testClientUserRelationships();

      const clientUsers = userResults.filter(r => r.userRole === 'client');
      const nonClientUsers = userResults.filter(r => r.userRole !== 'client');
      const usersWithCorrectAccess = userResults.filter(r => r.hasCorrectAccess);
      const usersWithIncorrectAccess = userResults.filter(r => !r.hasCorrectAccess);

      const recommendations: string[] = [];

      // Generate recommendations
      if (usersWithIncorrectAccess.length > 0) {
        recommendations.push('Some users have incorrect access permissions');
        
        const clientsWithFullAccess = usersWithIncorrectAccess.filter(r => 
          r.userRole === 'client' && r.accessibleProjects === r.totalProjects
        );
        
        if (clientsWithFullAccess.length > 0) {
          recommendations.push(`${clientsWithFullAccess.length} client users can see all projects - they need to be linked to their clients`);
        }
      }

      if (relationships.length === 0 && clientUsers.length > 0) {
        recommendations.push('No client-user relationships found - use the Admin panel to link client users to their clients');
      }

      if (recommendations.length === 0) {
        recommendations.push('Access control is working correctly!');
      }

      return {
        summary: {
          totalUsers: userResults.length,
          clientUsers: clientUsers.length,
          nonClientUsers: nonClientUsers.length,
          usersWithCorrectAccess: usersWithCorrectAccess.length,
          usersWithIncorrectAccess: usersWithIncorrectAccess.length
        },
        userResults,
        relationships,
        recommendations
      };
    } catch (error) {
      console.error('Error generating access report:', error);
      throw error;
    }
  }

  /**
   * Console log a formatted access report
   */
  static async logAccessReport(): Promise<void> {
    try {
      const report = await this.generateAccessReport();
      
      console.log('\n🔐 CLIENT ACCESS CONTROL REPORT');
      console.log('================================');
      
      console.log('\n📊 Summary:');
      console.log(`  Total Users: ${report.summary.totalUsers}`);
      console.log(`  Client Users: ${report.summary.clientUsers}`);
      console.log(`  Non-Client Users: ${report.summary.nonClientUsers}`);
      console.log(`  Users with Correct Access: ${report.summary.usersWithCorrectAccess}`);
      console.log(`  Users with Incorrect Access: ${report.summary.usersWithIncorrectAccess}`);
      
      console.log('\n👥 User Access Details:');
      report.userResults.forEach(result => {
        const status = result.hasCorrectAccess ? '✅' : '❌';
        console.log(`  ${status} ${result.userEmail} (${result.userRole}): ${result.details}`);
      });
      
      console.log('\n🔗 Client-User Relationships:');
      if (report.relationships.length === 0) {
        console.log('  No relationships found');
      } else {
        report.relationships.forEach(rel => {
          console.log(`  ${rel.client?.name} ← ${rel.user_profile?.email} (${rel.role})`);
        });
      }
      
      console.log('\n💡 Recommendations:');
      report.recommendations.forEach(rec => {
        console.log(`  • ${rec}`);
      });
      
      console.log('\n');
    } catch (error) {
      console.error('Error logging access report:', error);
    }
  }
}

// Export for easy testing in browser console
if (typeof window !== 'undefined') {
  (window as any).testClientAccess = ClientAccessTester;
}
