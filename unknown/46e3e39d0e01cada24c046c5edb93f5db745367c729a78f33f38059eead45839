import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { useToast } from '@/components/ui/use-toast';
import { TimeTrackingService, TimeEntry } from '@/lib/timeTrackingService';
import {
  Calendar,
  Clock,
  MapPin,
  RefreshCw,
  Download,
  PlayCircle,
  StopCircle,
  PauseCircle,
  Building,
  FileText,
  TrendingUp
} from 'lucide-react';

interface TimeEntryHistoryProps {
  userId?: string;
}

const TimeEntryHistory: React.FC<TimeEntryHistoryProps> = ({ userId }) => {
  const { toast } = useToast();
  const [loading, setLoading] = useState(false);
  const [timeEntries, setTimeEntries] = useState<TimeEntry[]>([]);
  const [startDate, setStartDate] = useState<string>(
    new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0]
  );
  const [endDate, setEndDate] = useState<string>(
    new Date().toISOString().split('T')[0]
  );

  useEffect(() => {
    if (userId) {
      loadTimeEntries();
    }
  }, [userId, startDate, endDate]);

  const loadTimeEntries = async () => {
    if (!userId) return;
    
    setLoading(true);
    try {
      const entries = await TimeTrackingService.getUserTimeEntries(
        userId,
        startDate,
        endDate + 'T23:59:59'
      );
      setTimeEntries(entries);
    } catch (error) {
      console.error('Error loading time entries:', error);
      toast({
        title: "Error",
        description: "Failed to load time entry history",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'active':
        return (
          <Badge className="bg-green-100 text-green-800">
            <PlayCircle className="w-3 h-3 mr-1" />
            Active
          </Badge>
        );
      case 'break':
        return (
          <Badge className="bg-yellow-100 text-yellow-800">
            <PauseCircle className="w-3 h-3 mr-1" />
            On Break
          </Badge>
        );
      case 'completed':
        return (
          <Badge className="bg-gray-100 text-gray-800">
            <StopCircle className="w-3 h-3 mr-1" />
            Completed
          </Badge>
        );
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  const formatTime = (timeString: string) => {
    return new Date(timeString).toLocaleString('en-US', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const formatDuration = (hours: number) => {
    const h = Math.floor(hours);
    const m = Math.round((hours - h) * 60);
    return `${h}h ${m}m`;
  };

  const exportHistory = () => {
    const csvContent = [
      ['Date', 'Site', 'Clock In', 'Clock Out', 'Total Hours', 'Overtime', 'Status', 'Description'].join(','),
      ...timeEntries.map(entry => [
        new Date(entry.clock_in_time).toLocaleDateString(),
        entry.site?.name || 'Unknown',
        formatTime(entry.clock_in_time),
        entry.clock_out_time ? formatTime(entry.clock_out_time) : 'Still working',
        entry.total_hours ? formatDuration(entry.total_hours) : 'N/A',
        entry.overtime_hours ? formatDuration(entry.overtime_hours) : '0h 0m',
        entry.status,
        entry.work_description || ''
      ].join(','))
    ].join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `time-entries-${startDate}-to-${endDate}.csv`;
    a.click();
    window.URL.revokeObjectURL(url);
  };

  // Calculate summary statistics
  const totalHours = timeEntries.reduce((sum, entry) => sum + (entry.total_hours || 0), 0);
  const totalOvertimeHours = timeEntries.reduce((sum, entry) => sum + (entry.overtime_hours || 0), 0);
  const completedEntries = timeEntries.filter(entry => entry.status === 'completed').length;
  const averageHoursPerDay = completedEntries > 0 ? totalHours / completedEntries : 0;

  return (
    <div className="space-y-6">
      {/* Filters and Summary */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Calendar className="h-5 w-5" />
            Time Entry History
          </CardTitle>
          <CardDescription>
            View your work time records and statistics
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col sm:flex-row gap-4 items-end mb-6">
            <div className="flex-1">
              <Label htmlFor="start-date">Start Date</Label>
              <Input
                id="start-date"
                type="date"
                value={startDate}
                onChange={(e) => setStartDate(e.target.value)}
              />
            </div>
            
            <div className="flex-1">
              <Label htmlFor="end-date">End Date</Label>
              <Input
                id="end-date"
                type="date"
                value={endDate}
                onChange={(e) => setEndDate(e.target.value)}
              />
            </div>

            <div className="flex gap-2">
              <Button onClick={loadTimeEntries} disabled={loading}>
                {loading ? (
                  <RefreshCw className="h-4 w-4 animate-spin" />
                ) : (
                  <RefreshCw className="h-4 w-4" />
                )}
              </Button>
              <Button variant="outline" onClick={exportHistory}>
                <Download className="h-4 w-4 mr-2" />
                Export
              </Button>
            </div>
          </div>

          {/* Summary Statistics */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center p-4 bg-blue-50 rounded-lg">
              <div className="text-2xl font-bold text-blue-600">{formatDuration(totalHours)}</div>
              <div className="text-sm text-blue-800">Total Hours</div>
            </div>
            <div className="text-center p-4 bg-orange-50 rounded-lg">
              <div className="text-2xl font-bold text-orange-600">{formatDuration(totalOvertimeHours)}</div>
              <div className="text-sm text-orange-800">Overtime</div>
            </div>
            <div className="text-center p-4 bg-green-50 rounded-lg">
              <div className="text-2xl font-bold text-green-600">{completedEntries}</div>
              <div className="text-sm text-green-800">Work Days</div>
            </div>
            <div className="text-center p-4 bg-purple-50 rounded-lg">
              <div className="text-2xl font-bold text-purple-600">{formatDuration(averageHoursPerDay)}</div>
              <div className="text-sm text-purple-800">Avg/Day</div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Time Entries List */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Clock className="h-5 w-5" />
            Time Entries
            <Badge variant="outline">{timeEntries.length} entries</Badge>
          </CardTitle>
        </CardHeader>
        <CardContent>
          {timeEntries.length === 0 ? (
            <div className="text-center py-12">
              <Calendar className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-gray-600 mb-2">No Time Entries</h3>
              <p className="text-gray-500">
                No time entries found for the selected date range.
              </p>
            </div>
          ) : (
            <div className="space-y-3">
              {timeEntries.map((entry) => (
                <div
                  key={entry.id}
                  className="flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50"
                >
                  <div className="flex items-center gap-4">
                    <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                      <Building className="h-6 w-6 text-blue-600" />
                    </div>
                    <div>
                      <div className="font-medium">{entry.site?.name || 'Unknown Site'}</div>
                      <div className="text-sm text-gray-500">
                        {new Date(entry.clock_in_time).toLocaleDateString('en-US', {
                          weekday: 'short',
                          month: 'short',
                          day: 'numeric',
                          year: 'numeric'
                        })}
                      </div>
                      {entry.work_description && (
                        <div className="text-sm text-gray-600 mt-1 flex items-center gap-1">
                          <FileText className="h-3 w-3" />
                          {entry.work_description}
                        </div>
                      )}
                    </div>
                  </div>
                  
                  <div className="flex items-center gap-6 text-sm">
                    <div className="text-center">
                      <div className="text-gray-500">Clock In</div>
                      <div className="font-medium">
                        {new Date(entry.clock_in_time).toLocaleTimeString('en-US', {
                          hour: '2-digit',
                          minute: '2-digit'
                        })}
                      </div>
                    </div>
                    
                    <div className="text-center">
                      <div className="text-gray-500">Clock Out</div>
                      <div className="font-medium">
                        {entry.clock_out_time 
                          ? new Date(entry.clock_out_time).toLocaleTimeString('en-US', {
                              hour: '2-digit',
                              minute: '2-digit'
                            })
                          : '-'
                        }
                      </div>
                    </div>
                    
                    <div className="text-center">
                      <div className="text-gray-500">Total</div>
                      <div className="font-medium">
                        {entry.total_hours ? formatDuration(entry.total_hours) : '-'}
                      </div>
                    </div>
                    
                    {entry.overtime_hours > 0 && (
                      <div className="text-center">
                        <div className="text-gray-500">Overtime</div>
                        <div className="font-medium text-orange-600 flex items-center gap-1">
                          <TrendingUp className="h-3 w-3" />
                          {formatDuration(entry.overtime_hours)}
                        </div>
                      </div>
                    )}
                    
                    <div>
                      {getStatusBadge(entry.status)}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default TimeEntryHistory;
