-- Client-Project Access Control System
-- This script creates proper relationships between clients, users, and projects
-- Run this in your Supabase SQL Editor

-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- 1. Add client_id to projects table for proper foreign key relationship
DO $$
BEGIN
  -- Add client_id field to projects table
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'projects' AND column_name = 'client_id') THEN
    ALTER TABLE public.projects ADD COLUMN client_id UUID REFERENCES public.clients(id);
    RAISE NOTICE '✓ Added client_id to projects table';
  ELSE
    RAISE NOTICE '- client_id already exists in projects table';
  END IF;
END $$;

-- 2. Create client_users junction table to link users to clients
CREATE TABLE IF NOT EXISTS public.client_users (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  client_id UUID NOT NULL REFERENCES public.clients(id) ON DELETE CASCADE,
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  role VARCHAR(50) DEFAULT 'client' CHECK (role IN ('client', 'contact', 'admin')),
  is_primary BOOLEAN DEFAULT FALSE,
  access_level VARCHAR(20) DEFAULT 'read' CHECK (access_level IN ('read', 'write', 'admin')),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  created_by UUID REFERENCES auth.users(id),
  
  -- Ensure unique user-client combinations
  UNIQUE(client_id, user_id)
);

-- 3. Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_projects_client_id ON public.projects(client_id);
CREATE INDEX IF NOT EXISTS idx_client_users_client_id ON public.client_users(client_id);
CREATE INDEX IF NOT EXISTS idx_client_users_user_id ON public.client_users(user_id);
CREATE INDEX IF NOT EXISTS idx_client_users_role ON public.client_users(role);

-- 4. Enable Row Level Security on client_users
ALTER TABLE public.client_users ENABLE ROW LEVEL SECURITY;

-- 5. Create RLS policies for client_users table
CREATE POLICY "Users can view their own client relationships" ON public.client_users
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Admins can view all client relationships" ON public.client_users
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM public.user_profiles 
      WHERE user_id = auth.uid() 
      AND role_name IN ('admin', 'management')
    )
  );

CREATE POLICY "Admins can manage client relationships" ON public.client_users
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM public.user_profiles 
      WHERE user_id = auth.uid() 
      AND role_name IN ('admin', 'management')
    )
  );

-- 6. Update existing projects to link with clients based on client_name
DO $$
DECLARE
  project_record RECORD;
  client_record RECORD;
  updated_count INTEGER := 0;
BEGIN
  -- Loop through projects that don't have client_id set
  FOR project_record IN 
    SELECT id, client_name 
    FROM public.projects 
    WHERE client_id IS NULL AND client_name IS NOT NULL
  LOOP
    -- Find matching client by name or company_name
    SELECT id INTO client_record
    FROM public.clients 
    WHERE LOWER(name) = LOWER(project_record.client_name) 
       OR LOWER(company_name) = LOWER(project_record.client_name)
    LIMIT 1;
    
    -- Update project with client_id if match found
    IF client_record.id IS NOT NULL THEN
      UPDATE public.projects 
      SET client_id = client_record.id 
      WHERE id = project_record.id;
      
      updated_count := updated_count + 1;
    END IF;
  END LOOP;
  
  RAISE NOTICE '✓ Updated % projects with client_id relationships', updated_count;
END $$;

-- 7. Create function to get user's accessible projects based on role
CREATE OR REPLACE FUNCTION get_user_accessible_projects(target_user_id UUID DEFAULT NULL)
RETURNS TABLE (
  id UUID,
  name VARCHAR(255),
  description VARCHAR(1000),
  status VARCHAR(50),
  priority VARCHAR(20),
  start_date VARCHAR(50),
  end_date VARCHAR(50),
  budget NUMERIC,
  spent NUMERIC,
  progress INTEGER,
  client_name VARCHAR(255),
  project_manager VARCHAR(255),
  location VARCHAR(255),
  date_added VARCHAR(50),
  last_modified VARCHAR(50),
  created_at TIMESTAMP WITH TIME ZONE,
  created_by_user_id UUID,
  created_by_name VARCHAR(255),
  created_by_avatar VARCHAR(500),
  client_id UUID
) AS $$
DECLARE
  user_id_to_check UUID;
  user_role TEXT;
BEGIN
  -- Use provided user_id or current authenticated user
  user_id_to_check := COALESCE(target_user_id, auth.uid());
  
  -- Get user role
  SELECT role_name INTO user_role
  FROM public.user_profiles 
  WHERE user_id = user_id_to_check;
  
  -- If user is admin, management, QS, or accountant, return all projects
  IF user_role IN ('admin', 'management', 'qs', 'accountant') THEN
    RETURN QUERY
    SELECT p.id, p.name, p.description, p.status, p.priority, p.start_date, p.end_date,
           p.budget, p.spent, p.progress, p.client_name, p.project_manager, p.location,
           p.date_added, p.last_modified, p.created_at, p.created_by_user_id,
           p.created_by_name, p.created_by_avatar, p.client_id
    FROM public.projects p
    ORDER BY p.date_added DESC;
  
  -- If user is client, return only their projects
  ELSIF user_role = 'client' THEN
    RETURN QUERY
    SELECT p.id, p.name, p.description, p.status, p.priority, p.start_date, p.end_date,
           p.budget, p.spent, p.progress, p.client_name, p.project_manager, p.location,
           p.date_added, p.last_modified, p.created_at, p.created_by_user_id,
           p.created_by_name, p.created_by_avatar, p.client_id
    FROM public.projects p
    INNER JOIN public.client_users cu ON p.client_id = cu.client_id
    WHERE cu.user_id = user_id_to_check
    ORDER BY p.date_added DESC;
  
  -- Default: return empty result for unknown roles
  ELSE
    RETURN;
  END IF;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 8. Create function to check if user can access specific project
CREATE OR REPLACE FUNCTION can_user_access_project(project_id UUID, target_user_id UUID DEFAULT NULL)
RETURNS BOOLEAN AS $$
DECLARE
  user_id_to_check UUID;
  user_role TEXT;
  project_client_id UUID;
  has_access BOOLEAN := FALSE;
BEGIN
  -- Use provided user_id or current authenticated user
  user_id_to_check := COALESCE(target_user_id, auth.uid());
  
  -- Get user role
  SELECT role_name INTO user_role
  FROM public.user_profiles 
  WHERE user_id = user_id_to_check;
  
  -- If user is admin, management, QS, or accountant, they can access all projects
  IF user_role IN ('admin', 'management', 'qs', 'accountant') THEN
    RETURN TRUE;
  END IF;
  
  -- If user is client, check if they're linked to the project's client
  IF user_role = 'client' THEN
    -- Get the project's client_id
    SELECT client_id INTO project_client_id
    FROM public.projects 
    WHERE id = project_id;
    
    -- Check if user is linked to this client
    SELECT EXISTS(
      SELECT 1 FROM public.client_users 
      WHERE client_id = project_client_id AND user_id = user_id_to_check
    ) INTO has_access;
    
    RETURN has_access;
  END IF;
  
  -- Default: no access for unknown roles
  RETURN FALSE;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 9. Create function to link user to client (for admin use)
CREATE OR REPLACE FUNCTION link_user_to_client(
  target_user_id UUID,
  target_client_id UUID,
  user_role TEXT DEFAULT 'client',
  access_level TEXT DEFAULT 'read',
  is_primary BOOLEAN DEFAULT FALSE
)
RETURNS BOOLEAN AS $$
BEGIN
  -- Only allow admins to create these links
  IF NOT EXISTS (
    SELECT 1 FROM public.user_profiles 
    WHERE user_id = auth.uid() 
    AND role_name IN ('admin', 'management')
  ) THEN
    RAISE EXCEPTION 'Only admins can link users to clients';
  END IF;
  
  -- Insert the relationship
  INSERT INTO public.client_users (
    client_id, user_id, role, access_level, is_primary, created_by
  ) VALUES (
    target_client_id, target_user_id, user_role, access_level, is_primary, auth.uid()
  ) ON CONFLICT (client_id, user_id) DO UPDATE SET
    role = EXCLUDED.role,
    access_level = EXCLUDED.access_level,
    is_primary = EXCLUDED.is_primary,
    updated_at = NOW();
  
  RETURN TRUE;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Success message
DO $$
BEGIN
  RAISE NOTICE '';
  RAISE NOTICE '✅ Client-Project Access Control System created successfully!';
  RAISE NOTICE '';
  RAISE NOTICE '📋 Tables updated:';
  RAISE NOTICE '  - projects: Added client_id foreign key';
  RAISE NOTICE '  - client_users: New junction table for user-client relationships';
  RAISE NOTICE '';
  RAISE NOTICE '🔧 Functions created:';
  RAISE NOTICE '  - get_user_accessible_projects(): Returns projects based on user role';
  RAISE NOTICE '  - can_user_access_project(): Checks project access permissions';
  RAISE NOTICE '  - link_user_to_client(): Links users to clients (admin only)';
  RAISE NOTICE '';
  RAISE NOTICE '🛡️ Security:';
  RAISE NOTICE '  - RLS policies enabled for client_users table';
  RAISE NOTICE '  - Role-based access control implemented';
  RAISE NOTICE '';
  RAISE NOTICE '🚀 Next steps:';
  RAISE NOTICE '1. Link client users to their respective clients using link_user_to_client()';
  RAISE NOTICE '2. Update frontend to use get_user_accessible_projects()';
  RAISE NOTICE '3. Test with different user roles';
END $$;
