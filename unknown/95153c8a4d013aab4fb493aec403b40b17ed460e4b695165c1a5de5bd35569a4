-- Simple fix for client access control without complex return types
-- This approach uses a simpler method that avoids type mismatch issues

-- Drop the problematic function
DROP FUNCTION IF EXISTS get_user_accessible_projects(UUID);

-- Create a simple function that just returns project IDs that a user can access
CREATE OR REPLACE FUNCTION get_user_accessible_project_ids(target_user_id UUID DEFAULT NULL)
RETURNS TABLE (project_id UUID) AS $$
DECLARE
  user_id_to_check UUID;
  user_role TEXT;
BEGIN
  -- Use provided user_id or current authenticated user
  user_id_to_check := COALESCE(target_user_id, auth.uid());
  
  -- Get user role
  SELECT role_name INTO user_role
  FROM public.user_profiles 
  WHERE user_id = user_id_to_check;
  
  -- If user is admin, management, QS, or accountant, return all project IDs
  IF user_role IN ('admin', 'management', 'qs', 'accountant') THEN
    RETURN QUERY
    SELECT p.id
    FROM public.projects p;
  
  -- If user is client, return only their project IDs
  ELSIF user_role = 'client' THEN
    RETURN QUERY
    SELECT p.id
    FROM public.projects p
    INNER JOIN public.client_users cu ON p.client_id = cu.client_id
    WHERE cu.user_id = user_id_to_check;
  
  -- Default: return empty result for unknown roles
  ELSE
    RETURN;
  END IF;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create a view for accessible projects that can be used with regular queries
CREATE OR REPLACE VIEW user_accessible_projects AS
SELECT 
  p.*,
  CASE 
    WHEN EXISTS (
      SELECT 1 FROM public.user_profiles up 
      WHERE up.user_id = auth.uid() 
      AND up.role_name IN ('admin', 'management', 'qs', 'accountant')
    ) THEN true
    WHEN EXISTS (
      SELECT 1 FROM public.client_users cu 
      WHERE cu.client_id = p.client_id 
      AND cu.user_id = auth.uid()
    ) THEN true
    ELSE false
  END as user_can_access
FROM public.projects p;

-- Enable RLS on projects table if not already enabled
ALTER TABLE public.projects ENABLE ROW LEVEL SECURITY;

-- Create RLS policy for projects based on user role
DROP POLICY IF EXISTS "Users can access projects based on role" ON public.projects;
CREATE POLICY "Users can access projects based on role" ON public.projects
  FOR SELECT USING (
    -- Admin, management, QS, accountant can see all projects
    EXISTS (
      SELECT 1 FROM public.user_profiles 
      WHERE user_id = auth.uid() 
      AND role_name IN ('admin', 'management', 'qs', 'accountant')
    )
    OR
    -- Client users can only see projects linked to their client
    EXISTS (
      SELECT 1 FROM public.client_users cu
      WHERE cu.client_id = projects.client_id
      AND cu.user_id = auth.uid()
    )
  );

-- Test the new approach
DO $$
DECLARE
  test_count INTEGER;
BEGIN
  -- Test the function
  SELECT COUNT(*) INTO test_count
  FROM get_user_accessible_project_ids();
  
  RAISE NOTICE '✅ Function test successful! Found % accessible project IDs', test_count;
  
  -- Test the view
  SELECT COUNT(*) INTO test_count
  FROM user_accessible_projects
  WHERE user_can_access = true;
  
  RAISE NOTICE '✅ View test successful! Found % accessible projects in view', test_count;
  
EXCEPTION WHEN OTHERS THEN
  RAISE NOTICE '❌ Test failed: %', SQLERRM;
END $$;

-- Show the new functions and views
SELECT 
  schemaname,
  viewname,
  definition
FROM pg_views 
WHERE viewname = 'user_accessible_projects';

RAISE NOTICE '';
RAISE NOTICE '🎉 Simple access control setup complete!';
RAISE NOTICE '';
RAISE NOTICE '📋 Available options:';
RAISE NOTICE '1. Use RLS: SELECT * FROM projects; (automatically filtered)';
RAISE NOTICE '2. Use view: SELECT * FROM user_accessible_projects WHERE user_can_access = true;';
RAISE NOTICE '3. Use function: SELECT * FROM projects WHERE id IN (SELECT project_id FROM get_user_accessible_project_ids());';
RAISE NOTICE '';
RAISE NOTICE '💡 Recommendation: Update frontend to use option 1 (RLS) for simplest implementation';
