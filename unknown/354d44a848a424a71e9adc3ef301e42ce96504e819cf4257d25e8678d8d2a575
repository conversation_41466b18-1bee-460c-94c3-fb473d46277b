import { supabase } from './supabase';

export const initializeSettings = async () => {
  try {
    console.log('🔍 Checking settings database setup...');
    
    // Check if notification preferences table exists
    const { data: notificationPrefs, error: notificationError } = await supabase
      .from('user_notification_preferences')
      .select('count')
      .limit(1);
    
    if (notificationError) {
      console.error('❌ Notification preferences table not found:', notificationError.message);
      return {
        success: false,
        error: 'Settings tables not found. Please run the settings SQL schema first.',
        details: notificationError
      };
    }
    
    // Check if system config table exists
    const { data: systemConfig, error: systemConfigError } = await supabase
      .from('system_config')
      .select('count')
      .limit(1);
    
    if (systemConfigError) {
      console.error('❌ System config table not found:', systemConfigError.message);
      return {
        success: false,
        error: 'System config table not found. Please run the settings SQL schema first.',
        details: systemConfigError
      };
    }
    
    console.log('✅ Settings database setup verified');
    return {
      success: true,
      message: 'Settings system is ready',
      tablesFound: true
    };
    
  } catch (error) {
    console.error('❌ Error checking settings setup:', error);
    return {
      success: false,
      error: 'Failed to verify settings setup',
      details: error
    };
  }
};

// Helper function to create default notification preferences for a user
export const createDefaultNotificationPreferences = async (userId: string) => {
  try {
    const { data, error } = await supabase
      .from('user_notification_preferences')
      .upsert({
        user_id: userId,
        email_notifications: true,
        push_notifications: true,
        sms_notifications: false,
        project_updates: true,
        financial_alerts: true,
        time_tracking_reminders: true,
        team_messages: true,
        system_announcements: true,
        invoice_notifications: true,
        deadline_reminders: true,
        notification_frequency: 'immediate',
        quiet_hours_enabled: false,
        quiet_hours_start: '22:00',
        quiet_hours_end: '08:00',
        two_factor_enabled: false
      })
      .select()
      .single();

    if (error) {
      throw error;
    }

    console.log('✅ Default notification preferences created for user:', userId);
    return {
      success: true,
      message: 'Default notification preferences created',
      data
    };

  } catch (error) {
    console.error('❌ Error creating default notification preferences:', error);
    return {
      success: false,
      error: 'Failed to create default notification preferences',
      details: error
    };
  }
};

// Helper function to get system configuration
export const getSystemConfiguration = async () => {
  try {
    const { data, error } = await supabase
      .from('system_config')
      .select('*')
      .eq('id', 1)
      .single();

    if (error) {
      throw error;
    }

    return {
      success: true,
      data
    };

  } catch (error) {
    console.error('❌ Error getting system configuration:', error);
    return {
      success: false,
      error: 'Failed to get system configuration',
      details: error
    };
  }
};

// Helper function to update system configuration
export const updateSystemConfiguration = async (config: any, userId: string) => {
  try {
    const { data, error } = await supabase
      .from('system_config')
      .upsert({
        id: 1,
        ...config,
        updated_at: new Date().toISOString(),
        updated_by: userId
      })
      .select()
      .single();

    if (error) {
      throw error;
    }

    // Log audit event
    await supabase.rpc('log_audit_event', {
      p_user_id: userId,
      p_action: 'UPDATE_SYSTEM_CONFIG',
      p_resource_type: 'system_config',
      p_resource_id: '1',
      p_new_values: config,
      p_success: true
    });

    return {
      success: true,
      data
    };

  } catch (error) {
    console.error('❌ Error updating system configuration:', error);
    
    // Log failed audit event
    try {
      await supabase.rpc('log_audit_event', {
        p_user_id: userId,
        p_action: 'UPDATE_SYSTEM_CONFIG',
        p_resource_type: 'system_config',
        p_resource_id: '1',
        p_success: false,
        p_error_message: error instanceof Error ? error.message : 'Unknown error'
      });
    } catch (auditError) {
      console.error('Failed to log audit event:', auditError);
    }

    return {
      success: false,
      error: 'Failed to update system configuration',
      details: error
    };
  }
};

// Helper function to log user activity
export const logUserActivity = async (
  userId: string,
  action: string,
  resourceType?: string,
  resourceId?: string,
  details?: any
) => {
  try {
    await supabase.rpc('log_audit_event', {
      p_user_id: userId,
      p_action: action,
      p_resource_type: resourceType,
      p_resource_id: resourceId,
      p_new_values: details,
      p_success: true
    });

    return { success: true };

  } catch (error) {
    console.error('❌ Error logging user activity:', error);
    return {
      success: false,
      error: 'Failed to log user activity',
      details: error
    };
  }
};

// Helper function to clean up expired sessions
export const cleanupExpiredSessions = async () => {
  try {
    const { data, error } = await supabase.rpc('cleanup_expired_sessions');

    if (error) {
      throw error;
    }

    console.log(`✅ Cleaned up ${data} expired sessions`);
    return {
      success: true,
      deletedCount: data
    };

  } catch (error) {
    console.error('❌ Error cleaning up expired sessions:', error);
    return {
      success: false,
      error: 'Failed to clean up expired sessions',
      details: error
    };
  }
};
