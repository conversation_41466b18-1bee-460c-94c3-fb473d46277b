-- Quick Fix for Message Notifications Error
-- This fixes the "record 'new' has no field 'created_by'" error

-- 1. First, ensure the messages table has the required creator fields
DO $$
BEGIN
  -- Check if messages table exists
  IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'messages' AND table_schema = 'public') THEN
    
    -- Add created_by_user_id field if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'messages' AND column_name = 'created_by_user_id' AND table_schema = 'public') THEN
      ALTER TABLE public.messages ADD COLUMN created_by_user_id UUID REFERENCES auth.users(id);
      RAISE NOTICE '✓ Added created_by_user_id to messages table';
    ELSE
      RAISE NOTICE '- created_by_user_id already exists in messages table';
    END IF;

    -- Add created_by_name field if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'messages' AND column_name = 'created_by_name' AND table_schema = 'public') THEN
      ALTER TABLE public.messages ADD COLUMN created_by_name TEXT;
      RAISE NOTICE '✓ Added created_by_name to messages table';
    ELSE
      RAISE NOTICE '- created_by_name already exists in messages table';
    END IF;

    -- Add created_by_avatar field if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'messages' AND column_name = 'created_by_avatar' AND table_schema = 'public') THEN
      ALTER TABLE public.messages ADD COLUMN created_by_avatar TEXT;
      RAISE NOTICE '✓ Added created_by_avatar to messages table';
    ELSE
      RAISE NOTICE '- created_by_avatar already exists in messages table';
    END IF;

  ELSE
    RAISE NOTICE '⚠ messages table does not exist';
  END IF;
END $$;

-- 2. Create function to automatically populate creator info for messages
CREATE OR REPLACE FUNCTION populate_message_creator_info()
RETURNS TRIGGER AS $$
DECLARE
  user_profile RECORD;
BEGIN
  -- Get current user's profile information
  SELECT 
    COALESCE(first_name || ' ' || last_name, email, 'Unknown User') as full_name,
    COALESCE(profile_picture_url, avatar_url) as avatar_url
  INTO user_profile
  FROM public.user_profiles 
  WHERE user_id = auth.uid()
  LIMIT 1;

  -- If no profile found, try to get from auth.users
  IF user_profile.full_name IS NULL THEN
    SELECT 
      COALESCE(raw_user_meta_data->>'full_name', email, 'Unknown User') as full_name,
      COALESCE(raw_user_meta_data->>'avatar_url', '') as avatar_url
    INTO user_profile
    FROM auth.users 
    WHERE id = auth.uid()
    LIMIT 1;
  END IF;

  -- Set creator fields
  NEW.created_by_user_id := auth.uid();
  NEW.created_by_name := COALESCE(user_profile.full_name, 'Unknown User');
  NEW.created_by_avatar := user_profile.avatar_url;

  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 3. Create trigger for messages table if it exists
DO $$
BEGIN
  IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'messages' AND table_schema = 'public') THEN
    -- Drop existing trigger if it exists
    DROP TRIGGER IF EXISTS set_creator_info_messages ON public.messages;
    
    -- Create new trigger
    CREATE TRIGGER set_creator_info_messages
      BEFORE INSERT ON public.messages
      FOR EACH ROW
      EXECUTE FUNCTION populate_message_creator_info();
      
    RAISE NOTICE '✓ Created trigger for messages table';
  END IF;
END $$;

-- 4. Ensure notifications table exists
CREATE TABLE IF NOT EXISTS public.notifications (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL,
    type VARCHAR(20) DEFAULT 'info',
    category VARCHAR(50) NOT NULL,
    title VARCHAR(255) NOT NULL,
    message TEXT NOT NULL,
    action_url VARCHAR(500),
    action_label VARCHAR(100),
    priority VARCHAR(20) DEFAULT 'medium',
    read BOOLEAN DEFAULT FALSE,
    related_entity_type VARCHAR(50),
    related_entity_id UUID,
    metadata JSONB DEFAULT '{}',
    expires_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_by UUID
);

-- 5. Add foreign key constraints for notifications if they don't exist
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints 
        WHERE constraint_name = 'notifications_user_id_fkey'
        AND table_name = 'notifications'
    ) THEN
        ALTER TABLE public.notifications 
        ADD CONSTRAINT notifications_user_id_fkey 
        FOREIGN KEY (user_id) REFERENCES auth.users(id) ON DELETE CASCADE;
        RAISE NOTICE '✓ Added foreign key constraint for notifications.user_id';
    END IF;
END $$;

-- 6. Set up Row Level Security for notifications
ALTER TABLE public.notifications ENABLE ROW LEVEL SECURITY;

DROP POLICY IF EXISTS "Users can view their own notifications" ON public.notifications;
DROP POLICY IF EXISTS "Users can update their own notifications" ON public.notifications;
DROP POLICY IF EXISTS "System can create notifications" ON public.notifications;

CREATE POLICY "Users can view their own notifications" ON public.notifications
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can update their own notifications" ON public.notifications
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "System can create notifications" ON public.notifications
    FOR INSERT WITH CHECK (true);

-- 7. Grant permissions
GRANT SELECT, UPDATE ON public.notifications TO authenticated;
GRANT INSERT ON public.notifications TO authenticated, anon;

-- 8. Create corrected function for message notifications
CREATE OR REPLACE FUNCTION create_message_notifications()
RETURNS TRIGGER AS $$
DECLARE
    channel_name TEXT;
    sender_name TEXT;
    user_record RECORD;
    notification_count INTEGER := 0;
    sender_id UUID;
BEGIN
    -- Only create notifications for new messages (not updates or deleted messages)
    IF TG_OP = 'INSERT' AND NEW.is_deleted = FALSE THEN
        
        -- Get the sender ID (use created_by_user_id if available, fallback to created_by)
        sender_id := COALESCE(NEW.created_by_user_id, NEW.created_by);
        
        -- Get channel name
        SELECT name INTO channel_name 
        FROM public.channels 
        WHERE id = NEW.channel_id;
        
        -- Default channel name if not found
        IF channel_name IS NULL THEN
            channel_name := 'Unknown Channel';
        END IF;
        
        -- Get sender name from user_profiles
        SELECT CONCAT(first_name, ' ', last_name) INTO sender_name
        FROM public.user_profiles 
        WHERE user_id = sender_id;
        
        -- If user_profiles doesn't exist or sender not found, use sender_name from message or default
        IF sender_name IS NULL OR sender_name = ' ' THEN
            sender_name := COALESCE(NEW.sender_name, NEW.created_by_name, 'User');
        END IF;
        
        -- Create notifications for all users except the sender
        -- Try user_profiles first, then fallback to auth.users
        IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'user_profiles') THEN
            FOR user_record IN 
                SELECT user_id 
                FROM public.user_profiles 
                WHERE user_id != sender_id
                AND user_id IS NOT NULL
            LOOP
                INSERT INTO public.notifications (
                    user_id,
                    category,
                    type,
                    title,
                    message,
                    action_url,
                    action_label,
                    priority,
                    related_entity_type,
                    related_entity_id,
                    metadata,
                    created_by
                ) VALUES (
                    user_record.user_id,
                    'message',
                    'info',
                    'New message in ' || channel_name,
                    sender_name || ': ' || LEFT(NEW.message_content, 100) || CASE WHEN LENGTH(NEW.message_content) > 100 THEN '...' ELSE '' END,
                    '/messages?channel=' || NEW.channel_id,
                    'View Message',
                    'medium',
                    'message',
                    NEW.id,
                    jsonb_build_object(
                        'channel_name', channel_name,
                        'sender_name', sender_name,
                        'message_id', NEW.id,
                        'channel_id', NEW.channel_id
                    ),
                    sender_id
                );
                notification_count := notification_count + 1;
            END LOOP;
        ELSE
            -- Fallback to auth.users if user_profiles doesn't exist
            FOR user_record IN 
                SELECT id as user_id 
                FROM auth.users 
                WHERE id != sender_id
            LOOP
                INSERT INTO public.notifications (
                    user_id,
                    category,
                    type,
                    title,
                    message,
                    action_url,
                    action_label,
                    priority,
                    related_entity_type,
                    related_entity_id,
                    metadata,
                    created_by
                ) VALUES (
                    user_record.user_id,
                    'message',
                    'info',
                    'New message in ' || channel_name,
                    sender_name || ': ' || LEFT(NEW.message_content, 100) || CASE WHEN LENGTH(NEW.message_content) > 100 THEN '...' ELSE '' END,
                    '/messages?channel=' || NEW.channel_id,
                    'View Message',
                    'medium',
                    'message',
                    NEW.id,
                    jsonb_build_object(
                        'channel_name', channel_name,
                        'sender_name', sender_name,
                        'message_id', NEW.id,
                        'channel_id', NEW.channel_id
                    ),
                    sender_id
                );
                notification_count := notification_count + 1;
            END LOOP;
        END IF;
        
        -- Log the notification creation (optional)
        RAISE NOTICE 'Created % message notifications for message ID: %', notification_count, NEW.id;
        
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 9. Create trigger for automatic message notifications
DROP TRIGGER IF EXISTS trigger_create_message_notifications ON public.messages;

-- Only create trigger if messages table exists
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'messages') THEN
        CREATE TRIGGER trigger_create_message_notifications
            AFTER INSERT ON public.messages
            FOR EACH ROW
            EXECUTE FUNCTION create_message_notifications();
        RAISE NOTICE '✓ Created trigger for automatic message notifications';
    ELSE
        RAISE NOTICE '⚠ Messages table does not exist - trigger not created';
    END IF;
END $$;

-- 10. Grant execute permission on the function
GRANT EXECUTE ON FUNCTION create_message_notifications() TO authenticated;
GRANT EXECUTE ON FUNCTION populate_message_creator_info() TO authenticated;

-- 11. Verification and status report
DO $$
DECLARE
    notifications_exists BOOLEAN;
    messages_exists BOOLEAN;
    trigger_exists BOOLEAN;
    creator_fields_exist BOOLEAN;
BEGIN
    RAISE NOTICE '';
    RAISE NOTICE '=== MESSAGE NOTIFICATIONS ERROR FIX COMPLETE ===';
    
    -- Check if tables exist
    SELECT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'notifications') INTO notifications_exists;
    SELECT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'messages') INTO messages_exists;
    
    -- Check if trigger exists
    SELECT EXISTS (
        SELECT 1 FROM information_schema.triggers 
        WHERE trigger_name = 'trigger_create_message_notifications'
    ) INTO trigger_exists;
    
    -- Check if creator fields exist
    SELECT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'messages' AND column_name = 'created_by_user_id'
    ) INTO creator_fields_exist;
    
    RAISE NOTICE 'Fix status:';
    RAISE NOTICE '  notifications table: %', notifications_exists;
    RAISE NOTICE '  messages table: %', messages_exists;
    RAISE NOTICE '  creator fields added: %', creator_fields_exist;
    RAISE NOTICE '  notification trigger: %', trigger_exists;
    RAISE NOTICE '';
    
    IF notifications_exists AND messages_exists AND trigger_exists AND creator_fields_exist THEN
        RAISE NOTICE '✓ All fixes applied successfully!';
        RAISE NOTICE '✓ The "created_by" error should now be resolved';
        RAISE NOTICE '✓ Message notifications will work automatically';
        RAISE NOTICE '✓ Try sending a message to test the system';
    ELSE
        RAISE NOTICE '⚠ Some components are missing - please check the setup';
    END IF;
    
    RAISE NOTICE '';
END $$;
