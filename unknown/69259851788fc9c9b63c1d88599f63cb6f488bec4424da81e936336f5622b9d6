-- Safe tasks table creation script
-- This script handles existing objects gracefully
-- Run this SQL in your Supabase SQL editor

-- Create tasks table only if it doesn't exist
DO $$ 
BEGIN
    IF NOT EXISTS (SELECT FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'tasks') THEN
        CREATE TABLE public.tasks (
            id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
            name VARCHAR(255) NOT NULL,
            description TEXT,
            status VARCHAR(50) NOT NULL DEFAULT 'Not Started',
            priority VARCHAR(50) NOT NULL DEFAULT 'Medium',
            start_date DATE,
            end_date DATE,
            progress INTEGER DEFAULT 0 CHECK (progress >= 0 AND progress <= 100),
            assignee VA<PERSON>HA<PERSON>(255),
            project_id UUID NOT NULL,
            estimated_hours DECIMAL(10,2) DEFAULT 0,
            actual_hours DECIMAL(10,2) DEFAULT 0,
            dependencies JSONB DEFAULT '[]'::jsonb,
            tags JSONB DEFAULT '[]'::jsonb,
            parent_task_id UUID,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL,
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL
        );
        
        RAISE NOTICE 'Tasks table created successfully';
    ELSE
        RAISE NOTICE 'Tasks table already exists, skipping creation';
    END IF;
END $$;

-- Add constraints only if they don't exist
DO $$
BEGIN
    -- Add foreign key constraint for project_id if it doesn't exist
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints 
        WHERE constraint_name = 'fk_tasks_project_id' 
        AND table_name = 'tasks' 
        AND table_schema = 'public'
    ) THEN
        ALTER TABLE public.tasks 
        ADD CONSTRAINT fk_tasks_project_id 
        FOREIGN KEY (project_id) REFERENCES public.projects(id) ON DELETE CASCADE;
        RAISE NOTICE 'Added foreign key constraint for project_id';
    END IF;

    -- Add self-referencing foreign key for parent tasks if it doesn't exist
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints 
        WHERE constraint_name = 'fk_tasks_parent_task_id' 
        AND table_name = 'tasks' 
        AND table_schema = 'public'
    ) THEN
        ALTER TABLE public.tasks 
        ADD CONSTRAINT fk_tasks_parent_task_id 
        FOREIGN KEY (parent_task_id) REFERENCES public.tasks(id) ON DELETE SET NULL;
        RAISE NOTICE 'Added foreign key constraint for parent_task_id';
    END IF;

    -- Add check constraint for status if it doesn't exist
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints 
        WHERE constraint_name = 'chk_tasks_status' 
        AND table_name = 'tasks' 
        AND table_schema = 'public'
    ) THEN
        ALTER TABLE public.tasks 
        ADD CONSTRAINT chk_tasks_status 
        CHECK (status IN ('Not Started', 'In Progress', 'Completed', 'On Hold', 'Cancelled'));
        RAISE NOTICE 'Added check constraint for status';
    END IF;

    -- Add check constraint for priority if it doesn't exist
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints 
        WHERE constraint_name = 'chk_tasks_priority' 
        AND table_name = 'tasks' 
        AND table_schema = 'public'
    ) THEN
        ALTER TABLE public.tasks 
        ADD CONSTRAINT chk_tasks_priority 
        CHECK (priority IN ('Low', 'Medium', 'High', 'Critical'));
        RAISE NOTICE 'Added check constraint for priority';
    END IF;
END $$;

-- Create indexes only if they don't exist
DO $$
BEGIN
    -- Create indexes for better performance
    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_tasks_project_id') THEN
        CREATE INDEX idx_tasks_project_id ON public.tasks(project_id);
        RAISE NOTICE 'Created index on project_id';
    END IF;

    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_tasks_status') THEN
        CREATE INDEX idx_tasks_status ON public.tasks(status);
        RAISE NOTICE 'Created index on status';
    END IF;

    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_tasks_priority') THEN
        CREATE INDEX idx_tasks_priority ON public.tasks(priority);
        RAISE NOTICE 'Created index on priority';
    END IF;

    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_tasks_assignee') THEN
        CREATE INDEX idx_tasks_assignee ON public.tasks(assignee);
        RAISE NOTICE 'Created index on assignee';
    END IF;

    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_tasks_start_date') THEN
        CREATE INDEX idx_tasks_start_date ON public.tasks(start_date);
        RAISE NOTICE 'Created index on start_date';
    END IF;

    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_tasks_end_date') THEN
        CREATE INDEX idx_tasks_end_date ON public.tasks(end_date);
        RAISE NOTICE 'Created index on end_date';
    END IF;

    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_tasks_parent_task_id') THEN
        CREATE INDEX idx_tasks_parent_task_id ON public.tasks(parent_task_id);
        RAISE NOTICE 'Created index on parent_task_id';
    END IF;

    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_tasks_created_at') THEN
        CREATE INDEX idx_tasks_created_at ON public.tasks(created_at);
        RAISE NOTICE 'Created index on created_at';
    END IF;
END $$;

-- Create or replace the update function
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = timezone('utc'::text, now());
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create trigger only if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.triggers 
        WHERE trigger_name = 'update_tasks_updated_at' 
        AND event_object_table = 'tasks'
    ) THEN
        CREATE TRIGGER update_tasks_updated_at
            BEFORE UPDATE ON public.tasks
            FOR EACH ROW
            EXECUTE FUNCTION update_updated_at_column();
        RAISE NOTICE 'Created update trigger for updated_at';
    ELSE
        RAISE NOTICE 'Update trigger already exists';
    END IF;
END $$;

-- Enable Row Level Security
ALTER TABLE public.tasks ENABLE ROW LEVEL SECURITY;

-- Handle RLS policies safely
DO $$
BEGIN
    -- Drop existing policy if it exists
    IF EXISTS (
        SELECT 1 FROM pg_policies 
        WHERE tablename = 'tasks' 
        AND policyname = 'Allow all operations for authenticated users'
    ) THEN
        DROP POLICY "Allow all operations for authenticated users" ON public.tasks;
        RAISE NOTICE 'Dropped existing RLS policy';
    END IF;

    -- Create the policy
    CREATE POLICY "Allow all operations for authenticated users" ON public.tasks
        FOR ALL USING (auth.role() = 'authenticated');
    RAISE NOTICE 'Created RLS policy for authenticated users';
END $$;

-- Grant permissions
GRANT ALL ON public.tasks TO authenticated;
GRANT ALL ON public.tasks TO service_role;

-- Create or replace the statistics view
CREATE OR REPLACE VIEW public.task_statistics AS
SELECT 
    project_id,
    COUNT(*) as total_tasks,
    COUNT(*) FILTER (WHERE status = 'Completed') as completed_tasks,
    COUNT(*) FILTER (WHERE status = 'In Progress') as in_progress_tasks,
    COUNT(*) FILTER (WHERE status = 'Not Started') as not_started_tasks,
    COUNT(*) FILTER (WHERE status = 'On Hold') as on_hold_tasks,
    COUNT(*) FILTER (WHERE status = 'Cancelled') as cancelled_tasks,
    ROUND(AVG(progress), 2) as average_progress,
    SUM(estimated_hours) as total_estimated_hours,
    SUM(actual_hours) as total_actual_hours,
    COUNT(*) FILTER (WHERE end_date < CURRENT_DATE AND status != 'Completed') as overdue_tasks
FROM public.tasks
GROUP BY project_id;

-- Grant access to the view
GRANT SELECT ON public.task_statistics TO authenticated;
GRANT SELECT ON public.task_statistics TO service_role;

-- Add comments for documentation
COMMENT ON TABLE public.tasks IS 'Project tasks with progress tracking and assignment management';
COMMENT ON COLUMN public.tasks.dependencies IS 'JSON array of task IDs that this task depends on';
COMMENT ON COLUMN public.tasks.tags IS 'JSON array of tags for categorizing and filtering tasks';
COMMENT ON COLUMN public.tasks.progress IS 'Task completion percentage (0-100)';
COMMENT ON COLUMN public.tasks.estimated_hours IS 'Estimated hours to complete the task';
COMMENT ON COLUMN public.tasks.actual_hours IS 'Actual hours spent on the task';
COMMENT ON COLUMN public.tasks.parent_task_id IS 'Parent task ID for creating task hierarchies/subtasks';

-- Final success message
DO $$
BEGIN
    RAISE NOTICE '✅ Tasks table setup completed successfully!';
    RAISE NOTICE 'You can now use the task management features in your application.';
END $$;
