// Fix orphaned users using Supabase Admin API
// This approach uses the proper Supabase Admin client instead of direct SQL

import { supabase } from '@/lib/supabase';

interface OrphanedUser {
  id: string;
  email: string;
  first_name: string;
  last_name: string;
  role_name: string;
  user_id: string | null;
}

export class OrphanedUserFixer {
  
  // Get all orphaned users (profiles without auth users)
  static async getOrphanedUsers(): Promise<OrphanedUser[]> {
    const { data: profiles, error } = await supabase
      .from('user_profiles')
      .select(`
        id,
        email,
        first_name,
        last_name,
        role_name,
        user_id
      `);

    if (error) {
      console.error('Error fetching profiles:', error);
      return [];
    }

    const orphaned: OrphanedUser[] = [];

    for (const profile of profiles) {
      if (!profile.user_id) {
        // Definitely orphaned - no user_id
        orphaned.push(profile);
      } else {
        // Check if the auth user actually exists
        try {
          const { data: authUser } = await supabase
            .from('auth.users')
            .select('id')
            .eq('id', profile.user_id)
            .single();

          if (!authUser) {
            orphaned.push(profile);
          }
        } catch {
          // Auth user doesn't exist
          orphaned.push(profile);
        }
      }
    }

    return orphaned;
  }

  // Fix a single orphaned user using the admin function
  static async fixOrphanedUser(email: string, password: string = 'TempPass123!'): Promise<{
    success: boolean;
    message: string;
    credentials?: { email: string; password: string };
  }> {
    try {
      console.log(`🔧 Fixing orphaned user: ${email}`);

      // Get the profile first
      const { data: profile, error: profileError } = await supabase
        .from('user_profiles')
        .select('*')
        .eq('email', email)
        .single();

      if (profileError || !profile) {
        return {
          success: false,
          message: `Profile not found for ${email}`
        };
      }

      console.log(`✅ Found profile for ${profile.first_name} ${profile.last_name}`);

      // Use the admin function to create the user properly
      const { data: result, error } = await supabase.rpc('admin_create_user_account', {
        p_email: email,
        p_first_name: profile.first_name,
        p_last_name: profile.last_name,
        p_role_name: profile.role_name || 'client',
        p_phone: profile.phone || null,
        p_department: profile.department || null,
        p_job_title: profile.job_title || null,
        p_temp_password: password
      });

      if (error) {
        console.error('Admin function error:', error);
        
        // If user already exists, try to update the existing profile
        if (error.message?.includes('already exists')) {
          return await this.linkExistingAuthUser(email);
        }
        
        return {
          success: false,
          message: `Failed to create auth user: ${error.message}`
        };
      }

      if (!result.success) {
        console.error('Admin function failed:', result.error);
        return {
          success: false,
          message: result.error || 'Unknown error'
        };
      }

      console.log('✅ Auth user created successfully');

      // Delete the old orphaned profile
      await supabase
        .from('user_profiles')
        .delete()
        .eq('id', profile.id);

      console.log('✅ Cleaned up old orphaned profile');

      return {
        success: true,
        message: `Successfully fixed orphaned user ${email}`,
        credentials: {
          email: email,
          password: result.user_data.temp_password
        }
      };

    } catch (error: any) {
      console.error('Error fixing orphaned user:', error);
      return {
        success: false,
        message: error.message || 'Unknown error occurred'
      };
    }
  }

  // Try to link an existing auth user to the profile
  static async linkExistingAuthUser(email: string): Promise<{
    success: boolean;
    message: string;
    credentials?: { email: string; password: string };
  }> {
    try {
      console.log(`🔗 Attempting to link existing auth user for: ${email}`);

      // Reset password for existing auth user
      const { data: resetResult, error: resetError } = await supabase.rpc('admin_reset_user_password', {
        p_email: email,
        p_new_password: 'TempPass123!'
      });

      if (resetError || !resetResult.success) {
        return {
          success: false,
          message: `Failed to reset password: ${resetError?.message || resetResult.error}`
        };
      }

      return {
        success: true,
        message: `Successfully reset password for existing user ${email}`,
        credentials: {
          email: email,
          password: resetResult.new_password
        }
      };

    } catch (error: any) {
      return {
        success: false,
        message: error.message || 'Failed to link existing auth user'
      };
    }
  }

  // Fix all orphaned users
  static async fixAllOrphanedUsers(): Promise<{
    success: boolean;
    message: string;
    fixed: Array<{ email: string; password: string }>;
    failed: Array<{ email: string; error: string }>;
  }> {
    try {
      const orphanedUsers = await this.getOrphanedUsers();
      console.log(`Found ${orphanedUsers.length} orphaned users`);

      const fixed: Array<{ email: string; password: string }> = [];
      const failed: Array<{ email: string; error: string }> = [];

      for (const user of orphanedUsers) {
        const result = await this.fixOrphanedUser(user.email);
        
        if (result.success && result.credentials) {
          fixed.push({
            email: result.credentials.email,
            password: result.credentials.password
          });
        } else {
          failed.push({
            email: user.email,
            error: result.message
          });
        }

        // Small delay to avoid rate limiting
        await new Promise(resolve => setTimeout(resolve, 100));
      }

      return {
        success: true,
        message: `Fixed ${fixed.length} users, ${failed.length} failed`,
        fixed,
        failed
      };

    } catch (error: any) {
      return {
        success: false,
        message: error.message || 'Failed to fix orphaned users',
        fixed: [],
        failed: []
      };
    }
  }
}
