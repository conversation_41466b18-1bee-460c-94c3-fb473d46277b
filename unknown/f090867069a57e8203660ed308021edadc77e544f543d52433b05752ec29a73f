-- Debug User Deletion Issues
-- Run this to check what's happening with user deletion

-- 1. Check current user count and list all users
DO $$
DECLARE
    user_count INTEGER;
    user_record RECORD;
BEGIN
    SELECT COUNT(*) INTO user_count FROM public.user_profiles;
    RAISE NOTICE '=== CURRENT USER STATUS ===';
    RAISE NOTICE 'Total users in database: %', user_count;
    RAISE NOTICE '';
    RAISE NOTICE 'User List:';
    
    FOR user_record IN 
        SELECT id, email, first_name, last_name, is_active, created_at
        FROM public.user_profiles 
        ORDER BY created_at DESC
    LOOP
        RAISE NOTICE '- %: % % (%) - Active: % - Created: %', 
            user_record.id, 
            user_record.first_name, 
            user_record.last_name, 
            user_record.email,
            user_record.is_active,
            user_record.created_at;
    END LOOP;
END $$;

-- 2. Check table permissions
DO $$
BEGIN
    RAISE NOTICE '';
    RAISE NOTICE '=== CHECKING PERMISSIONS ===';
    
    -- Check if RLS is enabled
    IF EXISTS (
        SELECT 1 FROM pg_class c 
        JOIN pg_namespace n ON n.oid = c.relnamespace 
        WHERE n.nspname = 'public' 
        AND c.relname = 'user_profiles' 
        AND c.relrowsecurity = true
    ) THEN
        RAISE NOTICE '⚠ Row Level Security is ENABLED on user_profiles';
        RAISE NOTICE 'This might prevent deletion. Consider disabling for testing.';
    ELSE
        RAISE NOTICE '✓ Row Level Security is DISABLED on user_profiles';
    END IF;
END $$;

-- 3. Test direct deletion capability
DO $$
DECLARE
    test_user_id UUID;
    test_email VARCHAR(255) := 'test-delete-' || EXTRACT(EPOCH FROM NOW()) || '@example.com';
    delete_count INTEGER;
BEGIN
    RAISE NOTICE '';
    RAISE NOTICE '=== TESTING DELETION CAPABILITY ===';
    
    -- Create a test user
    INSERT INTO public.user_profiles (
        email, 
        first_name, 
        last_name, 
        role_id,
        is_active
    ) VALUES (
        test_email,
        'Test',
        'Delete',
        (SELECT id FROM public.user_roles WHERE role_name = 'client' LIMIT 1),
        true
    ) RETURNING id INTO test_user_id;
    
    RAISE NOTICE 'Created test user: % (%)', test_email, test_user_id;
    
    -- Try to delete the test user
    DELETE FROM public.user_profiles WHERE id = test_user_id;
    GET DIAGNOSTICS delete_count = ROW_COUNT;
    
    RAISE NOTICE 'Deletion attempt result: % rows deleted', delete_count;
    
    IF delete_count > 0 THEN
        RAISE NOTICE '✓ Direct deletion WORKS';
    ELSE
        RAISE NOTICE '✗ Direct deletion FAILED';
    END IF;
    
EXCEPTION
    WHEN OTHERS THEN
        RAISE NOTICE '✗ Deletion test failed with error: %', SQLERRM;
END $$;

-- 4. Check if functions exist
DO $$
BEGIN
    RAISE NOTICE '';
    RAISE NOTICE '=== CHECKING FUNCTIONS ===';
    
    IF EXISTS (SELECT 1 FROM pg_proc WHERE proname = 'simple_delete_user') THEN
        RAISE NOTICE '✓ simple_delete_user function exists';
    ELSE
        RAISE NOTICE '✗ simple_delete_user function NOT found';
    END IF;
    
    IF EXISTS (SELECT 1 FROM pg_proc WHERE proname = 'rpc_delete_user') THEN
        RAISE NOTICE '✓ rpc_delete_user function exists';
    ELSE
        RAISE NOTICE '✗ rpc_delete_user function NOT found';
    END IF;
END $$;

-- 5. Disable RLS completely for testing
ALTER TABLE public.user_profiles DISABLE ROW LEVEL SECURITY;
GRANT ALL ON public.user_profiles TO authenticated;

-- 6. Final status check
DO $$
DECLARE
    final_count INTEGER;
BEGIN
    SELECT COUNT(*) INTO final_count FROM public.user_profiles;
    RAISE NOTICE '';
    RAISE NOTICE '=== FINAL STATUS ===';
    RAISE NOTICE 'Final user count: %', final_count;
    RAISE NOTICE 'RLS disabled and permissions granted';
    RAISE NOTICE 'User deletion should now work properly';
    RAISE NOTICE '';
    RAISE NOTICE 'Next steps:';
    RAISE NOTICE '1. Refresh your browser';
    RAISE NOTICE '2. Try deleting a user';
    RAISE NOTICE '3. Check browser console for detailed logs';
END $$;
