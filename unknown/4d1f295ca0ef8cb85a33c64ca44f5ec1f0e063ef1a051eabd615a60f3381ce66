-- Simple Email Solution - Works Without Admin API
-- This provides email content for manual sending until <PERSON><PERSON> is fully configured

-- 1. Reset email limits
UPDATE public.user_profiles 
SET setup_email_count = 0, email_status = 'pending'
WHERE setup_email_count > 0;

-- 2. Create simple email content function that works for all users
CREATE OR REPLACE FUNCTION public.generate_user_email_content(
    user_profile_id UUID
)
RETURNS TABLE(success BOOLEAN, message TEXT, email_data JSONB) AS $$
DECLARE
    user_record RECORD;
    current_count INTEGER;
    max_emails INTEGER := 20; -- Increased for testing
    email_content TEXT;
    email_subject TEXT;
    action_url TEXT;
    email_method TEXT;
BEGIN
    -- Get user information
    SELECT up.*, ur.role_name
    INTO user_record
    FROM public.user_profiles up
    JOIN public.user_roles ur ON up.role_id = ur.id
    WHERE up.id = user_profile_id;
    
    IF NOT FOUND THEN
        RETURN QUERY SELECT false, 'User not found', '{}'::jsonb;
        RETURN;
    END IF;
    
    -- Check email limits
    current_count := COALESCE(user_record.setup_email_count, 0);
    IF current_count >= max_emails THEN
        RETURN QUERY SELECT false, 'Maximum email limit reached (' || max_emails || ')', '{}'::jsonb;
        RETURN;
    END IF;
    
    -- Determine email type and content based on whether user has auth account
    IF user_record.user_id IS NULL THEN
        -- User needs to register first
        email_method := 'registration_invitation';
        email_subject := 'Welcome to Construction Management System - Complete Your Registration';
        action_url := 'http://localhost:5173/register?email=' || user_record.email || '&invited=true';
        
        email_content := 'Hello ' || user_record.first_name || ',

Welcome to Construction Management System!

Your account has been created with the following details:
- Email: ' || user_record.email || '
- Role: ' || user_record.role_name || '

To complete your account setup:

1. Click this link to register: ' || action_url || '
2. Create your password
3. Start using the system

If the link doesn''t work, copy and paste this URL into your browser:
' || action_url || '

If you have any questions, please contact your administrator.

Welcome to the team!

---
Construction Management System
' || CURRENT_DATE;

    ELSE
        -- User has auth account, can reset password
        email_method := 'password_reset';
        email_subject := 'Reset Your Password - Construction Management System';
        action_url := 'http://localhost:5173/login';
        
        email_content := 'Hello ' || user_record.first_name || ',

You can reset your password for Construction Management System.

Your account details:
- Email: ' || user_record.email || '
- Role: ' || user_record.role_name || '

To reset your password:

1. Go to: ' || action_url || '
2. Click "Forgot Password"
3. Enter your email: ' || user_record.email || '
4. Check your email for reset instructions

Or contact your administrator for assistance.

---
Construction Management System
' || CURRENT_DATE;

    END IF;
    
    -- Update email count
    UPDATE public.user_profiles 
    SET 
        setup_email_sent_at = NOW(),
        setup_email_count = current_count + 1,
        email_status = 'content_generated',
        updated_at = NOW()
    WHERE id = user_profile_id;
    
    -- Return email content
    RETURN QUERY SELECT 
        true, 
        'Email content generated successfully',
        jsonb_build_object(
            'to_email', user_record.email,
            'subject', email_subject,
            'text_content', email_content,
            'action_url', action_url,
            'user_name', user_record.first_name || ' ' || user_record.last_name,
            'method', email_method,
            'user_role', user_record.role_name,
            'has_auth_account', (user_record.user_id IS NOT NULL)
        );
        
EXCEPTION
    WHEN OTHERS THEN
        RETURN QUERY SELECT false, 'Email generation failed: ' || SQLERRM, '{}'::jsonb;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 3. Update main RPC function to use simple approach
CREATE OR REPLACE FUNCTION public.rpc_resend_setup_email(user_profile_id UUID)
RETURNS jsonb AS $$
DECLARE
    v_requesting_user_id UUID;
    email_result RECORD;
BEGIN
    v_requesting_user_id := auth.uid();
    
    IF v_requesting_user_id IS NULL THEN
        RETURN jsonb_build_object('success', false, 'error', 'Not authenticated');
    END IF;
    
    -- Generate email content
    SELECT * INTO email_result 
    FROM public.generate_user_email_content(user_profile_id);
    
    IF email_result.success THEN
        RETURN jsonb_build_object(
            'success', true, 
            'message', email_result.message,
            'email_data', email_result.email_data
        );
    ELSE
        RETURN jsonb_build_object('success', false, 'error', email_result.message);
    END IF;
    
EXCEPTION
    WHEN OTHERS THEN
        RETURN jsonb_build_object('success', false, 'error', SQLERRM);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 4. Grant permissions
GRANT EXECUTE ON FUNCTION public.generate_user_email_content(UUID) TO authenticated;

-- 5. Test the function
DO $$
DECLARE
    test_user_id UUID;
    email_result RECORD;
BEGIN
    RAISE NOTICE '';
    RAISE NOTICE '=== TESTING SIMPLE EMAIL SOLUTION ===';
    
    -- Find test user
    SELECT id INTO test_user_id 
    FROM public.user_profiles 
    WHERE email = '<EMAIL>'
    LIMIT 1;
    
    IF test_user_id IS NOT NULL THEN
        RAISE NOTICE 'Testing with user: <EMAIL>';
        
        SELECT * INTO email_result 
        FROM public.generate_user_email_content(test_user_id);
        
        RAISE NOTICE 'Result:';
        RAISE NOTICE '  Success: %', email_result.success;
        RAISE NOTICE '  Message: %', email_result.message;
        
        IF email_result.success THEN
            RAISE NOTICE '✓ EMAIL CONTENT GENERATION WORKING!';
            RAISE NOTICE 'Method: %', (email_result.email_data->>'method');
            RAISE NOTICE 'Action URL: %', (email_result.email_data->>'action_url');
        END IF;
    END IF;
    
    RAISE NOTICE '';
    RAISE NOTICE '=== SIMPLE EMAIL SOLUTION READY ===';
    RAISE NOTICE 'Try "Resend Setup Email" now - should work without errors!';
END $$;
