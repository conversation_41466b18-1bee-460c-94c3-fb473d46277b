-- Sample Company Assets Data
-- Run this after creating the main schema to populate with sample data

-- Insert sample company assets
INSERT INTO public.company_assets (
    asset_number, name, description, category_id, asset_type,
    purchase_price, current_value, depreciation_rate, purchase_date,
    brand, model, serial_number, condition, status,
    location, assigned_to, notes
) VALUES 
-- Heavy Equipment
('EQU0001', 'CAT 320 Excavator', 'Caterpillar 320 hydraulic excavator for construction projects', 
 (SELECT id FROM asset_categories WHERE name = 'Heavy Equipment'), 'Equipment',
 285000.00, 220000.00, 15.0, '2022-03-15',
 'Caterpillar', '320', 'CAT320-2022-001', 'Good', 'Active',
 'Main Construction Site', '<PERSON>', 'Primary excavator for large projects'),

('EQU0002', '<PERSON> 850K Dozer', '<PERSON> 850K crawler dozer with 6-way blade',
 (SELECT id FROM asset_categories WHERE name = 'Heavy Equipment'), 'Equipment',
 420000.00, 350000.00, 12.0, '2021-08-20',
 '<PERSON>', '850K', 'JD850K-2021-002', 'Excellent', 'Active',
 'Site B - Highway Project', '<PERSON>', 'Heavy-duty dozer for earthmoving'),

('EQU0003', 'Liebherr LTM 1070 Crane', 'Mobile crane with 70-ton lifting capacity',
 (SELECT id FROM asset_categories WHERE name = 'Heavy Equipment'), 'Equipment',
 650000.00, 520000.00, 10.0, '2020-11-10',
 'Liebherr', 'LTM 1070', 'LTM1070-2020-003', 'Good', 'Active',
 'Equipment Yard', 'Tom Rodriguez', 'Main lifting crane for high-rise projects'),

-- Vehicles
('VEH0001', 'Ford F-150 Pickup Truck', 'Company pickup truck for site supervision',
 (SELECT id FROM asset_categories WHERE name = 'Vehicles'), 'Vehicle',
 45000.00, 32000.00, 20.0, '2022-01-15',
 'Ford', 'F-150', 'F150-2022-001', 'Good', 'Active',
 'Office Parking', 'David Chen', 'Site supervisor vehicle'),

('VEH0002', 'Chevrolet Silverado 2500HD', 'Heavy-duty pickup for equipment transport',
 (SELECT id FROM asset_categories WHERE name = 'Vehicles'), 'Vehicle',
 55000.00, 38000.00, 18.0, '2021-09-05',
 'Chevrolet', 'Silverado 2500HD', 'CHEV2500-2021-002', 'Good', 'Active',
 'Site A - Downtown Project', 'Lisa Martinez', 'Equipment and material transport'),

('VEH0003', 'Mercedes Sprinter Van', 'Cargo van for tools and small equipment transport',
 (SELECT id FROM asset_categories WHERE name = 'Vehicles'), 'Vehicle',
 48000.00, 35000.00, 15.0, '2022-06-20',
 'Mercedes-Benz', 'Sprinter 2500', 'MB2500-2022-003', 'Excellent', 'Active',
 'Main Office', 'Equipment Team', 'Mobile tool storage and transport'),

-- Tools & Equipment
('TOO0001', 'Hilti TE 3000-AVR Demolition Hammer', 'Heavy-duty demolition hammer for concrete work',
 (SELECT id FROM asset_categories WHERE name = 'Tools & Equipment'), 'Tool',
 2500.00, 1800.00, 25.0, '2023-02-10',
 'Hilti', 'TE 3000-AVR', 'HILTI-TE3000-001', 'Excellent', 'Active',
 'Tool Storage - Site A', 'Construction Crew A', 'Primary demolition tool'),

('TOO0002', 'DeWalt DCS7485 Circular Saw', 'Cordless circular saw for framing work',
 (SELECT id FROM asset_categories WHERE name = 'Tools & Equipment'), 'Tool',
 450.00, 320.00, 30.0, '2023-01-15',
 'DeWalt', 'DCS7485', 'DW7485-2023-002', 'Good', 'Active',
 'Tool Storage - Main', 'Carpentry Team', 'Framing and cutting operations'),

-- Technology
('TEC0001', 'Dell Latitude 7420 Laptop', 'Project management laptop with CAD software',
 (SELECT id FROM asset_categories WHERE name = 'Technology'), 'Technology',
 1800.00, 1200.00, 25.0, '2022-04-01',
 'Dell', 'Latitude 7420', 'DELL7420-2022-001', 'Good', 'Active',
 'Main Office', 'Project Manager', 'AutoCAD and project management software'),

('TEC0002', 'iPad Pro 12.9" with Apple Pencil', 'Tablet for field documentation and drawings',
 (SELECT id FROM asset_categories WHERE name = 'Technology'), 'Technology',
 1200.00, 850.00, 20.0, '2022-07-15',
 'Apple', 'iPad Pro 12.9"', 'IPAD129-2022-002', 'Excellent', 'Active',
 'Field Office', 'Site Engineer', 'Field documentation and markup'),

-- Property
('PRO0001', 'Main Office Building', 'Corporate headquarters and administrative offices',
 (SELECT id FROM asset_categories WHERE name = 'Property'), 'Property',
 1200000.00, 1150000.00, 2.0, '2019-05-01',
 NULL, NULL, 'BLDG-MAIN-001', 'Good', 'Active',
 '123 Construction Ave, City', 'Facilities Management', 'Main corporate facility'),

('PRO0002', 'Equipment Storage Warehouse', 'Large warehouse for equipment and material storage',
 (SELECT id FROM asset_categories WHERE name = 'Property'), 'Property',
 800000.00, 750000.00, 3.0, '2020-02-15',
 NULL, NULL, 'WARE-001', 'Good', 'Active',
 '456 Industrial Blvd, City', 'Warehouse Manager', 'Primary equipment storage facility'),

-- Safety Equipment
('SAF0001', 'Fall Protection System', 'Complete fall protection system for high-rise work',
 (SELECT id FROM asset_categories WHERE name = 'Safety Equipment'), 'Equipment',
 8500.00, 6800.00, 15.0, '2022-03-01',
 'Miller', 'SkyGrip', 'MILLER-SG-001', 'Good', 'Active',
 'Safety Equipment Storage', 'Safety Officer', 'High-rise safety equipment'),

('SAF0002', 'Confined Space Entry Kit', 'Complete kit for confined space entry operations',
 (SELECT id FROM asset_categories WHERE name = 'Safety Equipment'), 'Equipment',
 3200.00, 2400.00, 20.0, '2021-11-20',
 'MSA', 'Workman Entry', 'MSA-WE-002', 'Good', 'Active',
 'Safety Equipment Storage', 'Safety Team', 'Confined space operations');

-- Insert sample maintenance records
INSERT INTO public.asset_maintenance (
    asset_id, maintenance_type, description, scheduled_date, completed_date,
    cost, labor_hours, status, priority, work_performed, technician
) VALUES 
-- Excavator maintenance
((SELECT id FROM company_assets WHERE asset_number = 'EQU0001'), 'Preventive', 
 'Routine 500-hour service and inspection', '2024-01-15', '2024-01-15',
 1200.00, 8.0, 'Completed', 'Medium', 
 'Changed engine oil, hydraulic fluid, filters. Inspected tracks and hydraulic system.', 'Mike Thompson'),

-- Dozer maintenance
((SELECT id FROM company_assets WHERE asset_number = 'EQU0002'), 'Preventive',
 'Annual service and blade inspection', '2024-02-01', '2024-02-01',
 1800.00, 12.0, 'Completed', 'Medium',
 'Complete service, blade adjustment, track tension check.', 'Sarah Johnson'),

-- Crane maintenance (upcoming)
((SELECT id FROM company_assets WHERE asset_number = 'EQU0003'), 'Inspection',
 'Annual crane certification and safety inspection', '2024-12-15', NULL,
 2500.00, 16.0, 'Scheduled', 'High',
 NULL, 'Certified Crane Inspector'),

-- Vehicle maintenance
((SELECT id FROM company_assets WHERE asset_number = 'VEH0001'), 'Preventive',
 'Oil change and routine maintenance', '2024-01-20', '2024-01-20',
 150.00, 2.0, 'Completed', 'Low',
 'Oil change, tire rotation, brake inspection.', 'Auto Service Center');

-- Insert sample depreciation records
INSERT INTO public.asset_depreciation (
    asset_id, year, opening_value, depreciation_amount, closing_value, depreciation_method
) VALUES 
-- Excavator depreciation for 2023
((SELECT id FROM company_assets WHERE asset_number = 'EQU0001'), 2023, 
 250000.00, 37500.00, 212500.00, 'Straight Line'),

-- Dozer depreciation for 2023
((SELECT id FROM company_assets WHERE asset_number = 'EQU0002'), 2023,
 380000.00, 45600.00, 334400.00, 'Straight Line'),

-- Crane depreciation for 2023
((SELECT id FROM company_assets WHERE asset_number = 'EQU0003'), 2023,
 580000.00, 58000.00, 522000.00, 'Straight Line');

-- Insert sample asset assignments
INSERT INTO public.asset_assignments (
    asset_id, assigned_to, location_to, assignment_date, purpose, condition_at_assignment
) VALUES 
-- Excavator assignment to current project
((SELECT id FROM company_assets WHERE asset_number = 'EQU0001'), 
 'Mike Johnson', 'Main Construction Site', '2024-01-01', 
 'Foundation excavation for Building A', 'Good'),

-- Dozer assignment
((SELECT id FROM company_assets WHERE asset_number = 'EQU0002'),
 'Sarah Wilson', 'Site B - Highway Project', '2024-02-15',
 'Site preparation and grading', 'Excellent'),

-- Laptop assignment
((SELECT id FROM company_assets WHERE asset_number = 'TEC0001'),
 'Project Manager', 'Main Office', '2022-04-01',
 'Project management and CAD work', 'Good');
