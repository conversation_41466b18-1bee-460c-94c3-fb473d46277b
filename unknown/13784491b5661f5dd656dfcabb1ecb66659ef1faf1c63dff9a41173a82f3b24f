# Database Setup for Client Financial Management

This directory contains SQL scripts to set up the database tables for the Client Financial Management system.

## Quick Setup

### Option 1: Using Supabase Dashboard (Recommended)

1. **Go to your Supabase project dashboard**
2. **Navigate to the SQL Editor**
3. **Copy and paste the contents of `setup-clients.sql`**
4. **Click "Run" to execute the script**

### Option 2: Using Supabase CLI

```bash
# If you have Supabase CLI installed
supabase db reset
supabase db push
```

### Option 3: Manual Setup

If you prefer to run the commands manually, execute them in this order:

1. **Create the main tables:**
   - `clients` - Main client information
   - `client_payments` - Payment tracking
   - `client_invoices` - Invoice management
   - `client_contacts` - Additional contacts per client
   - `client_documents` - Document storage references
   - `client_activities` - Activity/audit log

2. **Create indexes for performance**
3. **Enable Row Level Security (RLS)**
4. **Create policies for access control**
5. **Insert sample data**
6. **<PERSON>reate triggers for automatic timestamp updates**

## Database Schema Overview

### Core Tables

#### `clients`
- **Primary client information**
- Contact details, billing info, preferences
- Financial settings (credit limit, payment terms)
- Status tracking and categorization

#### `client_payments`
- **Payment transaction records**
- Links to invoices and projects
- Payment methods and status tracking
- Transaction fees and currency support

#### `client_invoices`
- **Invoice management**
- Line items stored as JSONB
- Status tracking (Draft → Sent → Paid/Overdue)
- Tax and discount calculations

#### `client_contacts`
- **Multiple contacts per client**
- Primary contact designation
- Role-based contact management

#### `client_documents`
- **Document references**
- File metadata and storage paths
- Document categorization

#### `client_activities`
- **Audit trail and activity log**
- User action tracking
- Communication history

### Key Features

#### 🔒 **Security**
- **Row Level Security (RLS)** enabled on all tables
- **Policies** configured for access control
- **UUID primary keys** for security

#### ⚡ **Performance**
- **Indexes** on frequently queried columns
- **Optimized queries** for dashboard analytics
- **Efficient joins** between related tables

#### 🔄 **Data Integrity**
- **Foreign key constraints** maintain relationships
- **Check constraints** ensure valid data
- **Automatic timestamps** with triggers
- **Cascade deletes** for cleanup

#### 📊 **Analytics Ready**
- **JSONB support** for flexible data
- **Aggregation-friendly** structure
- **Reporting optimized** indexes

## Sample Data

The setup script includes sample data for:

- **3 sample clients** (Construction, Real Estate, Government)
- **2 sample payments** with different payment methods
- **2 sample invoices** in different statuses

This allows you to immediately test the system functionality.

## Environment Variables

Make sure your `.env` file contains:

```env
VITE_SUPABASE_URL=your_supabase_project_url
VITE_SUPABASE_ANON_KEY=your_supabase_anon_key
```

## Verification

After running the setup, verify the installation:

1. **Check tables exist:**
   ```sql
   SELECT table_name FROM information_schema.tables 
   WHERE table_schema = 'public' 
   AND table_name LIKE 'client%';
   ```

2. **Check sample data:**
   ```sql
   SELECT name, email, status FROM public.clients;
   ```

3. **Test the application:**
   - Navigate to `/clients` in your application
   - Verify client list loads without errors
   - Test creating a new client

## Updates

### Making Email Optional

If you already created the table and need to make email optional:

```sql
-- Run this in Supabase SQL Editor
ALTER TABLE public.clients ALTER COLUMN email DROP NOT NULL;
```

Or use the provided script: `update-clients-email-optional.sql`

## Troubleshooting

### Common Issues

#### **"relation does not exist" error**
- Ensure you've run the setup script in your Supabase project
- Check that you're connected to the correct database
- Verify the tables were created successfully

#### **Permission denied errors**
- Check that RLS policies are properly configured
- Ensure your API key has the necessary permissions
- Verify the policies allow the operations you're trying to perform

#### **Connection issues**
- Verify your Supabase URL and API key in `.env`
- Check that your Supabase project is active
- Ensure network connectivity to Supabase

### Reset Database

If you need to start over:

```sql
-- Drop all client-related tables
DROP TABLE IF EXISTS public.client_activities CASCADE;
DROP TABLE IF EXISTS public.client_documents CASCADE;
DROP TABLE IF EXISTS public.client_contacts CASCADE;
DROP TABLE IF EXISTS public.client_invoices CASCADE;
DROP TABLE IF EXISTS public.client_payments CASCADE;
DROP TABLE IF EXISTS public.clients CASCADE;

-- Then re-run the setup script
```

## Next Steps

After setting up the database:

1. **Test the Client Management interface**
2. **Create your first real client**
3. **Set up project-client relationships**
4. **Configure payment tracking**
5. **Customize the analytics dashboard**

The system is now ready for production use with full client financial management capabilities!
