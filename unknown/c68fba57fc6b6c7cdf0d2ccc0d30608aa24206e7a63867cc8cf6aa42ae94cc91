import React, { useState, useEffect } from 'react';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useToast } from '@/components/ui/use-toast';
import { AssetService, CompanyAsset, AssetCategory } from '@/lib/assetService';
import { Loader2, Save, X } from 'lucide-react';

interface AssetFormProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  asset?: CompanyAsset | null;
  categories: AssetCategory[];
  onAssetSaved: () => void;
}

const AssetForm: React.FC<AssetFormProps> = ({
  open,
  onOpenChange,
  asset,
  categories,
  onAssetSaved
}) => {
  const { toast } = useToast();
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState({
    asset_number: '',
    name: '',
    description: '',
    category_id: '',
    asset_type: '',
    purchase_price: '',
    current_value: '',
    depreciation_rate: '',
    purchase_date: '',
    warranty_expiry: '',
    brand: '',
    model: '',
    serial_number: '',
    condition: 'Good',
    status: 'Active',
    location: '',
    assigned_to: '',
    insurance_policy: '',
    insurance_expiry: '',
    notes: ''
  });

  // Reset form when dialog opens/closes or asset changes
  useEffect(() => {
    if (open) {
      if (asset) {
        // Edit mode - populate form with asset data
        setFormData({
          asset_number: asset.asset_number || '',
          name: asset.name || '',
          description: asset.description || '',
          category_id: asset.category_id || '',
          asset_type: asset.asset_type || '',
          purchase_price: asset.purchase_price?.toString() || '',
          current_value: asset.current_value?.toString() || '',
          depreciation_rate: asset.depreciation_rate?.toString() || '',
          purchase_date: asset.purchase_date || '',
          warranty_expiry: asset.warranty_expiry || '',
          brand: asset.brand || '',
          model: asset.model || '',
          serial_number: asset.serial_number || '',
          condition: asset.condition || 'Good',
          status: asset.status || 'Active',
          location: asset.location || '',
          assigned_to: asset.assigned_to || '',
          insurance_policy: asset.insurance_policy || '',
          insurance_expiry: asset.insurance_expiry || '',
          notes: asset.notes || ''
        });
      } else {
        // Add mode - generate asset number and reset form
        generateAssetNumber();
      }
    }
  }, [open, asset]);

  const generateAssetNumber = async () => {
    if (formData.asset_type) {
      try {
        const assetNumber = await AssetService.generateAssetNumber(formData.asset_type);
        setFormData(prev => ({ ...prev, asset_number: assetNumber }));
      } catch (error) {
        console.error('Error generating asset number:', error);
      }
    }
  };

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    
    // Auto-generate asset number when asset type changes
    if (field === 'asset_type' && value && !asset) {
      AssetService.generateAssetNumber(value).then(assetNumber => {
        setFormData(prev => ({ ...prev, asset_number: assetNumber }));
      }).catch(console.error);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.name || !formData.asset_type || !formData.purchase_date) {
      toast({
        title: "Validation Error",
        description: "Please fill in all required fields (Name, Asset Type, Purchase Date).",
        variant: "destructive",
      });
      return;
    }

    setLoading(true);
    try {
      const assetData = {
        ...formData,
        purchase_price: parseFloat(formData.purchase_price) || 0,
        current_value: parseFloat(formData.current_value) || parseFloat(formData.purchase_price) || 0,
        depreciation_rate: parseFloat(formData.depreciation_rate) || 0,
        category_id: formData.category_id || null,
        warranty_expiry: formData.warranty_expiry || null,
        insurance_expiry: formData.insurance_expiry || null,
      };

      if (asset) {
        // Update existing asset
        await AssetService.updateAsset(asset.id, assetData);
        toast({
          title: "Asset Updated",
          description: `${formData.name} has been updated successfully.`,
        });
      } else {
        // Create new asset
        await AssetService.createAsset(assetData);
        toast({
          title: "Asset Created",
          description: `${formData.name} has been added to your assets.`,
        });
      }

      onAssetSaved();
      onOpenChange(false);
    } catch (error) {
      console.error('Error saving asset:', error);
      toast({
        title: "Error",
        description: "Failed to save asset. Please try again.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>
            {asset ? 'Edit Asset' : 'Add New Asset'}
          </DialogTitle>
          <DialogDescription>
            {asset ? 'Update asset information and details.' : 'Add a new asset to your company inventory.'}
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Basic Information */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="asset_number">Asset Number *</Label>
              <Input
                id="asset_number"
                value={formData.asset_number}
                onChange={(e) => handleInputChange('asset_number', e.target.value)}
                placeholder="Auto-generated"
                required
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="name">Asset Name *</Label>
              <Input
                id="name"
                value={formData.name}
                onChange={(e) => handleInputChange('name', e.target.value)}
                placeholder="Enter asset name"
                required
              />
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="description">Description</Label>
            <Textarea
              id="description"
              value={formData.description}
              onChange={(e) => handleInputChange('description', e.target.value)}
              placeholder="Enter asset description"
              rows={3}
            />
          </div>

          {/* Category and Type */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label>Category</Label>
              <Select value={formData.category_id} onValueChange={(value) => handleInputChange('category_id', value)}>
                <SelectTrigger>
                  <SelectValue placeholder="Select category" />
                </SelectTrigger>
                <SelectContent>
                  {categories.map((category) => (
                    <SelectItem key={category.id} value={category.id}>
                      {category.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <Label>Asset Type *</Label>
              <Select value={formData.asset_type} onValueChange={(value) => handleInputChange('asset_type', value)}>
                <SelectTrigger>
                  <SelectValue placeholder="Select asset type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="Equipment">Equipment</SelectItem>
                  <SelectItem value="Vehicle">Vehicle</SelectItem>
                  <SelectItem value="Property">Property</SelectItem>
                  <SelectItem value="Tool">Tool</SelectItem>
                  <SelectItem value="Machinery">Machinery</SelectItem>
                  <SelectItem value="Technology">Technology</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Financial Information */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="space-y-2">
              <Label htmlFor="purchase_price">Purchase Price</Label>
              <Input
                id="purchase_price"
                type="number"
                step="0.01"
                value={formData.purchase_price}
                onChange={(e) => handleInputChange('purchase_price', e.target.value)}
                placeholder="0.00"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="current_value">Current Value</Label>
              <Input
                id="current_value"
                type="number"
                step="0.01"
                value={formData.current_value}
                onChange={(e) => handleInputChange('current_value', e.target.value)}
                placeholder="0.00"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="depreciation_rate">Depreciation Rate (%)</Label>
              <Input
                id="depreciation_rate"
                type="number"
                step="0.1"
                value={formData.depreciation_rate}
                onChange={(e) => handleInputChange('depreciation_rate', e.target.value)}
                placeholder="0.0"
              />
            </div>
          </div>

          {/* Dates */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="purchase_date">Purchase Date *</Label>
              <Input
                id="purchase_date"
                type="date"
                value={formData.purchase_date}
                onChange={(e) => handleInputChange('purchase_date', e.target.value)}
                required
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="warranty_expiry">Warranty Expiry</Label>
              <Input
                id="warranty_expiry"
                type="date"
                value={formData.warranty_expiry}
                onChange={(e) => handleInputChange('warranty_expiry', e.target.value)}
              />
            </div>
          </div>

          {/* Asset Details */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="space-y-2">
              <Label htmlFor="brand">Brand</Label>
              <Input
                id="brand"
                value={formData.brand}
                onChange={(e) => handleInputChange('brand', e.target.value)}
                placeholder="Enter brand"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="model">Model</Label>
              <Input
                id="model"
                value={formData.model}
                onChange={(e) => handleInputChange('model', e.target.value)}
                placeholder="Enter model"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="serial_number">Serial Number</Label>
              <Input
                id="serial_number"
                value={formData.serial_number}
                onChange={(e) => handleInputChange('serial_number', e.target.value)}
                placeholder="Enter serial number"
              />
            </div>
          </div>

          {/* Status and Condition */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label>Condition</Label>
              <Select value={formData.condition} onValueChange={(value) => handleInputChange('condition', value)}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="Excellent">Excellent</SelectItem>
                  <SelectItem value="Good">Good</SelectItem>
                  <SelectItem value="Fair">Fair</SelectItem>
                  <SelectItem value="Poor">Poor</SelectItem>
                  <SelectItem value="Needs Repair">Needs Repair</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <Label>Status</Label>
              <Select value={formData.status} onValueChange={(value) => handleInputChange('status', value)}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="Active">Active</SelectItem>
                  <SelectItem value="Inactive">Inactive</SelectItem>
                  <SelectItem value="Under Maintenance">Under Maintenance</SelectItem>
                  <SelectItem value="Disposed">Disposed</SelectItem>
                  <SelectItem value="Lost">Lost</SelectItem>
                  <SelectItem value="Sold">Sold</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Location and Assignment */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="location">Location</Label>
              <Input
                id="location"
                value={formData.location}
                onChange={(e) => handleInputChange('location', e.target.value)}
                placeholder="Enter location"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="assigned_to">Assigned To</Label>
              <Input
                id="assigned_to"
                value={formData.assigned_to}
                onChange={(e) => handleInputChange('assigned_to', e.target.value)}
                placeholder="Enter person or department"
              />
            </div>
          </div>

          {/* Notes */}
          <div className="space-y-2">
            <Label htmlFor="notes">Notes</Label>
            <Textarea
              id="notes"
              value={formData.notes}
              onChange={(e) => handleInputChange('notes', e.target.value)}
              placeholder="Additional notes about this asset"
              rows={3}
            />
          </div>

          <DialogFooter>
            <Button type="button" variant="outline" onClick={() => onOpenChange(false)}>
              <X className="w-4 h-4 mr-2" />
              Cancel
            </Button>
            <Button type="submit" disabled={loading}>
              {loading ? (
                <Loader2 className="w-4 h-4 mr-2 animate-spin" />
              ) : (
                <Save className="w-4 h-4 mr-2" />
              )}
              {asset ? 'Update Asset' : 'Create Asset'}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
};

export default AssetForm;
