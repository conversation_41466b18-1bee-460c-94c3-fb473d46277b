-- COMPLETE AUTH SYSTEM WIPE
-- This will remove ALL authentication data and reset the auth system to blank state
-- Use this to start completely fresh

-- STEP 1: Remove all foreign key references first
DO $$
DECLARE
    profile_count INTEGER;
BEGIN
    RAISE NOTICE '';
    RAISE NOTICE '🔥 === COMPLETE AUTH SYSTEM WIPE === 🔥';
    RAISE NOTICE '';
    RAISE NOTICE 'This will completely remove ALL authentication data.';
    RAISE NOTICE 'You will need to recreate all users after this.';
    RAISE NOTICE '';
    
    RAISE NOTICE '=== STEP 1: REMOVING FOREIGN KEY REFERENCES ===';
    
    -- Remove all user_id references from user_profiles
    UPDATE public.user_profiles SET user_id = NULL;
    UPDATE public.user_profiles SET created_by_admin_id = NULL;
    
    SELECT COUNT(*) INTO profile_count FROM public.user_profiles;
    RAISE NOTICE '✅ Unlinked % user profiles from auth users', profile_count;
    
    -- Remove any other foreign key references that might exist
    -- Add more tables here if they reference auth.users
    
    RAISE NOTICE '';
END $$;

-- STEP 2: Delete ALL auth users
DO $$
DECLARE
    user_count INTEGER;
BEGIN
    RAISE NOTICE '=== STEP 2: DELETING ALL AUTH USERS ===';
    
    -- Count users before deletion
    SELECT COUNT(*) INTO user_count FROM auth.users;
    RAISE NOTICE 'Found % auth users to delete', user_count;
    
    -- Delete all auth users
    DELETE FROM auth.users;
    
    RAISE NOTICE '✅ Deleted all % auth users', user_count;
    RAISE NOTICE '';
END $$;

-- STEP 3: Clean up auth sessions and tokens
DO $$
DECLARE
    session_count INTEGER := 0;
    token_count INTEGER := 0;
BEGIN
    RAISE NOTICE '=== STEP 3: CLEANING UP AUTH SESSIONS AND TOKENS ===';
    
    -- Delete all sessions
    BEGIN
        SELECT COUNT(*) INTO session_count FROM auth.sessions;
        DELETE FROM auth.sessions;
        RAISE NOTICE '✅ Deleted % auth sessions', session_count;
    EXCEPTION
        WHEN OTHERS THEN
            RAISE NOTICE '⚠️ Could not delete sessions: %', SQLERRM;
    END;
    
    -- Delete all refresh tokens
    BEGIN
        SELECT COUNT(*) INTO token_count FROM auth.refresh_tokens;
        DELETE FROM auth.refresh_tokens;
        RAISE NOTICE '✅ Deleted % refresh tokens', token_count;
    EXCEPTION
        WHEN OTHERS THEN
            RAISE NOTICE '⚠️ Could not delete refresh tokens: %', SQLERRM;
    END;
    
    RAISE NOTICE '';
END $$;

-- STEP 4: Clean up any auth audit logs (if they exist)
DO $$
DECLARE
    audit_count INTEGER := 0;
BEGIN
    RAISE NOTICE '=== STEP 4: CLEANING UP AUTH AUDIT LOGS ===';
    
    -- Delete audit logs if table exists
    BEGIN
        IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_schema = 'auth' AND table_name = 'audit_log_entries') THEN
            SELECT COUNT(*) INTO audit_count FROM auth.audit_log_entries;
            DELETE FROM auth.audit_log_entries;
            RAISE NOTICE '✅ Deleted % audit log entries', audit_count;
        ELSE
            RAISE NOTICE '⚠️ No audit log table found';
        END IF;
    EXCEPTION
        WHEN OTHERS THEN
            RAISE NOTICE '⚠️ Could not delete audit logs: %', SQLERRM;
    END;
    
    RAISE NOTICE '';
END $$;

-- STEP 5: Clean up any auth identities (if they exist)
DO $$
DECLARE
    identity_count INTEGER := 0;
BEGIN
    RAISE NOTICE '=== STEP 5: CLEANING UP AUTH IDENTITIES ===';
    
    -- Delete identities if table exists
    BEGIN
        IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_schema = 'auth' AND table_name = 'identities') THEN
            SELECT COUNT(*) INTO identity_count FROM auth.identities;
            DELETE FROM auth.identities;
            RAISE NOTICE '✅ Deleted % auth identities', identity_count;
        ELSE
            RAISE NOTICE '⚠️ No identities table found';
        END IF;
    EXCEPTION
        WHEN OTHERS THEN
            RAISE NOTICE '⚠️ Could not delete identities: %', SQLERRM;
    END;
    
    RAISE NOTICE '';
END $$;

-- STEP 6: Clean up any other auth-related tables
DO $$
DECLARE
    table_record RECORD;
    cleanup_count INTEGER;
BEGIN
    RAISE NOTICE '=== STEP 6: CLEANING UP OTHER AUTH TABLES ===';
    
    -- List all auth schema tables and clean them
    FOR table_record IN 
        SELECT table_name 
        FROM information_schema.tables 
        WHERE table_schema = 'auth' 
        AND table_name NOT IN ('users', 'sessions', 'refresh_tokens', 'audit_log_entries', 'identities')
        ORDER BY table_name
    LOOP
        BEGIN
            EXECUTE format('SELECT COUNT(*) FROM auth.%I', table_record.table_name) INTO cleanup_count;
            EXECUTE format('DELETE FROM auth.%I', table_record.table_name);
            RAISE NOTICE '✅ Cleaned table auth.%: % records', table_record.table_name, cleanup_count;
        EXCEPTION
            WHEN OTHERS THEN
                RAISE NOTICE '⚠️ Could not clean auth.%: %', table_record.table_name, SQLERRM;
        END;
    END LOOP;
    
    RAISE NOTICE '';
END $$;

-- STEP 7: Reset any auth sequences (if they exist)
DO $$
BEGIN
    RAISE NOTICE '=== STEP 7: RESETTING AUTH SEQUENCES ===';
    
    -- Reset sequences if they exist
    BEGIN
        -- This will reset any auto-incrementing sequences in auth schema
        PERFORM setval(pg_get_serial_sequence('auth.' || table_name, column_name), 1, false)
        FROM information_schema.columns 
        WHERE table_schema = 'auth' 
        AND column_default LIKE 'nextval%';
        
        RAISE NOTICE '✅ Reset auth sequences';
    EXCEPTION
        WHEN OTHERS THEN
            RAISE NOTICE '⚠️ Could not reset sequences: %', SQLERRM;
    END;
    
    RAISE NOTICE '';
END $$;

-- STEP 8: Verification
DO $$
DECLARE
    remaining_users INTEGER;
    remaining_sessions INTEGER := 0;
    remaining_tokens INTEGER := 0;
    table_record RECORD;
    table_count INTEGER;
BEGIN
    RAISE NOTICE '=== STEP 8: VERIFICATION ===';
    
    -- Check remaining auth users
    SELECT COUNT(*) INTO remaining_users FROM auth.users;
    RAISE NOTICE 'Remaining auth users: %', remaining_users;
    
    -- Check remaining sessions
    BEGIN
        SELECT COUNT(*) INTO remaining_sessions FROM auth.sessions;
        RAISE NOTICE 'Remaining sessions: %', remaining_sessions;
    EXCEPTION
        WHEN OTHERS THEN
            RAISE NOTICE 'Sessions table: not accessible';
    END;
    
    -- Check remaining tokens
    BEGIN
        SELECT COUNT(*) INTO remaining_tokens FROM auth.refresh_tokens;
        RAISE NOTICE 'Remaining tokens: %', remaining_tokens;
    EXCEPTION
        WHEN OTHERS THEN
            RAISE NOTICE 'Tokens table: not accessible';
    END;
    
    -- Check all auth tables
    RAISE NOTICE '';
    RAISE NOTICE 'Auth schema table status:';
    FOR table_record IN 
        SELECT table_name 
        FROM information_schema.tables 
        WHERE table_schema = 'auth'
        ORDER BY table_name
    LOOP
        BEGIN
            EXECUTE format('SELECT COUNT(*) FROM auth.%I', table_record.table_name) INTO table_count;
            RAISE NOTICE '  - auth.%: % records', table_record.table_name, table_count;
        EXCEPTION
            WHEN OTHERS THEN
                RAISE NOTICE '  - auth.%: not accessible', table_record.table_name;
        END;
    END LOOP;
    
    RAISE NOTICE '';
END $$;

-- STEP 9: Final message
DO $$
BEGIN
    RAISE NOTICE '🎉 === AUTH SYSTEM WIPE COMPLETE === 🎉';
    RAISE NOTICE '';
    RAISE NOTICE '✅ ALL authentication data has been completely removed!';
    RAISE NOTICE '';
    RAISE NOTICE '📋 NEXT STEPS:';
    RAISE NOTICE '1. Your auth system is now completely clean';
    RAISE NOTICE '2. All user_profiles are unlinked from auth users';
    RAISE NOTICE '3. You can now create fresh auth users';
    RAISE NOTICE '4. Use Supabase Dashboard or API to create new users';
    RAISE NOTICE '';
    RAISE NOTICE '⚠️  IMPORTANT:';
    RAISE NOTICE '- No one can login until you recreate auth users';
    RAISE NOTICE '- All existing sessions are invalidated';
    RAISE NOTICE '- You will need to set up authentication from scratch';
    RAISE NOTICE '';
    RAISE NOTICE '🚀 Your auth system is now ready for a fresh start!';
    RAISE NOTICE '';
END $$;
