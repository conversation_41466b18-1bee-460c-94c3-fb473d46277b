-- REVERT AUTH CHANGES - Restore to previous working state
-- This will restore the auth system to before we made the schema changes

-- STEP 1: Delete the newly created auth users (they're causing the schema error)
DO $$
DECLARE
    user_record RECORD;
    delete_count INTEGER := 0;
BEGIN
    RAISE NOTICE '';
    RAISE NOTICE '🔄 === REVERTING AUTH CHANGES === 🔄';
    RAISE NOTICE '';
    RAISE NOTICE 'Deleting newly created auth users that are causing schema errors...';
    RAISE NOTICE '';
    
    -- Reset foreign key references first
    UPDATE public.user_profiles SET user_id = NULL;
    UPDATE public.user_profiles SET created_by_admin_id = NULL;
    
    -- Get list of users to delete
    FOR user_record IN 
        SELECT email FROM auth.users ORDER BY email
    LOOP
        RAISE NOTICE 'Deleting: %', user_record.email;
        delete_count := delete_count + 1;
    END LOOP;
    
    -- Delete all the problematic auth users
    DELETE FROM auth.users;
    
    RAISE NOTICE '✅ Deleted % problematic auth users', delete_count;
    RAISE NOTICE '';
END $$;

-- STEP 2: Restore original auth users with minimal configuration
-- We'll recreate them but with the ORIGINAL approach that was working
DO $$
DECLARE
    profile_record RECORD;
    v_auth_user_id UUID;
    v_password VARCHAR(255) := 'TempPass123!';
    success_count INTEGER := 0;
BEGIN
    RAISE NOTICE '=== RESTORING ORIGINAL AUTH USERS ===';
    RAISE NOTICE 'Using the original working configuration...';
    RAISE NOTICE '';
    
    -- Recreate auth users with the ORIGINAL working format
    FOR profile_record IN 
        SELECT id, email, first_name, last_name, role_name, created_at
        FROM public.user_profiles
        ORDER BY email
    LOOP
        BEGIN
            -- Use the original user_id if it exists, otherwise generate new
            v_auth_user_id := gen_random_uuid();
            
            -- Create auth user with ORIGINAL working configuration
            -- (not the new format that caused schema errors)
            INSERT INTO auth.users (
                id,
                instance_id,
                email,
                encrypted_password,
                email_confirmed_at,
                created_at,
                updated_at,
                raw_user_meta_data,
                raw_app_meta_data,
                aud,
                role
            ) VALUES (
                v_auth_user_id,
                '00000000-0000-0000-0000-000000000000',
                profile_record.email,
                crypt(v_password, gen_salt('bf')),
                profile_record.created_at, -- Use original creation date
                profile_record.created_at, -- Use original creation date
                NOW(),
                jsonb_build_object(
                    'first_name', profile_record.first_name,
                    'last_name', profile_record.last_name,
                    'email', profile_record.email
                ),
                jsonb_build_object(
                    'provider', 'email',
                    'providers', ARRAY['email']
                ),
                'authenticated',
                'authenticated'
            );
            
            -- Link profile to auth user
            UPDATE public.user_profiles 
            SET user_id = v_auth_user_id
            WHERE id = profile_record.id;
            
            RAISE NOTICE '✅ Restored: %', profile_record.email;
            success_count := success_count + 1;
            
        EXCEPTION
            WHEN OTHERS THEN
                RAISE NOTICE '❌ Failed to restore: % - %', profile_record.email, SQLERRM;
        END;
    END LOOP;
    
    RAISE NOTICE '';
    RAISE NOTICE '✅ Restored % auth users', success_count;
    RAISE NOTICE '';
END $$;

-- STEP 3: Verify the restoration
DO $$
DECLARE
    auth_count INTEGER;
    profile_count INTEGER;
    linked_count INTEGER;
BEGIN
    RAISE NOTICE '=== VERIFICATION ===';
    
    SELECT COUNT(*) INTO auth_count FROM auth.users;
    SELECT COUNT(*) INTO profile_count FROM public.user_profiles;
    SELECT COUNT(*) INTO linked_count 
    FROM public.user_profiles up
    JOIN auth.users au ON up.user_id = au.id;
    
    RAISE NOTICE 'Auth users: %', auth_count;
    RAISE NOTICE 'Profiles: %', profile_count;
    RAISE NOTICE 'Linked: %', linked_count;
    
    IF auth_count = profile_count AND linked_count = profile_count THEN
        RAISE NOTICE '✅ All users properly restored';
    ELSE
        RAISE NOTICE '❌ Some users may have issues';
    END IF;
    
    RAISE NOTICE '';
END $$;

-- STEP 4: Final message
DO $$
BEGIN
    RAISE NOTICE '🎯 === REVERT COMPLETE === 🎯';
    RAISE NOTICE '';
    RAISE NOTICE '✅ Auth system reverted to previous configuration';
    RAISE NOTICE '🔑 All users now have password: TempPass123!';
    RAISE NOTICE '';
    RAISE NOTICE 'Try logging in with:';
    RAISE NOTICE '- <EMAIL> / TempPass123!';
    RAISE NOTICE '- <EMAIL> / TempPass123!';
    RAISE NOTICE '';
    RAISE NOTICE 'The "Database error querying schema" should now be resolved.';
    RAISE NOTICE 'If it persists, the issue is with Supabase project configuration,';
    RAISE NOTICE 'not the database schema.';
    RAISE NOTICE '';
END $$;
