-- Quick Test Assets - Add a few sample assets to test the interface
-- Run this AFTER running the company_assets_simple.sql schema

-- Add some test assets
INSERT INTO public.company_assets (
    asset_number, name, description, category_id, asset_type,
    purchase_price, current_value, depreciation_rate, purchase_date,
    brand, model, condition, status, location, assigned_to
) VALUES 

-- Heavy Equipment
('EQU0001', 'CAT 320 Excavator', 'Caterpillar 320 hydraulic excavator for construction projects', 
 (SELECT id FROM asset_categories WHERE name = 'Heavy Equipment'), 'Equipment',
 285000.00, 220000.00, 15.0, '2022-03-15',
 'Caterpillar', '320', 'Good', 'Active',
 'Main Construction Site', '<PERSON>'),

-- Vehicle
('VEH0001', 'Ford F-150 Pickup Truck', 'Company pickup truck for site supervision',
 (SELECT id FROM asset_categories WHERE name = 'Vehicles'), 'Vehicle',
 45000.00, 32000.00, 20.0, '2022-01-15',
 'Ford', 'F-150', 'Good', 'Active',
 'Office Parking', '<PERSON>'),

-- Technology
('TEC0001', 'Dell Latitude 7420 Laptop', 'Project management laptop with CAD software',
 (SELECT id FROM asset_categories WHERE name = 'Technology'), 'Technology',
 1800.00, 1200.00, 25.0, '2022-04-01',
 'Dell', 'Latitude 7420', 'Good', 'Active',
 'Main Office', 'Project Manager'),

-- Tool
('TOO0001', 'Hilti TE 3000-AVR Demolition Hammer', 'Heavy-duty demolition hammer for concrete work',
 (SELECT id FROM asset_categories WHERE name = 'Tools & Equipment'), 'Tool',
 2500.00, 1800.00, 25.0, '2023-02-10',
 'Hilti', 'TE 3000-AVR', 'Excellent', 'Active',
 'Tool Storage - Site A', 'Construction Crew A'),

-- Property
('PRO0001', 'Main Office Building', 'Corporate headquarters and administrative offices',
 (SELECT id FROM asset_categories WHERE name = 'Property'), 'Property',
 1200000.00, 1150000.00, 2.0, '2019-05-01',
 NULL, NULL, 'Good', 'Active',
 '123 Construction Ave, City', 'Facilities Management');

-- Add a maintenance record
INSERT INTO public.asset_maintenance (
    asset_id, maintenance_type, description, scheduled_date, completed_date,
    cost, labor_hours, status, priority, work_performed, technician
) VALUES 
((SELECT id FROM company_assets WHERE asset_number = 'EQU0001'), 'Preventive', 
 'Routine 500-hour service and inspection', '2024-01-15', '2024-01-15',
 1200.00, 8.0, 'Completed', 'Medium', 
 'Changed engine oil, hydraulic fluid, filters. Inspected tracks and hydraulic system.', 'Mike Thompson');
