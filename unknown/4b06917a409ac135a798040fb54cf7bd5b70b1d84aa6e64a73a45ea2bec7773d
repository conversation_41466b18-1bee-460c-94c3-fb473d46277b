// Test Supabase connection and auth
import { createClient } from '@supabase/supabase-js';

export async function testSupabaseConnection() {
  console.log('🔍 Testing Supabase connection...');
  
  // Get environment variables
  const supabaseUrl = import.meta.env.VITE_SUPABASE_URL;
  const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY;
  
  console.log('Environment check:');
  console.log('- URL:', supabaseUrl);
  console.log('- Anon Key length:', supabaseAnonKey?.length || 0);
  console.log('- Anon Key starts with:', supabaseAnonKey?.substring(0, 20) + '...');
  
  if (!supabaseUrl || !supabaseAnonKey) {
    console.error('❌ Missing environment variables');
    return false;
  }
  
  try {
    // Create fresh client
    const testClient = createClient(supabaseUrl, supabaseAnonKey);
    
    // Test basic connection
    console.log('Testing basic database connection...');
    const { data, error } = await testClient
      .from('user_profiles')
      .select('count', { count: 'exact', head: true });
    
    if (error) {
      console.error('❌ Database connection failed:', error);
      return false;
    }
    
    console.log('✅ Database connection successful');
    
    // Test auth service
    console.log('Testing auth service...');
    try {
      const { data: authData, error: authError } = await testClient.auth.signInWithPassword({
        email: '<EMAIL>',
        password: 'NewPass123!'
      });
      
      if (authError) {
        console.error('❌ Auth test failed:', authError.message);
        console.error('Error details:', authError);
        return false;
      }
      
      console.log('✅ Auth test successful!');
      console.log('User:', authData.user?.email);
      
      // Clean up
      await testClient.auth.signOut();
      
      return true;
      
    } catch (authError: any) {
      console.error('❌ Auth service error:', authError.message);
      return false;
    }
    
  } catch (error: any) {
    console.error('❌ Connection test failed:', error.message);
    return false;
  }
}

// Run this in browser console
(window as any).testSupabaseConnection = testSupabaseConnection;
