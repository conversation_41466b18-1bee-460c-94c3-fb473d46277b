// Simple auth testing utility
// Tests actual login instead of trying to debug auth tables

import { supabase } from '@/lib/supabase';

export interface LoginTestResult {
  success: boolean;
  error?: string;
  user?: any;
  profileExists: boolean;
  profileData?: any;
}

export class SimpleAuthTester {
  
  // Test if a user can actually login with given credentials
  static async testLogin(email: string, password: string): Promise<LoginTestResult> {
    try {
      console.log(`🔍 Testing login for: ${email}`);
      
      // First check if profile exists
      const { data: profile, error: profileError } = await supabase
        .from('user_profiles')
        .select('*')
        .eq('email', email)
        .single();

      const profileExists = !!profile && !profileError;
      
      // Test actual login
      const { data: authData, error: authError } = await supabase.auth.signInWithPassword({
        email,
        password
      });

      if (authError) {
        // Login failed - but let's check why
        console.log(`❌ Login failed: ${authError.message}`);
        
        // Sign out any existing session to clean up
        await supabase.auth.signOut();
        
        return {
          success: false,
          error: authError.message,
          profileExists,
          profileData: profile
        };
      }

      // Login succeeded!
      console.log(`✅ Login successful for: ${email}`);
      
      // Sign out immediately to not interfere with current session
      await supabase.auth.signOut();
      
      return {
        success: true,
        user: authData.user,
        profileExists,
        profileData: profile
      };

    } catch (error: any) {
      console.error('Login test failed:', error);
      
      // Clean up any session
      await supabase.auth.signOut();
      
      return {
        success: false,
        error: error.message || 'Unknown error',
        profileExists: false
      };
    }
  }

  // Test multiple common passwords for a user
  static async testCommonPasswords(email: string): Promise<{
    success: boolean;
    workingPassword?: string;
    results: Array<{ password: string; success: boolean; error?: string }>;
  }> {
    const commonPasswords = [
      'TempPass123!',
      'password',
      'password123',
      'admin123',
      'temp123',
      '123456',
      'admin',
      'user123'
    ];

    const results: Array<{ password: string; success: boolean; error?: string }> = [];
    let workingPassword: string | undefined;

    console.log(`🔍 Testing common passwords for: ${email}`);

    for (const password of commonPasswords) {
      const result = await this.testLogin(email, password);
      
      results.push({
        password,
        success: result.success,
        error: result.error
      });

      if (result.success) {
        workingPassword = password;
        console.log(`✅ Found working password: ${password}`);
        break;
      }

      // Small delay to avoid rate limiting
      await new Promise(resolve => setTimeout(resolve, 500));
    }

    return {
      success: !!workingPassword,
      workingPassword,
      results
    };
  }

  // Get all profiles and test which ones can login
  static async testAllUsers(): Promise<{
    total: number;
    working: Array<{ email: string; password?: string }>;
    broken: Array<{ email: string; error: string }>;
  }> {
    try {
      // Get all profiles
      const { data: profiles, error } = await supabase
        .from('user_profiles')
        .select('email, first_name, last_name');

      if (error || !profiles) {
        throw new Error('Failed to fetch user profiles');
      }

      const working: Array<{ email: string; password?: string }> = [];
      const broken: Array<{ email: string; error: string }> = [];

      console.log(`🔍 Testing login for ${profiles.length} users...`);

      for (const profile of profiles) {
        console.log(`Testing: ${profile.email}`);
        
        const passwordTest = await this.testCommonPasswords(profile.email);
        
        if (passwordTest.success) {
          working.push({
            email: profile.email,
            password: passwordTest.workingPassword
          });
        } else {
          broken.push({
            email: profile.email,
            error: 'No working password found'
          });
        }

        // Delay between users to avoid rate limiting
        await new Promise(resolve => setTimeout(resolve, 1000));
      }

      return {
        total: profiles.length,
        working,
        broken
      };

    } catch (error: any) {
      console.error('Failed to test all users:', error);
      return {
        total: 0,
        working: [],
        broken: []
      };
    }
  }
}
