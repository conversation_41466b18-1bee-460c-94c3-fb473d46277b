-- Quick Fix for Settings Database Errors
-- Run this to immediately fix column errors

-- Drop the problematic view
DROP VIEW IF EXISTS user_security_summary;

-- Add missing column to user_notification_preferences if it doesn't exist
DO $$ 
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_schema = 'public' 
        AND table_name = 'user_notification_preferences' 
        AND column_name = 'two_factor_enabled'
    ) THEN
        ALTER TABLE public.user_notification_preferences 
        ADD COLUMN two_factor_enabled BOOLEAN DEFAULT false;
        RAISE NOTICE 'Added two_factor_enabled column';
    END IF;
EXCEPTION
    WHEN undefined_table THEN
        -- Table doesn't exist, create it
        CREATE TABLE public.user_notification_preferences (
            id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
            user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
            email_notifications BOOLEAN DEFAULT true,
            push_notifications B<PERSON><PERSON>EAN DEFAULT true,
            sms_notifications BOOLEAN DEFAULT false,
            project_updates BO<PERSON>EAN DEFAULT true,
            financial_alerts B<PERSON><PERSON><PERSON><PERSON> DEFAULT true,
            time_tracking_reminders BOOLEAN DEFAULT true,
            team_messages BOOLEAN DEFAULT true,
            system_announcements BOOLEAN DEFAULT true,
            invoice_notifications BOOLEAN DEFAULT true,
            deadline_reminders BOOLEAN DEFAULT true,
            notification_frequency VARCHAR(20) DEFAULT 'immediate',
            quiet_hours_enabled BOOLEAN DEFAULT false,
            quiet_hours_start TIME DEFAULT '22:00',
            quiet_hours_end TIME DEFAULT '08:00',
            two_factor_enabled BOOLEAN DEFAULT false,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            UNIQUE(user_id)
        );
        RAISE NOTICE 'Created user_notification_preferences table';
END $$;

-- Create system_config table if it doesn't exist
CREATE TABLE IF NOT EXISTS public.system_config (
    id INTEGER PRIMARY KEY DEFAULT 1,
    company_name VARCHAR(255) DEFAULT 'Construction Management System',
    company_email VARCHAR(255) DEFAULT '<EMAIL>',
    company_phone VARCHAR(50) DEFAULT '+****************',
    company_address TEXT DEFAULT '123 Business Ave, City, State 12345',
    timezone VARCHAR(50) DEFAULT 'America/New_York',
    date_format VARCHAR(20) DEFAULT 'MM/DD/YYYY',
    currency VARCHAR(10) DEFAULT 'USD',
    session_timeout INTEGER DEFAULT 30,
    max_file_size INTEGER DEFAULT 10,
    backup_frequency VARCHAR(20) DEFAULT 'daily',
    maintenance_mode BOOLEAN DEFAULT false,
    registration_enabled BOOLEAN DEFAULT true,
    email_verification_required BOOLEAN DEFAULT true,
    two_factor_required BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_by UUID REFERENCES auth.users(id),
    CHECK (id = 1)
);

-- Grant permissions
GRANT ALL ON public.user_notification_preferences TO authenticated;
GRANT ALL ON public.system_config TO authenticated;

-- Insert default system config
INSERT INTO public.system_config (id) VALUES (1) ON CONFLICT (id) DO NOTHING;

-- Create default preferences for existing users
INSERT INTO public.user_notification_preferences (user_id, two_factor_enabled)
SELECT u.id, false
FROM auth.users u
WHERE NOT EXISTS (
    SELECT 1 FROM public.user_notification_preferences unp 
    WHERE unp.user_id = u.id
)
ON CONFLICT (user_id) DO NOTHING;

RAISE NOTICE 'Quick fix applied - Settings should now work without errors!';
