import { supabase } from '@/lib/supabase';

export class DatabaseSetup {
  // Check if tables exist
  static async checkTablesExist(): Promise<{ [key: string]: boolean }> {
    const tables = [
      'expense_categories',
      'expenses',
      'revenue_entries',
      'projects',
      'cash_flow_transactions',
      'payment_records',
      'client_financials',
      'company_assets'
    ];

    const results: { [key: string]: boolean } = {};

    for (const table of tables) {
      try {
        const { data, error } = await supabase
          .from(table)
          .select('id')
          .limit(1);
        
        results[table] = !error;
        console.log(`Table ${table}: ${!error ? 'EXISTS' : 'MISSING'}`);
      } catch (err) {
        results[table] = false;
        console.log(`Table ${table}: MISSING`);
      }
    }

    return results;
  }

  // Create expense categories if they don't exist
  static async setupExpenseCategories(): Promise<boolean> {
    try {
      // Check if categories already exist
      const { data: existing } = await supabase
        .from('expense_categories')
        .select('id')
        .limit(1);

      if (existing && existing.length > 0) {
        console.log('Expense categories already exist');
        return true;
      }

      // Create default categories
      const categories = [
        { name: 'Materials', description: 'Construction materials and supplies', is_project_related: true },
        { name: 'Labor', description: 'Worker wages and contractor payments', is_project_related: true },
        { name: 'Equipment Rental', description: 'Rental of construction equipment', is_project_related: true },
        { name: 'Transportation', description: 'Vehicle fuel and transportation costs', is_project_related: true },
        { name: 'Office Expenses', description: 'Office supplies and administrative costs', is_project_related: false },
        { name: 'Utilities', description: 'Electricity, water, internet, phone', is_project_related: false },
        { name: 'Insurance', description: 'Business and equipment insurance', is_project_related: false },
        { name: 'Marketing', description: 'Advertising and promotional expenses', is_project_related: false },
        { name: 'Professional Services', description: 'Legal, accounting, consulting fees', is_project_related: false },
        { name: 'Maintenance', description: 'Equipment and facility maintenance', is_project_related: false },
        { name: 'Permits & Licenses', description: 'Government permits and licenses', is_project_related: true },
        { name: 'Subcontractors', description: 'Subcontractor payments', is_project_related: true }
      ];

      const { error } = await supabase
        .from('expense_categories')
        .insert(categories);

      if (error) throw error;

      console.log('Expense categories created successfully');
      return true;
    } catch (error) {
      console.error('Error setting up expense categories:', error);
      return false;
    }
  }

  // Create test expenses
  static async createTestExpenses(): Promise<boolean> {
    try {
      // Get first category ID
      const { data: categories } = await supabase
        .from('expense_categories')
        .select('id, name')
        .limit(3);

      if (!categories || categories.length === 0) {
        console.log('No expense categories found');
        return false;
      }

      // Create test expenses
      const testExpenses = [
        {
          category_id: categories[0].id,
          description: 'Steel beams for foundation work',
          amount: 2500,
          expense_date: new Date().toISOString().split('T')[0],
          payment_method: 'Bank Transfer',
          vendor_name: 'Steel Supply Co.',
          status: 'paid'
        },
        {
          category_id: categories[1]?.id || categories[0].id,
          description: 'Weekly crew wages',
          amount: 3200,
          expense_date: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
          payment_method: 'Cash',
          vendor_name: 'Construction Crew',
          status: 'paid'
        },
        {
          category_id: categories[2]?.id || categories[0].id,
          description: 'Excavator rental - 3 days',
          amount: 1800,
          expense_date: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
          payment_method: 'Credit Card',
          vendor_name: 'Equipment Rental Inc.',
          status: 'pending'
        }
      ];

      const { error } = await supabase
        .from('expenses')
        .insert(testExpenses);

      if (error) throw error;

      console.log('Test expenses created successfully');
      return true;
    } catch (error) {
      console.error('Error creating test expenses:', error);
      return false;
    }
  }

  // Create test revenue entries
  static async createTestRevenueEntries(): Promise<boolean> {
    try {
      const testRevenues = [
        {
          client_name: 'ABC Construction Corp',
          project_name: 'Downtown Office Complex',
          amount: 125000,
          revenue_date: new Date().toISOString().split('T')[0],
          category: 'Construction',
          description: 'Phase 1 completion payment',
          status: 'confirmed',
          invoice_number: 'INV-2024-001',
          reference_number: 'REF-ABC-001'
        },
        {
          client_name: 'City Municipal Works',
          project_name: 'Bridge Renovation Project',
          amount: 85000,
          revenue_date: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
          category: 'Infrastructure',
          description: 'Monthly progress payment',
          status: 'confirmed',
          invoice_number: 'INV-2024-002',
          reference_number: 'REF-CITY-001'
        },
        {
          client_name: 'XYZ Development LLC',
          project_name: 'Residential Complex Phase 2',
          amount: 95000,
          revenue_date: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
          category: 'Residential',
          description: 'Foundation work completion',
          status: 'confirmed',
          invoice_number: 'INV-2024-003',
          reference_number: 'REF-XYZ-001'
        },
        {
          client_name: 'Metro Shopping Centers',
          project_name: 'Retail Plaza Construction',
          amount: 110000,
          revenue_date: new Date(Date.now() - 14 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
          category: 'Commercial',
          description: 'Structural work payment',
          status: 'confirmed',
          invoice_number: 'INV-2024-004',
          reference_number: 'REF-METRO-001'
        },
        {
          client_name: 'Green Energy Solutions',
          project_name: 'Solar Panel Installation',
          amount: 45000,
          revenue_date: new Date(Date.now() - 21 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
          category: 'Energy',
          description: 'Installation services',
          status: 'confirmed',
          invoice_number: 'INV-2024-005',
          reference_number: 'REF-GREEN-001'
        },
        {
          client_name: 'Tech Startup Hub',
          project_name: 'Office Renovation',
          amount: 35000,
          revenue_date: new Date(Date.now() - 28 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
          category: 'Renovation',
          description: 'Interior renovation work',
          status: 'pending',
          invoice_number: 'INV-2024-006',
          reference_number: 'REF-TECH-001'
        }
      ];

      const { error } = await supabase
        .from('revenue_entries')
        .insert(testRevenues);

      if (error) throw error;

      console.log('Test revenue entries created successfully');
      return true;
    } catch (error) {
      console.error('Error creating test revenue entries:', error);
      return false;
    }
  }

  // Create test projects
  static async createTestProjects(): Promise<boolean> {
    try {
      const testProjects = [
        {
          name: 'Downtown Office Complex',
          description: 'Modern 15-story office building with underground parking and retail space',
          status: 'In Progress',
          priority: 'High',
          start_date: '2024-01-15',
          end_date: '2024-12-30',
          budget: 2500000,
          spent: 1250000,
          progress: 65,
          client_name: 'ABC Construction Corp',
          project_manager: 'John Smith',
          location: 'Downtown District, Main Street'
        },
        {
          name: 'Bridge Renovation Project',
          description: 'Complete renovation of the historic city bridge including structural reinforcement',
          status: 'In Progress',
          priority: 'Critical',
          start_date: '2024-02-01',
          end_date: '2024-08-15',
          budget: 1800000,
          spent: 900000,
          progress: 45,
          client_name: 'City Municipal Works',
          project_manager: 'Sarah Johnson',
          location: 'River Bridge, Highway 101'
        },
        {
          name: 'Residential Complex Phase 2',
          description: '120-unit residential complex with amenities and landscaping',
          status: 'Planning',
          priority: 'Medium',
          start_date: '2024-06-01',
          end_date: '2025-03-30',
          budget: 3200000,
          spent: 150000,
          progress: 15,
          client_name: 'XYZ Development LLC',
          project_manager: 'Mike Davis',
          location: 'Westside Development Area'
        },
        {
          name: 'Retail Plaza Construction',
          description: 'Shopping center with 25 retail units and food court',
          status: 'In Progress',
          priority: 'Medium',
          start_date: '2024-03-01',
          end_date: '2024-11-15',
          budget: 2100000,
          spent: 1400000,
          progress: 75,
          client_name: 'Metro Shopping Centers',
          project_manager: 'Lisa Chen',
          location: 'Commercial District, Oak Avenue'
        },
        {
          name: 'Solar Panel Installation',
          description: 'Large-scale solar panel installation for industrial facility',
          status: 'Completed',
          priority: 'Low',
          start_date: '2024-01-10',
          end_date: '2024-04-30',
          budget: 850000,
          spent: 820000,
          progress: 100,
          client_name: 'Green Energy Solutions',
          project_manager: 'David Wilson',
          location: 'Industrial Park, Zone B'
        },
        {
          name: 'Office Renovation',
          description: 'Complete interior renovation of tech startup office space',
          status: 'Completed',
          priority: 'Low',
          start_date: '2024-02-15',
          end_date: '2024-05-15',
          budget: 450000,
          spent: 435000,
          progress: 100,
          client_name: 'Tech Startup Hub',
          project_manager: 'Emma Rodriguez',
          location: 'Tech District, Innovation Center'
        },
        {
          name: 'Highway Expansion Project',
          description: 'Expansion of highway section including new lanes and improved drainage',
          status: 'On Hold',
          priority: 'High',
          start_date: '2024-05-01',
          end_date: '2025-02-28',
          budget: 4500000,
          spent: 200000,
          progress: 5,
          client_name: 'State Transportation Dept',
          project_manager: 'Robert Taylor',
          location: 'Highway 95, Mile Marker 45-52'
        },
        {
          name: 'School Modernization',
          description: 'Modernization of elementary school including new classrooms and technology upgrades',
          status: 'Planning',
          priority: 'Medium',
          start_date: '2024-07-01',
          end_date: '2024-12-20',
          budget: 1200000,
          spent: 50000,
          progress: 8,
          client_name: 'City School District',
          project_manager: 'Jennifer Adams',
          location: 'Maple Elementary School'
        }
      ];

      const { error } = await supabase
        .from('projects')
        .insert(testProjects);

      if (error) throw error;

      console.log('Test projects created successfully');
      return true;
    } catch (error) {
      console.error('Error creating test projects:', error);
      return false;
    }
  }

  // Create test cash flow transactions (disabled - using real data only)
  static async createTestCashFlowTransactions(): Promise<boolean> {
    console.log('Test cash flow transactions disabled - using real data only');
    return true;
  }

  // Create test payment records for revenue tracking (disabled - using real data only)
  static async createTestPaymentRecords(): Promise<boolean> {
    console.log('Test payment records disabled - using real data only');
    return true;
  }

  // Full setup process
  static async setupDatabase(): Promise<{ success: boolean; message: string }> {
    try {
      console.log('Starting database setup...');

      // Check if tables exist
      const tableStatus = await this.checkTablesExist();
      const missingTables = Object.entries(tableStatus)
        .filter(([_, exists]) => !exists)
        .map(([table, _]) => table);

      if (missingTables.length > 0) {
        return {
          success: false,
          message: `❌ MISSING TABLES: ${missingTables.join(', ')}.

🔧 TO FIX:
1. Go to: https://app.supabase.com/project/ygdaucsngasdutbvmevs/sql
2. Copy ALL content from: database/financial_schema.sql
3. Paste into SQL Editor and click "Run"
4. Come back and click "Setup Database & Test Data" again`
        };
      }

      // Setup expense categories
      const categoriesSetup = await this.setupExpenseCategories();
      if (!categoriesSetup) {
        return {
          success: false,
          message: 'Failed to setup expense categories'
        };
      }

      // Create test data
      await this.createTestExpenses();
      await this.createTestRevenueEntries();
      await this.createTestProjects();
      await this.createTestCashFlowTransactions();
      await this.createTestPaymentRecords();

      return {
        success: true,
        message: 'Database setup completed successfully with test data'
      };

    } catch (error) {
      console.error('Database setup error:', error);
      return {
        success: false,
        message: `Database setup failed: ${error instanceof Error ? error.message : 'Unknown error'}`
      };
    }
  }

  // Clear ALL demo/test data
  static async clearTestData(): Promise<boolean> {
    try {
      console.log('🧹 Starting comprehensive demo data cleanup...');

      // 1. Delete ALL revenue entries (keeping only real user data)
      const { data: revenueEntries } = await supabase
        .from('revenue_entries')
        .select('id, client_name, description, amount')
        .or('client_name.in.(ABC Construction Corp,City Municipal Works,XYZ Development LLC,Metro Shopping Centers,Green Energy Solutions,Tech Startup Hub,Manesa,ewqqew),description.ilike.%test%,description.ilike.%demo%,description.ilike.%sample%');

      if (revenueEntries && revenueEntries.length > 0) {
        console.log('🗑️ Deleting demo revenue entries:', revenueEntries);
        await supabase
          .from('revenue_entries')
          .delete()
          .in('id', revenueEntries.map(r => r.id));
      }

      // 2. Delete ALL cash flow transactions (demo data)
      const { data: cashFlowTransactions } = await supabase
        .from('cash_flow_transactions')
        .select('id, description, amount')
        .or('description.ilike.%test%,description.ilike.%demo%,description.ilike.%ABC Construction%,description.ilike.%XYZ Corp%,description.ilike.%City Council%,description.ilike.%Manesa%,description.ilike.%ewqqew%');

      if (cashFlowTransactions && cashFlowTransactions.length > 0) {
        console.log('🗑️ Deleting demo cash flow transactions:', cashFlowTransactions);
        await supabase
          .from('cash_flow_transactions')
          .delete()
          .in('id', cashFlowTransactions.map(t => t.id));
      }

      // 3. Delete demo projects
      await supabase
        .from('projects')
        .delete()
        .or('client_name.in.(ABC Construction Corp,City Municipal Works,XYZ Development LLC,Metro Shopping Centers,Green Energy Solutions,Tech Startup Hub,State Transportation Dept,City School District,Test Client),name.ilike.%test%');

      // 4. Delete demo expenses
      const { data: expenses } = await supabase
        .from('expenses')
        .select('id, vendor_name, description')
        .or('vendor_name.in.(Steel Supply Co.,Construction Crew,Equipment Rental Inc.),description.ilike.%test%,description.ilike.%demo%,description.ilike.%Steel beams%,description.ilike.%Weekly%,description.ilike.%Excavator%');

      if (expenses && expenses.length > 0) {
        console.log('🗑️ Deleting demo expenses:', expenses);
        await supabase
          .from('expenses')
          .delete()
          .in('id', expenses.map(e => e.id));
      }

      // 5. Delete demo client data from SQL setup
      await supabase
        .from('client_payments')
        .delete()
        .or('description.ilike.%Foundation Work%,description.ilike.%Electrical Installation%,reference_number.in.(TXN-123456,CHK-789012)');

      await supabase
        .from('client_invoices')
        .delete()
        .or('invoice_number.in.(INV-2024-001,INV-2024-002),description.ilike.%Foundation excavation%,description.ilike.%Electrical installation%');

      await supabase
        .from('clients')
        .delete()
        .or('email.in.(<EMAIL>,<EMAIL>,<EMAIL>),company_name.ilike.%ABC Construction%,company_name.ilike.%Metro Development%');

      // 6. Delete demo documents/invoices
      await supabase
        .from('documents')
        .delete()
        .or('type.eq.invoice,title.ilike.%test%,title.ilike.%demo%,title.ilike.%sample%');

      // 7. Delete demo payment records
      await supabase
        .from('payment_records')
        .delete()
        .or('reference_number.ilike.%test%,reference_number.ilike.%demo%,notes.ilike.%test%');

      // 8. Delete demo tasks
      await supabase
        .from('tasks')
        .delete()
        .or('name.ilike.%test%,name.ilike.%demo%,name.ilike.%Setup Test Task%,name.ilike.%Foundation Excavation%,assignee.eq.System Setup');

      console.log('✅ Comprehensive demo data cleanup completed successfully');
      return true;
    } catch (error) {
      console.error('Error clearing test data:', error);
      return false;
    }
  }
}
