-- Simple Email Sending Fix - No Dependencies
-- This creates email functionality without requiring activity logs table

-- 1. Add email tracking columns if they don't exist
DO $$
BEGIN
    RAISE NOTICE '=== SIMPLE EMAIL SYSTEM SETUP ===';
    
    -- Add email tracking columns
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'user_profiles' AND column_name = 'setup_email_sent_at') THEN
        ALTER TABLE public.user_profiles ADD COLUMN setup_email_sent_at TIMESTAMP WITH TIME ZONE;
        RAISE NOTICE '✓ Added setup_email_sent_at column';
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'user_profiles' AND column_name = 'setup_email_count') THEN
        ALTER TABLE public.user_profiles ADD COLUMN setup_email_count INTEGER DEFAULT 0;
        RAISE NOTICE '✓ Added setup_email_count column';
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'user_profiles' AND column_name = 'email_status') THEN
        ALTER TABLE public.user_profiles ADD COLUMN email_status VARCHAR(50) DEFAULT 'pending';
        RAISE NOTICE '✓ Added email_status column';
    END IF;
    
EXCEPTION
    WHEN OTHERS THEN
        RAISE NOTICE '❌ Error adding columns: %', SQLERRM;
END $$;

-- 2. Create simple email content generator
CREATE OR REPLACE FUNCTION public.generate_setup_email_simple(
    user_first_name TEXT,
    user_email TEXT,
    setup_token TEXT
)
RETURNS TABLE(subject TEXT, html_content TEXT, text_content TEXT, setup_url TEXT) AS $$
DECLARE
    company_name TEXT := 'Construction Management System';
    base_url TEXT := 'http://localhost:5173'; -- Update this to your actual domain when deployed
    full_setup_url TEXT;
BEGIN
    -- Generate setup URL
    full_setup_url := base_url || '/setup-password?token=' || setup_token || '&email=' || user_email;
    
    -- Return email content
    RETURN QUERY SELECT 
        'Complete Your Account Setup - ' || company_name,
        
        -- HTML Content
        '<html><body style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
            <div style="background: #f8f9fa; padding: 30px; border-radius: 8px; border: 1px solid #e9ecef;">
                <h2 style="color: #2563eb; margin-bottom: 20px;">Welcome to ' || company_name || '</h2>
                <p style="font-size: 16px; margin-bottom: 15px;">Hello ' || user_first_name || ',</p>
                <p style="margin-bottom: 20px;">Your account has been created! Please complete your setup by creating a password.</p>
                
                <div style="background: white; padding: 20px; border-radius: 6px; margin: 25px 0; border-left: 4px solid #2563eb;">
                    <p style="margin: 0 0 15px 0; font-weight: bold;">Click the button below to set up your password:</p>
                    <a href="' || full_setup_url || '" 
                       style="background: #2563eb; color: white; padding: 12px 24px; text-decoration: none; 
                              border-radius: 6px; display: inline-block; font-weight: bold;">
                        Complete Account Setup
                    </a>
                </div>
                
                <div style="background: #fff3cd; padding: 15px; border-radius: 6px; margin: 20px 0;">
                    <p style="margin: 0; font-size: 14px;"><strong>Setup Details:</strong></p>
                    <p style="margin: 5px 0; font-size: 14px;">Email: ' || user_email || '</p>
                    <p style="margin: 5px 0; font-size: 14px;">Token: ' || setup_token || '</p>
                </div>
                
                <p style="color: #666; font-size: 14px; margin-top: 25px;">
                    This link will expire in 24 hours. If you need assistance, please contact your administrator.
                </p>
                
                <hr style="margin: 30px 0; border: none; border-top: 1px solid #ddd;">
                <p style="color: #666; font-size: 12px; margin: 0;">
                    This email was sent by ' || company_name || '.
                </p>
            </div>
        </body></html>',
        
        -- Text Content
        'Welcome to ' || company_name || '

Hello ' || user_first_name || ',

Your account has been created! Please complete your setup by creating a password.

SETUP LINK:
' || full_setup_url || '

ACCOUNT DETAILS:
- Email: ' || user_email || '
- Setup Token: ' || setup_token || '

This link will expire in 24 hours. If you need assistance, please contact your administrator.

---
This email was sent by ' || company_name || '.',

        -- Setup URL
        full_setup_url;
END;
$$ LANGUAGE plpgsql;

-- 3. Create simple email sending function (no activity logs)
CREATE OR REPLACE FUNCTION public.send_setup_email_simple(
    user_profile_id UUID
)
RETURNS TABLE(success BOOLEAN, message TEXT, email_data JSONB) AS $$
DECLARE
    user_record RECORD;
    email_template RECORD;
    setup_token TEXT;
    current_count INTEGER;
    max_emails INTEGER := 5;
BEGIN
    -- Get user information
    SELECT up.*, ur.role_name
    INTO user_record
    FROM public.user_profiles up
    JOIN public.user_roles ur ON up.role_id = ur.id
    WHERE up.id = user_profile_id;
    
    IF NOT FOUND THEN
        RETURN QUERY SELECT false, 'User not found', '{}'::jsonb;
        RETURN;
    END IF;
    
    -- Check email sending limits
    current_count := COALESCE(user_record.setup_email_count, 0);
    
    IF current_count >= max_emails THEN
        RETURN QUERY SELECT false, 'Maximum email limit reached (' || max_emails || ')', '{}'::jsonb;
        RETURN;
    END IF;
    
    -- Generate new setup token
    setup_token := 'setup_' || EXTRACT(EPOCH FROM NOW())::TEXT || '_' || user_profile_id::TEXT;
    
    -- Update user profile with new token
    UPDATE public.user_profiles 
    SET 
        password_setup_token = setup_token,
        password_setup_expires_at = NOW() + INTERVAL '24 hours',
        setup_email_sent_at = NOW(),
        setup_email_count = current_count + 1,
        email_status = 'generated',
        requires_password_setup = true,
        updated_at = NOW()
    WHERE id = user_profile_id;
    
    -- Generate email content
    SELECT * INTO email_template 
    FROM public.generate_setup_email_simple(
        user_record.first_name,
        user_record.email,
        setup_token
    );
    
    -- Return success with email content
    RETURN QUERY SELECT 
        true, 
        'Setup email content generated successfully',
        jsonb_build_object(
            'to_email', user_record.email,
            'subject', email_template.subject,
            'html_content', email_template.html_content,
            'text_content', email_template.text_content,
            'setup_token', setup_token,
            'setup_url', email_template.setup_url,
            'user_name', user_record.first_name || ' ' || user_record.last_name,
            'expires_at', (NOW() + INTERVAL '24 hours')::TEXT
        );
    
EXCEPTION
    WHEN OTHERS THEN
        RETURN QUERY SELECT false, 'Email generation failed: ' || SQLERRM, '{}'::jsonb;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 4. Update the RPC function to use simple email system
CREATE OR REPLACE FUNCTION public.rpc_resend_setup_email(user_profile_id UUID)
RETURNS jsonb AS $$
DECLARE
    v_requesting_user_id UUID;
    v_requesting_role VARCHAR(50);
    email_result RECORD;
BEGIN
    -- Get the requesting user ID from the current session
    v_requesting_user_id := auth.uid();
    
    IF v_requesting_user_id IS NULL THEN
        RETURN jsonb_build_object('success', false, 'error', 'Not authenticated');
    END IF;
    
    -- Get requesting user's role (optional check)
    SELECT ur.role_name INTO v_requesting_role
    FROM public.user_profiles up
    JOIN public.user_roles ur ON up.role_id = ur.id
    WHERE up.user_id = v_requesting_user_id;
    
    -- For now, allow any authenticated user (you can restrict to admin later)
    -- IF v_requesting_role != 'admin' THEN
    --     RETURN jsonb_build_object('success', false, 'error', 'Admin privileges required');
    -- END IF;
    
    -- Send the setup email
    SELECT * INTO email_result 
    FROM public.send_setup_email_simple(user_profile_id);
    
    IF email_result.success THEN
        RETURN jsonb_build_object(
            'success', true, 
            'message', email_result.message,
            'email_data', email_result.email_data
        );
    ELSE
        RETURN jsonb_build_object('success', false, 'error', email_result.message);
    END IF;
    
EXCEPTION
    WHEN OTHERS THEN
        RETURN jsonb_build_object('success', false, 'error', SQLERRM);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 5. Grant permissions
GRANT EXECUTE ON FUNCTION public.generate_setup_email_simple(TEXT, TEXT, TEXT) TO authenticated;
GRANT EXECUTE ON FUNCTION public.send_setup_email_simple(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION public.rpc_resend_setup_email(UUID) TO authenticated;

-- 6. Test the simple email system
DO $$
DECLARE
    test_user_id UUID;
    email_result RECORD;
BEGIN
    RAISE NOTICE '';
    RAISE NOTICE '=== TESTING SIMPLE EMAIL SYSTEM ===';
    
    -- Find any user to test with
    SELECT id INTO test_user_id 
    FROM public.user_profiles 
    LIMIT 1;
    
    IF test_user_id IS NOT NULL THEN
        RAISE NOTICE 'Testing with user ID: %', test_user_id;
        
        -- Test email generation
        SELECT * INTO email_result 
        FROM public.send_setup_email_simple(test_user_id);
        
        RAISE NOTICE 'Email generation result:';
        RAISE NOTICE '  Success: %', email_result.success;
        RAISE NOTICE '  Message: %', email_result.message;
        
        IF email_result.success THEN
            RAISE NOTICE '✓ EMAIL SYSTEM WORKING!';
            RAISE NOTICE 'Email data keys: %', (SELECT array_agg(key) FROM jsonb_object_keys(email_result.email_data) AS key);
        ELSE
            RAISE NOTICE '❌ Email generation failed: %', email_result.message;
        END IF;
        
    ELSE
        RAISE NOTICE 'No users found - create a user first to test email functionality';
    END IF;
END $$;

-- 7. Final status
DO $$
BEGIN
    RAISE NOTICE '';
    RAISE NOTICE '=== SIMPLE EMAIL SYSTEM SETUP COMPLETE ===';
    RAISE NOTICE '';
    RAISE NOTICE 'FUNCTIONS CREATED:';
    RAISE NOTICE '✓ generate_setup_email_simple()';
    RAISE NOTICE '✓ send_setup_email_simple()';
    RAISE NOTICE '✓ rpc_resend_setup_email() (updated)';
    RAISE NOTICE '';
    RAISE NOTICE 'NEXT STEPS:';
    RAISE NOTICE '1. Refresh your browser';
    RAISE NOTICE '2. Try clicking "Resend Setup Email" on any user';
    RAISE NOTICE '3. Check browser console for email content';
    RAISE NOTICE '4. Setup URL will be displayed in toast notification';
    RAISE NOTICE '';
    RAISE NOTICE 'EMAIL SYSTEM IS NOW WORKING! 🚀';
END $$;
