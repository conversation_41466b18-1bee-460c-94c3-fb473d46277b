// Authentication debugging utilities
import { supabase } from '@/lib/supabase';

export interface AuthDebugInfo {
  profileExists: boolean;
  authUserExists: boolean;
  emailConfirmed: boolean;
  profileData?: any;
  authData?: any;
  mismatch: boolean;
}

export class AuthDebugger {
  // Check authentication status for a specific email
  static async checkAuthStatus(email: string): Promise<AuthDebugInfo> {
    try {
      // Check if profile exists
      const { data: profile, error: profileError } = await supabase
        .from('user_profiles')
        .select('*')
        .eq('email', email)
        .single();

      // Try to check auth user existence by attempting a password reset
      // This is the only reliable way to check if an auth user exists
      let authUserExists = false;
      let emailConfirmed = false;
      let authData = null;

      try {
        // Use a SQL function to check auth user existence
        const { data: authCheck, error: authError } = await supabase
          .rpc('check_auth_user_exists', { p_email: email });

        if (!authError && authCheck) {
          authUserExists = authCheck.exists;
          emailConfirmed = authCheck.email_confirmed;
          authData = authCheck.user_data;
        }
      } catch (authError) {
        console.log('Auth check via RPC failed, trying alternative method');

        // Alternative: Try to get current user if this is the logged-in user
        const { data: { user } } = await supabase.auth.getUser();
        if (user && user.email === email) {
          authUserExists = true;
          emailConfirmed = !!user.email_confirmed_at;
          authData = user;
        }
      }

      const result: AuthDebugInfo = {
        profileExists: !!profile && !profileError,
        authUserExists,
        emailConfirmed,
        profileData: profile,
        authData,
        mismatch: false
      };

      // Check for mismatches
      if (result.profileExists && result.authUserExists && profile && authData) {
        result.mismatch = profile.user_id !== authData.id;
      } else if (result.profileExists !== result.authUserExists) {
        result.mismatch = true;
      }

      return result;
    } catch (error) {
      console.error('Auth debug check failed:', error);
      return {
        profileExists: false,
        authUserExists: false,
        emailConfirmed: false,
        mismatch: true
      };
    }
  }

  // Get detailed auth information for debugging
  static async getDetailedAuthInfo(email: string): Promise<string> {
    const info = await this.checkAuthStatus(email);
    
    let report = `\n=== AUTH DEBUG REPORT FOR ${email} ===\n`;
    report += `Profile exists: ${info.profileExists}\n`;
    report += `Auth user exists: ${info.authUserExists}\n`;
    report += `Email confirmed: ${info.emailConfirmed}\n`;
    report += `Has mismatch: ${info.mismatch}\n\n`;

    if (info.profileData) {
      report += `Profile Data:\n`;
      report += `- ID: ${info.profileData.id}\n`;
      report += `- User ID: ${info.profileData.user_id || 'NULL'}\n`;
      report += `- Name: ${info.profileData.first_name} ${info.profileData.last_name}\n`;
      report += `- Role: ${info.profileData.role_name || 'Unknown'}\n`;
      report += `- Created: ${info.profileData.created_at}\n\n`;
    }

    if (info.authData) {
      report += `Auth Data:\n`;
      report += `- ID: ${info.authData.id}\n`;
      report += `- Email: ${info.authData.email}\n`;
      report += `- Confirmed: ${info.authData.email_confirmed_at || 'NOT CONFIRMED'}\n`;
      report += `- Last login: ${info.authData.last_sign_in_at || 'NEVER'}\n`;
      report += `- Created: ${info.authData.created_at}\n\n`;
    }

    if (info.mismatch) {
      report += `🚨 MISMATCH DETECTED:\n`;
      if (info.profileExists && !info.authUserExists) {
        report += `- Profile exists but no auth user found\n`;
        report += `- User needs to complete registration\n`;
      } else if (!info.profileExists && info.authUserExists) {
        report += `- Auth user exists but no profile found\n`;
        report += `- Profile creation failed during registration\n`;
      } else if (info.profileExists && info.authUserExists) {
        report += `- Profile and auth user have different IDs\n`;
        report += `- Data corruption or duplicate registration\n`;
      }
    }

    if (!info.emailConfirmed && info.authUserExists) {
      report += `📧 EMAIL NOT CONFIRMED:\n`;
      report += `- User needs to confirm email before login\n`;
      report += `- Check spam folder or resend confirmation\n`;
    }

    report += `\n=== END REPORT ===\n`;
    return report;
  }

  // Quick fix for common auth issues
  static async quickFix(email: string): Promise<{ success: boolean; message: string }> {
    try {
      const info = await this.checkAuthStatus(email);
      
      if (!info.profileExists && !info.authUserExists) {
        return {
          success: false,
          message: 'User does not exist. Please register first.'
        };
      }

      if (info.profileExists && !info.authUserExists) {
        return {
          success: false,
          message: 'Profile exists but auth user missing. Please contact admin to fix this account.'
        };
      }

      if (!info.profileExists && info.authUserExists) {
        return {
          success: false,
          message: 'Auth user exists but profile missing. Please complete registration.'
        };
      }

      if (!info.emailConfirmed) {
        return {
          success: false,
          message: 'Email not confirmed. Please check your email and click the confirmation link.'
        };
      }

      return {
        success: true,
        message: 'Account appears to be set up correctly. Try logging in again.'
      };
    } catch (error) {
      return {
        success: false,
        message: `Debug failed: ${error instanceof Error ? error.message : 'Unknown error'}`
      };
    }
  }
}

// Helper function to debug login issues
export const debugLoginIssue = async (email: string) => {
  console.log('🔍 Debugging login issue for:', email);
  const report = await AuthDebugger.getDetailedAuthInfo(email);
  console.log(report);
  
  const fix = await AuthDebugger.quickFix(email);
  console.log('💡 Quick fix suggestion:', fix.message);
  
  return { report, fix };
};
