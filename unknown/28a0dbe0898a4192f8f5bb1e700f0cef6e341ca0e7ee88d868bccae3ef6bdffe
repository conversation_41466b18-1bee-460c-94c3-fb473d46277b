-- Create function to create users directly in database
-- This bypasses Supabase Auth service issues

CREATE OR REPLACE FUNCTION public.create_user_directly(
    p_email VARCHAR(255),
    p_first_name VA<PERSON><PERSON><PERSON>(100),
    p_last_name <PERSON><PERSON><PERSON><PERSON>(100),
    p_role_id UUID,
    p_role_name <PERSON><PERSON><PERSON><PERSON>(50),
    p_password VARCHAR(255) DEFAULT 'TempPass123!'
)
RETURNS jsonb AS $$
DECLARE
    v_auth_user_id UUID;
    v_profile_id UUID;
    v_result jsonb;
BEGIN
    -- Check if email already exists
    IF EXISTS (SELECT 1 FROM public.user_profiles WHERE email = p_email) THEN
        RETURN jsonb_build_object(
            'success', false,
            'error', 'Email already exists: ' || p_email
        );
    END IF;
    
    -- Check if email exists in auth.users
    IF EXISTS (SELECT 1 FROM auth.users WHERE email = p_email) THEN
        RETURN jsonb_build_object(
            'success', false,
            'error', 'Auth user already exists for email: ' || p_email
        );
    END IF;
    
    -- Generate UUIDs
    v_auth_user_id := gen_random_uuid();
    v_profile_id := gen_random_uuid();
    
    BEGIN
        -- Create auth user
        INSERT INTO auth.users (
            id,
            instance_id,
            email,
            encrypted_password,
            email_confirmed_at,
            created_at,
            updated_at,
            raw_user_meta_data,
            raw_app_meta_data,
            aud,
            role
        ) VALUES (
            v_auth_user_id,
            '00000000-0000-0000-0000-000000000000',
            p_email,
            crypt(p_password, gen_salt('bf')),
            NOW(),
            NOW(),
            NOW(),
            jsonb_build_object(
                'first_name', p_first_name,
                'last_name', p_last_name,
                'email', p_email
            ),
            jsonb_build_object(
                'provider', 'email',
                'providers', ARRAY['email']
            ),
            'authenticated',
            'authenticated'
        );
        
        -- Create user profile
        INSERT INTO public.user_profiles (
            id,
            user_id,
            email,
            first_name,
            last_name,
            role_id,
            role_name,
            is_active,
            created_at,
            updated_at
        ) VALUES (
            v_profile_id,
            v_auth_user_id,
            p_email,
            p_first_name,
            p_last_name,
            p_role_id,
            p_role_name,
            true,
            NOW(),
            NOW()
        );
        
        -- Return success
        RETURN jsonb_build_object(
            'success', true,
            'message', 'User created successfully',
            'auth_user_id', v_auth_user_id,
            'profile_id', v_profile_id,
            'email', p_email,
            'full_name', p_first_name || ' ' || p_last_name,
            'role', p_role_name,
            'password', p_password
        );
        
    EXCEPTION
        WHEN OTHERS THEN
            -- Return error details
            RETURN jsonb_build_object(
                'success', false,
                'error', 'Failed to create user: ' || SQLERRM,
                'error_code', SQLSTATE
            );
    END;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant permission to use this function
GRANT EXECUTE ON FUNCTION public.create_user_directly(VARCHAR, VARCHAR, VARCHAR, UUID, VARCHAR, VARCHAR) TO authenticated;
GRANT EXECUTE ON FUNCTION public.create_user_directly(VARCHAR, VARCHAR, VARCHAR, UUID, VARCHAR, VARCHAR) TO anon;

-- Test the function
DO $$
DECLARE
    test_result jsonb;
    admin_role_id UUID;
BEGIN
    RAISE NOTICE '';
    RAISE NOTICE '=== TESTING create_user_directly FUNCTION ===';
    
    -- Get admin role ID
    SELECT id INTO admin_role_id FROM public.user_roles WHERE role_name = 'admin' LIMIT 1;
    
    IF admin_role_id IS NULL THEN
        RAISE NOTICE '❌ Admin role not found - cannot test function';
        RETURN;
    END IF;
    
    RAISE NOTICE 'Admin role ID: %', admin_role_id;
    
    -- Test creating a user (this will fail if email exists, which is expected)
    SELECT public.create_user_directly(
        '<EMAIL>',
        'Test',
        'User',
        admin_role_id,
        'admin',
        'TestPass123!'
    ) INTO test_result;
    
    RAISE NOTICE 'Test result: %', test_result;
    
    -- Clean up test user if created
    IF (test_result->>'success')::boolean THEN
        DELETE FROM public.user_profiles WHERE email = '<EMAIL>';
        DELETE FROM auth.users WHERE email = '<EMAIL>';
        RAISE NOTICE '✅ Cleaned up test user';
    END IF;
    
    RAISE NOTICE '';
    RAISE NOTICE '✅ Function create_user_directly is ready to use!';
    RAISE NOTICE '';
END $$;
