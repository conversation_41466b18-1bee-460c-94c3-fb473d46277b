-- Settings System Migration Script
-- Run this if you already have the settings tables but need to add missing columns

-- Add two_factor_enabled column to user_notification_preferences if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns
        WHERE table_schema = 'public'
        AND table_name = 'user_notification_preferences'
        AND column_name = 'two_factor_enabled'
    ) THEN
        ALTER TABLE public.user_notification_preferences
        ADD COLUMN two_factor_enabled BOOLEAN DEFAULT false;

        RAISE NOTICE 'Added two_factor_enabled column to user_notification_preferences table';
    ELSE
        RAISE NOTICE 'Column two_factor_enabled already exists in user_notification_preferences table';
    END IF;
END $$;

-- Create user_sessions table if it doesn't exist
CREATE TABLE IF NOT EXISTS public.user_sessions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    session_token VARCHAR(255) NOT NULL,
    ip_address INET,
    user_agent TEXT,
    last_activity TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Add missing columns to user_sessions if they don't exist
DO $$
BEGIN
    -- Check and add last_activity column
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns
        WHERE table_schema = 'public'
        AND table_name = 'user_sessions'
        AND column_name = 'last_activity'
    ) THEN
        ALTER TABLE public.user_sessions
        ADD COLUMN last_activity TIMESTAMP WITH TIME ZONE DEFAULT NOW();

        RAISE NOTICE 'Added last_activity column to user_sessions table';
    ELSE
        RAISE NOTICE 'Column last_activity already exists in user_sessions table';
    END IF;

    -- Check and add is_active column
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns
        WHERE table_schema = 'public'
        AND table_name = 'user_sessions'
        AND column_name = 'is_active'
    ) THEN
        ALTER TABLE public.user_sessions
        ADD COLUMN is_active BOOLEAN DEFAULT true;

        RAISE NOTICE 'Added is_active column to user_sessions table';
    ELSE
        RAISE NOTICE 'Column is_active already exists in user_sessions table';
    END IF;
END $$;

-- Create indexes for user_sessions table
CREATE INDEX IF NOT EXISTS idx_user_sessions_user_id ON public.user_sessions(user_id);
CREATE INDEX IF NOT EXISTS idx_user_sessions_token ON public.user_sessions(session_token);
CREATE INDEX IF NOT EXISTS idx_user_sessions_expires ON public.user_sessions(expires_at);
CREATE INDEX IF NOT EXISTS idx_user_sessions_last_activity ON public.user_sessions(last_activity);

-- Grant permissions on user_sessions table
GRANT ALL ON public.user_sessions TO authenticated;

-- Update the user_security_summary view to handle the corrected columns
DROP VIEW IF EXISTS user_security_summary;

-- Create a safer version of the view that handles missing tables gracefully
CREATE OR REPLACE VIEW user_security_summary AS
SELECT
    u.id as user_id,
    u.email,
    COALESCE(up.first_name, '') as first_name,
    COALESCE(up.last_name, '') as last_name,
    COALESCE(up.role_name, 'user') as role_name,
    COALESCE(unp.two_factor_enabled, false) as two_factor_enabled,
    COALESCE(unp.email_notifications, true) as email_notifications,
    COALESCE(session_data.active_sessions, 0) as active_sessions,
    session_data.last_activity,
    u.created_at as account_created
FROM auth.users u
LEFT JOIN public.user_profiles up ON u.id = up.user_id
LEFT JOIN public.user_notification_preferences unp ON u.id = unp.user_id
LEFT JOIN (
    SELECT
        user_id,
        COUNT(*) as active_sessions,
        MAX(last_activity) as last_activity
    FROM public.user_sessions
    WHERE is_active = true AND expires_at > NOW()
    GROUP BY user_id
) session_data ON u.id = session_data.user_id;

GRANT SELECT ON user_security_summary TO authenticated;

-- Ensure all existing users have default notification preferences
INSERT INTO public.user_notification_preferences (
    user_id,
    email_notifications,
    push_notifications,
    sms_notifications,
    project_updates,
    financial_alerts,
    time_tracking_reminders,
    team_messages,
    system_announcements,
    invoice_notifications,
    deadline_reminders,
    notification_frequency,
    quiet_hours_enabled,
    quiet_hours_start,
    quiet_hours_end,
    two_factor_enabled
)
SELECT 
    u.id,
    true,
    true,
    false,
    true,
    true,
    true,
    true,
    true,
    true,
    true,
    'immediate',
    false,
    '22:00',
    '08:00',
    false
FROM auth.users u
WHERE NOT EXISTS (
    SELECT 1 FROM public.user_notification_preferences unp 
    WHERE unp.user_id = u.id
);

-- Verify the migration
DO $$
DECLARE
    user_count INTEGER;
    pref_count INTEGER;
BEGIN
    SELECT COUNT(*) INTO user_count FROM auth.users;
    SELECT COUNT(*) INTO pref_count FROM public.user_notification_preferences;
    
    RAISE NOTICE 'Migration completed: % users, % notification preferences', user_count, pref_count;
    
    IF user_count > pref_count THEN
        RAISE WARNING 'Some users may not have notification preferences set up';
    ELSE
        RAISE NOTICE 'All users have notification preferences configured';
    END IF;
END $$;
