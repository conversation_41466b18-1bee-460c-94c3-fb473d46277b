import React, { useState } from 'react';
import {
  Video,
  Play,
  Clock,
  Star,
  Search,
  Filter,
  BookOpen,
  Users,
  Building2,
  CreditCard,
  Settings,
  BarChart3,
  Shield,
  Zap,
  ExternalLink,
  PlayCircle
} from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';

interface VideoTutorial {
  id: string;
  title: string;
  description: string;
  duration: string;
  difficulty: 'Beginner' | 'Intermediate' | 'Advanced';
  category: string;
  thumbnail: string;
  views: number;
  rating: number;
  popular?: boolean;
  new?: boolean;
}

const VideoTutorials: React.FC = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [selectedDifficulty, setSelectedDifficulty] = useState('all');

  const videoTutorials: VideoTutorial[] = [
    {
      id: '1',
      title: 'Getting Started with Martcosy',
      description: 'A comprehensive introduction to Martcosy Construction Management platform',
      duration: '12:34',
      difficulty: 'Beginner',
      category: 'Getting Started',
      thumbnail: '/api/placeholder/320/180',
      views: 15420,
      rating: 4.9,
      popular: true
    },
    {
      id: '2',
      title: 'Creating Your First Project',
      description: 'Step-by-step guide to setting up your first construction project',
      duration: '8:45',
      difficulty: 'Beginner',
      category: 'Project Management',
      thumbnail: '/api/placeholder/320/180',
      views: 12350,
      rating: 4.8,
      popular: true
    },
    {
      id: '3',
      title: 'Advanced Gantt Chart Features',
      description: 'Master the advanced features of Gantt charts for complex project scheduling',
      duration: '18:22',
      difficulty: 'Advanced',
      category: 'Project Management',
      thumbnail: '/api/placeholder/320/180',
      views: 8920,
      rating: 4.7
    },
    {
      id: '4',
      title: 'Invoice Management Masterclass',
      description: 'Complete guide to creating, managing, and tracking invoices',
      duration: '15:10',
      difficulty: 'Intermediate',
      category: 'Financial Management',
      thumbnail: '/api/placeholder/320/180',
      views: 9840,
      rating: 4.8,
      popular: true
    },
    {
      id: '5',
      title: 'User Roles and Permissions Setup',
      description: 'Configure user roles and permissions for your team',
      duration: '11:30',
      difficulty: 'Intermediate',
      category: 'User Management',
      thumbnail: '/api/placeholder/320/180',
      views: 7650,
      rating: 4.6
    },
    {
      id: '6',
      title: 'Financial Reports and Analytics',
      description: 'Generate comprehensive financial reports and understand your data',
      duration: '14:55',
      difficulty: 'Intermediate',
      category: 'Reports & Analytics',
      thumbnail: '/api/placeholder/320/180',
      views: 6780,
      rating: 4.7
    },
    {
      id: '7',
      title: 'Mobile App Overview',
      description: 'Learn how to use Martcosy on mobile devices',
      duration: '9:20',
      difficulty: 'Beginner',
      category: 'Getting Started',
      thumbnail: '/api/placeholder/320/180',
      views: 11200,
      rating: 4.5,
      new: true
    },
    {
      id: '8',
      title: 'Advanced Security Settings',
      description: 'Configure advanced security features and best practices',
      duration: '16:40',
      difficulty: 'Advanced',
      category: 'Security',
      thumbnail: '/api/placeholder/320/180',
      views: 4320,
      rating: 4.9
    }
  ];

  const categories = [
    'Getting Started',
    'Project Management',
    'Financial Management',
    'User Management',
    'Reports & Analytics',
    'Security',
    'Settings'
  ];

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'Beginner':
        return 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400';
      case 'Intermediate':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400';
      case 'Advanced':
        return 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400';
    }
  };

  const filteredVideos = videoTutorials.filter(video => {
    const matchesSearch = video.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         video.description.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesCategory = selectedCategory === 'all' || video.category === selectedCategory;
    const matchesDifficulty = selectedDifficulty === 'all' || video.difficulty === selectedDifficulty;
    
    return matchesSearch && matchesCategory && matchesDifficulty;
  });

  const popularVideos = videoTutorials.filter(video => video.popular);
  const newVideos = videoTutorials.filter(video => video.new);

  const formatViews = (views: number) => {
    if (views >= 1000) {
      return `${(views / 1000).toFixed(1)}K`;
    }
    return views.toString();
  };

  const playVideo = (videoId: string) => {
    // In a real implementation, this would open a video player or navigate to the video
    window.open(`https://help.martcosy.com/videos/${videoId}`, '_blank');
  };

  return (
    <div className="space-y-8 p-6 max-w-7xl mx-auto">
      {/* Header */}
      <div className="text-center space-y-4">
        <div className="flex items-center justify-center space-x-3">
          <div className="w-12 h-12 rounded-xl bg-red-50 dark:bg-red-900/20 flex items-center justify-center">
            <Video className="w-6 h-6 text-red-600 dark:text-red-400" />
          </div>
          <div>
            <h1 className="text-3xl font-bold text-gray-900 dark:text-white">Video Tutorials</h1>
            <p className="text-gray-600 dark:text-gray-400">Learn Martcosy with step-by-step video guides</p>
          </div>
        </div>

        {/* Search and Filters */}
        <div className="max-w-4xl mx-auto space-y-4">
          <div className="relative">
            <Search className="absolute left-3 top-3 h-5 w-5 text-gray-400" />
            <Input
              placeholder="Search video tutorials..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10 py-3"
            />
          </div>
          
          <div className="flex flex-wrap gap-4 justify-center">
            <Select value={selectedCategory} onValueChange={setSelectedCategory}>
              <SelectTrigger className="w-48">
                <SelectValue placeholder="All Categories" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Categories</SelectItem>
                {categories.map(category => (
                  <SelectItem key={category} value={category}>{category}</SelectItem>
                ))}
              </SelectContent>
            </Select>
            
            <Select value={selectedDifficulty} onValueChange={setSelectedDifficulty}>
              <SelectTrigger className="w-48">
                <SelectValue placeholder="All Levels" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Levels</SelectItem>
                <SelectItem value="Beginner">Beginner</SelectItem>
                <SelectItem value="Intermediate">Intermediate</SelectItem>
                <SelectItem value="Advanced">Advanced</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>
      </div>

      {/* Video Sections */}
      <Tabs defaultValue="all" className="w-full">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="all">All Videos</TabsTrigger>
          <TabsTrigger value="popular">Popular</TabsTrigger>
          <TabsTrigger value="new">New</TabsTrigger>
        </TabsList>

        <TabsContent value="all" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredVideos.map((video) => (
              <Card key={video.id} className="cursor-pointer hover:shadow-lg transition-shadow group">
                <div className="relative">
                  <div className="aspect-video bg-gray-200 dark:bg-gray-700 rounded-t-lg relative overflow-hidden">
                    <div className="absolute inset-0 bg-gradient-to-br from-blue-500 to-purple-600 opacity-20"></div>
                    <div className="absolute inset-0 flex items-center justify-center">
                      <div className="w-16 h-16 rounded-full bg-white/20 backdrop-blur-sm flex items-center justify-center group-hover:bg-white/30 transition-colors">
                        <Play className="w-8 h-8 text-white ml-1" />
                      </div>
                    </div>
                    <div className="absolute bottom-2 right-2 bg-black/70 text-white text-xs px-2 py-1 rounded">
                      {video.duration}
                    </div>
                    {video.popular && (
                      <Badge className="absolute top-2 left-2 bg-orange-500 text-white">
                        <Star className="w-3 h-3 mr-1" />
                        Popular
                      </Badge>
                    )}
                    {video.new && (
                      <Badge className="absolute top-2 left-2 bg-green-500 text-white">
                        New
                      </Badge>
                    )}
                  </div>
                </div>
                <CardContent className="p-4" onClick={() => playVideo(video.id)}>
                  <div className="space-y-3">
                    <div>
                      <h3 className="font-semibold text-gray-900 dark:text-white line-clamp-2">{video.title}</h3>
                      <p className="text-sm text-gray-600 dark:text-gray-400 line-clamp-2 mt-1">{video.description}</p>
                    </div>
                    
                    <div className="flex items-center justify-between">
                      <Badge className={`text-xs ${getDifficultyColor(video.difficulty)}`}>
                        {video.difficulty}
                      </Badge>
                      <span className="text-xs text-gray-500">{video.category}</span>
                    </div>
                    
                    <div className="flex items-center justify-between text-xs text-gray-500">
                      <div className="flex items-center gap-3">
                        <span>{formatViews(video.views)} views</span>
                        <div className="flex items-center gap-1">
                          <Star className="w-3 h-3 fill-yellow-400 text-yellow-400" />
                          <span>{video.rating}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="popular" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {popularVideos.map((video) => (
              <Card key={video.id} className="cursor-pointer hover:shadow-lg transition-shadow group">
                <div className="relative">
                  <div className="aspect-video bg-gray-200 dark:bg-gray-700 rounded-t-lg relative overflow-hidden">
                    <div className="absolute inset-0 bg-gradient-to-br from-orange-500 to-red-600 opacity-20"></div>
                    <div className="absolute inset-0 flex items-center justify-center">
                      <div className="w-16 h-16 rounded-full bg-white/20 backdrop-blur-sm flex items-center justify-center group-hover:bg-white/30 transition-colors">
                        <Play className="w-8 h-8 text-white ml-1" />
                      </div>
                    </div>
                    <div className="absolute bottom-2 right-2 bg-black/70 text-white text-xs px-2 py-1 rounded">
                      {video.duration}
                    </div>
                    <Badge className="absolute top-2 left-2 bg-orange-500 text-white">
                      <Star className="w-3 h-3 mr-1" />
                      Popular
                    </Badge>
                  </div>
                </div>
                <CardContent className="p-4" onClick={() => playVideo(video.id)}>
                  <div className="space-y-3">
                    <div>
                      <h3 className="font-semibold text-gray-900 dark:text-white line-clamp-2">{video.title}</h3>
                      <p className="text-sm text-gray-600 dark:text-gray-400 line-clamp-2 mt-1">{video.description}</p>
                    </div>
                    
                    <div className="flex items-center justify-between">
                      <Badge className={`text-xs ${getDifficultyColor(video.difficulty)}`}>
                        {video.difficulty}
                      </Badge>
                      <span className="text-xs text-gray-500">{video.category}</span>
                    </div>
                    
                    <div className="flex items-center justify-between text-xs text-gray-500">
                      <div className="flex items-center gap-3">
                        <span>{formatViews(video.views)} views</span>
                        <div className="flex items-center gap-1">
                          <Star className="w-3 h-3 fill-yellow-400 text-yellow-400" />
                          <span>{video.rating}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="new" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {newVideos.map((video) => (
              <Card key={video.id} className="cursor-pointer hover:shadow-lg transition-shadow group">
                <div className="relative">
                  <div className="aspect-video bg-gray-200 dark:bg-gray-700 rounded-t-lg relative overflow-hidden">
                    <div className="absolute inset-0 bg-gradient-to-br from-green-500 to-blue-600 opacity-20"></div>
                    <div className="absolute inset-0 flex items-center justify-center">
                      <div className="w-16 h-16 rounded-full bg-white/20 backdrop-blur-sm flex items-center justify-center group-hover:bg-white/30 transition-colors">
                        <Play className="w-8 h-8 text-white ml-1" />
                      </div>
                    </div>
                    <div className="absolute bottom-2 right-2 bg-black/70 text-white text-xs px-2 py-1 rounded">
                      {video.duration}
                    </div>
                    <Badge className="absolute top-2 left-2 bg-green-500 text-white">
                      New
                    </Badge>
                  </div>
                </div>
                <CardContent className="p-4" onClick={() => playVideo(video.id)}>
                  <div className="space-y-3">
                    <div>
                      <h3 className="font-semibold text-gray-900 dark:text-white line-clamp-2">{video.title}</h3>
                      <p className="text-sm text-gray-600 dark:text-gray-400 line-clamp-2 mt-1">{video.description}</p>
                    </div>
                    
                    <div className="flex items-center justify-between">
                      <Badge className={`text-xs ${getDifficultyColor(video.difficulty)}`}>
                        {video.difficulty}
                      </Badge>
                      <span className="text-xs text-gray-500">{video.category}</span>
                    </div>
                    
                    <div className="flex items-center justify-between text-xs text-gray-500">
                      <div className="flex items-center gap-3">
                        <span>{formatViews(video.views)} views</span>
                        <div className="flex items-center gap-1">
                          <Star className="w-3 h-3 fill-yellow-400 text-yellow-400" />
                          <span>{video.rating}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>
      </Tabs>

      {/* Call to Action */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <PlayCircle className="w-5 h-5" />
            Request a Tutorial
          </CardTitle>
          <CardDescription>
            Don't see a tutorial for what you need? Let us know what you'd like to learn!
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Button onClick={() => window.open('https://help.martcosy.com/request-tutorial', '_blank')}>
            Request Tutorial
            <ExternalLink className="w-4 h-4 ml-2" />
          </Button>
        </CardContent>
      </Card>
    </div>
  );
};

export default VideoTutorials;
