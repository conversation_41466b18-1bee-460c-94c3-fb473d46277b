import { supabase } from './supabase';

// Types for Company Assets
export interface AssetCategory {
  id: string;
  name: string;
  description?: string;
  icon?: string;
  created_at: string;
  updated_at: string;
}

export interface CompanyAsset {
  id: string;
  asset_number: string;
  name: string;
  description?: string;
  category_id?: string;
  asset_type: 'Equipment' | 'Vehicle' | 'Property' | 'Tool' | 'Machinery' | 'Technology';
  
  // Financial Information
  purchase_price: number;
  current_value: number;
  depreciation_rate?: number;
  purchase_date: string;
  warranty_expiry?: string;
  
  // Asset Details
  brand?: string;
  model?: string;
  serial_number?: string;
  condition: 'Excellent' | 'Good' | 'Fair' | 'Poor' | 'Needs Repair';
  status: 'Active' | 'Inactive' | 'Under Maintenance' | 'Disposed' | 'Lost' | 'Sold';
  
  // Location and Assignment
  location?: string;
  assigned_to?: string;
  project_id?: string;
  
  // Maintenance Information
  last_maintenance_date?: string;
  next_maintenance_date?: string;
  maintenance_interval_months?: number;
  maintenance_cost_ytd?: number;
  
  // Insurance and Compliance
  insurance_policy?: string;
  insurance_expiry?: string;
  compliance_certificates?: string[];
  
  // Additional Information
  notes?: string;
  attachments?: any;
  tags?: string[];
  
  // Audit Trail
  created_by?: string;
  updated_by?: string;
  created_at: string;
  updated_at: string;
  
  // Joined data
  category?: AssetCategory;
}

export interface AssetMaintenance {
  id: string;
  asset_id: string;
  maintenance_type: 'Preventive' | 'Corrective' | 'Emergency' | 'Inspection' | 'Upgrade';
  description: string;
  
  // Scheduling
  scheduled_date?: string;
  completed_date?: string;
  next_due_date?: string;
  
  // Cost Information
  cost?: number;
  labor_hours?: number;
  parts_cost?: number;
  external_service_cost?: number;
  
  // Service Provider
  service_provider?: string;
  technician?: string;
  
  // Status and Priority
  status: 'Scheduled' | 'In Progress' | 'Completed' | 'Cancelled' | 'Overdue';
  priority: 'Low' | 'Medium' | 'High' | 'Critical';
  
  // Documentation
  work_performed?: string;
  parts_replaced?: string[];
  recommendations?: string;
  attachments?: any;
  
  // Audit Trail
  created_by?: string;
  updated_by?: string;
  created_at: string;
  updated_at: string;
  
  // Joined data
  asset?: CompanyAsset;
}

export interface AssetSummary {
  totalAssets: number;
  totalValue: number;
  totalDepreciation: number;
  activeAssets: number;
  maintenanceDue: number;
  byCategory: { [key: string]: number };
  byStatus: { [key: string]: number };
  byType: { [key: string]: number };
}

export class AssetService {
  // Asset Categories
  static async getAssetCategories(): Promise<AssetCategory[]> {
    const { data, error } = await supabase
      .from('asset_categories')
      .select('*')
      .order('name');

    if (error) throw error;
    return data || [];
  }

  static async createAssetCategory(category: Omit<AssetCategory, 'id' | 'created_at' | 'updated_at'>): Promise<AssetCategory> {
    const { data, error } = await supabase
      .from('asset_categories')
      .insert([category])
      .select()
      .single();

    if (error) throw error;
    return data;
  }

  // Company Assets
  static async getAssets(filters?: {
    category_id?: string;
    asset_type?: string;
    status?: string;
    assigned_to?: string;
  }): Promise<CompanyAsset[]> {
    let query = supabase
      .from('company_assets')
      .select(`
        *,
        category:asset_categories(*)
      `)
      .order('created_at', { ascending: false });

    if (filters?.category_id) {
      query = query.eq('category_id', filters.category_id);
    }
    if (filters?.asset_type) {
      query = query.eq('asset_type', filters.asset_type);
    }
    if (filters?.status) {
      query = query.eq('status', filters.status);
    }
    if (filters?.assigned_to) {
      query = query.eq('assigned_to', filters.assigned_to);
    }

    const { data, error } = await query;
    if (error) throw error;
    return data || [];
  }

  static async getAssetById(id: string): Promise<CompanyAsset | null> {
    const { data, error } = await supabase
      .from('company_assets')
      .select(`
        *,
        category:asset_categories(*)
      `)
      .eq('id', id)
      .single();

    if (error) {
      if (error.code === 'PGRST116') return null;
      throw error;
    }
    return data;
  }

  static async createAsset(asset: Omit<CompanyAsset, 'id' | 'created_at' | 'updated_at' | 'category'>): Promise<CompanyAsset> {
    const { data, error } = await supabase
      .from('company_assets')
      .insert([asset])
      .select(`
        *,
        category:asset_categories(*)
      `)
      .single();

    if (error) throw error;
    return data;
  }

  static async updateAsset(id: string, updates: Partial<CompanyAsset>): Promise<CompanyAsset> {
    const { data, error } = await supabase
      .from('company_assets')
      .update(updates)
      .eq('id', id)
      .select(`
        *,
        category:asset_categories(*)
      `)
      .single();

    if (error) throw error;
    return data;
  }

  static async deleteAsset(id: string): Promise<void> {
    const { error } = await supabase
      .from('company_assets')
      .delete()
      .eq('id', id);

    if (error) throw error;
  }

  // Asset Maintenance
  static async getMaintenanceRecords(assetId?: string): Promise<AssetMaintenance[]> {
    let query = supabase
      .from('asset_maintenance')
      .select(`
        *,
        asset:company_assets(*)
      `)
      .order('created_at', { ascending: false });

    if (assetId) {
      query = query.eq('asset_id', assetId);
    }

    const { data, error } = await query;
    if (error) {
      console.error('Error fetching maintenance records:', error);
      throw error;
    }

    console.log(`Fetched ${data?.length || 0} maintenance records for asset ${assetId || 'all'}`);
    return data || [];
  }

  static async createMaintenanceRecord(maintenance: Omit<AssetMaintenance, 'id' | 'created_at' | 'updated_at' | 'asset'>): Promise<AssetMaintenance> {
    console.log('Creating maintenance record with data:', maintenance);

    const { data, error } = await supabase
      .from('asset_maintenance')
      .insert([maintenance])
      .select(`
        *,
        asset:company_assets(*)
      `)
      .single();

    if (error) {
      console.error('Error creating maintenance record:', error);
      throw error;
    }

    console.log('Successfully created maintenance record:', data);
    return data;
  }

  static async updateMaintenanceRecord(id: string, updates: Partial<AssetMaintenance>): Promise<AssetMaintenance> {
    const { data, error } = await supabase
      .from('asset_maintenance')
      .update(updates)
      .eq('id', id)
      .select(`
        *,
        asset:company_assets(*)
      `)
      .single();

    if (error) throw error;
    return data;
  }

  // Asset Summary and Analytics
  static async getAssetSummary(): Promise<AssetSummary> {
    const { data: assets, error } = await supabase
      .from('company_assets')
      .select(`
        *,
        category:asset_categories(name)
      `);

    if (error) throw error;

    const summary: AssetSummary = {
      totalAssets: assets?.length || 0,
      totalValue: 0,
      totalDepreciation: 0,
      activeAssets: 0,
      maintenanceDue: 0,
      byCategory: {},
      byStatus: {},
      byType: {}
    };

    if (assets) {
      for (const asset of assets) {
        summary.totalValue += asset.current_value || 0;
        summary.totalDepreciation += (asset.purchase_price || 0) - (asset.current_value || 0);
        
        if (asset.status === 'Active') {
          summary.activeAssets++;
        }

        // Count by category
        const categoryName = asset.category?.name || 'Uncategorized';
        summary.byCategory[categoryName] = (summary.byCategory[categoryName] || 0) + 1;

        // Count by status
        summary.byStatus[asset.status] = (summary.byStatus[asset.status] || 0) + 1;

        // Count by type
        summary.byType[asset.asset_type] = (summary.byType[asset.asset_type] || 0) + 1;
      }
    }

    // Get maintenance due count
    const { data: maintenanceDue } = await supabase
      .from('asset_maintenance')
      .select('id')
      .eq('status', 'Scheduled')
      .lte('next_due_date', new Date().toISOString().split('T')[0]);

    summary.maintenanceDue = maintenanceDue?.length || 0;

    return summary;
  }

  // Generate Asset Number
  static async generateAssetNumber(assetType: string): Promise<string> {
    const prefix = assetType.substring(0, 3).toUpperCase();
    const { data, error } = await supabase
      .from('company_assets')
      .select('asset_number')
      .like('asset_number', `${prefix}%`)
      .order('asset_number', { ascending: false })
      .limit(1);

    if (error) throw error;

    let nextNumber = 1;
    if (data && data.length > 0) {
      const lastNumber = data[0].asset_number;
      const numberPart = lastNumber.replace(prefix, '');
      nextNumber = parseInt(numberPart) + 1;
    }

    return `${prefix}${nextNumber.toString().padStart(4, '0')}`;
  }
}
