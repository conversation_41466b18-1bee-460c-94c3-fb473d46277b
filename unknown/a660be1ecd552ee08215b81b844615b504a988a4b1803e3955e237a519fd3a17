import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Database, ExternalLink, CheckCircle, AlertTriangle } from 'lucide-react';

const DatabaseSetupNotice = () => {
  const handleOpenSupabase = () => {
    window.open('https://supabase.com/dashboard', '_blank');
  };

  return (
    <Card className="border-blue-200 bg-blue-50">
      <CardHeader>
        <CardTitle className="flex items-center gap-2 text-blue-900">
          <Database className="h-5 w-5" />
          Database Setup Required
        </CardTitle>
        <CardDescription className="text-blue-700">
          To enable full Settings functionality, please set up the database tables.
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <Alert>
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            The Settings system is currently running in local mode. Changes will not be saved permanently until the database is configured.
          </AlertDescription>
        </Alert>

        <div className="space-y-3">
          <h4 className="font-medium text-blue-900">Quick Setup Steps:</h4>
          <ol className="space-y-2 text-sm text-blue-800">
            <li className="flex items-start gap-2">
              <span className="flex-shrink-0 w-5 h-5 bg-blue-200 text-blue-900 rounded-full flex items-center justify-center text-xs font-medium">1</span>
              <span>Open your Supabase dashboard</span>
            </li>
            <li className="flex items-start gap-2">
              <span className="flex-shrink-0 w-5 h-5 bg-blue-200 text-blue-900 rounded-full flex items-center justify-center text-xs font-medium">2</span>
              <span>Navigate to SQL Editor</span>
            </li>
            <li className="flex items-start gap-2">
              <span className="flex-shrink-0 w-5 h-5 bg-blue-200 text-blue-900 rounded-full flex items-center justify-center text-xs font-medium">3</span>
              <span>Copy and run the <code className="bg-blue-100 px-1 rounded">database/settings_simple.sql</code> file</span>
            </li>
            <li className="flex items-start gap-2">
              <span className="flex-shrink-0 w-5 h-5 bg-blue-200 text-blue-900 rounded-full flex items-center justify-center text-xs font-medium">4</span>
              <span>Refresh this page</span>
            </li>
          </ol>
        </div>

        <div className="bg-blue-100 p-3 rounded-md">
          <h5 className="font-medium text-blue-900 mb-2">What you'll get after setup:</h5>
          <ul className="space-y-1 text-sm text-blue-800">
            <li className="flex items-center gap-2">
              <CheckCircle className="h-3 w-3 text-green-600" />
              Persistent password changes
            </li>
            <li className="flex items-center gap-2">
              <CheckCircle className="h-3 w-3 text-green-600" />
              Saved notification preferences
            </li>
            <li className="flex items-center gap-2">
              <CheckCircle className="h-3 w-3 text-green-600" />
              Profile information storage
            </li>
            <li className="flex items-center gap-2">
              <CheckCircle className="h-3 w-3 text-green-600" />
              System configuration management
            </li>
          </ul>
        </div>

        <div className="flex gap-2">
          <Button onClick={handleOpenSupabase} className="flex-1">
            <ExternalLink className="h-4 w-4 mr-2" />
            Open Supabase Dashboard
          </Button>
          <Button variant="outline" onClick={() => window.location.reload()}>
            Refresh Page
          </Button>
        </div>
      </CardContent>
    </Card>
  );
};

export default DatabaseSetupNotice;
