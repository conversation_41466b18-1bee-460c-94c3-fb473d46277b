-- Fix role_name column issue in user_profiles table

-- 1. Check if role_name column exists
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 
        FROM information_schema.columns 
        WHERE table_name = 'user_profiles' 
        AND column_name = 'role_name'
        AND table_schema = 'public'
    ) THEN
        -- Add role_name column if it doesn't exist
        ALTER TABLE public.user_profiles 
        ADD COLUMN role_name VARCHAR(50);
        
        RAISE NOTICE 'Added role_name column to user_profiles table';
        
        -- Update existing records to have role_name based on role_id
        UPDATE public.user_profiles 
        SET role_name = ur.role_name
        FROM public.user_roles ur
        WHERE public.user_profiles.role_id = ur.id;
        
        RAISE NOTICE 'Updated existing user_profiles with role_name values';
    ELSE
        RAISE NOTICE 'role_name column already exists in user_profiles table';
    END IF;
END $$;

-- 2. Ensure all required columns exist
DO $$
BEGIN
    -- Check and add phone column
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'user_profiles' AND column_name = 'phone'
    ) THEN
        ALTER TABLE public.user_profiles ADD COLUMN phone VARCHAR(20);
        RAISE NOTICE 'Added phone column';
    END IF;
    
    -- Check and add department column
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'user_profiles' AND column_name = 'department'
    ) THEN
        ALTER TABLE public.user_profiles ADD COLUMN department VARCHAR(100);
        RAISE NOTICE 'Added department column';
    END IF;
    
    -- Check and add job_title column
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'user_profiles' AND column_name = 'job_title'
    ) THEN
        ALTER TABLE public.user_profiles ADD COLUMN job_title VARCHAR(100);
        RAISE NOTICE 'Added job_title column';
    END IF;
    
    -- Check and add is_active column
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'user_profiles' AND column_name = 'is_active'
    ) THEN
        ALTER TABLE public.user_profiles ADD COLUMN is_active BOOLEAN DEFAULT true;
        RAISE NOTICE 'Added is_active column';
    END IF;
END $$;

-- 3. Show current table structure
DO $$
DECLARE
    col_record RECORD;
BEGIN
    RAISE NOTICE '';
    RAISE NOTICE '=== CURRENT user_profiles TABLE STRUCTURE ===';
    FOR col_record IN 
        SELECT column_name, data_type, is_nullable, column_default
        FROM information_schema.columns 
        WHERE table_name = 'user_profiles' 
        AND table_schema = 'public'
        ORDER BY ordinal_position
    LOOP
        RAISE NOTICE 'Column: % | Type: % | Nullable: % | Default: %', 
            col_record.column_name, 
            col_record.data_type, 
            col_record.is_nullable,
            COALESCE(col_record.column_default, 'NULL');
    END LOOP;
    RAISE NOTICE '';
END $$;
