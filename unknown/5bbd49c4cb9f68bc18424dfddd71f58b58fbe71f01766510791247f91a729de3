import { useEffect } from 'react';

interface KeyboardShortcut {
  key: string;
  metaKey?: boolean;
  ctrlKey?: boolean;
  shiftKey?: boolean;
  altKey?: boolean;
  callback: () => void;
  description?: string;
}

export const useKeyboardShortcuts = (shortcuts: KeyboardShortcut[]) => {
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      for (const shortcut of shortcuts) {
        const keyMatch = event.key.toLowerCase() === shortcut.key.toLowerCase();
        const metaMatch = !!shortcut.metaKey === !!event.metaKey;
        const ctrlMatch = !!shortcut.ctrlKey === !!event.ctrlKey;
        const shiftMatch = !!shortcut.shiftKey === !!event.shiftKey;
        const altMatch = !!shortcut.altKey === !!event.altKey;

        if (keyMatch && metaMatch && ctrlMatch && shiftMatch && altMatch) {
          event.preventDefault();
          event.stopPropagation();
          shortcut.callback();
          break;
        }
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [shortcuts]);
};

// Global keyboard shortcuts
export const useGlobalKeyboardShortcuts = (callbacks: {
  openSearch?: () => void;
  openQuickActions?: () => void;
  openNotifications?: () => void;
  openMessages?: () => void;
}) => {
  useKeyboardShortcuts([
    {
      key: 'k',
      metaKey: true,
      callback: callbacks.openSearch || (() => {}),
      description: 'Open search'
    },
    {
      key: 'k',
      ctrlKey: true,
      callback: callbacks.openSearch || (() => {}),
      description: 'Open search (Windows/Linux)'
    },
    {
      key: 'p',
      metaKey: true,
      shiftKey: true,
      callback: callbacks.openQuickActions || (() => {}),
      description: 'Open quick actions'
    },
    {
      key: 'p',
      ctrlKey: true,
      shiftKey: true,
      callback: callbacks.openQuickActions || (() => {}),
      description: 'Open quick actions (Windows/Linux)'
    },
    {
      key: 'n',
      metaKey: true,
      callback: callbacks.openNotifications || (() => {}),
      description: 'Open notifications'
    },
    {
      key: 'n',
      ctrlKey: true,
      callback: callbacks.openNotifications || (() => {}),
      description: 'Open notifications (Windows/Linux)'
    },
    {
      key: 'm',
      metaKey: true,
      callback: callbacks.openMessages || (() => {}),
      description: 'Open messages'
    },
    {
      key: 'm',
      ctrlKey: true,
      callback: callbacks.openMessages || (() => {}),
      description: 'Open messages (Windows/Linux)'
    }
  ]);
};
