-- Fix Missing <NAME_EMAIL>
-- This creates the missing user profile for the existing auth user

-- 1. First, let's get the auth user information
DO $$
DECLARE
    v_auth_user RECORD;
    v_role_id UUID;
    v_profile_id UUID;
BEGIN
    RAISE NOTICE '';
    RAISE NOTICE '=== FIXING MISSING PROFILE ===';
    
    -- Get the auth user details
    SELECT * INTO v_auth_user
    FROM auth.users
    WHERE email = '<EMAIL>';
    
    IF v_auth_user.id IS NULL THEN
        RAISE NOTICE '❌ No auth user <NAME_EMAIL>';
        RETURN;
    END IF;
    
    RAISE NOTICE '✓ Found auth user: %', v_auth_user.id;
    RAISE NOTICE '  Email: %', v_auth_user.email;
    RAISE NOTICE '  Created: %', v_auth_user.created_at;
    RAISE NOTICE '  Confirmed: %', v_auth_user.email_confirmed_at;
    
    -- Get a default role (client role)
    SELECT id INTO v_role_id
    FROM public.user_roles
    WHERE role_name = 'client'
    LIMIT 1;
    
    IF v_role_id IS NULL THEN
        -- If no client role, get any role
        SELECT id INTO v_role_id
        FROM public.user_roles
        WHERE is_active = true
        LIMIT 1;
    END IF;
    
    IF v_role_id IS NULL THEN
        RAISE NOTICE '❌ No roles found in database';
        RETURN;
    END IF;
    
    RAISE NOTICE '✓ Using role ID: %', v_role_id;
    
    -- Create the missing profile
    INSERT INTO public.user_profiles (
        user_id,
        email,
        first_name,
        last_name,
        role_id,
        is_verified,
        is_active,
        email_status,
        created_at,
        updated_at
    ) VALUES (
        v_auth_user.id,
        v_auth_user.email,
        COALESCE(v_auth_user.raw_user_meta_data->>'first_name', 'User'),
        COALESCE(v_auth_user.raw_user_meta_data->>'last_name', ''),
        v_role_id,
        true,  -- Set as verified since auth user exists
        true,  -- Set as active
        'profile_created_manually',
        NOW(),
        NOW()
    )
    RETURNING id INTO v_profile_id;
    
    RAISE NOTICE '✓ Created profile with ID: %', v_profile_id;
    RAISE NOTICE '';
    RAISE NOTICE '=== PROFILE CREATION COMPLETE ===';
    RAISE NOTICE 'User should now be able to log in with:';
    RAISE NOTICE '  Email: <EMAIL>';
    RAISE NOTICE '  Password: [the password you set during registration]';
    RAISE NOTICE '';
    
EXCEPTION
    WHEN OTHERS THEN
        RAISE NOTICE '❌ Error creating profile: %', SQLERRM;
END $$;

-- 2. Verify the fix worked
SELECT 
    'After Fix' as status,
    up.id as profile_id,
    up.email,
    up.first_name,
    up.last_name,
    up.user_id,
    up.is_verified,
    up.email_status,
    up.is_active,
    ur.role_name
FROM public.user_profiles up
LEFT JOIN public.user_roles ur ON up.role_id = ur.id
WHERE up.email = '<EMAIL>';

-- 3. Test the debug function again
SELECT public.debug_login_attempt('<EMAIL>') as fixed_login_debug;

-- 4. Final verification
DO $$
DECLARE
    debug_result jsonb;
    diagnosis TEXT;
BEGIN
    RAISE NOTICE '';
    RAISE NOTICE '=== VERIFICATION AFTER FIX ===';
    
    SELECT public.debug_login_attempt('<EMAIL>') INTO debug_result;
    diagnosis := debug_result->>'diagnosis';
    
    RAISE NOTICE 'Email: %', debug_result->>'email';
    RAISE NOTICE 'Profile exists: %', debug_result->>'profile_exists';
    RAISE NOTICE 'User ID linked: %', debug_result->>'user_id';
    RAISE NOTICE 'Is verified: %', debug_result->>'is_verified';
    RAISE NOTICE 'Is active: %', debug_result->>'is_active';
    RAISE NOTICE 'DIAGNOSIS: %', diagnosis;
    RAISE NOTICE '';
    
    IF diagnosis = 'Profile and auth user look correct' THEN
        RAISE NOTICE '🎉 SUCCESS! Login should work now!';
        RAISE NOTICE '';
        RAISE NOTICE 'Try logging in with:';
        RAISE NOTICE '  Email: <EMAIL>';
        RAISE NOTICE '  Password: [your registration password]';
    ELSE
        RAISE NOTICE '⚠️  Issue still exists: %', diagnosis;
        RAISE NOTICE 'Additional troubleshooting may be needed.';
    END IF;
    
    RAISE NOTICE '';
    RAISE NOTICE '=== END VERIFICATION ===';
END $$;
