-- Fix Password Issue for Registered User
-- Since profile and auth user look correct, this is likely a password issue

-- 1. Check if email confirmation is needed
SELECT 
    id,
    email,
    email_confirmed_at,
    created_at,
    CASE 
        WHEN email_confirmed_at IS NULL THEN 'Email not confirmed'
        ELSE 'Email confirmed'
    END as email_status
FROM auth.users 
WHERE email = '<EMAIL>';

-- 2. Confirm the email if it's not confirmed
UPDATE auth.users 
SET 
    email_confirmed_at = COALESCE(email_confirmed_at, NOW()),
    updated_at = NOW()
WHERE email = '<EMAIL>' 
AND email_confirmed_at IS NULL;

-- 3. Check the result
SELECT 
    'After email confirmation' as status,
    email,
    email_confirmed_at IS NOT NULL as is_confirmed,
    created_at
FROM auth.users 
WHERE email = '<EMAIL>';

-- 4. Create a function to trigger password reset
CREATE OR REPLACE FUNCTION public.trigger_password_reset(
    p_email VARCHAR(255)
)
RETURNS jsonb AS $$
DECLARE
    v_user_exists BOOLEAN;
    v_profile_exists BOOLEAN;
BEGIN
    -- Check if user exists in auth
    SELECT EXISTS(SELECT 1 FROM auth.users WHERE email = p_email) INTO v_user_exists;
    
    -- Check if profile exists
    SELECT EXISTS(SELECT 1 FROM public.user_profiles WHERE email = p_email) INTO v_profile_exists;
    
    IF NOT v_user_exists THEN
        RETURN jsonb_build_object(
            'success', false,
            'error', 'No auth user found for this email'
        );
    END IF;
    
    IF NOT v_profile_exists THEN
        RETURN jsonb_build_object(
            'success', false,
            'error', 'No profile found for this email'
        );
    END IF;
    
    -- Ensure email is confirmed
    UPDATE auth.users 
    SET email_confirmed_at = COALESCE(email_confirmed_at, NOW())
    WHERE email = p_email;
    
    RETURN jsonb_build_object(
        'success', true,
        'message', 'User is ready for password reset',
        'email', p_email,
        'next_step', 'Use forgot password on login page'
    );
    
EXCEPTION
    WHEN OTHERS THEN
        RETURN jsonb_build_object(
            'success', false,
            'error', 'Failed to prepare password reset: ' || SQLERRM
        );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant permissions
GRANT EXECUTE ON FUNCTION public.trigger_password_reset(VARCHAR) TO authenticated, anon;

-- 5. Test the password reset preparation
SELECT public.trigger_password_reset('<EMAIL>') as password_reset_prep;

-- 6. Provide instructions
DO $$
DECLARE
    reset_result jsonb;
BEGIN
    RAISE NOTICE '';
    RAISE NOTICE '=== PASSWORD ISSUE FIX ===';
    
    -- Get reset preparation result
    SELECT public.trigger_password_reset('<EMAIL>') INTO reset_result;
    
    IF (reset_result->>'success')::boolean THEN
        RAISE NOTICE '✅ User is ready for password reset';
        RAISE NOTICE '';
        RAISE NOTICE 'NEXT STEPS:';
        RAISE NOTICE '1. Go to login page: http://192.168.1.37:8080/login';
        RAISE NOTICE '2. Click "Forgot Password"';
        RAISE NOTICE '3. Enter email: <EMAIL>';
        RAISE NOTICE '4. Check email for reset link';
        RAISE NOTICE '5. Set new password';
        RAISE NOTICE '6. Try logging in with new password';
    ELSE
        RAISE NOTICE '❌ Password reset preparation failed: %', reset_result->>'error';
    END IF;
    
    RAISE NOTICE '';
    RAISE NOTICE 'ALTERNATIVE: Try these common passwords that might have been set:';
    RAISE NOTICE '- The password you just created during registration';
    RAISE NOTICE '- Check if caps lock is on';
    RAISE NOTICE '- Try typing password in notepad first to verify';
    RAISE NOTICE '';
    RAISE NOTICE '=== END PASSWORD FIX ===';
END $$;
