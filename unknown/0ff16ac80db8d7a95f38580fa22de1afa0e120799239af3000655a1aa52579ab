-- Simple Auth Fix - Focus on the core "Database error querying schema" issue
-- This avoids problematic table operations and focuses on user configuration

-- 1. Check what auth tables and columns actually exist
DO $$
DECLARE
    table_info RECORD;
    column_info RECORD;
BEGIN
    RAISE NOTICE '';
    RAISE NOTICE '=== AUTH SCHEMA INSPECTION ===';

    -- List auth schema tables
    RAISE NOTICE 'Auth schema tables:';
    FOR table_info IN
        SELECT table_name
        FROM information_schema.tables
        WHERE table_schema = 'auth'
        ORDER BY table_name
    LOOP
        RAISE NOTICE '  - %', table_info.table_name;
    END LOOP;

    -- Check auth.users columns
    RAISE NOTICE '';
    RAISE NOTICE 'auth.users columns:';
    FOR column_info IN
        SELECT column_name, data_type, is_nullable
        FROM information_schema.columns
        WHERE table_schema = 'auth' AND table_name = 'users'
        ORDER BY ordinal_position
    LOOP
        RAISE NOTICE '  - % (% - %)', column_info.column_name, column_info.data_type,
            CASE WHEN column_info.is_nullable = 'YES' THEN 'nullable' ELSE 'not null' END;
    END LOOP;

    RAISE NOTICE '';
END $$;

-- 2. Fix the specific user without touching sessions/tokens
DO $$
DECLARE
    v_user_id UUID;
    v_password VARCHAR(255) := 'TempPass123!';
    v_current_user RECORD;
BEGIN
    RAISE NOTICE '=== FIXING <EMAIL> ===';

    -- Get current user state
    SELECT
        id,
        email,
        encrypted_password IS NOT NULL as has_password,
        email_confirmed_at,
        aud,
        role,
        instance_id,
        created_at,
        updated_at
    INTO v_current_user
    FROM auth.users
    WHERE email = '<EMAIL>';

    IF v_current_user.id IS NULL THEN
        RAISE NOTICE '❌ User not found: <EMAIL>';
        RETURN;
    END IF;

    RAISE NOTICE 'Current user state:';
    RAISE NOTICE '  ID: %', v_current_user.id;
    RAISE NOTICE '  Has Password: %', v_current_user.has_password;
    RAISE NOTICE '  Email Confirmed: %', v_current_user.email_confirmed_at IS NOT NULL;
    RAISE NOTICE '  Aud: %', COALESCE(v_current_user.aud, 'NULL');
    RAISE NOTICE '  Role: %', COALESCE(v_current_user.role, 'NULL');
    RAISE NOTICE '  Instance ID: %', COALESCE(v_current_user.instance_id::text, 'NULL');

    -- Update user with all required fields
    UPDATE auth.users
    SET
        encrypted_password = crypt(v_password, gen_salt('bf')),
        email_confirmed_at = COALESCE(email_confirmed_at, NOW()),
        aud = 'authenticated',
        role = 'authenticated',
        instance_id = COALESCE(instance_id, '00000000-0000-0000-0000-000000000000'::uuid),
        updated_at = NOW()
    WHERE id = v_current_user.id;

    RAISE NOTICE '✅ Updated user configuration';
    RAISE NOTICE '🔑 Password set to: %', v_password;

    RAISE NOTICE '';
END $$;

-- 3. Verify the fix
DO $$
DECLARE
    v_user RECORD;
    v_password VARCHAR(255) := 'TempPass123!';
    v_password_valid BOOLEAN;
BEGIN
    RAISE NOTICE '=== VERIFICATION ===';

    -- Get updated user state
    SELECT
        id,
        email,
        encrypted_password,
        email_confirmed_at,
        aud,
        role,
        instance_id,
        updated_at
    INTO v_user
    FROM auth.users
    WHERE email = '<EMAIL>';

    IF v_user.id IS NULL THEN
        RAISE NOTICE '❌ User not found after update';
        RETURN;
    END IF;
    
    BEGIN
        -- Create auth user with proper Supabase format
        INSERT INTO auth.users (
            id,
            instance_id,
            email,
            encrypted_password,
            email_confirmed_at,
            email_change_sent_at,
            recovery_sent_at,
            created_at,
            updated_at,
            raw_user_meta_data,
            raw_app_meta_data,
            is_super_admin,
            role,
            aud
        ) VALUES (
            v_new_auth_id,
            '00000000-0000-0000-0000-000000000000',
            p_email,
            crypt(v_temp_password, gen_salt('bf')),
            NOW(),
            NULL,
            NULL,
            NOW(),
            NOW(),
            jsonb_build_object(
                'first_name', v_profile.first_name,
                'last_name', v_profile.last_name,
                'email', p_email
            ),
            jsonb_build_object(
                'provider', 'email',
                'providers', ARRAY['email']
            ),
            false,
            'authenticated',
            'authenticated'
        );
        
        -- Update the profile to link to the new auth user
        UPDATE public.user_profiles 
        SET 
            user_id = v_new_auth_id,
            role_name = v_profile.role_name_value,
            updated_at = NOW()
        WHERE email = p_email;
        
        RETURN jsonb_build_object(
            'success', true,
            'message', 'Successfully fixed orphaned profile',
            'email', p_email,
            'password', v_temp_password,
            'auth_user_id', v_new_auth_id
        );
        
    EXCEPTION
        WHEN OTHERS THEN
            RETURN jsonb_build_object(
                'success', false,
                'error', 'Failed to fix profile: ' || SQLERRM
            );
    END;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant permission
GRANT EXECUTE ON FUNCTION public.fix_orphaned_profile(VARCHAR) TO authenticated;

-- 4. Fix the specific user
DO $$
DECLARE
    fix_result jsonb;
BEGIN
    RAISE NOTICE '';
    RAISE NOTICE '=== FIXING <EMAIL> ===';
    
    SELECT public.fix_orphaned_profile('<EMAIL>') INTO fix_result;
    
    IF (fix_result->>'success')::boolean THEN
        RAISE NOTICE '✅ SUCCESS: %', fix_result->>'message';
        RAISE NOTICE '📧 Email: %', fix_result->>'email';
        RAISE NOTICE '🔑 Password: %', fix_result->>'password';
        RAISE NOTICE '🆔 Auth ID: %', fix_result->>'auth_user_id';
    ELSE
        RAISE NOTICE '❌ FAILED: %', fix_result->>'error';
    END IF;
    
    RAISE NOTICE '';
END $$;

-- 5. Verify the fix
DO $$
DECLARE
    v_profile_exists BOOLEAN;
    v_auth_exists BOOLEAN;
    v_linked BOOLEAN;
    v_auth_id UUID;
    v_profile_id UUID;
BEGIN
    RAISE NOTICE '=== VERIFICATION ===';
    
    -- Check if profile exists
    SELECT EXISTS(SELECT 1 FROM public.user_profiles WHERE email = '<EMAIL>')
    INTO v_profile_exists;
    
    -- Check if auth user exists
    SELECT EXISTS(SELECT 1 FROM auth.users WHERE email = '<EMAIL>')
    INTO v_auth_exists;
    
    -- Check if they're properly linked
    SELECT 
        up.user_id, up.id,
        EXISTS(SELECT 1 FROM auth.users au WHERE au.id = up.user_id)
    INTO v_auth_id, v_profile_id, v_linked
    FROM public.user_profiles up
    WHERE up.email = '<EMAIL>';
    
    RAISE NOTICE 'Profile exists: %', v_profile_exists;
    RAISE NOTICE 'Auth user exists: %', v_auth_exists;
    RAISE NOTICE 'Properly linked: %', v_linked;
    RAISE NOTICE 'Profile ID: %', v_profile_id;
    RAISE NOTICE 'Auth User ID: %', v_auth_id;
    
    IF v_profile_exists AND v_auth_exists AND v_linked THEN
        RAISE NOTICE '';
        RAISE NOTICE '🎉 SUCCESS! User should be able to login now with:';
        RAISE NOTICE '   Email: <EMAIL>';
        RAISE NOTICE '   Password: TempPass123!';
        RAISE NOTICE '';
    ELSE
        RAISE NOTICE '';
        RAISE NOTICE '❌ Something is still wrong. Check the details above.';
        RAISE NOTICE '';
    END IF;
END $$;
