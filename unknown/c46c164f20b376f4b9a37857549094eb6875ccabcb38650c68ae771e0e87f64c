-- Company Management System Database Schema
-- This script creates tables and functions for comprehensive company management

-- =============================================
-- COMPANY SETTINGS TABLE
-- =============================================

-- Create company_settings table if it doesn't exist
CREATE TABLE IF NOT EXISTS public.company_settings (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    logo_url TEXT,
    address TEXT,
    phone VARCHAR(50),
    email VARCHAR(255) NOT NULL,
    website VARCHAR(255),
    tax_number VARCHAR(100),
    registration_number VARCHAR(100),
    industry VARCHAR(100),
    founded_date DATE,
    employee_count INTEGER,
    description TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =============================================
-- ENHANCED USER ROLES TABLE
-- =============================================

-- Update user_roles table with additional fields
ALTER TABLE public.user_roles 
ADD COLUMN IF NOT EXISTS role_display_name VARCHAR(100),
ADD COLUMN IF NOT EXISTS description TEXT,
ADD COLUMN IF NOT EXISTS permissions JSONB DEFAULT '{}',
ADD COLUMN IF NOT EXISTS is_active BOOLEAN DEFAULT true,
ADD COLUMN IF NOT EXISTS created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
ADD COLUMN IF NOT EXISTS updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW();

-- Update existing roles with display names if they don't exist
UPDATE public.user_roles 
SET role_display_name = CASE 
    WHEN role_name = 'admin' THEN 'Administrator'
    WHEN role_name = 'management' THEN 'Management'
    WHEN role_name = 'accountant' THEN 'Accountant'
    WHEN role_name = 'qs' THEN 'Quantity Surveyor'
    WHEN role_name = 'client' THEN 'Client'
    ELSE INITCAP(role_name)
END
WHERE role_display_name IS NULL;

-- =============================================
-- ENHANCED AUDIT LOGS TABLE
-- =============================================

-- Create audit_logs table if it doesn't exist
CREATE TABLE IF NOT EXISTS public.audit_logs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES auth.users(id) ON DELETE SET NULL,
    action VARCHAR(100) NOT NULL,
    resource_type VARCHAR(50),
    resource_id VARCHAR(255),
    old_values JSONB,
    new_values JSONB,
    ip_address INET,
    user_agent TEXT,
    session_id VARCHAR(255),
    success BOOLEAN DEFAULT true,
    error_message TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_audit_logs_user_id ON public.audit_logs(user_id);
CREATE INDEX IF NOT EXISTS idx_audit_logs_action ON public.audit_logs(action);
CREATE INDEX IF NOT EXISTS idx_audit_logs_resource_type ON public.audit_logs(resource_type);
CREATE INDEX IF NOT EXISTS idx_audit_logs_created_at ON public.audit_logs(created_at);

-- =============================================
-- USER SESSIONS TABLE
-- =============================================

-- Create user_sessions table for session management
CREATE TABLE IF NOT EXISTS public.user_sessions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    session_token VARCHAR(255) UNIQUE NOT NULL,
    ip_address INET,
    user_agent TEXT,
    device_info JSONB,
    is_active BOOLEAN DEFAULT true,
    last_activity TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes
CREATE INDEX IF NOT EXISTS idx_user_sessions_user_id ON public.user_sessions(user_id);
CREATE INDEX IF NOT EXISTS idx_user_sessions_token ON public.user_sessions(session_token);
CREATE INDEX IF NOT EXISTS idx_user_sessions_expires_at ON public.user_sessions(expires_at);

-- =============================================
-- TEAM MANAGEMENT TABLES
-- =============================================

-- Create departments table
CREATE TABLE IF NOT EXISTS public.departments (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(100) NOT NULL UNIQUE,
    description TEXT,
    manager_id UUID REFERENCES public.user_profiles(id) ON DELETE SET NULL,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create team_memberships table for flexible team assignments
CREATE TABLE IF NOT EXISTS public.team_memberships (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES public.user_profiles(id) ON DELETE CASCADE,
    department_id UUID REFERENCES public.departments(id) ON DELETE CASCADE,
    role_in_team VARCHAR(100),
    start_date DATE DEFAULT CURRENT_DATE,
    end_date DATE,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(user_id, department_id)
);

-- =============================================
-- PERMISSION MANAGEMENT FUNCTIONS
-- =============================================

-- Function to log audit events
CREATE OR REPLACE FUNCTION public.log_audit_event(
    p_user_id UUID,
    p_action VARCHAR(100),
    p_resource_type VARCHAR(50) DEFAULT NULL,
    p_resource_id VARCHAR(255) DEFAULT NULL,
    p_old_values JSONB DEFAULT NULL,
    p_new_values JSONB DEFAULT NULL,
    p_success BOOLEAN DEFAULT true,
    p_error_message TEXT DEFAULT NULL
) RETURNS UUID AS $$
DECLARE
    audit_id UUID;
    client_ip INET;
    client_user_agent TEXT;
BEGIN
    -- Try to get client IP and user agent from current request
    BEGIN
        client_ip := inet_client_addr();
        client_user_agent := current_setting('request.headers', true)::json->>'user-agent';
    EXCEPTION WHEN OTHERS THEN
        client_ip := NULL;
        client_user_agent := NULL;
    END;

    INSERT INTO public.audit_logs (
        user_id,
        action,
        resource_type,
        resource_id,
        old_values,
        new_values,
        ip_address,
        user_agent,
        success,
        error_message
    ) VALUES (
        p_user_id,
        p_action,
        p_resource_type,
        p_resource_id,
        p_old_values,
        p_new_values,
        client_ip,
        client_user_agent,
        p_success,
        p_error_message
    ) RETURNING id INTO audit_id;

    RETURN audit_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get company statistics
CREATE OR REPLACE FUNCTION public.get_company_stats()
RETURNS JSON AS $$
DECLARE
    result JSON;
BEGIN
    SELECT json_build_object(
        'total_users', (SELECT COUNT(*) FROM public.user_profiles),
        'active_users', (SELECT COUNT(*) FROM public.user_profiles WHERE is_active = true),
        'pending_users', (SELECT COUNT(*) FROM public.user_profiles WHERE requires_password_setup = true),
        'total_projects', (SELECT COUNT(*) FROM public.projects),
        'active_projects', (SELECT COUNT(*) FROM public.projects WHERE status = 'active'),
        'total_clients', (SELECT COUNT(*) FROM public.clients),
        'departments', (SELECT COUNT(*) FROM public.departments WHERE is_active = true),
        'recent_activities', (SELECT COUNT(*) FROM public.audit_logs WHERE created_at > NOW() - INTERVAL '24 hours')
    ) INTO result;

    RETURN result;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get user activity summary
CREATE OR REPLACE FUNCTION public.get_user_activity_summary(p_user_id UUID)
RETURNS JSON AS $$
DECLARE
    result JSON;
BEGIN
    SELECT json_build_object(
        'total_actions', COUNT(*),
        'last_activity', MAX(created_at),
        'actions_today', COUNT(*) FILTER (WHERE created_at > CURRENT_DATE),
        'actions_this_week', COUNT(*) FILTER (WHERE created_at > CURRENT_DATE - INTERVAL '7 days'),
        'most_common_action', (
            SELECT action 
            FROM public.audit_logs 
            WHERE user_id = p_user_id 
            GROUP BY action 
            ORDER BY COUNT(*) DESC 
            LIMIT 1
        )
    ) INTO result
    FROM public.audit_logs
    WHERE user_id = p_user_id;

    RETURN result;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =============================================
-- ROW LEVEL SECURITY POLICIES
-- =============================================

-- Enable RLS on audit_logs
ALTER TABLE public.audit_logs ENABLE ROW LEVEL SECURITY;

-- Policy for audit logs - only admins and management can view all logs
CREATE POLICY "audit_logs_select_policy" ON public.audit_logs
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.user_profiles up
            JOIN public.user_roles ur ON up.role_id = ur.id
            WHERE up.user_id = auth.uid()
            AND ur.role_name IN ('admin', 'management')
        )
        OR user_id = auth.uid() -- Users can see their own audit logs
    );

-- Policy for company settings - only admins can modify
CREATE POLICY "company_settings_select_policy" ON public.company_settings
    FOR SELECT USING (true); -- Everyone can read company settings

CREATE POLICY "company_settings_modify_policy" ON public.company_settings
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM public.user_profiles up
            JOIN public.user_roles ur ON up.role_id = ur.id
            WHERE up.user_id = auth.uid()
            AND ur.role_name = 'admin'
        )
    );

-- Enable RLS on company_settings
ALTER TABLE public.company_settings ENABLE ROW LEVEL SECURITY;

-- =============================================
-- INITIAL DATA
-- =============================================

-- Insert default company settings if none exist
INSERT INTO public.company_settings (
    name, 
    email, 
    description
) 
SELECT 
    'Martcosy Construction',
    '<EMAIL>',
    'Professional construction management company'
WHERE NOT EXISTS (SELECT 1 FROM public.company_settings);

-- Insert default departments if none exist
INSERT INTO public.departments (name, description) VALUES
    ('Administration', 'Administrative and management functions'),
    ('Construction', 'Construction and project execution'),
    ('Finance', 'Financial management and accounting'),
    ('Quality Assurance', 'Quality control and surveying'),
    ('Client Relations', 'Client management and communication')
ON CONFLICT (name) DO NOTHING;

-- =============================================
-- GRANTS AND PERMISSIONS
-- =============================================

-- Grant necessary permissions
GRANT SELECT, INSERT, UPDATE ON public.company_settings TO authenticated;
GRANT SELECT, INSERT ON public.audit_logs TO authenticated;
GRANT SELECT ON public.departments TO authenticated;
GRANT SELECT, INSERT, UPDATE, DELETE ON public.team_memberships TO authenticated;
GRANT SELECT ON public.user_sessions TO authenticated;

-- Grant execute permissions on functions
GRANT EXECUTE ON FUNCTION public.log_audit_event TO authenticated;
GRANT EXECUTE ON FUNCTION public.get_company_stats TO authenticated;
GRANT EXECUTE ON FUNCTION public.get_user_activity_summary TO authenticated;
