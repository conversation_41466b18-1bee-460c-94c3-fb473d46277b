-- CLEANUP SCRIPT - Remove All Asset Tables
-- Run this in your Supabase SQL Editor to completely remove all asset-related tables
-- This will allow you to start fresh with a clean schema

-- Step 1: Drop all triggers first
DROP TRIGGER IF EXISTS update_asset_categories_updated_at ON public.asset_categories;
DROP TRIGGER IF EXISTS update_company_assets_updated_at ON public.company_assets;
DROP TRIGGER IF EXISTS update_asset_maintenance_updated_at ON public.asset_maintenance;

-- Step 2: Drop all policies (Row Level Security)
DROP POLICY IF EXISTS "Allow all operations on asset_categories" ON public.asset_categories;
DROP POLICY IF EXISTS "Allow all operations on company_assets" ON public.company_assets;
DROP POLICY IF EXISTS "Allow all operations on asset_maintenance" ON public.asset_maintenance;
DROP POLICY IF EXISTS "Allow all operations on asset_depreciation" ON public.asset_depreciation;
DROP POLICY IF EXISTS "Allow all operations on asset_assignments" ON public.asset_assignments;

-- Step 3: Drop all indexes
DROP INDEX IF EXISTS public.idx_company_assets_category;
DROP INDEX IF EXISTS public.idx_company_assets_type;
DROP INDEX IF EXISTS public.idx_company_assets_status;
DROP INDEX IF EXISTS public.idx_company_assets_assigned_to;
DROP INDEX IF EXISTS public.idx_company_assets_project;
DROP INDEX IF EXISTS public.idx_asset_maintenance_asset;
DROP INDEX IF EXISTS public.idx_asset_maintenance_status;
DROP INDEX IF EXISTS public.idx_asset_maintenance_due_date;
DROP INDEX IF EXISTS public.idx_asset_depreciation_asset;
DROP INDEX IF EXISTS public.idx_asset_assignments_asset;

-- Step 4: Drop all tables (in reverse dependency order)
-- Drop child tables first (those with foreign keys)
DROP TABLE IF EXISTS public.asset_assignments CASCADE;
DROP TABLE IF EXISTS public.asset_depreciation CASCADE;
DROP TABLE IF EXISTS public.asset_maintenance CASCADE;
DROP TABLE IF EXISTS public.company_assets CASCADE;

-- Drop parent table last
DROP TABLE IF EXISTS public.asset_categories CASCADE;

-- Step 5: Drop functions
DROP FUNCTION IF EXISTS public.update_updated_at_column() CASCADE;
DROP FUNCTION IF EXISTS public.calculate_asset_depreciation(UUID, DATE) CASCADE;

-- Step 6: Clean up any remaining sequences (if any were created)
DROP SEQUENCE IF EXISTS public.asset_categories_id_seq CASCADE;
DROP SEQUENCE IF EXISTS public.company_assets_id_seq CASCADE;
DROP SEQUENCE IF EXISTS public.asset_maintenance_id_seq CASCADE;
DROP SEQUENCE IF EXISTS public.asset_depreciation_id_seq CASCADE;
DROP SEQUENCE IF EXISTS public.asset_assignments_id_seq CASCADE;

-- Verification: Check if tables still exist
-- Run this to confirm all tables are removed
SELECT table_name 
FROM information_schema.tables 
WHERE table_schema = 'public' 
AND table_name LIKE '%asset%'
ORDER BY table_name;

-- If the query above returns no rows, the cleanup was successful!

-- IMPORTANT: After running this cleanup script, you can now run the correct schema:
-- 1. Run: database/company_assets_simple.sql
-- 2. Run: database/quick_test_assets.sql (optional sample data)

-- This will give you a clean, consistent schema without conflicts.
