import React from 'react';
import { Link } from 'react-router-dom';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Building, ExternalLink, Mail, Phone } from 'lucide-react';
import { useClients } from '@/hooks/useClients';

interface ClientLinkProps {
  clientName: string;
  variant?: 'badge' | 'button' | 'link' | 'card';
  showDetails?: boolean;
  className?: string;
}

export const ClientLink: React.FC<ClientLinkProps> = ({
  clientName,
  variant = 'badge',
  showDetails = false,
  className = ''
}) => {
  const { clients } = useClients();
  
  // Find the client by name (in a real app, you'd use client_id)
  const client = clients.find(c => 
    c.name.toLowerCase() === clientName.toLowerCase() ||
    c.company_name?.toLowerCase() === clientName.toLowerCase()
  );

  const handleClientClick = () => {
    if (client) {
      // Navigate to client details
      window.open(`/clients?client=${client.id}`, '_blank');
    }
  };

  if (!client) {
    // If client not found, just show the name
    return (
      <span className={`text-gray-600 ${className}`}>
        {clientName}
      </span>
    );
  }

  switch (variant) {
    case 'badge':
      return (
        <Badge 
          variant="outline" 
          className={`cursor-pointer hover:bg-blue-50 hover:border-blue-300 ${className}`}
          onClick={handleClientClick}
        >
          <Building className="w-3 h-3 mr-1" />
          {clientName}
          <ExternalLink className="w-3 h-3 ml-1" />
        </Badge>
      );

    case 'button':
      return (
        <Button
          variant="outline"
          size="sm"
          onClick={handleClientClick}
          className={className}
        >
          <Building className="w-4 h-4 mr-2" />
          {clientName}
          <ExternalLink className="w-4 h-4 ml-2" />
        </Button>
      );

    case 'link':
      return (
        <button
          onClick={handleClientClick}
          className={`text-blue-600 hover:text-blue-800 hover:underline flex items-center ${className}`}
        >
          <Building className="w-4 h-4 mr-1" />
          {clientName}
          <ExternalLink className="w-3 h-3 ml-1" />
        </button>
      );

    case 'card':
      return (
        <div className={`border rounded-lg p-3 hover:shadow-md transition-shadow cursor-pointer ${className}`}>
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                <Building className="w-4 h-4 text-blue-600" />
              </div>
              <div>
                <p className="font-medium text-gray-900">{client.name}</p>
                {client.company_name && client.company_name !== client.name && (
                  <p className="text-sm text-gray-500">{client.company_name}</p>
                )}
              </div>
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={handleClientClick}
            >
              <ExternalLink className="w-4 h-4" />
            </Button>
          </div>
          
          {showDetails && (
            <div className="mt-3 space-y-2">
              <div className="flex items-center space-x-2 text-sm text-gray-600">
                <Mail className="w-3 h-3" />
                <span>{client.email}</span>
              </div>
              {client.phone && (
                <div className="flex items-center space-x-2 text-sm text-gray-600">
                  <Phone className="w-3 h-3" />
                  <span>{client.phone}</span>
                </div>
              )}
              <div className="flex items-center justify-between">
                <Badge 
                  className={
                    client.status === 'Active' ? 'bg-green-100 text-green-800' :
                    client.status === 'Inactive' ? 'bg-gray-100 text-gray-800' :
                    'bg-red-100 text-red-800'
                  }
                >
                  {client.status}
                </Badge>
                {client.industry && (
                  <Badge variant="outline">
                    {client.industry}
                  </Badge>
                )}
              </div>
            </div>
          )}
        </div>
      );

    default:
      return (
        <span className={className}>
          {clientName}
        </span>
      );
  }
};

// Hook to get client projects
export const useClientProjects = (clientId: string) => {
  const { clients } = useClients();
  
  // This would typically come from a projects API call
  // For now, we'll return mock data
  const client = clients.find(c => c.id === clientId);
  
  return {
    client,
    projects: [], // This would be fetched from API
    loading: false,
    error: null
  };
};

// Component to show client's projects
interface ClientProjectsProps {
  clientId: string;
  maxProjects?: number;
}

export const ClientProjects: React.FC<ClientProjectsProps> = ({
  clientId,
  maxProjects = 5
}) => {
  const { client, projects, loading } = useClientProjects(clientId);

  if (loading) {
    return <div className="text-sm text-gray-500">Loading projects...</div>;
  }

  if (!client) {
    return <div className="text-sm text-gray-500">Client not found</div>;
  }

  if (projects.length === 0) {
    return <div className="text-sm text-gray-500">No projects found</div>;
  }

  return (
    <div className="space-y-2">
      <h4 className="font-medium text-gray-900">Recent Projects</h4>
      <div className="space-y-1">
        {projects.slice(0, maxProjects).map((project: any) => (
          <div key={project.id} className="flex items-center justify-between text-sm">
            <Link 
              to={`/projects/${project.id}`}
              className="text-blue-600 hover:underline"
            >
              {project.name}
            </Link>
            <Badge variant="outline" className="text-xs">
              {project.status}
            </Badge>
          </div>
        ))}
      </div>
      {projects.length > maxProjects && (
        <Link 
          to={`/projects?client=${clientId}`}
          className="text-sm text-blue-600 hover:underline"
        >
          View all {projects.length} projects →
        </Link>
      )}
    </div>
  );
};
