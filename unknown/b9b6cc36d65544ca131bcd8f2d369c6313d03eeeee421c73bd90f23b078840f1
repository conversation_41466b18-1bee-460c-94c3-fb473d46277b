
import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';

const WorkforceOverview = () => {
  const workforceData = {
    onSite: 156,
    onLeave: 12,
    sick: 3,
    unexcused: 2,
    total: 173
  };

  const attendanceRate = ((workforceData.onSite / workforceData.total) * 100).toFixed(1);

  return (
    <Card>
      <CardHeader>
        <CardTitle>Workforce Status</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="grid grid-cols-2 gap-4">
          <div className="space-y-2">
            <div className="flex justify-between">
              <span className="text-sm text-gray-600">On Site</span>
              <Badge variant="secondary" className="bg-green-100 text-green-800">
                {workforceData.onSite}
              </Badge>
            </div>
            <div className="flex justify-between">
              <span className="text-sm text-gray-600">On Leave</span>
              <Badge variant="secondary" className="bg-blue-100 text-blue-800">
                {workforceData.onLeave}
              </Badge>
            </div>
          </div>
          <div className="space-y-2">
            <div className="flex justify-between">
              <span className="text-sm text-gray-600">Sick</span>
              <Badge variant="secondary" className="bg-yellow-100 text-yellow-800">
                {workforceData.sick}
              </Badge>
            </div>
            <div className="flex justify-between">
              <span className="text-sm text-gray-600">Unexcused</span>
              <Badge variant="secondary" className="bg-red-100 text-red-800">
                {workforceData.unexcused}
              </Badge>
            </div>
          </div>
        </div>
        <div className="pt-4 border-t">
          <div className="flex justify-between items-center">
            <span className="font-medium">Attendance Rate</span>
            <span className="text-2xl font-bold text-green-600">{attendanceRate}%</span>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default WorkforceOverview;
