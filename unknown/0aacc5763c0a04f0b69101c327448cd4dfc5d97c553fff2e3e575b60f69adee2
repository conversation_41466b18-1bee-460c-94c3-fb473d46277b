import React from 'react';
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { 
  BarChart3,
  TrendingUp,
  TrendingDown,
  DollarSign,
  Clock,
  Users,
  Target,
  AlertTriangle,
  CheckCircle,
  Calendar,
  Activity
} from 'lucide-react';

interface Project {
  id: string;
  name: string;
  status: string;
  priority: string;
  start_date: string;
  end_date: string;
  budget: number;
  spent: number;
  progress: number;
  client_name: string;
  project_manager: string;
}

interface Task {
  id: string;
  name: string;
  status: string;
  priority: string;
  progress: number;
  estimated_hours: number;
  actual_hours: number;
  project_id: string;
}

interface ProjectAnalyticsProps {
  projects: Project[];
  tasks: Task[];
}

export const ProjectAnalytics: React.FC<ProjectAnalyticsProps> = ({
  projects,
  tasks
}) => {
  // Calculate overall statistics
  const totalProjects = projects.length;
  const completedProjects = projects.filter(p => p.status === 'Completed').length;
  const inProgressProjects = projects.filter(p => p.status === 'In Progress').length;
  const onHoldProjects = projects.filter(p => p.status === 'On Hold').length;
  
  const totalBudget = projects.reduce((sum, p) => sum + p.budget, 0);
  const totalSpent = projects.reduce((sum, p) => sum + p.spent, 0);
  const budgetUtilization = totalBudget > 0 ? (totalSpent / totalBudget) * 100 : 0;
  
  const averageProgress = projects.length > 0 
    ? projects.reduce((sum, p) => sum + p.progress, 0) / projects.length 
    : 0;

  // Task statistics
  const totalTasks = tasks.length;
  const completedTasks = tasks.filter(t => t.status === 'Completed').length;
  const inProgressTasks = tasks.filter(t => t.status === 'In Progress').length;
  const overdueTasks = tasks.filter(t => {
    const today = new Date();
    const endDate = new Date(t.end_date || '');
    return endDate < today && t.status !== 'Completed';
  }).length;

  const totalEstimatedHours = tasks.reduce((sum, t) => sum + t.estimated_hours, 0);
  const totalActualHours = tasks.reduce((sum, t) => sum + t.actual_hours, 0);
  const timeEfficiency = totalEstimatedHours > 0 
    ? (totalEstimatedHours / totalActualHours) * 100 
    : 0;

  // Project status distribution
  const statusDistribution = [
    { status: 'Completed', count: completedProjects, color: 'bg-green-500' },
    { status: 'In Progress', count: inProgressProjects, color: 'bg-blue-500' },
    { status: 'On Hold', count: onHoldProjects, color: 'bg-yellow-500' },
    { status: 'Planning', count: projects.filter(p => p.status === 'Planning').length, color: 'bg-purple-500' },
    { status: 'Cancelled', count: projects.filter(p => p.status === 'Cancelled').length, color: 'bg-red-500' }
  ];

  // Priority distribution
  const priorityDistribution = [
    { priority: 'Critical', count: projects.filter(p => p.priority === 'Critical').length, color: 'bg-red-500' },
    { priority: 'High', count: projects.filter(p => p.priority === 'High').length, color: 'bg-orange-500' },
    { priority: 'Medium', count: projects.filter(p => p.priority === 'Medium').length, color: 'bg-yellow-500' },
    { priority: 'Low', count: projects.filter(p => p.priority === 'Low').length, color: 'bg-green-500' }
  ];

  // Top performing projects
  const topProjects = [...projects]
    .sort((a, b) => b.progress - a.progress)
    .slice(0, 5);

  // Projects at risk (over budget or behind schedule)
  const projectsAtRisk = projects.filter(p => {
    const budgetOverrun = p.spent > p.budget;
    const today = new Date();
    const endDate = new Date(p.end_date);
    const scheduleDelay = endDate < today && p.status !== 'Completed';
    return budgetOverrun || scheduleDelay;
  });

  // Format currency
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  };

  // Format percentage
  const formatPercentage = (value: number) => {
    return `${Math.round(value)}%`;
  };

  return (
    <div className="space-y-6">
      {/* Analytics Header */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <BarChart3 className="h-6 w-6 text-blue-600" />
                📊 Comprehensive Project Analytics
              </CardTitle>
              <CardDescription>
                Real-time insights and performance metrics for all construction projects
              </CardDescription>
            </div>
            <div className="flex items-center gap-2">
              <Badge variant="secondary" className="bg-green-100 text-green-800">
                ✅ Live Data
              </Badge>
              <Badge variant="outline">
                {totalProjects} Projects Analyzed
              </Badge>
            </div>
          </div>
        </CardHeader>
      </Card>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-2">
              <Target className="h-8 w-8 text-blue-500" />
              <div>
                <p className="text-sm font-medium text-gray-600">Total Projects</p>
                <p className="text-2xl font-bold">{totalProjects}</p>
                <p className="text-xs text-gray-500">
                  {completedProjects} completed
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-2">
              <DollarSign className="h-8 w-8 text-green-500" />
              <div>
                <p className="text-sm font-medium text-gray-600">Budget Status</p>
                <p className="text-2xl font-bold">{formatPercentage(budgetUtilization)}</p>
                <p className="text-xs text-gray-500">
                  {formatCurrency(totalSpent)} / {formatCurrency(totalBudget)}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-2">
              <Activity className="h-8 w-8 text-purple-500" />
              <div>
                <p className="text-sm font-medium text-gray-600">Avg Progress</p>
                <p className="text-2xl font-bold">{formatPercentage(averageProgress)}</p>
                <p className="text-xs text-gray-500">
                  Across all projects
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-2">
              <Clock className="h-8 w-8 text-orange-500" />
              <div>
                <p className="text-sm font-medium text-gray-600">Time Efficiency</p>
                <p className="text-2xl font-bold">{formatPercentage(timeEfficiency)}</p>
                <p className="text-xs text-gray-500">
                  {totalActualHours}h / {totalEstimatedHours}h
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Charts and Distributions */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Project Status Distribution */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <BarChart3 className="w-5 h-5" />
              <span>Project Status Distribution</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {statusDistribution.map((item) => (
                <div key={item.status} className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div className={`w-4 h-4 rounded ${item.color}`} />
                    <span className="text-sm font-medium">{item.status}</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <span className="text-sm text-gray-600">{item.count}</span>
                    <div className="w-20 bg-gray-200 rounded-full h-2">
                      <div
                        className={`h-2 rounded-full ${item.color}`}
                        style={{
                          width: totalProjects > 0 ? `${(item.count / totalProjects) * 100}%` : '0%'
                        }}
                      />
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Priority Distribution */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <AlertTriangle className="w-5 h-5" />
              <span>Priority Distribution</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {priorityDistribution.map((item) => (
                <div key={item.priority} className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div className={`w-4 h-4 rounded ${item.color}`} />
                    <span className="text-sm font-medium">{item.priority}</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <span className="text-sm text-gray-600">{item.count}</span>
                    <div className="w-20 bg-gray-200 rounded-full h-2">
                      <div
                        className={`h-2 rounded-full ${item.color}`}
                        style={{
                          width: totalProjects > 0 ? `${(item.count / totalProjects) * 100}%` : '0%'
                        }}
                      />
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Task Overview */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <CheckCircle className="w-5 h-5" />
            <span>Task Overview</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="text-center">
              <p className="text-2xl font-bold text-blue-600">{totalTasks}</p>
              <p className="text-sm text-gray-600">Total Tasks</p>
            </div>
            <div className="text-center">
              <p className="text-2xl font-bold text-green-600">{completedTasks}</p>
              <p className="text-sm text-gray-600">Completed</p>
            </div>
            <div className="text-center">
              <p className="text-2xl font-bold text-orange-600">{inProgressTasks}</p>
              <p className="text-sm text-gray-600">In Progress</p>
            </div>
            <div className="text-center">
              <p className="text-2xl font-bold text-red-600">{overdueTasks}</p>
              <p className="text-sm text-gray-600">Overdue</p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Top Performing Projects */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <TrendingUp className="w-5 h-5" />
            <span>Top Performing Projects</span>
          </CardTitle>
          <CardDescription>
            Projects with highest completion rates
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {topProjects.map((project, index) => (
              <div key={project.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div className="flex items-center space-x-3">
                  <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                    <span className="text-sm font-bold text-blue-600">#{index + 1}</span>
                  </div>
                  <div>
                    <p className="font-medium">{project.name}</p>
                    <p className="text-sm text-gray-600">{project.client_name}</p>
                  </div>
                </div>
                <div className="flex items-center space-x-4">
                  <Badge className={
                    project.status === 'Completed' ? 'bg-green-100 text-green-800' :
                    project.status === 'In Progress' ? 'bg-blue-100 text-blue-800' :
                    'bg-gray-100 text-gray-800'
                  }>
                    {project.status}
                  </Badge>
                  <div className="text-right">
                    <p className="font-bold">{project.progress}%</p>
                    <Progress value={project.progress} className="w-20 h-2" />
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Projects at Risk */}
      {projectsAtRisk.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <AlertTriangle className="w-5 h-5 text-red-500" />
              <span>Projects at Risk</span>
            </CardTitle>
            <CardDescription>
              Projects that are over budget or behind schedule
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {projectsAtRisk.map((project) => {
                const budgetOverrun = project.spent > project.budget;
                const today = new Date();
                const endDate = new Date(project.end_date);
                const scheduleDelay = endDate < today && project.status !== 'Completed';
                
                return (
                  <div key={project.id} className="flex items-center justify-between p-3 border border-red-200 bg-red-50 rounded-lg">
                    <div>
                      <p className="font-medium">{project.name}</p>
                      <div className="flex items-center space-x-4 mt-1">
                        {budgetOverrun && (
                          <Badge variant="destructive" className="text-xs">
                            Over Budget: {formatCurrency(project.spent - project.budget)}
                          </Badge>
                        )}
                        {scheduleDelay && (
                          <Badge variant="destructive" className="text-xs">
                            Behind Schedule
                          </Badge>
                        )}
                      </div>
                    </div>
                    <div className="text-right">
                      <p className="text-sm text-gray-600">Progress: {project.progress}%</p>
                      <p className="text-sm text-gray-600">
                        Budget: {formatCurrency(project.spent)} / {formatCurrency(project.budget)}
                      </p>
                    </div>
                  </div>
                );
              })}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Advanced Analytics */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Financial Performance */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <DollarSign className="w-5 h-5 text-green-500" />
              <span>Financial Performance</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex justify-between items-center">
                <span className="text-sm font-medium">Total Revenue</span>
                <span className="text-lg font-bold text-green-600">{formatCurrency(totalBudget)}</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm font-medium">Total Spent</span>
                <span className="text-lg font-bold text-red-600">{formatCurrency(totalSpent)}</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm font-medium">Remaining Budget</span>
                <span className="text-lg font-bold text-blue-600">{formatCurrency(totalBudget - totalSpent)}</span>
              </div>
              <div className="pt-2">
                <div className="flex justify-between text-sm mb-1">
                  <span>Budget Utilization</span>
                  <span>{formatPercentage(budgetUtilization)}</span>
                </div>
                <Progress value={budgetUtilization} className="h-3" />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Team Performance */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Users className="w-5 h-5 text-purple-500" />
              <span>Team Performance</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex justify-between items-center">
                <span className="text-sm font-medium">Active Project Managers</span>
                <span className="text-lg font-bold">{new Set(projects.map(p => p.project_manager)).size}</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm font-medium">Average Project Progress</span>
                <span className="text-lg font-bold">{formatPercentage(averageProgress)}</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm font-medium">Time Efficiency</span>
                <span className={`text-lg font-bold ${timeEfficiency >= 100 ? 'text-green-600' : 'text-orange-600'}`}>
                  {formatPercentage(timeEfficiency)}
                </span>
              </div>
              <div className="pt-2">
                <div className="flex justify-between text-sm mb-1">
                  <span>Overall Efficiency</span>
                  <span>{formatPercentage((averageProgress + timeEfficiency) / 2)}</span>
                </div>
                <Progress value={(averageProgress + timeEfficiency) / 2} className="h-3" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Project Timeline Overview */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Calendar className="w-5 h-5 text-indigo-500" />
            <span>Project Timeline Overview</span>
          </CardTitle>
          <CardDescription>
            Current project schedules and deadlines
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {projects.slice(0, 6).map((project) => {
              const startDate = new Date(project.start_date);
              const endDate = new Date(project.end_date);
              const today = new Date();
              const totalDays = Math.ceil((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24));
              const elapsedDays = Math.ceil((today.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24));
              const timeProgress = Math.max(0, Math.min(100, (elapsedDays / totalDays) * 100));

              return (
                <div key={project.id} className="p-4 border rounded-lg">
                  <div className="flex justify-between items-start mb-2">
                    <div>
                      <h4 className="font-medium">{project.name}</h4>
                      <p className="text-sm text-gray-600">{project.client_name}</p>
                    </div>
                    <Badge className={
                      project.status === 'Completed' ? 'bg-green-100 text-green-800' :
                      project.status === 'In Progress' ? 'bg-blue-100 text-blue-800' :
                      project.status === 'On Hold' ? 'bg-yellow-100 text-yellow-800' :
                      'bg-gray-100 text-gray-800'
                    }>
                      {project.status}
                    </Badge>
                  </div>
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <p className="text-gray-600">Progress: {project.progress}%</p>
                      <Progress value={project.progress} className="h-2 mt-1" />
                    </div>
                    <div>
                      <p className="text-gray-600">Timeline: {formatPercentage(timeProgress)}</p>
                      <Progress value={timeProgress} className="h-2 mt-1" />
                    </div>
                  </div>
                  <div className="flex justify-between text-xs text-gray-500 mt-2">
                    <span>Start: {startDate.toLocaleDateString()}</span>
                    <span>End: {endDate.toLocaleDateString()}</span>
                  </div>
                </div>
              );
            })}
          </div>
        </CardContent>
      </Card>

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Activity className="w-5 h-5 text-blue-500" />
            <span>Quick Insights</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="text-center p-4 bg-blue-50 rounded-lg">
              <p className="text-2xl font-bold text-blue-600">{formatPercentage((completedProjects / totalProjects) * 100)}</p>
              <p className="text-sm text-gray-600">Completion Rate</p>
            </div>
            <div className="text-center p-4 bg-green-50 rounded-lg">
              <p className="text-2xl font-bold text-green-600">{formatPercentage(budgetUtilization)}</p>
              <p className="text-sm text-gray-600">Budget Efficiency</p>
            </div>
            <div className="text-center p-4 bg-purple-50 rounded-lg">
              <p className="text-2xl font-bold text-purple-600">{projectsAtRisk.length}</p>
              <p className="text-sm text-gray-600">Projects at Risk</p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
