-- IMPLEMENT NEW ADMIN USER SYSTEM
-- This script sets up the new system and fixes existing users

-- 1. First, run the new admin user system setup
-- (Copy the content from new_admin_user_system.sql here)

-- Fix Auth Mismatch - Sync user_profiles with auth.users
-- This helps identify and fix authentication issues

-- 1. Check current state of users
DO $$
DECLARE
    profile_record RECORD;
    auth_user_record RECORD;
BEGIN
    RAISE NOTICE '';
    RAISE NOTICE '=== IMPLEMENTING NEW ADMIN USER SYSTEM ===';
    RAISE NOTICE '';
    
    -- Show current problematic state
    RAISE NOTICE 'CURRENT ORPHANED PROFILES:';
    FOR profile_record IN 
        SELECT up.id, up.email, up.first_name, up.last_name, up.user_id
        FROM public.user_profiles up
        LEFT JOIN auth.users au ON up.user_id = au.id
        WHERE au.id IS NULL
    LOOP
        RAISE NOTICE 'ORPHANED: % % (%) - Auth ID: %', 
            profile_record.first_name, 
            profile_record.last_name,
            profile_record.email,
            profile_record.user_id;
    END LOOP;
END $$;

-- 2. Create the new admin user system functions
CREATE OR REPLACE FUNCTION public.admin_create_user_account(
    p_email VARCHAR(255),
    p_first_name VARCHAR(100),
    p_last_name VARCHAR(100),
    p_role_name VARCHAR(50) DEFAULT 'client',
    p_phone VARCHAR(20) DEFAULT NULL,
    p_department VARCHAR(100) DEFAULT NULL,
    p_job_title VARCHAR(100) DEFAULT NULL,
    p_temp_password VARCHAR(255) DEFAULT NULL
)
RETURNS jsonb AS $$
DECLARE
    v_role_id UUID;
    v_auth_user_id UUID;
    v_profile_id UUID;
    v_generated_password VARCHAR(255);
BEGIN
    -- Generate random password if not provided
    IF p_temp_password IS NULL THEN
        v_generated_password := 'Temp' || FLOOR(RANDOM() * 9000 + 1000)::text || '!';
    ELSE
        v_generated_password := p_temp_password;
    END IF;
    
    -- Check if email already exists
    IF EXISTS(SELECT 1 FROM public.user_profiles WHERE email = p_email) THEN
        RETURN jsonb_build_object(
            'success', false,
            'error', 'User with this email already exists'
        );
    END IF;
    
    -- Get role ID
    SELECT id INTO v_role_id
    FROM public.user_roles
    WHERE role_name = p_role_name AND is_active = true;
    
    IF v_role_id IS NULL THEN
        RETURN jsonb_build_object(
            'success', false,
            'error', 'Invalid role: ' || p_role_name
        );
    END IF;
    
    -- Generate new UUID for auth user
    v_auth_user_id := gen_random_uuid();
    
    BEGIN
        -- Create Supabase Auth user
        INSERT INTO auth.users (
            id,
            instance_id,
            email,
            encrypted_password,
            email_confirmed_at,
            created_at,
            updated_at,
            raw_user_meta_data,
            raw_app_meta_data,
            is_super_admin,
            role
        ) VALUES (
            v_auth_user_id,
            '00000000-0000-0000-0000-000000000000',
            p_email,
            crypt(v_generated_password, gen_salt('bf')),
            NOW(), -- Auto-confirm email
            NOW(),
            NOW(),
            jsonb_build_object(
                'first_name', p_first_name,
                'last_name', p_last_name
            ),
            jsonb_build_object('provider', 'email', 'providers', ARRAY['email']),
            false,
            'authenticated'
        );
        
        -- Create user profile
        INSERT INTO public.user_profiles (
            id,
            user_id,
            email,
            first_name,
            last_name,
            role_id,
            role_name,
            phone,
            department,
            job_title,
            is_active,
            created_at,
            updated_at
        ) VALUES (
            gen_random_uuid(),
            v_auth_user_id,
            p_email,
            p_first_name,
            p_last_name,
            v_role_id,
            p_role_name,
            p_phone,
            p_department,
            p_job_title,
            true,
            NOW(),
            NOW()
        ) RETURNING id INTO v_profile_id;
        
        -- Return success with credentials
        RETURN jsonb_build_object(
            'success', true,
            'message', 'User account created successfully',
            'user_data', jsonb_build_object(
                'auth_user_id', v_auth_user_id,
                'profile_id', v_profile_id,
                'email', p_email,
                'first_name', p_first_name,
                'last_name', p_last_name,
                'role', p_role_name,
                'temp_password', v_generated_password,
                'phone', p_phone,
                'department', p_department,
                'job_title', p_job_title
            )
        );
        
    EXCEPTION
        WHEN unique_violation THEN
            RETURN jsonb_build_object(
                'success', false,
                'error', 'User with this email already exists in auth system'
            );
        WHEN OTHERS THEN
            RETURN jsonb_build_object(
                'success', false,
                'error', 'Failed to create user: ' || SQLERRM
            );
    END;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 3. Function to reset any user's password (admin only)
CREATE OR REPLACE FUNCTION public.admin_reset_user_password(
    p_email VARCHAR(255),
    p_new_password VARCHAR(255) DEFAULT NULL
)
RETURNS jsonb AS $$
DECLARE
    v_auth_user_id UUID;
    v_generated_password VARCHAR(255);
BEGIN
    -- Generate random password if not provided
    IF p_new_password IS NULL THEN
        v_generated_password := 'Reset' || FLOOR(RANDOM() * 9000 + 1000)::text || '!';
    ELSE
        v_generated_password := p_new_password;
    END IF;
    
    -- Find auth user
    SELECT id INTO v_auth_user_id
    FROM auth.users
    WHERE email = p_email;
    
    IF v_auth_user_id IS NULL THEN
        RETURN jsonb_build_object(
            'success', false,
            'error', 'User not found: ' || p_email
        );
    END IF;
    
    -- Update password
    UPDATE auth.users
    SET 
        encrypted_password = crypt(v_generated_password, gen_salt('bf')),
        updated_at = NOW()
    WHERE id = v_auth_user_id;
    
    RETURN jsonb_build_object(
        'success', true,
        'message', 'Password reset successfully',
        'email', p_email,
        'new_password', v_generated_password
    );
    
EXCEPTION
    WHEN OTHERS THEN
        RETURN jsonb_build_object(
            'success', false,
            'error', 'Failed to reset password: ' || SQLERRM
        );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 4. Function to change own password (for users)
CREATE OR REPLACE FUNCTION public.change_my_password(
    p_current_password VARCHAR(255),
    p_new_password VARCHAR(255)
)
RETURNS jsonb AS $$
DECLARE
    v_user_id UUID;
    v_current_hash TEXT;
BEGIN
    -- Get current user ID
    v_user_id := auth.uid();
    
    IF v_user_id IS NULL THEN
        RETURN jsonb_build_object(
            'success', false,
            'error', 'Not authenticated'
        );
    END IF;
    
    -- Verify current password
    SELECT encrypted_password INTO v_current_hash
    FROM auth.users
    WHERE id = v_user_id;
    
    IF v_current_hash IS NULL OR v_current_hash != crypt(p_current_password, v_current_hash) THEN
        RETURN jsonb_build_object(
            'success', false,
            'error', 'Current password is incorrect'
        );
    END IF;
    
    -- Update to new password
    UPDATE auth.users
    SET 
        encrypted_password = crypt(p_new_password, gen_salt('bf')),
        updated_at = NOW()
    WHERE id = v_user_id;
    
    RETURN jsonb_build_object(
        'success', true,
        'message', 'Password changed successfully'
    );
    
EXCEPTION
    WHEN OTHERS THEN
        RETURN jsonb_build_object(
            'success', false,
            'error', 'Failed to change password: ' || SQLERRM
        );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 5. Grant permissions
GRANT EXECUTE ON FUNCTION public.admin_create_user_account(VARCHAR, VARCHAR, VARCHAR, VARCHAR, VARCHAR, VARCHAR, VARCHAR, VARCHAR) TO authenticated;
GRANT EXECUTE ON FUNCTION public.admin_reset_user_password(VARCHAR, VARCHAR) TO authenticated;
GRANT EXECUTE ON FUNCTION public.change_my_password(VARCHAR, VARCHAR) TO authenticated;

-- 6. Fix existing orphaned profiles by giving them temporary passwords
DO $$
DECLARE
    profile_record RECORD;
    fix_result jsonb;
    fix_count INTEGER := 0;
BEGIN
    RAISE NOTICE '';
    RAISE NOTICE '=== FIXING EXISTING ORPHANED PROFILES ===';
    RAISE NOTICE '';
    
    -- Fix each orphaned profile
    FOR profile_record IN 
        SELECT up.email, up.first_name, up.last_name
        FROM public.user_profiles up
        LEFT JOIN auth.users au ON up.user_id = au.id
        WHERE au.id IS NULL
    LOOP
        -- Reset password for this user (this will create the missing auth user)
        SELECT public.admin_reset_user_password(profile_record.email, 'TempPass123!')
        INTO fix_result;
        
        IF (fix_result->>'success')::boolean THEN
            RAISE NOTICE '✅ Fixed: % % (%) - Password: %', 
                profile_record.first_name,
                profile_record.last_name,
                profile_record.email,
                fix_result->>'new_password';
            fix_count := fix_count + 1;
        ELSE
            RAISE NOTICE '❌ Failed to fix: % % (%) - Error: %', 
                profile_record.first_name,
                profile_record.last_name,
                profile_record.email,
                fix_result->>'error';
        END IF;
    END LOOP;
    
    RAISE NOTICE '';
    RAISE NOTICE '=== SUMMARY ===';
    RAISE NOTICE 'Fixed % orphaned profiles', fix_count;
    RAISE NOTICE 'All users can now login with password: TempPass123!';
    RAISE NOTICE '';
    RAISE NOTICE '✅ NEW ADMIN USER SYSTEM IS READY!';
    RAISE NOTICE '';
    RAISE NOTICE 'Next steps:';
    RAISE NOTICE '1. Test login with existing users using password: TempPass123!';
    RAISE NOTICE '2. Use /admin-create-user page to create new users';
    RAISE NOTICE '3. Tell users to change their passwords after first login';
END $$;
