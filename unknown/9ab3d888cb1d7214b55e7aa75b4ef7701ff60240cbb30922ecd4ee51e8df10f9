import { AnalyticsData } from './analytics';

// Export service for generating PDF and Excel reports
export class ExportService {
  // Generate PDF report
  static async exportToPDF(data: AnalyticsData, reportType: 'financial' | 'revenue' | 'expenses' | 'cashflow' = 'financial'): Promise<void> {
    try {
      console.log('🔄 Generating PDF report...');
      
      // Create HTML content for the report
      const htmlContent = this.generateHTMLReport(data, reportType);
      
      // Create a new window for printing
      const printWindow = window.open('', '_blank');
      if (!printWindow) {
        throw new Error('Unable to open print window. Please check popup blockers.');
      }

      // Write the HTML content
      printWindow.document.write(`
        <!DOCTYPE html>
        <html>
        <head>
          <title>${this.getReportTitle(reportType)} - ${new Date().toLocaleDateString()}</title>
          <style>
            ${this.getPrintStyles()}
          </style>
        </head>
        <body>
          ${htmlContent}
        </body>
        </html>
      `);

      printWindow.document.close();
      
      // Wait for content to load then print
      printWindow.onload = () => {
        setTimeout(() => {
          printWindow.print();
          printWindow.close();
        }, 250);
      };

      console.log('✅ PDF report generated successfully');
    } catch (error) {
      console.error('❌ Error generating PDF report:', error);
      throw error;
    }
  }

  // Generate Excel/CSV export
  static async exportToExcel(data: AnalyticsData, reportType: 'financial' | 'revenue' | 'expenses' | 'cashflow' = 'financial'): Promise<void> {
    try {
      console.log('🔄 Generating Excel/CSV export...');
      
      let csvContent = '';
      const timestamp = new Date().toISOString().split('T')[0];
      
      switch (reportType) {
        case 'revenue':
          csvContent = this.generateRevenueCSV(data);
          break;
        case 'expenses':
          csvContent = this.generateExpenseCSV(data);
          break;
        case 'cashflow':
          csvContent = this.generateCashFlowCSV(data);
          break;
        default:
          csvContent = this.generateFinancialSummaryCSV(data);
      }

      // Create and download the file
      const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
      const link = document.createElement('a');
      const url = URL.createObjectURL(blob);
      
      link.setAttribute('href', url);
      link.setAttribute('download', `${reportType}-report-${timestamp}.csv`);
      link.style.visibility = 'hidden';
      
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      console.log('✅ Excel/CSV export completed successfully');
    } catch (error) {
      console.error('❌ Error generating Excel export:', error);
      throw error;
    }
  }

  // Generate comprehensive HTML report
  private static generateHTMLReport(data: AnalyticsData, reportType: string): string {
    const { financialSummary, revenueAnalytics, expenseAnalytics, cashFlowAnalytics, insights } = data;
    const reportDate = new Date().toLocaleDateString();
    
    return `
      <div class="report-container">
        <header class="report-header">
          <h1>${this.getReportTitle(reportType)}</h1>
          <p class="report-date">Generated on ${reportDate}</p>
          <p class="report-period">Period: ${financialSummary.period}</p>
        </header>

        <section class="executive-summary">
          <h2>Executive Summary</h2>
          <div class="summary-grid">
            <div class="summary-item">
              <h3>Total Revenue</h3>
              <p class="amount positive">$${financialSummary.totalRevenue.toLocaleString()}</p>
            </div>
            <div class="summary-item">
              <h3>Total Expenses</h3>
              <p class="amount negative">$${financialSummary.totalExpenses.toLocaleString()}</p>
            </div>
            <div class="summary-item">
              <h3>Net Profit</h3>
              <p class="amount ${financialSummary.netProfit >= 0 ? 'positive' : 'negative'}">
                $${financialSummary.netProfit.toLocaleString()}
              </p>
            </div>
            <div class="summary-item">
              <h3>Profit Margin</h3>
              <p class="percentage">${financialSummary.profitMargin.toFixed(1)}%</p>
            </div>
          </div>
        </section>

        ${reportType === 'financial' || reportType === 'revenue' ? this.generateRevenueSection(revenueAnalytics) : ''}
        ${reportType === 'financial' || reportType === 'expenses' ? this.generateExpenseSection(expenseAnalytics) : ''}
        ${reportType === 'financial' || reportType === 'cashflow' ? this.generateCashFlowSection(cashFlowAnalytics) : ''}
        ${this.generateInsightsSection(insights)}
        
        <footer class="report-footer">
          <p>This report was automatically generated by the Construction Management System</p>
          <p>Report ID: ${this.generateReportId()}</p>
        </footer>
      </div>
    `;
  }

  private static generateRevenueSection(revenueAnalytics: any): string {
    return `
      <section class="revenue-section">
        <h2>Revenue Analysis</h2>
        <p class="section-summary">Total Revenue: $${revenueAnalytics.totalRevenue.toLocaleString()}</p>
        
        <h3>Revenue by Category</h3>
        <table class="data-table">
          <thead>
            <tr>
              <th>Category</th>
              <th>Amount</th>
              <th>Percentage</th>
              <th>Transactions</th>
            </tr>
          </thead>
          <tbody>
            ${revenueAnalytics.revenueByCategory.map((item: any) => `
              <tr>
                <td>${item.category}</td>
                <td>$${item.amount.toLocaleString()}</td>
                <td>${item.percentage.toFixed(1)}%</td>
                <td>${item.count}</td>
              </tr>
            `).join('')}
          </tbody>
        </table>

        <h3>Top Clients</h3>
        <table class="data-table">
          <thead>
            <tr>
              <th>Client</th>
              <th>Revenue</th>
              <th>Percentage</th>
              <th>Projects</th>
            </tr>
          </thead>
          <tbody>
            ${revenueAnalytics.revenueByClient.slice(0, 10).map((item: any) => `
              <tr>
                <td>${item.clientName}</td>
                <td>$${item.amount.toLocaleString()}</td>
                <td>${item.percentage.toFixed(1)}%</td>
                <td>${item.projectCount}</td>
              </tr>
            `).join('')}
          </tbody>
        </table>
      </section>
    `;
  }

  private static generateExpenseSection(expenseAnalytics: any): string {
    return `
      <section class="expense-section">
        <h2>Expense Analysis</h2>
        <p class="section-summary">Total Expenses: $${expenseAnalytics.totalExpenses.toLocaleString()}</p>
        
        <h3>Expenses by Category</h3>
        <table class="data-table">
          <thead>
            <tr>
              <th>Category</th>
              <th>Amount</th>
              <th>Percentage</th>
              <th>Transactions</th>
            </tr>
          </thead>
          <tbody>
            ${expenseAnalytics.expensesByCategory.map((item: any) => `
              <tr>
                <td>${item.category}</td>
                <td>$${item.amount.toLocaleString()}</td>
                <td>${item.percentage.toFixed(1)}%</td>
                <td>${item.count}</td>
              </tr>
            `).join('')}
          </tbody>
        </table>

        <h3>Top Expenses</h3>
        <table class="data-table">
          <thead>
            <tr>
              <th>Description</th>
              <th>Amount</th>
              <th>Category</th>
              <th>Date</th>
            </tr>
          </thead>
          <tbody>
            ${expenseAnalytics.topExpenses.slice(0, 10).map((item: any) => `
              <tr>
                <td>${item.description}</td>
                <td>$${item.amount.toLocaleString()}</td>
                <td>${item.category}</td>
                <td>${new Date(item.date).toLocaleDateString()}</td>
              </tr>
            `).join('')}
          </tbody>
        </table>
      </section>
    `;
  }

  private static generateCashFlowSection(cashFlowAnalytics: any): string {
    return `
      <section class="cashflow-section">
        <h2>Cash Flow Analysis</h2>
        <div class="cashflow-summary">
          <div class="cashflow-item">
            <h3>Total Inflows</h3>
            <p class="amount positive">$${cashFlowAnalytics.inflows.toLocaleString()}</p>
          </div>
          <div class="cashflow-item">
            <h3>Total Outflows</h3>
            <p class="amount negative">$${cashFlowAnalytics.outflows.toLocaleString()}</p>
          </div>
          <div class="cashflow-item">
            <h3>Net Cash Flow</h3>
            <p class="amount ${cashFlowAnalytics.netCashFlow >= 0 ? 'positive' : 'negative'}">
              $${cashFlowAnalytics.netCashFlow.toLocaleString()}
            </p>
          </div>
        </div>

        <h3>Monthly Cash Flow</h3>
        <table class="data-table">
          <thead>
            <tr>
              <th>Month</th>
              <th>Amount</th>
              <th>Transactions</th>
            </tr>
          </thead>
          <tbody>
            ${cashFlowAnalytics.cashFlowByMonth.map((item: any) => `
              <tr>
                <td>${item.month}</td>
                <td>$${item.amount.toLocaleString()}</td>
                <td>${item.count}</td>
              </tr>
            `).join('')}
          </tbody>
        </table>
      </section>
    `;
  }

  private static generateInsightsSection(insights: any[]): string {
    if (!insights.length) return '';
    
    return `
      <section class="insights-section">
        <h2>Business Insights</h2>
        <div class="insights-grid">
          ${insights.map(insight => `
            <div class="insight-item ${insight.type}">
              <h3>${insight.title}</h3>
              <p>${insight.description}</p>
              ${insight.recommendation ? `<p class="recommendation"><strong>Recommendation:</strong> ${insight.recommendation}</p>` : ''}
              <span class="priority ${insight.priority}">${insight.priority.toUpperCase()} PRIORITY</span>
            </div>
          `).join('')}
        </div>
      </section>
    `;
  }

  // CSV Generation Methods
  private static generateFinancialSummaryCSV(data: AnalyticsData): string {
    const { financialSummary, revenueAnalytics, expenseAnalytics } = data;
    
    let csv = 'Financial Summary Report\n';
    csv += `Generated on,${new Date().toLocaleDateString()}\n`;
    csv += `Period,${financialSummary.period}\n\n`;
    
    csv += 'Metric,Amount\n';
    csv += `Total Revenue,$${financialSummary.totalRevenue}\n`;
    csv += `Total Expenses,$${financialSummary.totalExpenses}\n`;
    csv += `Net Profit,$${financialSummary.netProfit}\n`;
    csv += `Profit Margin,${financialSummary.profitMargin.toFixed(1)}%\n\n`;
    
    csv += 'Revenue by Category\n';
    csv += 'Category,Amount,Percentage,Transactions\n';
    revenueAnalytics.revenueByCategory.forEach((item: any) => {
      csv += `${item.category},$${item.amount},${item.percentage.toFixed(1)}%,${item.count}\n`;
    });
    
    csv += '\nExpenses by Category\n';
    csv += 'Category,Amount,Percentage,Transactions\n';
    expenseAnalytics.expensesByCategory.forEach((item: any) => {
      csv += `${item.category},$${item.amount},${item.percentage.toFixed(1)}%,${item.count}\n`;
    });
    
    return csv;
  }

  private static generateRevenueCSV(data: AnalyticsData): string {
    const { revenueAnalytics } = data;
    
    let csv = 'Revenue Analysis Report\n';
    csv += `Generated on,${new Date().toLocaleDateString()}\n`;
    csv += `Total Revenue,$${revenueAnalytics.totalRevenue}\n\n`;
    
    csv += 'Revenue by Category\n';
    csv += 'Category,Amount,Percentage,Transactions\n';
    revenueAnalytics.revenueByCategory.forEach((item: any) => {
      csv += `${item.category},$${item.amount},${item.percentage.toFixed(1)}%,${item.count}\n`;
    });
    
    csv += '\nRevenue by Client\n';
    csv += 'Client,Amount,Percentage,Projects\n';
    revenueAnalytics.revenueByClient.forEach((item: any) => {
      csv += `${item.clientName},$${item.amount},${item.percentage.toFixed(1)}%,${item.projectCount}\n`;
    });
    
    return csv;
  }

  private static generateExpenseCSV(data: AnalyticsData): string {
    const { expenseAnalytics } = data;
    
    let csv = 'Expense Analysis Report\n';
    csv += `Generated on,${new Date().toLocaleDateString()}\n`;
    csv += `Total Expenses,$${expenseAnalytics.totalExpenses}\n\n`;
    
    csv += 'Expenses by Category\n';
    csv += 'Category,Amount,Percentage,Transactions\n';
    expenseAnalytics.expensesByCategory.forEach((item: any) => {
      csv += `${item.category},$${item.amount},${item.percentage.toFixed(1)}%,${item.count}\n`;
    });
    
    csv += '\nTop Expenses\n';
    csv += 'Description,Amount,Category,Date\n';
    expenseAnalytics.topExpenses.forEach((item: any) => {
      csv += `"${item.description}",$${item.amount},${item.category},${new Date(item.date).toLocaleDateString()}\n`;
    });
    
    return csv;
  }

  private static generateCashFlowCSV(data: AnalyticsData): string {
    const { cashFlowAnalytics } = data;
    
    let csv = 'Cash Flow Analysis Report\n';
    csv += `Generated on,${new Date().toLocaleDateString()}\n`;
    csv += `Net Cash Flow,$${cashFlowAnalytics.netCashFlow}\n`;
    csv += `Total Inflows,$${cashFlowAnalytics.inflows}\n`;
    csv += `Total Outflows,$${cashFlowAnalytics.outflows}\n\n`;
    
    csv += 'Monthly Cash Flow\n';
    csv += 'Month,Amount,Transactions\n';
    cashFlowAnalytics.cashFlowByMonth.forEach((item: any) => {
      csv += `${item.month},$${item.amount},${item.count}\n`;
    });
    
    return csv;
  }

  // Helper methods
  private static getReportTitle(reportType: string): string {
    switch (reportType) {
      case 'revenue': return 'Revenue Analysis Report';
      case 'expenses': return 'Expense Analysis Report';
      case 'cashflow': return 'Cash Flow Analysis Report';
      default: return 'Financial Analysis Report';
    }
  }

  private static generateReportId(): string {
    return `RPT-${Date.now()}-${Math.random().toString(36).substr(2, 9).toUpperCase()}`;
  }

  private static getPrintStyles(): string {
    return `
      body { font-family: Arial, sans-serif; margin: 20px; color: #333; }
      .report-container { max-width: 800px; margin: 0 auto; }
      .report-header { text-align: center; margin-bottom: 30px; border-bottom: 2px solid #333; padding-bottom: 20px; }
      .report-header h1 { margin: 0; font-size: 24px; }
      .report-date, .report-period { margin: 5px 0; color: #666; }
      .executive-summary { margin-bottom: 30px; }
      .summary-grid { display: grid; grid-template-columns: repeat(2, 1fr); gap: 20px; margin-top: 15px; }
      .summary-item { text-align: center; padding: 15px; border: 1px solid #ddd; }
      .summary-item h3 { margin: 0 0 10px 0; font-size: 14px; color: #666; }
      .amount { font-size: 18px; font-weight: bold; margin: 0; }
      .amount.positive { color: #10b981; }
      .amount.negative { color: #ef4444; }
      .percentage { font-size: 16px; font-weight: bold; margin: 0; }
      .data-table { width: 100%; border-collapse: collapse; margin: 15px 0; }
      .data-table th, .data-table td { border: 1px solid #ddd; padding: 8px; text-align: left; }
      .data-table th { background-color: #f5f5f5; font-weight: bold; }
      .insights-grid { display: grid; grid-template-columns: 1fr; gap: 15px; }
      .insight-item { padding: 15px; border-left: 4px solid #3b82f6; background-color: #f8f9fa; }
      .insight-item.positive { border-left-color: #10b981; }
      .insight-item.negative { border-left-color: #ef4444; }
      .insight-item.warning { border-left-color: #f59e0b; }
      .insight-item h3 { margin: 0 0 10px 0; font-size: 16px; }
      .recommendation { font-style: italic; margin-top: 10px; }
      .priority { display: inline-block; padding: 2px 8px; border-radius: 4px; font-size: 10px; font-weight: bold; }
      .priority.high { background-color: #fee2e2; color: #dc2626; }
      .priority.medium { background-color: #fef3c7; color: #d97706; }
      .priority.low { background-color: #e0f2fe; color: #0369a1; }
      .report-footer { margin-top: 40px; text-align: center; font-size: 12px; color: #666; border-top: 1px solid #ddd; padding-top: 20px; }
      section { margin-bottom: 30px; }
      h2 { color: #333; border-bottom: 1px solid #ddd; padding-bottom: 10px; }
      h3 { color: #555; margin-top: 20px; }
      .section-summary { font-weight: bold; margin-bottom: 15px; }
      .cashflow-summary { display: grid; grid-template-columns: repeat(3, 1fr); gap: 20px; margin: 15px 0; }
      .cashflow-item { text-align: center; padding: 15px; border: 1px solid #ddd; }
      @media print { body { margin: 0; } .report-container { max-width: none; } }
    `;
  }
}
