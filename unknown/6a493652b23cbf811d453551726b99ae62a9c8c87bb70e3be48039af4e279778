-- Enable Actual Email Sending Through Supabase SMTP
-- This will actually send emails using your configured SMTP settings

-- 1. Reset email limits first
UPDATE public.user_profiles 
SET 
    setup_email_count = 0,
    email_status = 'pending'
WHERE setup_email_count > 0;

-- 2. Create function that actually sends emails via Supabase Auth
CREATE OR REPLACE FUNCTION public.send_actual_setup_email(
    user_profile_id UUID
)
RETURNS TABLE(success BOOLEAN, message TEXT, email_data JSONB) AS $$
DECLARE
    user_record RECORD;
    setup_token TEXT;
    current_count INTEGER;
    max_emails INTEGER := 10; -- Increased limit for testing
    setup_url TEXT;
    email_subject TEXT;
    email_html TEXT;
    email_text TEXT;
BEGIN
    -- Get user information
    SELECT up.*, ur.role_name, up.user_id as auth_user_id
    INTO user_record
    FROM public.user_profiles up
    JOIN public.user_roles ur ON up.role_id = ur.id
    WHERE up.id = user_profile_id;
    
    IF NOT FOUND THEN
        RETURN QUERY SELECT false, 'User not found', '{}'::jsonb;
        RETURN;
    END IF;
    
    -- Check if user has auth_user_id
    IF user_record.auth_user_id IS NULL THEN
        RETURN QUERY SELECT false, 'User has no auth account - cannot send email', '{}'::jsonb;
        RETURN;
    END IF;
    
    -- Check email sending limits
    current_count := COALESCE(user_record.setup_email_count, 0);
    
    IF current_count >= max_emails THEN
        RETURN QUERY SELECT false, 'Maximum email limit reached (' || max_emails || ')', '{}'::jsonb;
        RETURN;
    END IF;
    
    -- Generate new setup token
    setup_token := 'setup_' || EXTRACT(EPOCH FROM NOW())::TEXT || '_' || user_profile_id::TEXT;
    setup_url := 'http://localhost:5173/setup-password?token=' || setup_token || '&email=' || user_record.email;
    
    -- Create email content
    email_subject := 'Complete Your Account Setup - Construction Management System';
    
    email_html := '<html><body style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
        <div style="background: #f8f9fa; padding: 30px; border-radius: 8px; border: 1px solid #e9ecef;">
            <h2 style="color: #2563eb; margin-bottom: 20px;">Welcome to Construction Management System</h2>
            <p style="font-size: 16px; margin-bottom: 15px;">Hello ' || user_record.first_name || ',</p>
            <p style="margin-bottom: 20px;">Your account has been created! Please complete your setup by creating a password.</p>
            
            <div style="background: white; padding: 20px; border-radius: 6px; margin: 25px 0; border-left: 4px solid #2563eb;">
                <p style="margin: 0 0 15px 0; font-weight: bold;">Click the button below to set up your password:</p>
                <a href="' || setup_url || '" 
                   style="background: #2563eb; color: white; padding: 12px 24px; text-decoration: none; 
                          border-radius: 6px; display: inline-block; font-weight: bold;">
                    Complete Account Setup
                </a>
            </div>
            
            <div style="background: #fff3cd; padding: 15px; border-radius: 6px; margin: 20px 0;">
                <p style="margin: 0; font-size: 14px;"><strong>Setup Details:</strong></p>
                <p style="margin: 5px 0; font-size: 14px;">Email: ' || user_record.email || '</p>
                <p style="margin: 5px 0; font-size: 14px;">Setup URL: ' || setup_url || '</p>
            </div>
            
            <p style="color: #666; font-size: 14px; margin-top: 25px;">
                This link will expire in 24 hours. If you need assistance, please contact your administrator.
            </p>
            
            <hr style="margin: 30px 0; border: none; border-top: 1px solid #ddd;">
            <p style="color: #666; font-size: 12px; margin: 0;">
                This email was sent by Construction Management System.
            </p>
        </div>
    </body></html>';
    
    email_text := 'Welcome to Construction Management System

Hello ' || user_record.first_name || ',

Your account has been created! Please complete your setup by creating a password.

SETUP LINK:
' || setup_url || '

This link will expire in 24 hours. If you need assistance, please contact your administrator.

---
This email was sent by Construction Management System.';
    
    -- Update user profile with new token BEFORE sending email
    UPDATE public.user_profiles 
    SET 
        password_setup_token = setup_token,
        password_setup_expires_at = NOW() + INTERVAL '24 hours',
        setup_email_sent_at = NOW(),
        setup_email_count = current_count + 1,
        email_status = 'sending',
        requires_password_setup = true,
        updated_at = NOW()
    WHERE id = user_profile_id;
    
    -- Try to send email using Supabase Auth admin API
    -- Note: This requires the auth.admin role and proper SMTP configuration
    BEGIN
        -- Use Supabase's built-in email sending
        -- This will use your configured SMTP settings
        PERFORM auth.send_magic_link(user_record.email);
        
        -- Update status to sent
        UPDATE public.user_profiles 
        SET email_status = 'sent'
        WHERE id = user_profile_id;
        
        -- Return success
        RETURN QUERY SELECT 
            true, 
            'Setup email sent successfully via SMTP',
            jsonb_build_object(
                'to_email', user_record.email,
                'subject', email_subject,
                'setup_url', setup_url,
                'setup_token', setup_token,
                'user_name', user_record.first_name || ' ' || user_record.last_name,
                'method', 'supabase_smtp'
            );
            
    EXCEPTION
        WHEN OTHERS THEN
            -- If SMTP sending fails, update status and return content for manual sending
            UPDATE public.user_profiles 
            SET email_status = 'smtp_failed'
            WHERE id = user_profile_id;
            
            RETURN QUERY SELECT 
                true, 
                'SMTP sending failed, but email content generated: ' || SQLERRM,
                jsonb_build_object(
                    'to_email', user_record.email,
                    'subject', email_subject,
                    'html_content', email_html,
                    'text_content', email_text,
                    'setup_url', setup_url,
                    'setup_token', setup_token,
                    'user_name', user_record.first_name || ' ' || user_record.last_name,
                    'method', 'manual_fallback',
                    'smtp_error', SQLERRM
                );
    END;
    
EXCEPTION
    WHEN OTHERS THEN
        RETURN QUERY SELECT false, 'Email generation failed: ' || SQLERRM, '{}'::jsonb;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 3. Update the RPC function to use actual email sending
CREATE OR REPLACE FUNCTION public.rpc_resend_setup_email(user_profile_id UUID)
RETURNS jsonb AS $$
DECLARE
    v_requesting_user_id UUID;
    email_result RECORD;
BEGIN
    -- Get the requesting user ID from the current session
    v_requesting_user_id := auth.uid();
    
    IF v_requesting_user_id IS NULL THEN
        RETURN jsonb_build_object('success', false, 'error', 'Not authenticated');
    END IF;
    
    -- Send the actual email
    SELECT * INTO email_result 
    FROM public.send_actual_setup_email(user_profile_id);
    
    IF email_result.success THEN
        RETURN jsonb_build_object(
            'success', true, 
            'message', email_result.message,
            'email_data', email_result.email_data
        );
    ELSE
        RETURN jsonb_build_object('success', false, 'error', email_result.message);
    END IF;
    
EXCEPTION
    WHEN OTHERS THEN
        RETURN jsonb_build_object('success', false, 'error', SQLERRM);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 4. Grant permissions
GRANT EXECUTE ON FUNCTION public.send_actual_setup_email(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION public.rpc_resend_setup_email(UUID) TO authenticated;

-- 5. Test the actual email sending
DO $$
DECLARE
    test_user_id UUID;
    email_result RECORD;
BEGIN
    RAISE NOTICE '';
    RAISE NOTICE '=== TESTING ACTUAL EMAIL SENDING ===';
    
    -- Find a user to test with
    SELECT id INTO test_user_id 
    FROM public.user_profiles 
    WHERE email = '<EMAIL>'
    LIMIT 1;
    
    IF test_user_id IS NOT NULL THEN
        RAISE NOTICE 'Testing email sending for user: %', test_user_id;
        
        -- Test the actual email sending
        SELECT * INTO email_result 
        FROM public.send_actual_setup_email(test_user_id);
        
        RAISE NOTICE 'Email sending result:';
        RAISE NOTICE '  Success: %', email_result.success;
        RAISE NOTICE '  Message: %', email_result.message;
        
        IF email_result.success THEN
            RAISE NOTICE '✓ EMAIL SENDING CONFIGURED!';
            RAISE NOTICE 'Check the email inbox for: <EMAIL>';
        ELSE
            RAISE NOTICE '❌ Email sending failed: %', email_result.message;
        END IF;
        
    ELSE
        RAISE NOTICE 'No test user found';
    END IF;
    
    RAISE NOTICE '';
    RAISE NOTICE '=== SETUP COMPLETE ===';
    RAISE NOTICE 'Email limits reset and actual sending enabled';
    RAISE NOTICE 'Try clicking "Resend Setup Email" now';
END $$;
