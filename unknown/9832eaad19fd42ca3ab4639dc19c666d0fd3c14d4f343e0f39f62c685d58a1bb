import React, { useState } from 'react';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/components/ui/use-toast';
import { CompanyAsset, AssetService, AssetMaintenance } from '@/lib/assetService';
import { 
  Edit, 
  Trash2, 
  Wrench, 
  FileText, 
  Calendar, 
  DollarSign, 
  MapPin, 
  User,
  AlertTriangle,
  CheckCircle
} from 'lucide-react';
import AssetForm from './AssetForm';
import MaintenanceScheduler from './MaintenanceScheduler';
import AssetHistory from './AssetHistory';

interface AssetManagementProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  asset: CompanyAsset | null;
  categories: any[];
  onAssetUpdated: () => void;
}

const AssetManagement: React.FC<AssetManagementProps> = ({
  open,
  onOpenChange,
  asset,
  categories,
  onAssetUpdated
}) => {
  const { toast } = useToast();
  const [showEditForm, setShowEditForm] = useState(false);
  const [showMaintenanceScheduler, setShowMaintenanceScheduler] = useState(false);
  const [showHistory, setShowHistory] = useState(false);
  const [loading, setLoading] = useState(false);
  const [maintenanceRecords, setMaintenanceRecords] = useState<AssetMaintenance[]>([]);
  const [maintenanceLoading, setMaintenanceLoading] = useState(false);

  // Load maintenance records when component opens
  React.useEffect(() => {
    if (open && asset) {
      loadMaintenanceRecords();
    }
  }, [open, asset]);

  const loadMaintenanceRecords = async () => {
    if (!asset) return;

    setMaintenanceLoading(true);
    try {
      const records = await AssetService.getMaintenanceRecords(asset.id);
      setMaintenanceRecords(records);

      // Calculate YTD maintenance cost
      const currentYear = new Date().getFullYear();
      const ytdCost = records
        .filter(record => {
          const recordYear = new Date(record.created_at).getFullYear();
          return recordYear === currentYear && record.status === 'Completed';
        })
        .reduce((sum, record) => sum + (record.cost || 0), 0);

      console.log('Loaded maintenance records:', records);
      console.log('YTD maintenance cost:', ytdCost);
    } catch (error) {
      console.error('Error loading maintenance records:', error);
    } finally {
      setMaintenanceLoading(false);
    }
  };

  if (!asset) return null;

  const handleDelete = async () => {
    if (!confirm(`Are you sure you want to delete ${asset.name}? This action cannot be undone.`)) {
      return;
    }

    setLoading(true);
    try {
      await AssetService.deleteAsset(asset.id);
      toast({
        title: "Asset Deleted",
        description: `${asset.name} has been removed from your assets.`,
      });
      onAssetUpdated();
      onOpenChange(false);
    } catch (error) {
      console.error('Error deleting asset:', error);
      toast({
        title: "Error",
        description: "Failed to delete asset. Please try again.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const generateReport = () => {
    if (!asset) return;

    // Create a comprehensive asset report
    const reportData = {
      assetInfo: {
        name: asset.name,
        assetNumber: asset.asset_number,
        type: asset.asset_type,
        category: asset.category?.name || 'Uncategorized',
        status: asset.status,
        condition: asset.condition
      },
      financial: {
        purchasePrice: asset.purchase_price,
        currentValue: asset.current_value,
        depreciation: asset.purchase_price - asset.current_value,
        depreciationRate: asset.depreciation_rate
      },
      details: {
        brand: asset.brand,
        model: asset.model,
        serialNumber: asset.serial_number,
        purchaseDate: asset.purchase_date,
        location: asset.location,
        assignedTo: asset.assigned_to
      },
      maintenance: {
        lastMaintenance: asset.last_maintenance_date,
        nextMaintenance: asset.next_maintenance_date,
        maintenanceInterval: asset.maintenance_interval_months,
        ytdMaintenanceCost: asset.maintenance_cost_ytd
      }
    };

    // Generate and download report
    const reportContent = `
ASSET REPORT
============

Asset Information:
- Name: ${reportData.assetInfo.name}
- Asset Number: ${reportData.assetInfo.assetNumber}
- Type: ${reportData.assetInfo.type}
- Category: ${reportData.assetInfo.category}
- Status: ${reportData.assetInfo.status}
- Condition: ${reportData.assetInfo.condition}

Financial Information:
- Purchase Price: $${reportData.financial.purchasePrice.toLocaleString()}
- Current Value: $${reportData.financial.currentValue.toLocaleString()}
- Total Depreciation: $${reportData.financial.depreciation.toLocaleString()}
- Depreciation Rate: ${reportData.financial.depreciationRate}% per year

Asset Details:
- Brand: ${reportData.details.brand || 'N/A'}
- Model: ${reportData.details.model || 'N/A'}
- Serial Number: ${reportData.details.serialNumber || 'N/A'}
- Purchase Date: ${new Date(reportData.details.purchaseDate).toLocaleDateString()}
- Location: ${reportData.details.location || 'N/A'}
- Assigned To: ${reportData.details.assignedTo || 'Unassigned'}

Maintenance Information:
- Last Maintenance: ${reportData.maintenance.lastMaintenance ? new Date(reportData.maintenance.lastMaintenance).toLocaleDateString() : 'None recorded'}
- Next Maintenance: ${reportData.maintenance.nextMaintenance ? new Date(reportData.maintenance.nextMaintenance).toLocaleDateString() : 'Not scheduled'}
- Maintenance Interval: ${reportData.maintenance.maintenanceInterval || 12} months
- YTD Maintenance Cost: $${(reportData.maintenance.ytdMaintenanceCost || 0).toLocaleString()}

Generated on: ${new Date().toLocaleDateString()} at ${new Date().toLocaleTimeString()}
    `.trim();

    // Create and download the report
    const blob = new Blob([reportContent], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `Asset_Report_${asset.asset_number}_${new Date().toISOString().split('T')[0]}.txt`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);

    toast({
      title: "Report Generated",
      description: `Asset report for ${asset.name} has been downloaded.`,
    });
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  };

  const getStatusColor = (status: string) => {
    const colorMap: { [key: string]: string } = {
      'Active': 'bg-green-100 text-green-800',
      'Inactive': 'bg-gray-100 text-gray-800',
      'Under Maintenance': 'bg-yellow-100 text-yellow-800',
      'Disposed': 'bg-red-100 text-red-800',
      'Lost': 'bg-red-100 text-red-800',
      'Sold': 'bg-blue-100 text-blue-800'
    };
    return colorMap[status] || 'bg-gray-100 text-gray-800';
  };

  const getConditionColor = (condition: string) => {
    const colorMap: { [key: string]: string } = {
      'Excellent': 'bg-green-100 text-green-800',
      'Good': 'bg-blue-100 text-blue-800',
      'Fair': 'bg-yellow-100 text-yellow-800',
      'Poor': 'bg-orange-100 text-orange-800',
      'Needs Repair': 'bg-red-100 text-red-800'
    };
    return colorMap[condition] || 'bg-gray-100 text-gray-800';
  };

  return (
    <>
      <Dialog open={open} onOpenChange={onOpenChange}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="flex items-center justify-between">
              <span>{asset.name}</span>
              <div className="flex items-center space-x-2">
                <Badge className={getStatusColor(asset.status)}>
                  {asset.status}
                </Badge>
                <Badge className={getConditionColor(asset.condition)}>
                  {asset.condition}
                </Badge>
              </div>
            </DialogTitle>
            <DialogDescription>
              Asset #{asset.asset_number} • {asset.asset_type}
            </DialogDescription>
          </DialogHeader>

          <Tabs defaultValue="details" className="w-full">
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="details">Details</TabsTrigger>
              <TabsTrigger value="financial">Financial</TabsTrigger>
              <TabsTrigger value="maintenance">Maintenance</TabsTrigger>
              <TabsTrigger value="actions">Actions</TabsTrigger>
            </TabsList>

            <TabsContent value="details" className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">Basic Information</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-3">
                    <div>
                      <span className="font-medium text-gray-600">Name:</span>
                      <p className="text-gray-900">{asset.name}</p>
                    </div>
                    <div>
                      <span className="font-medium text-gray-600">Description:</span>
                      <p className="text-gray-900">{asset.description || 'No description'}</p>
                    </div>
                    <div>
                      <span className="font-medium text-gray-600">Category:</span>
                      <p className="text-gray-900">{asset.category?.name || 'Uncategorized'}</p>
                    </div>
                    <div>
                      <span className="font-medium text-gray-600">Type:</span>
                      <p className="text-gray-900">{asset.asset_type}</p>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">Asset Details</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-3">
                    {asset.brand && (
                      <div>
                        <span className="font-medium text-gray-600">Brand:</span>
                        <p className="text-gray-900">{asset.brand}</p>
                      </div>
                    )}
                    {asset.model && (
                      <div>
                        <span className="font-medium text-gray-600">Model:</span>
                        <p className="text-gray-900">{asset.model}</p>
                      </div>
                    )}
                    {asset.serial_number && (
                      <div>
                        <span className="font-medium text-gray-600">Serial Number:</span>
                        <p className="text-gray-900">{asset.serial_number}</p>
                      </div>
                    )}
                    <div>
                      <span className="font-medium text-gray-600">Purchase Date:</span>
                      <p className="text-gray-900">{new Date(asset.purchase_date).toLocaleDateString()}</p>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg flex items-center">
                      <MapPin className="w-5 h-5 mr-2" />
                      Location & Assignment
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-3">
                    <div>
                      <span className="font-medium text-gray-600">Location:</span>
                      <p className="text-gray-900">{asset.location || 'Not specified'}</p>
                    </div>
                    <div>
                      <span className="font-medium text-gray-600">Assigned To:</span>
                      <p className="text-gray-900 flex items-center">
                        <User className="w-4 h-4 mr-1" />
                        {asset.assigned_to || 'Unassigned'}
                      </p>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">Additional Information</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-3">
                    {asset.warranty_expiry && (
                      <div>
                        <span className="font-medium text-gray-600">Warranty Expiry:</span>
                        <p className="text-gray-900">{new Date(asset.warranty_expiry).toLocaleDateString()}</p>
                      </div>
                    )}
                    {asset.insurance_policy && (
                      <div>
                        <span className="font-medium text-gray-600">Insurance Policy:</span>
                        <p className="text-gray-900">{asset.insurance_policy}</p>
                      </div>
                    )}
                    {asset.notes && (
                      <div>
                        <span className="font-medium text-gray-600">Notes:</span>
                        <p className="text-gray-900">{asset.notes}</p>
                      </div>
                    )}
                  </CardContent>
                </Card>
              </div>
            </TabsContent>

            <TabsContent value="financial" className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <Card>
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-gray-600">Purchase Price</p>
                        <p className="text-2xl font-bold text-gray-900">{formatCurrency(asset.purchase_price)}</p>
                      </div>
                      <DollarSign className="w-8 h-8 text-blue-600" />
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-gray-600">Current Value</p>
                        <p className="text-2xl font-bold text-gray-900">{formatCurrency(asset.current_value)}</p>
                      </div>
                      <DollarSign className="w-8 h-8 text-green-600" />
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-gray-600">Depreciation</p>
                        <p className="text-2xl font-bold text-red-600">
                          {formatCurrency(asset.purchase_price - asset.current_value)}
                        </p>
                        <p className="text-xs text-gray-500">
                          {asset.depreciation_rate}% annual rate
                        </p>
                      </div>
                      <DollarSign className="w-8 h-8 text-red-600" />
                    </div>
                  </CardContent>
                </Card>
              </div>

              <Card>
                <CardHeader>
                  <CardTitle>Financial Summary</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    <div className="flex justify-between">
                      <span className="text-gray-600">Purchase Date:</span>
                      <span className="font-medium">{new Date(asset.purchase_date).toLocaleDateString()}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Age:</span>
                      <span className="font-medium">
                        {Math.floor((Date.now() - new Date(asset.purchase_date).getTime()) / (1000 * 60 * 60 * 24 * 365))} years
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Depreciation Rate:</span>
                      <span className="font-medium">{asset.depreciation_rate}% per year</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Total Depreciation:</span>
                      <span className="font-medium text-red-600">
                        {formatCurrency(asset.purchase_price - asset.current_value)}
                      </span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="maintenance" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <Wrench className="w-5 h-5 mr-2" />
                    Maintenance Information
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  {maintenanceLoading ? (
                    <div className="flex items-center justify-center h-32">
                      <div className="text-center">
                        <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600 mx-auto"></div>
                        <p className="mt-2 text-sm text-gray-600">Loading maintenance data...</p>
                      </div>
                    </div>
                  ) : (
                    <>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                          <span className="font-medium text-gray-600">Last Maintenance:</span>
                          <p className="text-gray-900">
                            {(() => {
                              const lastCompleted = maintenanceRecords
                                .filter(record => record.status === 'Completed')
                                .sort((a, b) => new Date(b.completed_date || b.created_at).getTime() - new Date(a.completed_date || a.created_at).getTime())[0];
                              return lastCompleted?.completed_date
                                ? new Date(lastCompleted.completed_date).toLocaleDateString()
                                : asset.last_maintenance_date
                                ? new Date(asset.last_maintenance_date).toLocaleDateString()
                                : 'No maintenance recorded';
                            })()}
                          </p>
                        </div>
                        <div>
                          <span className="font-medium text-gray-600">Next Maintenance:</span>
                          <p className="text-gray-900">
                            {(() => {
                              const nextScheduled = maintenanceRecords
                                .filter(record => record.status === 'Scheduled' && record.scheduled_date)
                                .sort((a, b) => new Date(a.scheduled_date!).getTime() - new Date(b.scheduled_date!).getTime())[0];
                              return nextScheduled?.scheduled_date
                                ? new Date(nextScheduled.scheduled_date).toLocaleDateString()
                                : asset.next_maintenance_date
                                ? new Date(asset.next_maintenance_date).toLocaleDateString()
                                : 'Not scheduled';
                            })()}
                          </p>
                        </div>
                        <div>
                          <span className="font-medium text-gray-600">Maintenance Interval:</span>
                          <p className="text-gray-900">{asset.maintenance_interval_months || 12} months</p>
                        </div>
                        <div>
                          <span className="font-medium text-gray-600">YTD Maintenance Cost:</span>
                          <p className="text-gray-900">
                            {(() => {
                              const currentYear = new Date().getFullYear();
                              const ytdCost = maintenanceRecords
                                .filter(record => {
                                  const recordYear = new Date(record.created_at).getFullYear();
                                  return recordYear === currentYear && record.status === 'Completed';
                                })
                                .reduce((sum, record) => sum + (record.cost || 0), 0);
                              return formatCurrency(ytdCost);
                            })()}
                          </p>
                        </div>
                      </div>

                      {/* Recent Maintenance Records */}
                      {maintenanceRecords.length > 0 && (
                        <div className="mt-6">
                          <h4 className="font-medium text-gray-700 mb-3">Recent Maintenance ({maintenanceRecords.length} records)</h4>
                          <div className="space-y-2 max-h-32 overflow-y-auto">
                            {maintenanceRecords.slice(0, 3).map((record) => (
                              <div key={record.id} className="flex items-center justify-between p-2 bg-gray-50 rounded text-sm">
                                <div className="flex-1">
                                  <p className="font-medium">{record.maintenance_type}</p>
                                  <p className="text-gray-600 text-xs">{record.description}</p>
                                </div>
                                <div className="text-right">
                                  <Badge className={getStatusColor(record.status)} variant="outline">
                                    {record.status}
                                  </Badge>
                                  {record.cost && record.cost > 0 && (
                                    <p className="text-xs text-gray-500 mt-1">{formatCurrency(record.cost)}</p>
                                  )}
                                </div>
                              </div>
                            ))}
                          </div>
                        </div>
                      )}
                    </>
                  )}
                  
                  <div className="mt-4">
                    <Button
                      variant="outline"
                      className="w-full"
                      onClick={() => setShowMaintenanceScheduler(true)}
                    >
                      <Calendar className="w-4 h-4 mr-2" />
                      Schedule Maintenance
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="actions" className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">Asset Actions</CardTitle>
                    <CardDescription>Manage this asset</CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-3">
                    <Button 
                      onClick={() => setShowEditForm(true)}
                      className="w-full"
                    >
                      <Edit className="w-4 h-4 mr-2" />
                      Edit Asset
                    </Button>
                    
                    <Button
                      variant="outline"
                      className="w-full"
                      onClick={generateReport}
                    >
                      <FileText className="w-4 h-4 mr-2" />
                      Generate Report
                    </Button>

                    <Button
                      variant="outline"
                      className="w-full"
                      onClick={() => setShowHistory(true)}
                    >
                      <Calendar className="w-4 h-4 mr-2" />
                      View History
                    </Button>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg text-red-600">Danger Zone</CardTitle>
                    <CardDescription>Irreversible actions</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <Button 
                      variant="destructive" 
                      onClick={handleDelete}
                      disabled={loading}
                      className="w-full"
                    >
                      <Trash2 className="w-4 h-4 mr-2" />
                      Delete Asset
                    </Button>
                    <p className="text-xs text-gray-500 mt-2">
                      This action cannot be undone. The asset will be permanently removed.
                    </p>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>
          </Tabs>
        </DialogContent>
      </Dialog>

      {/* Edit Asset Form */}
      <AssetForm
        open={showEditForm}
        onOpenChange={setShowEditForm}
        asset={asset}
        categories={categories}
        onAssetSaved={() => {
          setShowEditForm(false);
          onAssetUpdated();
        }}
      />

      {/* Maintenance Scheduler */}
      <MaintenanceScheduler
        open={showMaintenanceScheduler}
        onOpenChange={setShowMaintenanceScheduler}
        asset={asset}
        onMaintenanceScheduled={() => {
          setShowMaintenanceScheduler(false);
          loadMaintenanceRecords(); // Reload maintenance data
          onAssetUpdated();
        }}
      />

      {/* Asset History */}
      <AssetHistory
        open={showHistory}
        onOpenChange={setShowHistory}
        asset={asset}
      />
    </>
  );
};

export default AssetManagement;
