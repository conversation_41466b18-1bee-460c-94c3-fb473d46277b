-- Check current database schema and fix any issues
-- Run this first to understand the current state

-- 1. Check user_profiles table structure
DO $$
DECLARE
    column_record RECORD;
BEGIN
    RAISE NOTICE '';
    RAISE NOTICE '=== CHECKING DATABASE SCHEMA ===';
    RAISE NOTICE '';
    
    RAISE NOTICE 'USER_PROFILES TABLE COLUMNS:';
    FOR column_record IN 
        SELECT column_name, data_type, is_nullable
        FROM information_schema.columns 
        WHERE table_name = 'user_profiles' 
        AND table_schema = 'public'
        ORDER BY ordinal_position
    LOOP
        RAISE NOTICE '- %: % (nullable: %)', 
            column_record.column_name, 
            column_record.data_type,
            column_record.is_nullable;
    END LOOP;
    
    RAISE NOTICE '';
    RAISE NOTICE 'USER_ROLES TABLE COLUMNS:';
    FOR column_record IN 
        SELECT column_name, data_type, is_nullable
        FROM information_schema.columns 
        WHERE table_name = 'user_roles' 
        AND table_schema = 'public'
        ORDER BY ordinal_position
    LOOP
        RAISE NOTICE '- %: % (nullable: %)', 
            column_record.column_name, 
            column_record.data_type,
            column_record.is_nullable;
    END LOOP;
END $$;

-- 2. Check if role_name column exists in user_profiles
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'user_profiles' 
        AND column_name = 'role_name' 
        AND table_schema = 'public'
    ) THEN
        RAISE NOTICE '';
        RAISE NOTICE '⚠️  MISSING COLUMN: role_name not found in user_profiles';
        RAISE NOTICE 'Adding role_name column...';
        
        -- Add the missing column
        ALTER TABLE public.user_profiles 
        ADD COLUMN IF NOT EXISTS role_name VARCHAR(50);
        
        -- Update existing records to have role_name based on role_id
        UPDATE public.user_profiles 
        SET role_name = (
            SELECT ur.role_name 
            FROM public.user_roles ur 
            WHERE ur.id = user_profiles.role_id
        )
        WHERE role_name IS NULL;
        
        RAISE NOTICE '✅ Added role_name column and populated existing records';
    ELSE
        RAISE NOTICE '✅ role_name column exists in user_profiles';
    END IF;
END $$;

-- 3. Check existing functions
DO $$
BEGIN
    RAISE NOTICE '';
    RAISE NOTICE 'CHECKING EXISTING FUNCTIONS:';
    
    IF EXISTS (SELECT 1 FROM pg_proc WHERE proname = 'admin_create_user_account') THEN
        RAISE NOTICE '✅ admin_create_user_account function exists';
    ELSE
        RAISE NOTICE '❌ admin_create_user_account function missing - need to run implement_new_system.sql';
    END IF;
    
    IF EXISTS (SELECT 1 FROM pg_proc WHERE proname = 'admin_reset_user_password') THEN
        RAISE NOTICE '✅ admin_reset_user_password function exists';
    ELSE
        RAISE NOTICE '❌ admin_reset_user_password function missing';
    END IF;
    
    IF EXISTS (SELECT 1 FROM pg_proc WHERE proname = 'change_my_password') THEN
        RAISE NOTICE '✅ change_my_password function exists';
    ELSE
        RAISE NOTICE '❌ change_my_password function missing';
    END IF;
END $$;

-- 4. Show sample data
DO $$
DECLARE
    profile_record RECORD;
    role_record RECORD;
BEGIN
    RAISE NOTICE '';
    RAISE NOTICE 'SAMPLE DATA:';
    RAISE NOTICE '';
    
    RAISE NOTICE 'USER_ROLES:';
    FOR role_record IN 
        SELECT id, role_name, role_display_name, is_active
        FROM public.user_roles 
        ORDER BY role_name
        LIMIT 10
    LOOP
        RAISE NOTICE '- %: % (active: %)', 
            role_record.role_name, 
            role_record.role_display_name,
            role_record.is_active;
    END LOOP;
    
    RAISE NOTICE '';
    RAISE NOTICE 'USER_PROFILES (first 5):';
    FOR profile_record IN 
        SELECT email, first_name, last_name, role_id, role_name
        FROM public.user_profiles 
        ORDER BY created_at DESC
        LIMIT 5
    LOOP
        RAISE NOTICE '- %: % % (role_id: %, role_name: %)', 
            profile_record.email,
            profile_record.first_name, 
            profile_record.last_name,
            COALESCE(profile_record.role_id::text, 'NULL'),
            COALESCE(profile_record.role_name, 'NULL');
    END LOOP;
END $$;

-- 5. Quick fix for immediate testing
CREATE OR REPLACE FUNCTION public.admin_create_user_account_temp(
    p_email VARCHAR(255),
    p_first_name VARCHAR(100),
    p_last_name VARCHAR(100),
    p_role_name VARCHAR(50) DEFAULT 'client'
)
RETURNS jsonb AS $$
DECLARE
    v_role_id UUID;
    v_auth_user_id UUID;
    v_profile_id UUID;
    v_generated_password VARCHAR(255);
BEGIN
    -- Generate random password
    v_generated_password := 'Temp' || FLOOR(RANDOM() * 9000 + 1000)::text || '!';
    
    -- Check if email already exists
    IF EXISTS(SELECT 1 FROM public.user_profiles WHERE email = p_email) THEN
        RETURN jsonb_build_object(
            'success', false,
            'error', 'User with this email already exists'
        );
    END IF;
    
    -- Get role ID
    SELECT id INTO v_role_id
    FROM public.user_roles
    WHERE role_name = p_role_name AND is_active = true;
    
    IF v_role_id IS NULL THEN
        RETURN jsonb_build_object(
            'success', false,
            'error', 'Invalid role: ' || p_role_name
        );
    END IF;
    
    -- Generate new UUID for auth user
    v_auth_user_id := gen_random_uuid();
    
    BEGIN
        -- Create Supabase Auth user
        INSERT INTO auth.users (
            id,
            instance_id,
            email,
            encrypted_password,
            email_confirmed_at,
            created_at,
            updated_at,
            raw_user_meta_data,
            raw_app_meta_data,
            is_super_admin,
            role
        ) VALUES (
            v_auth_user_id,
            '00000000-0000-0000-0000-000000000000',
            p_email,
            crypt(v_generated_password, gen_salt('bf')),
            NOW(),
            NOW(),
            NOW(),
            jsonb_build_object(
                'first_name', p_first_name,
                'last_name', p_last_name
            ),
            jsonb_build_object('provider', 'email', 'providers', ARRAY['email']),
            false,
            'authenticated'
        );
        
        -- Create user profile (with role_name if column exists)
        INSERT INTO public.user_profiles (
            id,
            user_id,
            email,
            first_name,
            last_name,
            role_id,
            role_name,
            is_active,
            created_at,
            updated_at
        ) VALUES (
            gen_random_uuid(),
            v_auth_user_id,
            p_email,
            p_first_name,
            p_last_name,
            v_role_id,
            p_role_name,
            true,
            NOW(),
            NOW()
        ) RETURNING id INTO v_profile_id;
        
        RETURN jsonb_build_object(
            'success', true,
            'message', 'User account created successfully',
            'user_data', jsonb_build_object(
                'auth_user_id', v_auth_user_id,
                'profile_id', v_profile_id,
                'email', p_email,
                'first_name', p_first_name,
                'last_name', p_last_name,
                'role', p_role_name,
                'temp_password', v_generated_password
            )
        );
        
    EXCEPTION
        WHEN OTHERS THEN
            RETURN jsonb_build_object(
                'success', false,
                'error', 'Failed to create user: ' || SQLERRM
            );
    END;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant permissions
GRANT EXECUTE ON FUNCTION public.admin_create_user_account_temp(VARCHAR, VARCHAR, VARCHAR, VARCHAR) TO authenticated;

DO $$
BEGIN
    RAISE NOTICE '';
    RAISE NOTICE '=== SCHEMA CHECK COMPLETE ===';
    RAISE NOTICE '';
    RAISE NOTICE 'Next steps:';
    RAISE NOTICE '1. Check the output above for any missing columns or functions';
    RAISE NOTICE '2. If role_name column was missing, it has been added';
    RAISE NOTICE '3. Temporary function admin_create_user_account_temp created for testing';
    RAISE NOTICE '4. Run the full implement_new_system.sql script next';
    RAISE NOTICE '';
END $$;
