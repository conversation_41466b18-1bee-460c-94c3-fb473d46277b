-- Fix Client-Users Relationships and Foreign Keys
-- This script ensures proper foreign key relationships exist for the client_users table

-- 1. First, check if the client_users table exists, if not create it
CREATE TABLE IF NOT EXISTS public.client_users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    client_id UUID NOT NULL,
    user_id UUID NOT NULL,
    role VARCHAR(50) DEFAULT 'client',
    access_level VARCHAR(20) DEFAULT 'read',
    is_primary BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_by <PERSON><PERSON><PERSON>,
    UNIQUE(client_id, user_id)
);

-- 2. Add foreign key constraints if they don't exist
DO $$
BEGIN
    -- Add foreign key to clients table
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints 
        WHERE constraint_name = 'client_users_client_id_fkey'
        AND table_name = 'client_users'
    ) THEN
        ALTER TABLE public.client_users 
        ADD CONSTRAINT client_users_client_id_fkey 
        FOREIGN KEY (client_id) REFERENCES public.clients(id) ON DELETE CASCADE;
        RAISE NOTICE 'Added foreign key constraint: client_users_client_id_fkey';
    END IF;

    -- Add foreign key to user_profiles table (if it exists)
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'user_profiles') THEN
        IF NOT EXISTS (
            SELECT 1 FROM information_schema.table_constraints 
            WHERE constraint_name = 'client_users_user_id_fkey'
            AND table_name = 'client_users'
        ) THEN
            ALTER TABLE public.client_users 
            ADD CONSTRAINT client_users_user_id_fkey 
            FOREIGN KEY (user_id) REFERENCES public.user_profiles(user_id) ON DELETE CASCADE;
            RAISE NOTICE 'Added foreign key constraint: client_users_user_id_fkey (to user_profiles)';
        END IF;
    ELSE
        -- If user_profiles doesn't exist, try auth.users
        IF NOT EXISTS (
            SELECT 1 FROM information_schema.table_constraints 
            WHERE constraint_name = 'client_users_user_id_fkey'
            AND table_name = 'client_users'
        ) THEN
            ALTER TABLE public.client_users 
            ADD CONSTRAINT client_users_user_id_fkey 
            FOREIGN KEY (user_id) REFERENCES auth.users(id) ON DELETE CASCADE;
            RAISE NOTICE 'Added foreign key constraint: client_users_user_id_fkey (to auth.users)';
        END IF;
    END IF;

    -- Add foreign key to created_by field
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints 
        WHERE constraint_name = 'client_users_created_by_fkey'
        AND table_name = 'client_users'
    ) THEN
        ALTER TABLE public.client_users 
        ADD CONSTRAINT client_users_created_by_fkey 
        FOREIGN KEY (created_by) REFERENCES auth.users(id) ON DELETE SET NULL;
        RAISE NOTICE 'Added foreign key constraint: client_users_created_by_fkey';
    END IF;
END $$;

-- 3. Create or update the link_user_to_client function
CREATE OR REPLACE FUNCTION link_user_to_client(
  target_user_id UUID,
  target_client_id UUID,
  user_role TEXT DEFAULT 'client',
  access_level TEXT DEFAULT 'read',
  is_primary BOOLEAN DEFAULT FALSE
)
RETURNS BOOLEAN AS $$
BEGIN
  -- Check if user exists (try user_profiles first, then auth.users)
  IF NOT EXISTS (
    SELECT 1 FROM public.user_profiles WHERE user_id = target_user_id
    UNION
    SELECT 1 FROM auth.users WHERE id = target_user_id
  ) THEN
    RAISE EXCEPTION 'User with ID % does not exist', target_user_id;
  END IF;

  -- Check if client exists
  IF NOT EXISTS (SELECT 1 FROM public.clients WHERE id = target_client_id) THEN
    RAISE EXCEPTION 'Client with ID % does not exist', target_client_id;
  END IF;

  -- Insert or update the relationship
  INSERT INTO public.client_users (
    client_id, user_id, role, access_level, is_primary, created_by
  ) VALUES (
    target_client_id, target_user_id, user_role, access_level, is_primary, auth.uid()
  ) ON CONFLICT (client_id, user_id) DO UPDATE SET
    role = EXCLUDED.role,
    access_level = EXCLUDED.access_level,
    is_primary = EXCLUDED.is_primary,
    updated_at = NOW();
  
  RETURN TRUE;
EXCEPTION
  WHEN OTHERS THEN
    RAISE EXCEPTION 'Failed to link user to client: %', SQLERRM;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 4. Set up Row Level Security policies
ALTER TABLE public.client_users ENABLE ROW LEVEL SECURITY;

-- Drop existing policies if they exist
DROP POLICY IF EXISTS "Users can view their own client relationships" ON public.client_users;
DROP POLICY IF EXISTS "Admins can view all client relationships" ON public.client_users;
DROP POLICY IF EXISTS "Admins can manage client relationships" ON public.client_users;

-- Create RLS policies
CREATE POLICY "Users can view their own client relationships" ON public.client_users
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Admins can view all client relationships" ON public.client_users
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM public.user_profiles 
      WHERE user_id = auth.uid() 
      AND role_name IN ('admin', 'management')
    )
    OR
    -- Fallback if user_profiles doesn't exist
    NOT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'user_profiles')
  );

CREATE POLICY "Admins can manage client relationships" ON public.client_users
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM public.user_profiles 
      WHERE user_id = auth.uid() 
      AND role_name IN ('admin', 'management')
    )
    OR
    -- Fallback if user_profiles doesn't exist
    NOT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'user_profiles')
  );

-- 5. Grant necessary permissions
GRANT SELECT, INSERT, UPDATE, DELETE ON public.client_users TO authenticated;
GRANT EXECUTE ON FUNCTION link_user_to_client(UUID, UUID, TEXT, TEXT, BOOLEAN) TO authenticated;

-- 6. Create updated_at trigger
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS update_client_users_updated_at ON public.client_users;
CREATE TRIGGER update_client_users_updated_at
    BEFORE UPDATE ON public.client_users
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- 7. Verification and status report
DO $$
DECLARE
    constraint_count INTEGER;
    table_exists BOOLEAN;
    user_table_name TEXT;
BEGIN
    RAISE NOTICE '';
    RAISE NOTICE '=== CLIENT-USERS RELATIONSHIP FIX COMPLETE ===';
    
    -- Check if client_users table exists
    SELECT EXISTS (
        SELECT 1 FROM information_schema.tables 
        WHERE table_name = 'client_users'
    ) INTO table_exists;
    
    RAISE NOTICE 'client_users table exists: %', table_exists;
    
    -- Count foreign key constraints
    SELECT COUNT(*) INTO constraint_count
    FROM information_schema.table_constraints 
    WHERE table_name = 'client_users' 
    AND constraint_type = 'FOREIGN KEY';
    
    RAISE NOTICE 'Foreign key constraints: %', constraint_count;
    
    -- Check which user table is available
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'user_profiles') THEN
        user_table_name := 'user_profiles';
    ELSIF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'user_security_summary') THEN
        user_table_name := 'user_security_summary';
    ELSE
        user_table_name := 'auth.users (fallback)';
    END IF;
    
    RAISE NOTICE 'User table available: %', user_table_name;
    RAISE NOTICE '';
    RAISE NOTICE 'Setup complete! The ClientUserManagement component should now work properly.';
    RAISE NOTICE '';
END $$;
