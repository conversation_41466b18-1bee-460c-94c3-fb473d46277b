-- Messages System Database Schema
-- Run this in your Supabase SQL Editor

-- 1. Create message channels table (for organizing conversations)
CREATE TABLE IF NOT EXISTS public.message_channels (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    channel_type VARCHAR(20) DEFAULT 'general' CHECK (channel_type IN ('general', 'project', 'team', 'direct', 'announcement')),
    project_id UUID, -- Reference to projects table if it's a project channel
    is_private BOOLEAN DEFAULT false,
    created_by VARCHAR(255),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 2. Create messages table
CREATE TABLE IF NOT EXISTS public.messages (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    channel_id UUID NOT NULL REFERENCES public.message_channels(id) ON DELETE CASCADE,
    sender_name <PERSON><PERSON><PERSON><PERSON>(255) NOT NULL,
    sender_email VARCHAR(255),
    sender_role VARCHAR(100),
    message_content TEXT NOT NULL,
    message_type VARCHAR(20) DEFAULT 'text' CHECK (message_type IN ('text', 'file', 'image', 'system', 'announcement')),
    
    -- Message metadata
    is_edited BOOLEAN DEFAULT false,
    edited_at TIMESTAMP WITH TIME ZONE,
    reply_to_message_id UUID REFERENCES public.messages(id) ON DELETE SET NULL,
    
    -- File attachments (if any)
    attachments JSONB, -- Store file references, URLs, etc.
    
    -- Message status
    is_pinned BOOLEAN DEFAULT false,
    is_deleted BOOLEAN DEFAULT false,
    
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 3. Create message reactions table (for emoji reactions)
CREATE TABLE IF NOT EXISTS public.message_reactions (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    message_id UUID NOT NULL REFERENCES public.messages(id) ON DELETE CASCADE,
    user_name VARCHAR(255) NOT NULL,
    user_email VARCHAR(255),
    reaction_emoji VARCHAR(10) NOT NULL, -- emoji like 👍, ❤️, 😊, etc.
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Ensure one reaction per user per message per emoji
    UNIQUE(message_id, user_email, reaction_emoji)
);

-- 4. Create channel members table (for private channels and permissions)
CREATE TABLE IF NOT EXISTS public.channel_members (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    channel_id UUID NOT NULL REFERENCES public.message_channels(id) ON DELETE CASCADE,
    user_name VARCHAR(255) NOT NULL,
    user_email VARCHAR(255) NOT NULL,
    role VARCHAR(20) DEFAULT 'member' CHECK (role IN ('admin', 'moderator', 'member')),
    joined_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Ensure one membership per user per channel
    UNIQUE(channel_id, user_email)
);

-- 5. Create message read status table (to track who has read what)
CREATE TABLE IF NOT EXISTS public.message_read_status (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    message_id UUID NOT NULL REFERENCES public.messages(id) ON DELETE CASCADE,
    user_email VARCHAR(255) NOT NULL,
    read_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Ensure one read status per user per message
    UNIQUE(message_id, user_email)
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_messages_channel ON public.messages(channel_id);
CREATE INDEX IF NOT EXISTS idx_messages_created_at ON public.messages(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_messages_sender ON public.messages(sender_email);
CREATE INDEX IF NOT EXISTS idx_message_reactions_message ON public.message_reactions(message_id);
CREATE INDEX IF NOT EXISTS idx_channel_members_channel ON public.channel_members(channel_id);
CREATE INDEX IF NOT EXISTS idx_channel_members_user ON public.channel_members(user_email);
CREATE INDEX IF NOT EXISTS idx_message_read_status_message ON public.message_read_status(message_id);
CREATE INDEX IF NOT EXISTS idx_message_read_status_user ON public.message_read_status(user_email);

-- Enable Row Level Security
ALTER TABLE public.message_channels ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.messages ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.message_reactions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.channel_members ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.message_read_status ENABLE ROW LEVEL SECURITY;

-- Create RLS Policies (Allow all operations for now)
CREATE POLICY "Allow all operations on message_channels" ON public.message_channels FOR ALL USING (true);
CREATE POLICY "Allow all operations on messages" ON public.messages FOR ALL USING (true);
CREATE POLICY "Allow all operations on message_reactions" ON public.message_reactions FOR ALL USING (true);
CREATE POLICY "Allow all operations on channel_members" ON public.channel_members FOR ALL USING (true);
CREATE POLICY "Allow all operations on message_read_status" ON public.message_read_status FOR ALL USING (true);

-- Create update timestamp function and triggers
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_message_channels_updated_at BEFORE UPDATE ON public.message_channels FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_messages_updated_at BEFORE UPDATE ON public.messages FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Insert default channels
INSERT INTO public.message_channels (name, description, channel_type, is_private) VALUES
('General', 'General team discussions and announcements', 'general', false),
('Project Updates', 'Project status updates and coordination', 'project', false),
('Team Chat', 'Casual team conversations', 'team', false),
('Announcements', 'Important company announcements', 'announcement', false),
('Technical Discussion', 'Technical discussions and problem solving', 'general', false)
ON CONFLICT DO NOTHING;

-- Insert some sample messages
INSERT INTO public.messages (channel_id, sender_name, sender_email, sender_role, message_content, message_type) VALUES
((SELECT id FROM message_channels WHERE name = 'General'), 'System', '<EMAIL>', 'system', 'Welcome to the team messaging system! 🎉', 'system'),
((SELECT id FROM message_channels WHERE name = 'General'), 'Project Manager', '<EMAIL>', 'manager', 'Good morning team! Let''s have a productive day ahead.', 'text'),
((SELECT id FROM message_channels WHERE name = 'Project Updates'), 'Site Supervisor', '<EMAIL>', 'supervisor', 'Construction on Building A is progressing well. We''re on schedule for the foundation work.', 'text'),
((SELECT id FROM message_channels WHERE name = 'Team Chat'), 'Engineer', '<EMAIL>', 'engineer', 'Has anyone seen the latest CAD drawings for the downtown project?', 'text'),
((SELECT id FROM message_channels WHERE name = 'Announcements'), 'HR Manager', '<EMAIL>', 'admin', 'Reminder: Safety training session tomorrow at 2 PM in the main conference room.', 'announcement')
ON CONFLICT DO NOTHING;

-- Add some sample reactions
INSERT INTO public.message_reactions (message_id, user_name, user_email, reaction_emoji) VALUES
((SELECT id FROM messages WHERE message_content LIKE 'Good morning team%'), 'Engineer', '<EMAIL>', '👍'),
((SELECT id FROM messages WHERE message_content LIKE 'Good morning team%'), 'Site Supervisor', '<EMAIL>', '☕'),
((SELECT id FROM messages WHERE message_content LIKE 'Construction on Building A%'), 'Project Manager', '<EMAIL>', '🎉'),
((SELECT id FROM messages WHERE message_content LIKE 'Safety training session%'), 'Engineer', '<EMAIL>', '✅'),
((SELECT id FROM messages WHERE message_content LIKE 'Safety training session%'), 'Site Supervisor', '<EMAIL>', '✅')
ON CONFLICT DO NOTHING;

-- Comments for documentation
COMMENT ON TABLE public.message_channels IS 'Channels for organizing team conversations';
COMMENT ON TABLE public.messages IS 'Individual messages within channels';
COMMENT ON TABLE public.message_reactions IS 'Emoji reactions to messages';
COMMENT ON TABLE public.channel_members IS 'Channel membership and permissions';
COMMENT ON TABLE public.message_read_status IS 'Track which messages users have read';
