import React, { useState, useEffect } from 'react';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { AssetService, CompanyAsset, AssetMaintenance } from '@/lib/assetService';
import { 
  History, 
  Wrench, 
  DollarSign, 
  Calendar, 
  User, 
  FileText,
  Clock,
  CheckCircle,
  AlertTriangle,
  XCircle
} from 'lucide-react';

interface AssetHistoryProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  asset: CompanyAsset | null;
}

const AssetHistory: React.FC<AssetHistoryProps> = ({
  open,
  onOpenChange,
  asset
}) => {
  const [maintenanceHistory, setMaintenanceHistory] = useState<AssetMaintenance[]>([]);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (open && asset) {
      loadMaintenanceHistory();
    }
  }, [open, asset]);

  const loadMaintenanceHistory = async () => {
    if (!asset) return;
    
    setLoading(true);
    try {
      const history = await AssetService.getMaintenanceRecords(asset.id);
      setMaintenanceHistory(history);
    } catch (error) {
      console.error('Error loading maintenance history:', error);
    } finally {
      setLoading(false);
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'Completed':
        return <CheckCircle className="w-4 h-4 text-green-600" />;
      case 'In Progress':
        return <Clock className="w-4 h-4 text-blue-600" />;
      case 'Scheduled':
        return <Calendar className="w-4 h-4 text-yellow-600" />;
      case 'Cancelled':
        return <XCircle className="w-4 h-4 text-red-600" />;
      case 'Overdue':
        return <AlertTriangle className="w-4 h-4 text-red-600" />;
      default:
        return <Clock className="w-4 h-4 text-gray-600" />;
    }
  };

  const getStatusColor = (status: string) => {
    const colorMap: { [key: string]: string } = {
      'Completed': 'bg-green-100 text-green-800',
      'In Progress': 'bg-blue-100 text-blue-800',
      'Scheduled': 'bg-yellow-100 text-yellow-800',
      'Cancelled': 'bg-red-100 text-red-800',
      'Overdue': 'bg-red-100 text-red-800'
    };
    return colorMap[status] || 'bg-gray-100 text-gray-800';
  };

  const getPriorityColor = (priority: string) => {
    const colorMap: { [key: string]: string } = {
      'Critical': 'bg-red-100 text-red-800',
      'High': 'bg-orange-100 text-orange-800',
      'Medium': 'bg-yellow-100 text-yellow-800',
      'Low': 'bg-green-100 text-green-800'
    };
    return colorMap[priority] || 'bg-gray-100 text-gray-800';
  };

  // Calculate asset age and usage statistics
  const calculateAssetStats = () => {
    if (!asset) return null;

    const purchaseDate = new Date(asset.purchase_date);
    const today = new Date();
    const ageInDays = Math.floor((today.getTime() - purchaseDate.getTime()) / (1000 * 60 * 60 * 24));
    const ageInYears = Math.floor(ageInDays / 365);
    const ageInMonths = Math.floor((ageInDays % 365) / 30);

    const totalMaintenanceCost = maintenanceHistory.reduce((sum, record) => sum + (record.cost || 0), 0);
    const completedMaintenance = maintenanceHistory.filter(record => record.status === 'Completed').length;

    return {
      ageInYears,
      ageInMonths,
      totalMaintenanceCost,
      completedMaintenance,
      totalMaintenance: maintenanceHistory.length
    };
  };

  const stats = calculateAssetStats();

  if (!asset) return null;

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center">
            <History className="w-5 h-5 mr-2" />
            Asset History
          </DialogTitle>
          <DialogDescription>
            Complete history and timeline for {asset.name} (#{asset.asset_number})
          </DialogDescription>
        </DialogHeader>

        <Tabs defaultValue="overview" className="w-full">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="maintenance">Maintenance History</TabsTrigger>
            <TabsTrigger value="timeline">Timeline</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-4">
            {stats && (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <Card>
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-gray-600">Asset Age</p>
                        <p className="text-2xl font-bold text-gray-900">
                          {stats.ageInYears}y {stats.ageInMonths}m
                        </p>
                      </div>
                      <Calendar className="w-8 h-8 text-blue-600" />
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-gray-600">Total Maintenance</p>
                        <p className="text-2xl font-bold text-gray-900">{formatCurrency(stats.totalMaintenanceCost)}</p>
                      </div>
                      <DollarSign className="w-8 h-8 text-green-600" />
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-gray-600">Maintenance Records</p>
                        <p className="text-2xl font-bold text-gray-900">{stats.totalMaintenance}</p>
                      </div>
                      <Wrench className="w-8 h-8 text-orange-600" />
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-gray-600">Completed</p>
                        <p className="text-2xl font-bold text-gray-900">{stats.completedMaintenance}</p>
                      </div>
                      <CheckCircle className="w-8 h-8 text-green-600" />
                    </div>
                  </CardContent>
                </Card>
              </div>
            )}

            <Card>
              <CardHeader>
                <CardTitle>Asset Lifecycle</CardTitle>
                <CardDescription>Key milestones and events</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center space-x-3 p-3 bg-blue-50 rounded-lg">
                    <div className="w-2 h-2 bg-blue-600 rounded-full"></div>
                    <div className="flex-1">
                      <p className="font-medium">Asset Purchased</p>
                      <p className="text-sm text-gray-600">{new Date(asset.purchase_date).toLocaleDateString()}</p>
                    </div>
                    <Badge variant="outline">Purchase</Badge>
                  </div>

                  {asset.warranty_expiry && (
                    <div className="flex items-center space-x-3 p-3 bg-yellow-50 rounded-lg">
                      <div className="w-2 h-2 bg-yellow-600 rounded-full"></div>
                      <div className="flex-1">
                        <p className="font-medium">Warranty Expires</p>
                        <p className="text-sm text-gray-600">{new Date(asset.warranty_expiry).toLocaleDateString()}</p>
                      </div>
                      <Badge variant="outline">Warranty</Badge>
                    </div>
                  )}

                  {asset.last_maintenance_date && (
                    <div className="flex items-center space-x-3 p-3 bg-green-50 rounded-lg">
                      <div className="w-2 h-2 bg-green-600 rounded-full"></div>
                      <div className="flex-1">
                        <p className="font-medium">Last Maintenance</p>
                        <p className="text-sm text-gray-600">{new Date(asset.last_maintenance_date).toLocaleDateString()}</p>
                      </div>
                      <Badge variant="outline">Maintenance</Badge>
                    </div>
                  )}

                  {asset.next_maintenance_date && (
                    <div className="flex items-center space-x-3 p-3 bg-orange-50 rounded-lg">
                      <div className="w-2 h-2 bg-orange-600 rounded-full"></div>
                      <div className="flex-1">
                        <p className="font-medium">Next Maintenance Due</p>
                        <p className="text-sm text-gray-600">{new Date(asset.next_maintenance_date).toLocaleDateString()}</p>
                      </div>
                      <Badge variant="outline">Upcoming</Badge>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="maintenance" className="space-y-4">
            {loading ? (
              <div className="flex items-center justify-center h-32">
                <div className="text-center">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
                  <p className="mt-2 text-sm text-gray-600">Loading maintenance history...</p>
                </div>
              </div>
            ) : maintenanceHistory.length === 0 ? (
              <Card>
                <CardContent className="p-8 text-center">
                  <Wrench className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">No Maintenance Records</h3>
                  <p className="text-gray-600">No maintenance has been recorded for this asset yet.</p>
                </CardContent>
              </Card>
            ) : (
              <div className="space-y-4">
                {maintenanceHistory.map((record) => (
                  <Card key={record.id}>
                    <CardContent className="p-4">
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <div className="flex items-center space-x-2 mb-2">
                            {getStatusIcon(record.status)}
                            <h3 className="font-medium">{record.description}</h3>
                            <Badge className={getStatusColor(record.status)}>
                              {record.status}
                            </Badge>
                            <Badge className={getPriorityColor(record.priority)}>
                              {record.priority}
                            </Badge>
                          </div>
                          
                          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 text-sm">
                            <div>
                              <span className="font-medium text-gray-600">Type:</span>
                              <p>{record.maintenance_type}</p>
                            </div>
                            <div>
                              <span className="font-medium text-gray-600">Scheduled:</span>
                              <p>{record.scheduled_date ? new Date(record.scheduled_date).toLocaleDateString() : 'Not set'}</p>
                            </div>
                            <div>
                              <span className="font-medium text-gray-600">Completed:</span>
                              <p>{record.completed_date ? new Date(record.completed_date).toLocaleDateString() : 'Not completed'}</p>
                            </div>
                            <div>
                              <span className="font-medium text-gray-600">Cost:</span>
                              <p className="font-medium">{formatCurrency(record.cost || 0)}</p>
                            </div>
                          </div>

                          {record.technician && (
                            <div className="mt-2 flex items-center text-sm text-gray-600">
                              <User className="w-4 h-4 mr-1" />
                              <span>Technician: {record.technician}</span>
                            </div>
                          )}

                          {record.work_performed && (
                            <div className="mt-2">
                              <span className="font-medium text-gray-600 text-sm">Work Performed:</span>
                              <p className="text-sm text-gray-700 mt-1">{record.work_performed}</p>
                            </div>
                          )}

                          {record.recommendations && (
                            <div className="mt-2">
                              <span className="font-medium text-gray-600 text-sm">Recommendations:</span>
                              <p className="text-sm text-gray-700 mt-1">{record.recommendations}</p>
                            </div>
                          )}
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            )}
          </TabsContent>

          <TabsContent value="timeline" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Asset Timeline</CardTitle>
                <CardDescription>Chronological view of all asset events</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="relative">
                  <div className="absolute left-4 top-0 bottom-0 w-0.5 bg-gray-200"></div>
                  
                  <div className="space-y-6">
                    {/* Purchase Event */}
                    <div className="relative flex items-start space-x-4">
                      <div className="relative z-10 w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center">
                        <DollarSign className="w-4 h-4 text-white" />
                      </div>
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center justify-between">
                          <h3 className="font-medium">Asset Purchased</h3>
                          <span className="text-sm text-gray-500">{new Date(asset.purchase_date).toLocaleDateString()}</span>
                        </div>
                        <p className="text-sm text-gray-600">
                          Purchased for {formatCurrency(asset.purchase_price)}
                        </p>
                      </div>
                    </div>

                    {/* Maintenance Events */}
                    {maintenanceHistory
                      .sort((a, b) => new Date(a.created_at).getTime() - new Date(b.created_at).getTime())
                      .map((record) => (
                        <div key={record.id} className="relative flex items-start space-x-4">
                          <div className="relative z-10 w-8 h-8 bg-orange-600 rounded-full flex items-center justify-center">
                            <Wrench className="w-4 h-4 text-white" />
                          </div>
                          <div className="flex-1 min-w-0">
                            <div className="flex items-center justify-between">
                              <h3 className="font-medium">{record.maintenance_type} Maintenance</h3>
                              <span className="text-sm text-gray-500">
                                {record.completed_date 
                                  ? new Date(record.completed_date).toLocaleDateString()
                                  : new Date(record.created_at).toLocaleDateString()
                                }
                              </span>
                            </div>
                            <p className="text-sm text-gray-600">{record.description}</p>
                            {record.cost && record.cost > 0 && (
                              <p className="text-sm text-gray-500">Cost: {formatCurrency(record.cost)}</p>
                            )}
                          </div>
                        </div>
                      ))}

                    {/* Current Status */}
                    <div className="relative flex items-start space-x-4">
                      <div className="relative z-10 w-8 h-8 bg-green-600 rounded-full flex items-center justify-center">
                        <CheckCircle className="w-4 h-4 text-white" />
                      </div>
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center justify-between">
                          <h3 className="font-medium">Current Status</h3>
                          <span className="text-sm text-gray-500">Today</span>
                        </div>
                        <p className="text-sm text-gray-600">
                          Status: {asset.status} • Condition: {asset.condition}
                        </p>
                        <p className="text-sm text-gray-500">
                          Current Value: {formatCurrency(asset.current_value)}
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </DialogContent>
    </Dialog>
  );
};

export default AssetHistory;
