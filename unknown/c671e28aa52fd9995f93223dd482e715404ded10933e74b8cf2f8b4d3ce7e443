/**
 * Utility to clean up all demo/sample data and ensure system uses only Supabase data
 */

import { supabase } from '@/lib/supabase';

export class DemoDataCleanup {
  
  /**
   * Remove all localStorage demo data
   */
  static clearAllLocalStorageData(): void {
    console.log('🧹 Clearing all localStorage demo data...');
    
    const keysToRemove = [
      'client_payments',
      'client_invoices',
      'documents',
      'clients',
      'employees',
      'projects',
      'workforce',
      'tasks',
      'expenses',
      'revenue_entries',
      'cash_flow_transactions',
      'payment_records',
      'company_info',
      'financial_summary',
      'cash_flow_data',
      'analytics_data'
    ];

    keysToRemove.forEach(key => {
      const data = localStorage.getItem(key);
      if (data) {
        console.log(`📋 Removing ${key}: ${JSON.parse(data).length || 'N/A'} records`);
        localStorage.removeItem(key);
      }
    });

    console.log('✅ All localStorage demo data cleared');
  }

  /**
   * Show current localStorage data summary
   */
  static showLocalStorageDataSummary(): Record<string, any> {
    console.log('📊 Current localStorage data summary:');
    
    const summary: Record<string, any> = {};
    
    const keysToCheck = [
      'client_payments',
      'client_invoices', 
      'documents',
      'clients',
      'employees',
      'projects',
      'workforce',
      'tasks'
    ];

    keysToCheck.forEach(key => {
      const data = localStorage.getItem(key);
      if (data) {
        try {
          const parsed = JSON.parse(data);
          const count = Array.isArray(parsed) ? parsed.length : 'Object';
          summary[key] = { count, hasData: true };
          console.log(`📋 ${key}: ${count} records`);
        } catch {
          summary[key] = { count: 'Invalid JSON', hasData: true };
          console.log(`📋 ${key}: Invalid JSON data`);
        }
      } else {
        summary[key] = { count: 0, hasData: false };
        console.log(`📋 ${key}: No data`);
      }
    });

    return summary;
  }

  /**
   * Clear demo data from Supabase database
   */
  static async clearSupabaseDemoData(): Promise<void> {
    console.log('🗄️ Clearing demo data from Supabase...');

    try {
      // Clear test cash flow transactions
      const { error: cashFlowError } = await supabase
        .from('cash_flow_transactions')
        .delete()
        .or('description.ilike.%test%,description.ilike.%demo%,description.ilike.%sample%');

      if (cashFlowError) console.warn('Cash flow cleanup warning:', cashFlowError);

      // Clear test payment records
      const { error: paymentError } = await supabase
        .from('payment_records')
        .delete()
        .or('notes.ilike.%test%,notes.ilike.%demo%,notes.ilike.%sample%');

      if (paymentError) console.warn('Payment records cleanup warning:', paymentError);

      // Clear test expenses
      const { error: expenseError } = await supabase
        .from('expenses')
        .delete()
        .or('description.ilike.%test%,description.ilike.%demo%,description.ilike.%sample%');

      if (expenseError) console.warn('Expenses cleanup warning:', expenseError);

      console.log('✅ Supabase demo data cleared');
    } catch (error) {
      console.error('❌ Error clearing Supabase demo data:', error);
    }
  }

  /**
   * Reset system to use only Supabase data
   */
  static async resetToSupabaseOnly(): Promise<void> {
    console.log('🔄 Resetting system to use only Supabase data...');

    // Clear all localStorage
    this.clearAllLocalStorageData();

    // Clear demo data from Supabase
    await this.clearSupabaseDemoData();

    // Force page reload to reinitialize with clean state
    console.log('🔄 Reloading page to reinitialize with clean state...');
    window.location.reload();
  }

  /**
   * Backup localStorage data before clearing (optional)
   */
  static backupLocalStorageData(): string {
    console.log('💾 Creating backup of localStorage data...');
    
    const backup: Record<string, any> = {};
    
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);
      if (key) {
        backup[key] = localStorage.getItem(key);
      }
    }

    const backupString = JSON.stringify(backup, null, 2);
    console.log('✅ Backup created');
    
    // Optionally download as file
    const blob = new Blob([backupString], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `localStorage-backup-${new Date().toISOString().split('T')[0]}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
    
    return backupString;
  }

  /**
   * Check if system is using demo data
   */
  static isDemoDataPresent(): boolean {
    const demoKeys = ['client_payments', 'client_invoices', 'documents'];
    return demoKeys.some(key => {
      const data = localStorage.getItem(key);
      return data && JSON.parse(data).length > 0;
    });
  }

  /**
   * Get financial data source summary
   */
  static getFinancialDataSummary(): Record<string, any> {
    const summary = {
      localStorage: {
        client_payments: 0,
        client_invoices: 0,
        documents: 0,
        totalPaymentAmount: 0,
        totalInvoiceAmount: 0
      },
      usingDemoData: false
    };

    // Check localStorage data
    const payments = JSON.parse(localStorage.getItem('client_payments') || '[]');
    const invoices = JSON.parse(localStorage.getItem('client_invoices') || '[]');
    const documents = JSON.parse(localStorage.getItem('documents') || '[]');

    summary.localStorage.client_payments = payments.length;
    summary.localStorage.client_invoices = invoices.length;
    summary.localStorage.documents = documents.length;
    
    summary.localStorage.totalPaymentAmount = payments.reduce((sum: number, p: any) => sum + (p.amount || 0), 0);
    summary.localStorage.totalInvoiceAmount = invoices.reduce((sum: number, i: any) => sum + (i.total_amount || 0), 0);
    
    summary.usingDemoData = this.isDemoDataPresent();

    return summary;
  }
}

// Global functions for easy access in browser console
(window as any).cleanupDemo = DemoDataCleanup;
(window as any).clearDemoData = () => DemoDataCleanup.resetToSupabaseOnly();
(window as any).showDemoData = () => DemoDataCleanup.showLocalStorageDataSummary();
(window as any).backupDemoData = () => DemoDataCleanup.backupLocalStorageData();
