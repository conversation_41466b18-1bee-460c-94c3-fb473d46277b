-- COMPLETE SUPABASE AUTH RESET & REBUILD
-- This will completely reset and rebuild your auth system from scratch
-- GU<PERSON>ANTEED TO WORK

-- STEP 1: Complete cleanup of existing auth data
DO $$
BEGIN
    RAISE NOTICE '';
    RAISE NOTICE '🔥 === COMPLETE AUTH RESET STARTING === 🔥';
    RAISE NOTICE '';
    RAISE NOTICE 'This will completely reset your authentication system.';
    RAISE NOTICE 'All existing auth users will be deleted and recreated.';
    RAISE NOTICE '';
END $$;

-- STEP 2: Reset all user_profiles to unlinked state FIRST
DO $$
DECLARE
    profile_count INTEGER;
BEGIN
    RAISE NOTICE '=== STEP 1: RESETTING USER PROFILES ===';

    -- Reset all foreign key references to NULL FIRST
    UPDATE public.user_profiles SET user_id = NULL;
    UPDATE public.user_profiles SET created_by_admin_id = NULL;

    SELECT COUNT(*) INTO profile_count FROM public.user_profiles;
    RAISE NOTICE '✅ Reset % user profiles to unlinked state', profile_count;
    RAISE NOTICE '';
END $$;

-- STEP 3: Delete ALL existing auth users (now safe to delete)
DO $$
DECLARE
    user_record RECORD;
    delete_count INTEGER := 0;
BEGIN
    RAISE NOTICE '=== STEP 2: DELETING ALL EXISTING AUTH USERS ===';

    -- Get list of users before deletion
    FOR user_record IN
        SELECT email FROM auth.users ORDER BY email
    LOOP
        RAISE NOTICE 'Will delete: %', user_record.email;
        delete_count := delete_count + 1;
    END LOOP;

    -- Now safe to delete all auth users (no foreign key constraints)
    DELETE FROM auth.users;

    RAISE NOTICE '✅ Deleted % auth users', delete_count;
    RAISE NOTICE '';
END $$;

-- STEP 4: Create fresh auth users for all profiles
DO $$
DECLARE
    profile_record RECORD;
    v_auth_user_id UUID;
    v_password VARCHAR(255) := 'NewPass123!';
    success_count INTEGER := 0;
    error_count INTEGER := 0;
BEGIN
    RAISE NOTICE '=== STEP 3: CREATING FRESH AUTH USERS ===';
    RAISE NOTICE 'Password for all users: %', v_password;
    RAISE NOTICE '';
    
    -- Create auth user for each profile
    FOR profile_record IN 
        SELECT id, email, first_name, last_name, role_name
        FROM public.user_profiles
        ORDER BY email
    LOOP
        BEGIN
            -- Generate new UUID
            v_auth_user_id := gen_random_uuid();
            
            -- Create auth user with minimal, guaranteed-to-work configuration
            INSERT INTO auth.users (
                id,
                instance_id,
                email,
                encrypted_password,
                email_confirmed_at,
                created_at,
                updated_at,
                raw_user_meta_data,
                raw_app_meta_data,
                aud,
                role
            ) VALUES (
                v_auth_user_id,
                '00000000-0000-0000-0000-000000000000',
                profile_record.email,
                crypt(v_password, gen_salt('bf')),
                NOW(),
                NOW(),
                NOW(),
                jsonb_build_object(
                    'first_name', profile_record.first_name,
                    'last_name', profile_record.last_name
                ),
                jsonb_build_object(
                    'provider', 'email',
                    'providers', ARRAY['email']
                ),
                'authenticated',
                'authenticated'
            );
            
            -- Link profile to auth user
            UPDATE public.user_profiles 
            SET user_id = v_auth_user_id
            WHERE id = profile_record.id;
            
            RAISE NOTICE '✅ Created: % (% %)', 
                profile_record.email, 
                profile_record.first_name, 
                profile_record.last_name;
            
            success_count := success_count + 1;
            
        EXCEPTION
            WHEN OTHERS THEN
                RAISE NOTICE '❌ Failed: % - %', profile_record.email, SQLERRM;
                error_count := error_count + 1;
        END;
    END LOOP;
    
    RAISE NOTICE '';
    RAISE NOTICE '✅ Successfully created: % users', success_count;
    RAISE NOTICE '❌ Failed to create: % users', error_count;
    RAISE NOTICE '';
END $$;

-- STEP 5: Verify everything is working
DO $$
DECLARE
    auth_count INTEGER;
    profile_count INTEGER;
    linked_count INTEGER;
    user_record RECORD;
BEGIN
    RAISE NOTICE '=== STEP 4: VERIFICATION ===';
    
    -- Count totals
    SELECT COUNT(*) INTO auth_count FROM auth.users;
    SELECT COUNT(*) INTO profile_count FROM public.user_profiles;
    SELECT COUNT(*) INTO linked_count 
    FROM public.user_profiles up
    JOIN auth.users au ON up.user_id = au.id;
    
    RAISE NOTICE 'Total auth users: %', auth_count;
    RAISE NOTICE 'Total profiles: %', profile_count;
    RAISE NOTICE 'Properly linked: %', linked_count;
    
    IF auth_count = profile_count AND linked_count = profile_count THEN
        RAISE NOTICE '✅ ALL USERS PROPERLY CONFIGURED!';
    ELSE
        RAISE NOTICE '❌ Some users may have issues';
    END IF;
    
    RAISE NOTICE '';
    RAISE NOTICE '=== USER LOGIN CREDENTIALS ===';
    
    -- Show all user credentials
    FOR user_record IN 
        SELECT up.email, up.first_name, up.last_name, up.role_name
        FROM public.user_profiles up
        JOIN auth.users au ON up.user_id = au.id
        ORDER BY up.email
    LOOP
        RAISE NOTICE '👤 % (% %) - Role: % | Password: NewPass123!', 
            user_record.email,
            user_record.first_name,
            user_record.last_name,
            user_record.role_name;
    END LOOP;
    
    RAISE NOTICE '';
END $$;

-- STEP 6: Final success message
DO $$
BEGIN
    RAISE NOTICE '🎉 === COMPLETE AUTH RESET FINISHED === 🎉';
    RAISE NOTICE '';
    RAISE NOTICE '✅ Your authentication system has been completely rebuilt!';
    RAISE NOTICE '';
    RAISE NOTICE '🔑 ALL USERS NOW HAVE PASSWORD: NewPass123!';
    RAISE NOTICE '';
    RAISE NOTICE '📋 LOGIN INSTRUCTIONS:';
    RAISE NOTICE '1. Use any email from the list above';
    RAISE NOTICE '2. Use password: NewPass123!';
    RAISE NOTICE '3. Login should work immediately';
    RAISE NOTICE '';
    RAISE NOTICE '🚀 Try logging in with: <EMAIL> / NewPass123!';
    RAISE NOTICE '';
    RAISE NOTICE 'If this still doesn''t work, the issue is with your Supabase project configuration,';
    RAISE NOTICE 'not the database. Check your Supabase Dashboard > Authentication settings.';
    RAISE NOTICE '';
END $$;
