-- Fix Supabase Site URL Configuration
-- This updates the site URL to use the correct port 8080

-- Check current auth configuration
SELECT 
    name,
    value
FROM auth.config
WHERE name IN ('SITE_URL', 'URI_ALLOW_LIST');

-- Update the site URL to use port 8080
UPDATE auth.config 
SET value = 'http://************:8080'
WHERE name = 'SITE_URL';

-- Update the URI allow list to include port 8080
UPDATE auth.config 
SET value = 'http://localhost:8080,http://************:8080,http://127.0.0.1:8080'
WHERE name = 'URI_ALLOW_LIST';

-- Insert site URL if it doesn't exist
INSERT INTO auth.config (name, value)
SELECT 'SITE_URL', 'http://************:8080'
WHERE NOT EXISTS (SELECT 1 FROM auth.config WHERE name = 'SITE_URL');

-- Insert URI allow list if it doesn't exist
INSERT INTO auth.config (name, value)
SELECT 'URI_ALLOW_LIST', 'http://localhost:8080,http://************:8080,http://127.0.0.1:8080'
WHERE NOT EXISTS (SELECT 1 FROM auth.config WHERE name = 'URI_ALLOW_LIST');

-- Check the updated configuration
SELECT 
    name,
    value,
    'Updated to port 8080' as status
FROM auth.config
WHERE name IN ('SITE_URL', 'URI_ALLOW_LIST');

-- Test notification
DO $$
BEGIN
    RAISE NOTICE '';
    RAISE NOTICE '=== SITE URL CONFIGURATION UPDATED ===';
    RAISE NOTICE 'Site URL updated to: http://************:8080';
    RAISE NOTICE 'URI Allow List updated to include port 8080';
    RAISE NOTICE '';
    RAISE NOTICE 'Password reset emails should now redirect to port 8080';
    RAISE NOTICE 'instead of localhost:3000';
    RAISE NOTICE '';
END $$;
