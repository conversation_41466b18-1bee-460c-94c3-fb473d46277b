-- Simple Foreign Key Fix for Invited Registration
-- This makes the foreign key constraint more flexible to handle timing issues

-- 1. Drop the existing foreign key constraint
ALTER TABLE public.user_profiles 
DROP CONSTRAINT IF EXISTS user_profiles_user_id_fkey;

-- 2. Recreate it as DEFERRABLE (allows temporary constraint violations within a transaction)
ALTER TABLE public.user_profiles 
ADD CONSTRAINT user_profiles_user_id_fkey 
FOREIGN KEY (user_id) REFERENCES auth.users(id) 
ON DELETE CASCADE 
DEFERRABLE INITIALLY DEFERRED;

-- 3. Alternative: Make user_id nullable temporarily during registration
-- This allows profiles to exist without auth users initially
ALTER TABLE public.user_profiles 
ALTER COLUMN user_id DROP NOT NULL;

-- 4. Add a check to ensure user_id is set when is_verified is true
ALTER TABLE public.user_profiles 
ADD CONSTRAINT user_profiles_verified_has_user_id 
CHECK (
    (is_verified = false) OR 
    (is_verified = true AND user_id IS NOT NULL)
);

-- 5. Create a simple update function that doesn't check auth.users
CREATE OR R<PERSON>LACE FUNCTION public.simple_update_profile(
    p_email VARCHAR(255),
    p_auth_user_id UUID,
    p_first_name VARCHAR(100),
    p_last_name VARCHAR(100)
)
RETURNS jsonb AS $$
DECLARE
    v_updated_count INTEGER;
BEGIN
    -- Update the existing user profile
    UPDATE public.user_profiles
    SET 
        user_id = p_auth_user_id,
        first_name = p_first_name,
        last_name = p_last_name,
        is_verified = true,
        email_status = 'registration_completed',
        updated_at = NOW()
    WHERE email = p_email 
    AND user_id IS NULL; -- Only update if not already activated
    
    GET DIAGNOSTICS v_updated_count = ROW_COUNT;
    
    IF v_updated_count = 0 THEN
        -- Check if profile exists but is already activated
        IF EXISTS (SELECT 1 FROM public.user_profiles WHERE email = p_email AND user_id IS NOT NULL) THEN
            RETURN jsonb_build_object(
                'success', false,
                'error', 'Account already activated. Please use the login page.'
            );
        ELSE
            RETURN jsonb_build_object(
                'success', false,
                'error', 'No invitation found for this email address'
            );
        END IF;
    END IF;
    
    -- Return success
    RETURN jsonb_build_object(
        'success', true,
        'message', 'Profile updated successfully',
        'updated_count', v_updated_count
    );
    
EXCEPTION
    WHEN OTHERS THEN
        RETURN jsonb_build_object(
            'success', false,
            'error', 'Profile update failed: ' || SQLERRM
        );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 6. Grant permissions
GRANT EXECUTE ON FUNCTION public.simple_update_profile(VARCHAR, UUID, VARCHAR, VARCHAR) TO anon, authenticated;

-- 7. Test the changes
DO $$
DECLARE
    test_email VARCHAR(255);
    constraint_info RECORD;
BEGIN
    RAISE NOTICE '';
    RAISE NOTICE '=== SIMPLE FOREIGN KEY FIX APPLIED ===';
    
    -- Check the new constraint
    SELECT 
        tc.constraint_name,
        tc.is_deferrable,
        tc.initially_deferred
    INTO constraint_info
    FROM information_schema.table_constraints tc
    WHERE tc.table_name = 'user_profiles' 
    AND tc.constraint_name = 'user_profiles_user_id_fkey';
    
    IF constraint_info.constraint_name IS NOT NULL THEN
        RAISE NOTICE 'Foreign key constraint updated:';
        RAISE NOTICE '  Name: %', constraint_info.constraint_name;
        RAISE NOTICE '  Deferrable: %', constraint_info.is_deferrable;
        RAISE NOTICE '  Initially Deferred: %', constraint_info.initially_deferred;
    END IF;
    
    -- Check if user_id is now nullable
    IF EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'user_profiles' 
        AND column_name = 'user_id' 
        AND is_nullable = 'YES'
    ) THEN
        RAISE NOTICE 'user_id column is now nullable ✓';
    ELSE
        RAISE NOTICE 'user_id column is still NOT NULL';
    END IF;
    
    -- Find a test email
    SELECT email INTO test_email 
    FROM public.user_profiles 
    WHERE user_id IS NULL 
    LIMIT 1;
    
    IF test_email IS NOT NULL THEN
        RAISE NOTICE 'Found test email for registration: %', test_email;
    END IF;
    
    RAISE NOTICE '';
    RAISE NOTICE 'Changes applied:';
    RAISE NOTICE '1. ✓ Foreign key constraint made deferrable';
    RAISE NOTICE '2. ✓ user_id column made nullable';
    RAISE NOTICE '3. ✓ Added verification constraint';
    RAISE NOTICE '4. ✓ Created simple_update_profile() function';
    RAISE NOTICE '';
    RAISE NOTICE 'This should resolve the foreign key constraint errors!';
    RAISE NOTICE 'Try the registration again now.';
    RAISE NOTICE '';
END $$;
