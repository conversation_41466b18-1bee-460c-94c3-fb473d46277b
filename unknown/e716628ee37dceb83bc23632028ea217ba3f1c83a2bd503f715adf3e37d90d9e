import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, Ta<PERSON><PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { 
  Plus, 
  Search, 
  Filter, 
  Truck, 
  Car, 
  Wrench, 
  Laptop, 
  Building, 
  Shield,
  Printer,
  Cog,
  Calendar,
  DollarSign,
  TrendingUp,
  TrendingDown,
  AlertTriangle,
  CheckCircle,
  Clock,
  MapPin,
  User,
  FileText,
  Settings,
  RefreshCw
} from 'lucide-react';
import { AssetService, CompanyAsset, AssetCategory, AssetSummary } from '@/lib/assetService';
import AssetForm from './AssetForm';
import AssetManagement from './AssetManagement';

const CompanyAssets: React.FC = () => {
  const [assets, setAssets] = useState<CompanyAsset[]>([]);
  const [categories, setCategories] = useState<AssetCategory[]>([]);
  const [summary, setSummary] = useState<AssetSummary | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string>('');
  const [selectedType, setSelectedType] = useState<string>('');
  const [selectedStatus, setSelectedStatus] = useState<string>('');
  const [showAddForm, setShowAddForm] = useState(false);
  const [showManageDialog, setShowManageDialog] = useState(false);
  const [selectedAsset, setSelectedAsset] = useState<CompanyAsset | null>(null);

  // Load data
  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    try {
      setLoading(true);
      setError(null);
      const [assetsData, categoriesData, summaryData] = await Promise.all([
        AssetService.getAssets({
          category_id: selectedCategory && selectedCategory !== 'all' ? selectedCategory : undefined,
          asset_type: selectedType && selectedType !== 'all' ? selectedType : undefined,
          status: selectedStatus && selectedStatus !== 'all' ? selectedStatus : undefined,
        }),
        AssetService.getAssetCategories(),
        AssetService.getAssetSummary()
      ]);

      setAssets(assetsData);
      setCategories(categoriesData);
      setSummary(summaryData);
    } catch (error) {
      console.error('Error loading asset data:', error);
      setError('Failed to load asset data. Please ensure the database tables are created.');
      setAssets([]);
      setCategories([]);
      setSummary(null);
    } finally {
      setLoading(false);
    }
  };

  // Filter assets based on search term
  const filteredAssets = assets.filter(asset =>
    asset.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    asset.asset_number.toLowerCase().includes(searchTerm.toLowerCase()) ||
    asset.brand?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    asset.model?.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Get icon for asset type
  const getAssetTypeIcon = (type: string) => {
    const iconMap: { [key: string]: React.ReactNode } = {
      'Equipment': <Truck className="w-4 h-4" />,
      'Vehicle': <Car className="w-4 h-4" />,
      'Tool': <Wrench className="w-4 h-4" />,
      'Technology': <Laptop className="w-4 h-4" />,
      'Property': <Building className="w-4 h-4" />,
      'Machinery': <Cog className="w-4 h-4" />
    };
    return iconMap[type] || <FileText className="w-4 h-4" />;
  };

  // Get status color
  const getStatusColor = (status: string) => {
    const colorMap: { [key: string]: string } = {
      'Active': 'bg-green-100 text-green-800',
      'Inactive': 'bg-gray-100 text-gray-800',
      'Under Maintenance': 'bg-yellow-100 text-yellow-800',
      'Disposed': 'bg-red-100 text-red-800',
      'Lost': 'bg-red-100 text-red-800',
      'Sold': 'bg-blue-100 text-blue-800'
    };
    return colorMap[status] || 'bg-gray-100 text-gray-800';
  };

  // Get condition color
  const getConditionColor = (condition: string) => {
    const colorMap: { [key: string]: string } = {
      'Excellent': 'bg-green-100 text-green-800',
      'Good': 'bg-blue-100 text-blue-800',
      'Fair': 'bg-yellow-100 text-yellow-800',
      'Poor': 'bg-orange-100 text-orange-800',
      'Needs Repair': 'bg-red-100 text-red-800'
    };
    return colorMap[condition] || 'bg-gray-100 text-gray-800';
  };

  // Format currency
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-2 text-sm text-gray-600">Loading assets...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <AlertTriangle className="w-12 h-12 text-red-500 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">Database Setup Required</h3>
          <p className="text-gray-600 mb-4 max-w-md">{error}</p>
          <div className="text-sm text-gray-500">
            <p>Please run the database setup scripts:</p>
            <ol className="list-decimal list-inside mt-2 space-y-1">
              <li>cash_flow_function.sql</li>
              <li>company_assets_simple.sql</li>
              <li>quick_test_assets.sql (optional)</li>
            </ol>
          </div>
          <Button onClick={loadData} className="mt-4">
            <RefreshCw className="w-4 h-4 mr-2" />
            Retry
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Company Assets</h2>
          <p className="text-gray-600">Manage equipment, vehicles, and property</p>
        </div>
        <Button
          onClick={() => setShowAddForm(true)}
          className="flex items-center space-x-2"
        >
          <Plus className="w-4 h-4" />
          <span>Add Asset</span>
        </Button>
      </div>

      {/* Summary Cards */}
      {summary && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Total Assets</p>
                  <p className="text-2xl font-bold text-gray-900">{summary.totalAssets}</p>
                </div>
                <div className="p-2 bg-blue-100 rounded-lg">
                  <FileText className="w-6 h-6 text-blue-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Total Value</p>
                  <p className="text-2xl font-bold text-gray-900">{formatCurrency(summary.totalValue)}</p>
                </div>
                <div className="p-2 bg-green-100 rounded-lg">
                  <DollarSign className="w-6 h-6 text-green-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Active Assets</p>
                  <p className="text-2xl font-bold text-gray-900">{summary.activeAssets}</p>
                </div>
                <div className="p-2 bg-green-100 rounded-lg">
                  <CheckCircle className="w-6 h-6 text-green-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Maintenance Due</p>
                  <p className="text-2xl font-bold text-gray-900">{summary.maintenanceDue}</p>
                </div>
                <div className="p-2 bg-yellow-100 rounded-lg">
                  <AlertTriangle className="w-6 h-6 text-yellow-600" />
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Filters */}
      <Card>
        <CardContent className="p-4">
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <Input
                  placeholder="Search assets..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <Select value={selectedCategory} onValueChange={setSelectedCategory}>
              <SelectTrigger className="w-full md:w-48">
                <SelectValue placeholder="All Categories" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Categories</SelectItem>
                {categories.map((category) => (
                  <SelectItem key={category.id} value={category.id}>
                    {category.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <Select value={selectedType} onValueChange={setSelectedType}>
              <SelectTrigger className="w-full md:w-48">
                <SelectValue placeholder="All Types" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Types</SelectItem>
                <SelectItem value="Equipment">Equipment</SelectItem>
                <SelectItem value="Vehicle">Vehicle</SelectItem>
                <SelectItem value="Property">Property</SelectItem>
                <SelectItem value="Tool">Tool</SelectItem>
                <SelectItem value="Machinery">Machinery</SelectItem>
                <SelectItem value="Technology">Technology</SelectItem>
              </SelectContent>
            </Select>
            <Select value={selectedStatus} onValueChange={setSelectedStatus}>
              <SelectTrigger className="w-full md:w-48">
                <SelectValue placeholder="All Status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value="Active">Active</SelectItem>
                <SelectItem value="Inactive">Inactive</SelectItem>
                <SelectItem value="Under Maintenance">Under Maintenance</SelectItem>
                <SelectItem value="Disposed">Disposed</SelectItem>
                <SelectItem value="Lost">Lost</SelectItem>
                <SelectItem value="Sold">Sold</SelectItem>
              </SelectContent>
            </Select>
            <Button variant="outline" onClick={loadData}>
              <Filter className="w-4 h-4 mr-2" />
              Apply
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Assets List */}
      <Card>
        <CardHeader>
          <CardTitle>Assets ({filteredAssets.length})</CardTitle>
          <CardDescription>
            Complete list of company assets with details and status
          </CardDescription>
        </CardHeader>
        <CardContent>
          {filteredAssets.length === 0 ? (
            <div className="text-center py-8">
              <FileText className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">No assets found</h3>
              <p className="text-gray-600 mb-4">
                {searchTerm || selectedCategory || selectedType || selectedStatus
                  ? 'No assets match your current filters.'
                  : 'Get started by adding your first company asset.'}
              </p>
              <Button onClick={() => setShowAddForm(true)}>
                <Plus className="w-4 h-4 mr-2" />
                Add First Asset
              </Button>
            </div>
          ) : (
            <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-4">
              {filteredAssets.map((asset) => (
                <Card key={asset.id} className="hover:shadow-md transition-shadow">
                  <CardContent className="p-4">
                    <div className="flex items-start justify-between mb-3">
                      <div className="flex items-center space-x-2">
                        {getAssetTypeIcon(asset.asset_type)}
                        <div>
                          <h3 className="font-medium text-gray-900">{asset.name}</h3>
                          <p className="text-sm text-gray-600">{asset.asset_number}</p>
                        </div>
                      </div>
                      <Badge className={getStatusColor(asset.status)}>
                        {asset.status}
                      </Badge>
                    </div>

                    <div className="space-y-2 text-sm">
                      {asset.brand && asset.model && (
                        <div className="flex justify-between">
                          <span className="text-gray-600">Brand/Model:</span>
                          <span className="font-medium">{asset.brand} {asset.model}</span>
                        </div>
                      )}
                      
                      <div className="flex justify-between">
                        <span className="text-gray-600">Value:</span>
                        <span className="font-medium">{formatCurrency(asset.current_value)}</span>
                      </div>

                      <div className="flex justify-between">
                        <span className="text-gray-600">Condition:</span>
                        <Badge className={getConditionColor(asset.condition)}>
                          {asset.condition}
                        </Badge>
                      </div>

                      {asset.location && (
                        <div className="flex justify-between">
                          <span className="text-gray-600">Location:</span>
                          <span className="font-medium flex items-center">
                            <MapPin className="w-3 h-3 mr-1" />
                            {asset.location}
                          </span>
                        </div>
                      )}

                      {asset.assigned_to && (
                        <div className="flex justify-between">
                          <span className="text-gray-600">Assigned to:</span>
                          <span className="font-medium flex items-center">
                            <User className="w-3 h-3 mr-1" />
                            {asset.assigned_to}
                          </span>
                        </div>
                      )}
                    </div>

                    <div className="flex justify-between items-center mt-4 pt-3 border-t">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => {
                          setSelectedAsset(asset);
                          setShowManageDialog(true);
                        }}
                      >
                        <Settings className="w-3 h-3 mr-1" />
                        Manage
                      </Button>
                      <div className="text-xs text-gray-500">
                        {asset.category?.name || 'Uncategorized'}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Add Asset Form */}
      <AssetForm
        open={showAddForm}
        onOpenChange={setShowAddForm}
        categories={categories}
        onAssetSaved={loadData}
      />

      {/* Asset Management Dialog */}
      <AssetManagement
        open={showManageDialog}
        onOpenChange={setShowManageDialog}
        asset={selectedAsset}
        categories={categories}
        onAssetUpdated={loadData}
      />
    </div>
  );
};

export default CompanyAssets;
