-- Fix Port 8080 URLs - Update All Email Functions
-- This ensures all email URLs use the correct port 8080

-- 1. Update the main RPC function to use port 8080
CREATE OR REPLACE FUNCTION public.rpc_resend_setup_email(user_profile_id UUID)
RETURNS jsonb AS $$
DECLARE
    v_requesting_user_id UUID;
    user_record RECORD;
    email_content TEXT;
    email_subject TEXT;
    action_url TEXT;
    current_count INTEGER;
    max_emails INTEGER := 50;
BEGIN
    v_requesting_user_id := auth.uid();
    
    IF v_requesting_user_id IS NULL THEN
        RETURN jsonb_build_object('success', false, 'error', 'Not authenticated');
    END IF;
    
    -- Get user information
    SELECT up.*, ur.role_name
    INTO user_record
    FROM public.user_profiles up
    JOIN public.user_roles ur ON up.role_id = ur.id
    WHERE up.id = user_profile_id;
    
    IF NOT FOUND THEN
        RETURN jsonb_build_object('success', false, 'error', 'User not found');
    END IF;
    
    -- Check email limits
    current_count := COALESCE(user_record.setup_email_count, 0);
    IF current_count >= max_emails THEN
        RETURN jsonb_build_object('success', false, 'error', 'Maximum email limit reached (' || max_emails || ')');
    END IF;
    
    -- Determine email type and content based on whether user has auth account
    IF user_record.user_id IS NULL THEN
        -- User needs to register first
        email_subject := 'Account Activation Required - Construction Management System';
        action_url := 'http://************:8080/invited-register?email=' || user_record.email || '&invited=true';
        
        email_content := 'Dear ' || user_record.first_name || ',

Your account has been created for Construction Management System.

Account Information:
• Email: ' || user_record.email || '
• Role: ' || INITCAP(user_record.role_name) || '
• Status: Activation Required

To activate your account:

1. Visit: ' || action_url || '
2. Complete the registration process
3. Set your secure password
4. Begin using the system

This link is valid for 7 days. If you experience any issues, please contact our support team.

Best regards,
Construction Management System Team

---
This is an automated message. Please do not reply to this email.
For support, contact: <EMAIL>
Construction Management System | Professional Construction Management
Generated on: ' || TO_CHAR(CURRENT_DATE, 'Month DD, YYYY');

    ELSE
        -- User has auth account, can reset password
        email_subject := 'Password Reset Request - Construction Management System';
        action_url := 'http://************:8080/login';
        
        email_content := 'Dear ' || user_record.first_name || ',

A password reset has been requested for your Construction Management System account.

Account Information:
• Email: ' || user_record.email || '
• Role: ' || INITCAP(user_record.role_name) || '
• Status: Password Reset Required

To reset your password:

1. Visit: ' || action_url || '
2. Click "Forgot Password"
3. Enter your email address
4. Follow the instructions sent to your email

If you did not request this reset, please contact our support team immediately.

Best regards,
Construction Management System Team

---
This is an automated message. Please do not reply to this email.
For support, contact: <EMAIL>
Construction Management System | Professional Construction Management
Generated on: ' || TO_CHAR(CURRENT_DATE, 'Month DD, YYYY');

    END IF;
    
    -- Update email count
    UPDATE public.user_profiles 
    SET 
        setup_email_sent_at = NOW(),
        setup_email_count = current_count + 1,
        email_status = 'port_8080_fixed',
        updated_at = NOW()
    WHERE id = user_profile_id;
    
    -- Return email content with correct port
    RETURN jsonb_build_object(
        'success', true, 
        'message', 'Email content generated with correct port 8080',
        'email_data', jsonb_build_object(
            'to_email', user_record.email,
            'subject', email_subject,
            'text_content', email_content,
            'action_url', action_url,
            'user_name', user_record.first_name || ' ' || user_record.last_name,
            'method', CASE WHEN user_record.user_id IS NULL THEN 'registration_invitation' ELSE 'password_reset' END,
            'user_role', user_record.role_name,
            'has_auth_account', (user_record.user_id IS NOT NULL),
            'correct_port', '8080'
        )
    );
    
EXCEPTION
    WHEN OTHERS THEN
        RETURN jsonb_build_object('success', false, 'error', SQLERRM);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 2. Update any other email functions that might exist
CREATE OR REPLACE FUNCTION public.generate_user_email_content(user_profile_id UUID)
RETURNS TABLE(success BOOLEAN, message TEXT, email_data JSONB) AS $$
DECLARE
    user_record RECORD;
    current_count INTEGER;
    max_emails INTEGER := 50;
    email_content TEXT;
    email_subject TEXT;
    action_url TEXT;
    email_method TEXT;
BEGIN
    -- Get user information
    SELECT up.*, ur.role_name
    INTO user_record
    FROM public.user_profiles up
    JOIN public.user_roles ur ON up.role_id = ur.id
    WHERE up.id = user_profile_id;
    
    IF NOT FOUND THEN
        RETURN QUERY SELECT false, 'User not found', '{}'::jsonb;
        RETURN;
    END IF;
    
    -- Check email limits
    current_count := COALESCE(user_record.setup_email_count, 0);
    IF current_count >= max_emails THEN
        RETURN QUERY SELECT false, 'Maximum email limit reached (' || max_emails || ')', '{}'::jsonb;
        RETURN;
    END IF;
    
    -- Determine email type and content based on whether user has auth account
    IF user_record.user_id IS NULL THEN
        -- User needs to register first
        email_method := 'registration_invitation';
        email_subject := 'Account Activation Required - Construction Management System';
        action_url := 'http://************:8080/invited-register?email=' || user_record.email || '&invited=true';
        
        email_content := 'Dear ' || user_record.first_name || ',

Your account has been created for Construction Management System.

Account Information:
• Email: ' || user_record.email || '
• Role: ' || INITCAP(user_record.role_name) || '
• Status: Activation Required

To activate your account:

1. Visit: ' || action_url || '
2. Complete the registration process
3. Set your secure password
4. Begin using the system

This link is valid for 7 days.

Best regards,
Construction Management System Team

---
Generated on: ' || TO_CHAR(CURRENT_DATE, 'Month DD, YYYY');

    ELSE
        -- User has auth account, can reset password
        email_method := 'password_reset';
        email_subject := 'Password Reset Request - Construction Management System';
        action_url := 'http://************:8080/login';
        
        email_content := 'Dear ' || user_record.first_name || ',

A password reset has been requested for your account.

Account Information:
• Email: ' || user_record.email || '
• Role: ' || INITCAP(user_record.role_name) || '

To reset your password:

1. Visit: ' || action_url || '
2. Click "Forgot Password"
3. Enter your email address
4. Follow the instructions sent to your email

Best regards,
Construction Management System Team

---
Generated on: ' || TO_CHAR(CURRENT_DATE, 'Month DD, YYYY');

    END IF;
    
    -- Update email count
    UPDATE public.user_profiles 
    SET 
        setup_email_sent_at = NOW(),
        setup_email_count = current_count + 1,
        email_status = 'port_8080_updated',
        updated_at = NOW()
    WHERE id = user_profile_id;
    
    -- Return email content
    RETURN QUERY SELECT 
        true, 
        'Email content generated with port 8080',
        jsonb_build_object(
            'to_email', user_record.email,
            'subject', email_subject,
            'text_content', email_content,
            'action_url', action_url,
            'user_name', user_record.first_name || ' ' || user_record.last_name,
            'method', email_method,
            'user_role', user_record.role_name,
            'has_auth_account', (user_record.user_id IS NOT NULL),
            'correct_port', '8080'
        );
        
EXCEPTION
    WHEN OTHERS THEN
        RETURN QUERY SELECT false, 'Email generation failed: ' || SQLERRM, '{}'::jsonb;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 3. Grant permissions
GRANT EXECUTE ON FUNCTION public.rpc_resend_setup_email(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION public.generate_user_email_content(UUID) TO authenticated;

-- 4. Test the fix
DO $$
DECLARE
    test_user_id UUID;
    email_result RECORD;
BEGIN
    RAISE NOTICE '';
    RAISE NOTICE '=== TESTING PORT 8080 FIX ===';
    
    -- Find a test user
    SELECT id INTO test_user_id 
    FROM public.user_profiles 
    WHERE email LIKE '%@gmail.com'
    LIMIT 1;
    
    IF test_user_id IS NOT NULL THEN
        RAISE NOTICE 'Testing with user ID: %', SUBSTRING(test_user_id::TEXT, 1, 8);
        
        SELECT * INTO email_result 
        FROM public.generate_user_email_content(test_user_id);
        
        RAISE NOTICE 'Result:';
        RAISE NOTICE '  Success: %', email_result.success;
        RAISE NOTICE '  Message: %', email_result.message;
        
        IF email_result.success THEN
            RAISE NOTICE '  Action URL: %', (email_result.email_data->>'action_url');
            RAISE NOTICE '  Correct Port: %', (email_result.email_data->>'correct_port');
            
            IF (email_result.email_data->>'action_url') LIKE '%:8080%' THEN
                RAISE NOTICE '✓ PORT 8080 FIX WORKING!';
            ELSE
                RAISE NOTICE '❌ Still using wrong port!';
            END IF;
        END IF;
    END IF;
    
    RAISE NOTICE '';
    RAISE NOTICE '=== PORT 8080 FIX COMPLETE ===';
    RAISE NOTICE 'All email URLs now use: http://************:8080';
    RAISE NOTICE 'Try "Resend Setup Email" now!';
END $$;
