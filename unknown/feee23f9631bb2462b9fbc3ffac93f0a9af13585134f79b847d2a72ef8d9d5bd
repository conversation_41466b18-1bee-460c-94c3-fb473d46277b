-- Emergency Fix for Message Triggers
-- This removes all conflicting triggers and recreates them correctly

-- 1. Drop ALL existing message-related triggers to start fresh
DROP TRIGGER IF EXISTS trigger_create_message_notifications ON public.messages;
DROP TRIGGER IF EXISTS set_creator_info_messages ON public.messages;
DROP TRIGGER IF EXISTS message_notifications_trigger ON public.messages;
DROP TRIGGER IF EXISTS create_message_notifications_trigger ON public.messages;

-- 2. Drop ALL existing message notification functions to start fresh
DROP FUNCTION IF EXISTS create_message_notifications() CASCADE;
DROP FUNCTION IF EXISTS populate_message_creator_info() CASCADE;

-- 3. Check what columns actually exist in the messages table
DO $$
DECLARE
    col_record RECORD;
BEGIN
    RAISE NOTICE '';
    RAISE NOTICE '=== CURRENT MESSAGES TABLE STRUCTURE ===';
    
    FOR col_record IN 
        SELECT column_name, data_type, is_nullable
        FROM information_schema.columns 
        WHERE table_name = 'messages' AND table_schema = 'public'
        ORDER BY ordinal_position
    LOOP
        RAISE NOTICE 'Column: % (Type: %, Nullable: %)', col_record.column_name, col_record.data_type, col_record.is_nullable;
    END LOOP;
    
    RAISE NOTICE '';
END $$;

-- 4. Add missing creator fields if they don't exist
DO $$
BEGIN
    -- Add created_by_user_id field if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'messages' AND column_name = 'created_by_user_id' AND table_schema = 'public') THEN
        ALTER TABLE public.messages ADD COLUMN created_by_user_id UUID;
        RAISE NOTICE '✓ Added created_by_user_id to messages table';
    ELSE
        RAISE NOTICE '- created_by_user_id already exists in messages table';
    END IF;

    -- Add created_by_name field if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'messages' AND column_name = 'created_by_name' AND table_schema = 'public') THEN
        ALTER TABLE public.messages ADD COLUMN created_by_name TEXT;
        RAISE NOTICE '✓ Added created_by_name to messages table';
    ELSE
        RAISE NOTICE '- created_by_name already exists in messages table';
    END IF;

    -- Add created_by_avatar field if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'messages' AND column_name = 'created_by_avatar' AND table_schema = 'public') THEN
        ALTER TABLE public.messages ADD COLUMN created_by_avatar TEXT;
        RAISE NOTICE '✓ Added created_by_avatar to messages table';
    ELSE
        RAISE NOTICE '- created_by_avatar already exists in messages table';
    END IF;
END $$;

-- 5. Create a simple function to populate creator info (no external dependencies)
CREATE OR REPLACE FUNCTION populate_message_creator_info()
RETURNS TRIGGER AS $$
BEGIN
    -- Set creator fields using auth.uid() and basic info
    NEW.created_by_user_id := auth.uid();
    NEW.created_by_name := COALESCE(NEW.sender_name, 'Unknown User');
    NEW.created_by_avatar := NULL;

    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 6. Create trigger for populating creator info
CREATE TRIGGER set_creator_info_messages
    BEFORE INSERT ON public.messages
    FOR EACH ROW
    EXECUTE FUNCTION populate_message_creator_info();

-- 7. Create a simple notification function that works with existing fields
CREATE OR REPLACE FUNCTION create_message_notifications()
RETURNS TRIGGER AS $$
DECLARE
    user_record RECORD;
    notification_count INTEGER := 0;
    sender_id UUID;
    sender_name TEXT;
    channel_name TEXT := 'General';
BEGIN
    -- Only create notifications for new messages
    IF TG_OP = 'INSERT' AND COALESCE(NEW.is_deleted, FALSE) = FALSE THEN
        
        -- Get sender ID from available fields
        sender_id := COALESCE(NEW.created_by_user_id, auth.uid());
        
        -- Get sender name from available fields
        sender_name := COALESCE(NEW.created_by_name, NEW.sender_name, 'User');
        
        -- Try to get channel name, fallback to 'General'
        BEGIN
            SELECT name INTO channel_name 
            FROM public.channels 
            WHERE id = NEW.channel_id;
        EXCEPTION WHEN OTHERS THEN
            channel_name := 'General';
        END;
        
        -- Default if no channel found
        IF channel_name IS NULL THEN
            channel_name := 'General';
        END IF;
        
        -- Create notifications for other users
        BEGIN
            -- Try user_profiles first
            FOR user_record IN 
                SELECT user_id 
                FROM public.user_profiles 
                WHERE user_id != sender_id
                AND user_id IS NOT NULL
                LIMIT 50  -- Limit to prevent too many notifications
            LOOP
                BEGIN
                    INSERT INTO public.notifications (
                        user_id,
                        category,
                        type,
                        title,
                        message,
                        action_url,
                        action_label,
                        priority,
                        related_entity_type,
                        related_entity_id,
                        metadata,
                        created_by
                    ) VALUES (
                        user_record.user_id,
                        'message',
                        'info',
                        'New message in ' || channel_name,
                        sender_name || ': ' || LEFT(COALESCE(NEW.message_content, NEW.content, 'New message'), 100),
                        '/messages?channel=' || NEW.channel_id,
                        'View Message',
                        'medium',
                        'message',
                        NEW.id,
                        jsonb_build_object(
                            'channel_name', channel_name,
                            'sender_name', sender_name,
                            'message_id', NEW.id,
                            'channel_id', NEW.channel_id
                        ),
                        sender_id
                    );
                    notification_count := notification_count + 1;
                EXCEPTION WHEN OTHERS THEN
                    -- Skip this notification if it fails
                    CONTINUE;
                END;
            END LOOP;
        EXCEPTION WHEN OTHERS THEN
            -- If user_profiles doesn't exist, try auth.users
            FOR user_record IN 
                SELECT id as user_id 
                FROM auth.users 
                WHERE id != sender_id
                LIMIT 50
            LOOP
                BEGIN
                    INSERT INTO public.notifications (
                        user_id,
                        category,
                        type,
                        title,
                        message,
                        action_url,
                        action_label,
                        priority,
                        related_entity_type,
                        related_entity_id,
                        metadata,
                        created_by
                    ) VALUES (
                        user_record.user_id,
                        'message',
                        'info',
                        'New message in ' || channel_name,
                        sender_name || ': ' || LEFT(COALESCE(NEW.message_content, NEW.content, 'New message'), 100),
                        '/messages?channel=' || NEW.channel_id,
                        'View Message',
                        'medium',
                        'message',
                        NEW.id,
                        jsonb_build_object(
                            'channel_name', channel_name,
                            'sender_name', sender_name,
                            'message_id', NEW.id,
                            'channel_id', NEW.channel_id
                        ),
                        sender_id
                    );
                    notification_count := notification_count + 1;
                EXCEPTION WHEN OTHERS THEN
                    -- Skip this notification if it fails
                    CONTINUE;
                END;
            END LOOP;
        END;
        
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 8. Create trigger for automatic message notifications
CREATE TRIGGER trigger_create_message_notifications
    AFTER INSERT ON public.messages
    FOR EACH ROW
    EXECUTE FUNCTION create_message_notifications();

-- 9. Grant permissions
GRANT EXECUTE ON FUNCTION create_message_notifications() TO authenticated, anon;
GRANT EXECUTE ON FUNCTION populate_message_creator_info() TO authenticated, anon;

-- 10. Final verification
DO $$
DECLARE
    trigger_count INTEGER;
    function_count INTEGER;
BEGIN
    RAISE NOTICE '';
    RAISE NOTICE '=== EMERGENCY FIX COMPLETE ===';
    
    -- Count triggers
    SELECT COUNT(*) INTO trigger_count
    FROM information_schema.triggers 
    WHERE event_object_table = 'messages';
    
    -- Count functions
    SELECT COUNT(*) INTO function_count
    FROM information_schema.routines 
    WHERE routine_name IN ('create_message_notifications', 'populate_message_creator_info');
    
    RAISE NOTICE 'Message triggers created: %', trigger_count;
    RAISE NOTICE 'Message functions created: %', function_count;
    
    IF trigger_count >= 2 AND function_count >= 2 THEN
        RAISE NOTICE '✓ Emergency fix applied successfully!';
        RAISE NOTICE '✓ Try sending a message now - the error should be resolved';
    ELSE
        RAISE NOTICE '⚠ Some components may be missing';
    END IF;
    
    RAISE NOTICE '';
END $$;
