-- Alternative approach: Delete and recreate the auth user properly
-- This uses a more reliable method

-- 1. First, let's clean up the existing broken auth user
DO $$
DECLARE
    v_auth_id UUID;
BEGIN
    RAISE NOTICE '';
    RAISE NOTICE '=== CLEANING UP EXISTING AUTH USER ===';
    
    -- Get the current auth user ID
    SELECT id INTO v_auth_id
    FROM auth.users
    WHERE email = '<EMAIL>';
    
    IF v_auth_id IS NOT NULL THEN
        -- Delete the broken auth user
        DELETE FROM auth.users WHERE id = v_auth_id;
        RAISE NOTICE '✅ Deleted broken auth user: %', v_auth_id;
    ELSE
        RAISE NOTICE '⚠️ No existing auth user found';
    END IF;
    
    -- Reset the profile's user_id to NULL
    UPDATE public.user_profiles 
    SET user_id = NULL
    WHERE email = '<EMAIL>';
    
    RAISE NOTICE '✅ Reset profile user_id to NULL';
    
END $$;

-- 2. Create a more robust auth user creation function
CREATE OR REPLACE FUNCTION public.create_auth_user_properly(
    p_email VARCHAR(255),
    p_password VARCHAR(255),
    p_first_name VARCHAR(100),
    p_last_name VARCHAR(100)
)
RETURNS jsonb AS $$
DECLARE
    v_auth_user_id UUID;
    v_instance_id UUID := '00000000-0000-0000-0000-000000000000';
BEGIN
    -- Generate new UUID
    v_auth_user_id := gen_random_uuid();
    
    BEGIN
        -- Insert with all required fields for Supabase auth
        INSERT INTO auth.users (
            id,
            instance_id,
            email,
            encrypted_password,
            email_confirmed_at,
            email_change_sent_at,
            recovery_sent_at,
            created_at,
            updated_at,
            confirmation_token,
            email_change,
            email_change_token_new,
            recovery_token,
            raw_user_meta_data,
            raw_app_meta_data,
            is_super_admin,
            role,
            aud,
            confirmation_sent_at,
            recovery_sent_at,
            email_change_token_current,
            email_change_confirm_status,
            banned_until,
            deleted_at
        ) VALUES (
            v_auth_user_id,
            v_instance_id,
            p_email,
            crypt(p_password, gen_salt('bf')),
            NOW(), -- Email confirmed
            NULL,
            NULL,
            NOW(),
            NOW(),
            '',
            '',
            '',
            '',
            jsonb_build_object(
                'first_name', p_first_name,
                'last_name', p_last_name,
                'email', p_email,
                'email_verified', true,
                'phone_verified', false
            ),
            jsonb_build_object(
                'provider', 'email',
                'providers', ARRAY['email']
            ),
            false,
            'authenticated',
            'authenticated',
            NOW(),
            NULL,
            '',
            0,
            NULL,
            NULL
        );
        
        RETURN jsonb_build_object(
            'success', true,
            'auth_user_id', v_auth_user_id,
            'message', 'Auth user created successfully'
        );
        
    EXCEPTION
        WHEN OTHERS THEN
            RETURN jsonb_build_object(
                'success', false,
                'error', 'Failed to create auth user: ' || SQLERRM
            );
    END;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant permission
GRANT EXECUTE ON FUNCTION public.create_auth_user_properly(VARCHAR, VARCHAR, VARCHAR, VARCHAR) TO authenticated;

-- 3. Now recreate the auth user properly
DO $$
DECLARE
    v_profile RECORD;
    auth_result jsonb;
    v_password VARCHAR(255) := 'TempPass123!';
BEGIN
    RAISE NOTICE '';
    RAISE NOTICE '=== RECREATING AUTH USER PROPERLY ===';
    
    -- Get profile info
    SELECT * INTO v_profile
    FROM public.user_profiles
    WHERE email = '<EMAIL>';
    
    IF v_profile.email IS NULL THEN
        RAISE NOTICE '❌ Profile not found';
        RETURN;
    END IF;
    
    RAISE NOTICE '✅ Found profile: % %', v_profile.first_name, v_profile.last_name;
    
    -- Create auth user
    SELECT public.create_auth_user_properly(
        '<EMAIL>',
        v_password,
        v_profile.first_name,
        v_profile.last_name
    ) INTO auth_result;
    
    IF (auth_result->>'success')::boolean THEN
        RAISE NOTICE '✅ Auth user created: %', auth_result->>'auth_user_id';
        
        -- Update profile to link to new auth user
        UPDATE public.user_profiles 
        SET user_id = (auth_result->>'auth_user_id')::UUID
        WHERE email = '<EMAIL>';
        
        RAISE NOTICE '✅ Profile linked to auth user';
        RAISE NOTICE '';
        RAISE NOTICE '🎉 SUCCESS! Login credentials:';
        RAISE NOTICE '   Email: <EMAIL>';
        RAISE NOTICE '   Password: %', v_password;
        RAISE NOTICE '';
        
    ELSE
        RAISE NOTICE '❌ Failed to create auth user: %', auth_result->>'error';
    END IF;
    
END $$;

-- 4. Final verification
DO $$
DECLARE
    v_profile_exists BOOLEAN;
    v_auth_exists BOOLEAN;
    v_linked BOOLEAN;
    v_auth_confirmed BOOLEAN;
BEGIN
    RAISE NOTICE '=== FINAL VERIFICATION ===';
    
    -- Check profile exists
    SELECT EXISTS(SELECT 1 FROM public.user_profiles WHERE email = '<EMAIL>')
    INTO v_profile_exists;
    
    -- Check auth user exists
    SELECT EXISTS(SELECT 1 FROM auth.users WHERE email = '<EMAIL>')
    INTO v_auth_exists;
    
    -- Check if properly linked
    SELECT EXISTS(
        SELECT 1 FROM public.user_profiles up
        JOIN auth.users au ON up.user_id = au.id
        WHERE up.email = '<EMAIL>'
    ) INTO v_linked;
    
    -- Check if email is confirmed
    SELECT email_confirmed_at IS NOT NULL
    INTO v_auth_confirmed
    FROM auth.users
    WHERE email = '<EMAIL>';
    
    RAISE NOTICE 'Profile exists: %', v_profile_exists;
    RAISE NOTICE 'Auth user exists: %', v_auth_exists;
    RAISE NOTICE 'Properly linked: %', v_linked;
    RAISE NOTICE 'Email confirmed: %', v_auth_confirmed;
    
    IF v_profile_exists AND v_auth_exists AND v_linked AND v_auth_confirmed THEN
        RAISE NOTICE '';
        RAISE NOTICE '🎉 ALL CHECKS PASSED! User should be able to login now.';
        RAISE NOTICE '';
    ELSE
        RAISE NOTICE '';
        RAISE NOTICE '❌ Some checks failed. Login may not work.';
        RAISE NOTICE '';
    END IF;
END $$;
