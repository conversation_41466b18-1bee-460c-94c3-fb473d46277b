import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { useToast } from '@/components/ui/use-toast';
import { useAuth } from '@/contexts/AuthContext';
import { UserService, AuditLog } from '@/lib/userService';
import {
  Activity, Search, Filter, Download, RefreshCw, Calendar, User, Shield,
  Building2, FileText, Settings, AlertTriangle, CheckCircle, XCircle,
  Edit, Trash2, Plus, Eye, Clock
} from 'lucide-react';

const ActivityLog: React.FC = () => {
  const { toast } = useToast();
  const { user, profile } = useAuth();
  const [loading, setLoading] = useState(false);
  const [activities, setActivities] = useState<AuditLog[]>([]);
  const [filteredActivities, setFilteredActivities] = useState<AuditLog[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [actionFilter, setActionFilter] = useState('all');
  const [resourceFilter, setResourceFilter] = useState('all');
  const [dateFilter, setDateFilter] = useState('all');

  useEffect(() => {
    loadActivityLog();
  }, []);

  useEffect(() => {
    filterActivities();
  }, [activities, searchTerm, actionFilter, resourceFilter, dateFilter]);

  const loadActivityLog = async () => {
    try {
      setLoading(true);
      const logs = await UserService.getAuditLogs(100, 0);
      setActivities(logs);
    } catch (error) {
      console.error('Error loading activity log:', error);
      toast({
        title: "Error",
        description: "Failed to load activity log",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const filterActivities = () => {
    let filtered = activities;

    // Search filter
    if (searchTerm) {
      filtered = filtered.filter(activity =>
        activity.action.toLowerCase().includes(searchTerm.toLowerCase()) ||
        activity.resource_type?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        activity.resource_id?.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Action filter
    if (actionFilter !== 'all') {
      filtered = filtered.filter(activity => activity.action === actionFilter);
    }

    // Resource filter
    if (resourceFilter !== 'all') {
      filtered = filtered.filter(activity => activity.resource_type === resourceFilter);
    }

    // Date filter
    if (dateFilter !== 'all') {
      const now = new Date();
      const filterDate = new Date();
      
      switch (dateFilter) {
        case 'today':
          filterDate.setHours(0, 0, 0, 0);
          break;
        case 'week':
          filterDate.setDate(now.getDate() - 7);
          break;
        case 'month':
          filterDate.setMonth(now.getMonth() - 1);
          break;
        default:
          filterDate.setFullYear(1970);
      }
      
      filtered = filtered.filter(activity => 
        new Date(activity.created_at) >= filterDate
      );
    }

    setFilteredActivities(filtered);
  };

  const getActivityIcon = (action: string, resourceType?: string) => {
    const iconClass = "w-4 h-4";
    
    if (action.includes('create') || action.includes('add')) {
      return <Plus className={`${iconClass} text-green-500`} />;
    } else if (action.includes('update') || action.includes('edit') || action.includes('change')) {
      return <Edit className={`${iconClass} text-blue-500`} />;
    } else if (action.includes('delete') || action.includes('remove')) {
      return <Trash2 className={`${iconClass} text-red-500`} />;
    } else if (action.includes('view') || action.includes('access')) {
      return <Eye className={`${iconClass} text-gray-500`} />;
    } else if (action.includes('login') || action.includes('logout')) {
      return <User className={`${iconClass} text-purple-500`} />;
    }

    // Resource-based icons
    switch (resourceType) {
      case 'user_profile':
        return <User className={`${iconClass} text-blue-500`} />;
      case 'project':
        return <Building2 className={`${iconClass} text-green-500`} />;
      case 'invoice':
      case 'financial':
        return <FileText className={`${iconClass} text-orange-500`} />;
      case 'role':
      case 'permission':
        return <Shield className={`${iconClass} text-purple-500`} />;
      case 'system':
        return <Settings className={`${iconClass} text-gray-500`} />;
      default:
        return <Activity className={`${iconClass} text-gray-500`} />;
    }
  };

  const getActionBadge = (action: string) => {
    const badgeClass = "text-xs";
    
    if (action.includes('create') || action.includes('add')) {
      return <Badge className={`${badgeClass} bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300`}>
        {action}
      </Badge>;
    } else if (action.includes('update') || action.includes('edit') || action.includes('change')) {
      return <Badge className={`${badgeClass} bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300`}>
        {action}
      </Badge>;
    } else if (action.includes('delete') || action.includes('remove')) {
      return <Badge className={`${badgeClass} bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300`}>
        {action}
      </Badge>;
    } else if (action.includes('login') || action.includes('logout')) {
      return <Badge className={`${badgeClass} bg-purple-100 text-purple-800 dark:bg-purple-900/30 dark:text-purple-300`}>
        {action}
      </Badge>;
    }

    return <Badge variant="secondary" className={badgeClass}>
      {action}
    </Badge>;
  };

  const formatTimeAgo = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));
    
    if (diffInMinutes < 1) return 'Just now';
    if (diffInMinutes < 60) return `${diffInMinutes}m ago`;
    if (diffInMinutes < 1440) return `${Math.floor(diffInMinutes / 60)}h ago`;
    if (diffInMinutes < 10080) return `${Math.floor(diffInMinutes / 1440)}d ago`;
    
    return date.toLocaleDateString();
  };

  const exportActivityLog = () => {
    const csvContent = [
      ['Timestamp', 'Action', 'Resource Type', 'Resource ID', 'User ID', 'IP Address'].join(','),
      ...filteredActivities.map(activity => [
        activity.created_at,
        activity.action,
        activity.resource_type || '',
        activity.resource_id || '',
        activity.user_id || '',
        activity.ip_address || ''
      ].join(','))
    ].join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `activity-log-${new Date().toISOString().split('T')[0]}.csv`;
    a.click();
    window.URL.revokeObjectURL(url);
  };

  const uniqueActions = Array.from(new Set(activities.map(a => a.action).filter(Boolean)));
  const uniqueResources = Array.from(new Set(activities.map(a => a.resource_type).filter(Boolean)));

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white">Activity Log</h2>
          <p className="text-gray-600 dark:text-gray-400">Monitor all system activities and user actions</p>
        </div>
        
        <div className="flex items-center space-x-2">
          <Button variant="outline" onClick={loadActivityLog} disabled={loading}>
            <RefreshCw className={`w-4 h-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
          <Button variant="outline" onClick={exportActivityLog}>
            <Download className="w-4 h-4 mr-2" />
            Export
          </Button>
        </div>
      </div>

      {/* Filters */}
      <Card>
        <CardContent className="p-6">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <Input
                  placeholder="Search activities..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            
            <Select value={actionFilter} onValueChange={setActionFilter}>
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Filter by action" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Actions</SelectItem>
                {uniqueActions.map(action => (
                  <SelectItem key={action} value={action}>{action}</SelectItem>
                ))}
              </SelectContent>
            </Select>

            <Select value={resourceFilter} onValueChange={setResourceFilter}>
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Filter by resource" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Resources</SelectItem>
                {uniqueResources.map(resource => (
                  <SelectItem key={resource} value={resource}>{resource}</SelectItem>
                ))}
              </SelectContent>
            </Select>

            <Select value={dateFilter} onValueChange={setDateFilter}>
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Filter by date" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Time</SelectItem>
                <SelectItem value="today">Today</SelectItem>
                <SelectItem value="week">Last Week</SelectItem>
                <SelectItem value="month">Last Month</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Activity Table */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Activity className="w-5 h-5 mr-2" />
            Recent Activities ({filteredActivities.length})
          </CardTitle>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="space-y-4">
              {[...Array(10)].map((_, i) => (
                <div key={i} className="flex items-center space-x-4 p-4 animate-pulse">
                  <div className="w-8 h-8 bg-gray-200 rounded"></div>
                  <div className="flex-1 space-y-2">
                    <div className="h-4 bg-gray-200 rounded w-1/3"></div>
                    <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                  </div>
                  <div className="h-4 bg-gray-200 rounded w-20"></div>
                </div>
              ))}
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Activity</TableHead>
                  <TableHead>Action</TableHead>
                  <TableHead>Resource</TableHead>
                  <TableHead>User</TableHead>
                  <TableHead>Time</TableHead>
                  <TableHead>IP Address</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredActivities.map((activity) => (
                  <TableRow key={activity.id}>
                    <TableCell>
                      <div className="flex items-center space-x-3">
                        {getActivityIcon(activity.action, activity.resource_type)}
                        <div>
                          <p className="font-medium text-sm">{activity.action}</p>
                          {activity.resource_id && (
                            <p className="text-xs text-gray-500">ID: {activity.resource_id}</p>
                          )}
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>{getActionBadge(activity.action)}</TableCell>
                    <TableCell>
                      {activity.resource_type ? (
                        <Badge variant="outline" className="text-xs">
                          {activity.resource_type}
                        </Badge>
                      ) : (
                        '-'
                      )}
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center space-x-2">
                        <Avatar className="w-6 h-6">
                          <AvatarFallback className="text-xs bg-gray-100 dark:bg-gray-800">
                            {activity.user_id ? activity.user_id.substring(0, 2).toUpperCase() : 'SY'}
                          </AvatarFallback>
                        </Avatar>
                        <span className="text-sm">{activity.user_id || 'System'}</span>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center space-x-1">
                        <Clock className="w-3 h-3 text-gray-400" />
                        <span className="text-sm">{formatTimeAgo(activity.created_at)}</span>
                      </div>
                    </TableCell>
                    <TableCell className="text-sm text-gray-500">
                      {activity.ip_address || '-'}
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default ActivityLog;
