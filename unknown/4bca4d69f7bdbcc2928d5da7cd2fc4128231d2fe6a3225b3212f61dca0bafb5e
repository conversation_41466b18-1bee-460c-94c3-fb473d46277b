import React from 'react';
import { useRoleAccess, RolePermissions } from '@/hooks/useRoleAccess';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Shield, Lock } from 'lucide-react';

interface ProtectedComponentProps {
  children: React.ReactNode;
  permission?: keyof RolePermissions;
  resource?: string;
  roles?: string[];
  fallback?: React.ReactNode;
  showError?: boolean;
}

const ProtectedComponent: React.FC<ProtectedComponentProps> = ({
  children,
  permission,
  resource,
  roles,
  fallback,
  showError = true
}) => {
  const { userRole, hasPermission, canAccess } = useRoleAccess();

  // Check role-based access
  if (roles && !roles.includes(userRole)) {
    if (fallback) return <>{fallback}</>;
    if (!showError) return null;
    
    return (
      <Alert className="border-red-200 bg-red-50">
        <Shield className="h-4 w-4" />
        <AlertDescription>
          <div className="flex items-center gap-2">
            <Lock className="h-4 w-4" />
            <span>Access denied. Required roles: {roles.join(', ')}</span>
          </div>
        </AlertDescription>
      </Alert>
    );
  }

  // Check permission-based access
  if (permission && !hasPermission(permission)) {
    if (fallback) return <>{fallback}</>;
    if (!showError) return null;
    
    return (
      <Alert className="border-red-200 bg-red-50">
        <Shield className="h-4 w-4" />
        <AlertDescription>
          <div className="flex items-center gap-2">
            <Lock className="h-4 w-4" />
            <span>You don't have permission to access this feature.</span>
          </div>
        </AlertDescription>
      </Alert>
    );
  }

  // Check resource-based access
  if (resource && !canAccess(resource)) {
    if (fallback) return <>{fallback}</>;
    if (!showError) return null;
    
    return (
      <Alert className="border-red-200 bg-red-50">
        <Shield className="h-4 w-4" />
        <AlertDescription>
          <div className="flex items-center gap-2">
            <Lock className="h-4 w-4" />
            <span>You don't have access to {resource}.</span>
          </div>
        </AlertDescription>
      </Alert>
    );
  }

  // User has access, render children
  return <>{children}</>;
};

export default ProtectedComponent;
