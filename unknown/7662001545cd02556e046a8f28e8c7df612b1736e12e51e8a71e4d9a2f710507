-- Company Assets Management - Simple Setup
-- Run these commands one by one in your Supabase SQL Editor

-- Step 1: Create Asset Categories Table
CREATE TABLE IF NOT EXISTS public.asset_categories (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    name VARCHAR(100) NOT NULL UNIQUE,
    description TEXT,
    icon VARCHAR(50),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Step 2: Insert Default Categories
INSERT INTO public.asset_categories (name, description, icon) VALUES
('Heavy Equipment', 'Excavators, bulldozers, cranes, and other heavy machinery', 'Truck'),
('Vehicles', 'Company cars, trucks, vans, and transportation vehicles', 'Car'),
('Tools & Equipment', 'Hand tools, power tools, and small equipment', 'Wrench'),
('Technology', 'Computers, tablets, software licenses, and IT equipment', 'Laptop'),
('Property', 'Buildings, land, warehouses, and real estate', 'Building'),
('Safety Equipment', 'Safety gear, protective equipment, and safety systems', 'Shield'),
('Office Equipment', 'Furniture, printers, office supplies, and fixtures', 'Printer'),
('Machinery', 'Manufacturing equipment, generators, and industrial machines', 'Cog')
ON CONFLICT (name) DO NOTHING;

-- Step 3: Create Company Assets Table
CREATE TABLE IF NOT EXISTS public.company_assets (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    asset_number VARCHAR(50) UNIQUE NOT NULL,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    category_id UUID REFERENCES public.asset_categories(id) ON DELETE SET NULL,
    asset_type VARCHAR(50) NOT NULL CHECK (asset_type IN ('Equipment', 'Vehicle', 'Property', 'Tool', 'Machinery', 'Technology')),
    
    -- Financial Information
    purchase_price DECIMAL(15,2) NOT NULL DEFAULT 0,
    current_value DECIMAL(15,2) NOT NULL DEFAULT 0,
    depreciation_rate DECIMAL(5,2) DEFAULT 0,
    purchase_date DATE NOT NULL,
    warranty_expiry DATE,
    
    -- Asset Details
    brand VARCHAR(100),
    model VARCHAR(100),
    serial_number VARCHAR(100),
    condition VARCHAR(20) DEFAULT 'Good' CHECK (condition IN ('Excellent', 'Good', 'Fair', 'Poor', 'Needs Repair')),
    status VARCHAR(20) DEFAULT 'Active' CHECK (status IN ('Active', 'Inactive', 'Under Maintenance', 'Disposed', 'Lost', 'Sold')),
    
    -- Location and Assignment
    location VARCHAR(255),
    assigned_to VARCHAR(255),
    project_id UUID,
    
    -- Maintenance Information
    last_maintenance_date DATE,
    next_maintenance_date DATE,
    maintenance_interval_months INTEGER DEFAULT 12,
    maintenance_cost_ytd DECIMAL(12,2) DEFAULT 0,
    
    -- Insurance and Compliance
    insurance_policy VARCHAR(100),
    insurance_expiry DATE,
    compliance_certificates TEXT[],
    
    -- Additional Information
    notes TEXT,
    attachments JSONB,
    tags TEXT[],
    
    -- Audit Trail
    created_by VARCHAR(255),
    updated_by VARCHAR(255),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Step 4: Create Asset Maintenance Table
CREATE TABLE IF NOT EXISTS public.asset_maintenance (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    asset_id UUID NOT NULL REFERENCES public.company_assets(id) ON DELETE CASCADE,
    maintenance_type VARCHAR(50) NOT NULL CHECK (maintenance_type IN ('Preventive', 'Corrective', 'Emergency', 'Inspection', 'Upgrade')),
    description TEXT NOT NULL,
    
    -- Scheduling
    scheduled_date DATE,
    completed_date DATE,
    next_due_date DATE,
    
    -- Cost Information
    cost DECIMAL(12,2) DEFAULT 0,
    labor_hours DECIMAL(8,2) DEFAULT 0,
    parts_cost DECIMAL(12,2) DEFAULT 0,
    external_service_cost DECIMAL(12,2) DEFAULT 0,
    
    -- Service Provider
    service_provider VARCHAR(255),
    technician VARCHAR(255),
    
    -- Status and Priority
    status VARCHAR(20) DEFAULT 'Scheduled' CHECK (status IN ('Scheduled', 'In Progress', 'Completed', 'Cancelled', 'Overdue')),
    priority VARCHAR(20) DEFAULT 'Medium' CHECK (priority IN ('Low', 'Medium', 'High', 'Critical')),
    
    -- Documentation
    work_performed TEXT,
    parts_replaced TEXT[],
    recommendations TEXT,
    attachments JSONB,
    
    -- Audit Trail
    created_by VARCHAR(255),
    updated_by VARCHAR(255),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Step 5: Create Asset Depreciation Table
CREATE TABLE IF NOT EXISTS public.asset_depreciation (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    asset_id UUID NOT NULL REFERENCES public.company_assets(id) ON DELETE CASCADE,
    year INTEGER NOT NULL,
    opening_value DECIMAL(15,2) NOT NULL,
    depreciation_amount DECIMAL(15,2) NOT NULL,
    closing_value DECIMAL(15,2) NOT NULL,
    depreciation_method VARCHAR(50) DEFAULT 'Straight Line',
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Step 6: Create Asset Assignments Table
CREATE TABLE IF NOT EXISTS public.asset_assignments (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    asset_id UUID NOT NULL REFERENCES public.company_assets(id) ON DELETE CASCADE,
    assigned_from VARCHAR(255),
    assigned_to VARCHAR(255) NOT NULL,
    project_id UUID,
    location_from VARCHAR(255),
    location_to VARCHAR(255) NOT NULL,
    assignment_date DATE NOT NULL DEFAULT CURRENT_DATE,
    return_date DATE,
    purpose TEXT,
    condition_at_assignment VARCHAR(20),
    condition_at_return VARCHAR(20),
    notes TEXT,
    created_by VARCHAR(255),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Step 7: Create Indexes
CREATE INDEX IF NOT EXISTS idx_company_assets_category ON public.company_assets(category_id);
CREATE INDEX IF NOT EXISTS idx_company_assets_type ON public.company_assets(asset_type);
CREATE INDEX IF NOT EXISTS idx_company_assets_status ON public.company_assets(status);
CREATE INDEX IF NOT EXISTS idx_asset_maintenance_asset ON public.asset_maintenance(asset_id);
CREATE INDEX IF NOT EXISTS idx_asset_maintenance_status ON public.asset_maintenance(status);

-- Step 8: Enable Row Level Security
ALTER TABLE public.asset_categories ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.company_assets ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.asset_maintenance ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.asset_depreciation ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.asset_assignments ENABLE ROW LEVEL SECURITY;

-- Step 9: Create RLS Policies (Allow all operations)
CREATE POLICY "Allow all operations on asset_categories" ON public.asset_categories FOR ALL USING (true);
CREATE POLICY "Allow all operations on company_assets" ON public.company_assets FOR ALL USING (true);
CREATE POLICY "Allow all operations on asset_maintenance" ON public.asset_maintenance FOR ALL USING (true);
CREATE POLICY "Allow all operations on asset_depreciation" ON public.asset_depreciation FOR ALL USING (true);
CREATE POLICY "Allow all operations on asset_assignments" ON public.asset_assignments FOR ALL USING (true);

-- Step 10: Create update timestamp function and triggers
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_asset_categories_updated_at BEFORE UPDATE ON public.asset_categories FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_company_assets_updated_at BEFORE UPDATE ON public.company_assets FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_asset_maintenance_updated_at BEFORE UPDATE ON public.asset_maintenance FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
