-- Ensure User Roles Exist for Dropdown
-- Run this in Supabase SQL Editor if roles are not showing in the Create User dialog

-- Check if user_roles table exists
DO $$
BEGIN
    IF NOT EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'user_roles') THEN
        RAISE EXCEPTION 'user_roles table does not exist! Please run user_management_simple.sql first';
    END IF;
    RAISE NOTICE 'user_roles table exists';
END $$;

-- Check current roles
DO $$
DECLARE
    role_count INTEGER;
BEGIN
    SELECT COUNT(*) INTO role_count FROM public.user_roles;
    RAISE NOTICE 'Current roles in database: %', role_count;
    
    -- Show existing roles
    FOR role_record IN 
        SELECT role_name, role_display_name, is_active 
        FROM public.user_roles 
        ORDER BY role_name
    LOOP
        RAISE NOTICE 'Role: % (%) - Active: %', role_record.role_name, role_record.role_display_name, role_record.is_active;
    <PERSON><PERSON> LOOP;
END $$;

-- Insert default roles if they don't exist
INSERT INTO public.user_roles (role_name, role_display_name, description, permissions, is_active) VALUES
('admin', 'Administrator', 'Full system access with all administrative privileges', '{
    "users": {"create": true, "read": true, "update": true, "delete": true},
    "projects": {"create": true, "read": true, "update": true, "delete": true},
    "financials": {"create": true, "read": true, "update": true, "delete": true},
    "assets": {"create": true, "read": true, "update": true, "delete": true},
    "reports": {"create": true, "read": true, "update": true, "delete": true},
    "settings": {"create": true, "read": true, "update": true, "delete": true}
}', true),
('management', 'Management', 'Management level access to oversee projects and teams', '{
    "users": {"create": false, "read": true, "update": false, "delete": false},
    "projects": {"create": true, "read": true, "update": true, "delete": false},
    "financials": {"create": false, "read": true, "update": false, "delete": false},
    "assets": {"create": false, "read": true, "update": false, "delete": false},
    "reports": {"create": true, "read": true, "update": false, "delete": false},
    "settings": {"create": false, "read": true, "update": false, "delete": false}
}', true),
('accountant', 'Accountant', 'Financial management and accounting access', '{
    "users": {"create": false, "read": false, "update": false, "delete": false},
    "projects": {"create": false, "read": true, "update": false, "delete": false},
    "financials": {"create": true, "read": true, "update": true, "delete": false},
    "assets": {"create": false, "read": true, "update": false, "delete": false},
    "reports": {"create": true, "read": true, "update": false, "delete": false},
    "settings": {"create": false, "read": false, "update": false, "delete": false}
}', true),
('qs', 'Quantity Surveyor', 'Quantity surveying and cost estimation access', '{
    "users": {"create": false, "read": false, "update": false, "delete": false},
    "projects": {"create": true, "read": true, "update": true, "delete": false},
    "financials": {"create": false, "read": true, "update": false, "delete": false},
    "assets": {"create": false, "read": true, "update": false, "delete": false},
    "reports": {"create": true, "read": true, "update": false, "delete": false},
    "settings": {"create": false, "read": false, "update": false, "delete": false}
}', true),
('client', 'Client', 'Client access to view project progress and reports', '{
    "users": {"create": false, "read": false, "update": false, "delete": false},
    "projects": {"create": false, "read": true, "update": false, "delete": false},
    "financials": {"create": false, "read": false, "update": false, "delete": false},
    "assets": {"create": false, "read": false, "update": false, "delete": false},
    "reports": {"create": false, "read": true, "update": false, "delete": false},
    "settings": {"create": false, "read": false, "update": false, "delete": false}
}', true)
ON CONFLICT (role_name) DO UPDATE SET
    role_display_name = EXCLUDED.role_display_name,
    description = EXCLUDED.description,
    permissions = EXCLUDED.permissions,
    is_active = EXCLUDED.is_active,
    updated_at = NOW();

-- Verify roles were created/updated
DO $$
DECLARE
    role_count INTEGER;
BEGIN
    SELECT COUNT(*) INTO role_count FROM public.user_roles WHERE is_active = true;
    RAISE NOTICE '=== ROLES SETUP COMPLETE ===';
    RAISE NOTICE 'Active roles in database: %', role_count;
    
    -- Show all active roles
    FOR role_record IN 
        SELECT role_name, role_display_name 
        FROM public.user_roles 
        WHERE is_active = true
        ORDER BY role_name
    LOOP
        RAISE NOTICE '✓ %: %', role_record.role_name, role_record.role_display_name;
    END LOOP;
    
    RAISE NOTICE '';
    RAISE NOTICE 'The Create User dialog should now show all roles in the dropdown.';
    RAISE NOTICE 'If roles still don''t appear, check the browser console for errors.';
END $$;

-- Test query to verify roles can be selected
SELECT 
    role_name,
    role_display_name,
    is_active,
    created_at
FROM public.user_roles 
WHERE is_active = true
ORDER BY role_display_name;
