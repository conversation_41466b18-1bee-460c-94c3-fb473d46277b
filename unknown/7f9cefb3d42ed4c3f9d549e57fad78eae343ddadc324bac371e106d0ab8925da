// Direct Supabase auth test - bypass our app logic
import { createClient } from '@supabase/supabase-js';

// Create a fresh Supabase client for testing
const supabaseUrl = import.meta.env.VITE_SUPABASE_URL;
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY;

const testClient = createClient(supabaseUrl, supabaseAnonKey);

export class DirectAuthTester {
  
  // Test direct Supabase auth without any app interference
  static async testDirectLogin(email: string, password: string): Promise<{
    success: boolean;
    error?: string;
    user?: any;
    session?: any;
    details?: any;
  }> {
    try {
      console.log('🔍 Testing direct Supabase auth for:', email);
      
      // Clear any existing session first
      await testClient.auth.signOut();
      
      // Attempt login
      const { data, error } = await testClient.auth.signInWithPassword({
        email,
        password
      });
      
      if (error) {
        console.log('❌ Direct auth failed:', error.message);
        return {
          success: false,
          error: error.message,
          details: {
            code: error.status,
            name: error.name,
            stack: error.stack
          }
        };
      }
      
      console.log('✅ Direct auth successful!');
      console.log('User:', data.user?.email);
      console.log('Session:', !!data.session);
      
      // Clean up - sign out
      await testClient.auth.signOut();
      
      return {
        success: true,
        user: data.user,
        session: data.session
      };
      
    } catch (error: any) {
      console.error('Direct auth test error:', error);
      return {
        success: false,
        error: error.message || 'Unknown error'
      };
    }
  }
  
  // Test with multiple passwords
  static async testMultiplePasswords(email: string): Promise<{
    success: boolean;
    workingPassword?: string;
    results: Array<{ password: string; success: boolean; error?: string }>;
  }> {
    const passwords = [
      'TempPass123!',
      'password',
      'password123',
      'admin123',
      'temp123',
      '123456',
      'admin',
      'user123',
      'test123',
      'demo123'
    ];
    
    const results: Array<{ password: string; success: boolean; error?: string }> = [];
    let workingPassword: string | undefined;
    
    console.log(`🔍 Testing ${passwords.length} passwords for: ${email}`);
    
    for (const password of passwords) {
      console.log(`Testing password: ${password}`);
      
      const result = await this.testDirectLogin(email, password);
      
      results.push({
        password,
        success: result.success,
        error: result.error
      });
      
      if (result.success) {
        workingPassword = password;
        console.log(`✅ Found working password: ${password}`);
        break;
      }
      
      // Small delay to avoid rate limiting
      await new Promise(resolve => setTimeout(resolve, 1000));
    }
    
    return {
      success: !!workingPassword,
      workingPassword,
      results
    };
  }
  
  // Test Supabase configuration
  static async testSupabaseConfig(): Promise<{
    success: boolean;
    config: any;
    error?: string;
  }> {
    try {
      console.log('🔍 Testing Supabase configuration...');
      
      // Test basic connection
      const { data, error } = await testClient
        .from('user_profiles')
        .select('count', { count: 'exact', head: true });
      
      if (error) {
        return {
          success: false,
          config: {
            url: supabaseUrl,
            hasAnonKey: !!supabaseAnonKey,
            anonKeyLength: supabaseAnonKey?.length || 0
          },
          error: error.message
        };
      }
      
      return {
        success: true,
        config: {
          url: supabaseUrl,
          hasAnonKey: !!supabaseAnonKey,
          anonKeyLength: supabaseAnonKey?.length || 0,
          profileCount: data
        }
      };
      
    } catch (error: any) {
      return {
        success: false,
        config: {
          url: supabaseUrl,
          hasAnonKey: !!supabaseAnonKey,
          anonKeyLength: supabaseAnonKey?.length || 0
        },
        error: error.message
      };
    }
  }
  
  // Get current session info
  static async getCurrentSession(): Promise<{
    hasSession: boolean;
    user?: any;
    session?: any;
  }> {
    try {
      const { data: { session }, error } = await testClient.auth.getSession();
      
      return {
        hasSession: !!session,
        user: session?.user,
        session: session
      };
    } catch (error) {
      return {
        hasSession: false
      };
    }
  }
}
