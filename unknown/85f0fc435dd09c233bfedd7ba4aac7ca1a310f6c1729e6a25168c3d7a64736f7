-- Create clients table
CREATE TABLE IF NOT EXISTS public.clients (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    company_name VARCHAR(255),
    email VARCHAR(255),
    phone VARCHAR(50),
    address TEXT,
    city VARCHAR(100),
    state VARCHAR(50),
    zip_code VARCHAR(20),
    country VARCHAR(100) DEFAULT 'USA',
    contact_person VARCHAR(255),
    tax_id VARCHAR(50),
    payment_terms INTEGER DEFAULT 30,
    credit_limit DECIMAL(12,2),
    status VARCHAR(20) DEFAULT 'Active' CHECK (status IN ('Active', 'Inactive', 'Suspended')),
    client_type VARCHAR(20) DEFAULT 'Company' CHECK (client_type IN ('Individual', 'Company', 'Government', 'Non-Profit')),
    industry VARCHAR(100),
    website VARCHAR(255),
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    last_contact TIMESTAMP WITH TIME ZONE,
    preferred_communication VARCHAR(20) DEFAULT 'Email' CHECK (preferred_communication IN ('Email', 'Phone', 'SMS', 'Mail'))
);

-- Create client_payments table
CREATE TABLE IF NOT EXISTS public.client_payments (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    client_id UUID NOT NULL REFERENCES public.clients(id) ON DELETE CASCADE,
    invoice_id VARCHAR(100),
    project_id VARCHAR(100),
    amount DECIMAL(12,2) NOT NULL,
    payment_date DATE NOT NULL,
    payment_method VARCHAR(50) NOT NULL,
    reference_number VARCHAR(100),
    description TEXT,
    status VARCHAR(20) DEFAULT 'Completed' CHECK (status IN ('Pending', 'Completed', 'Failed', 'Cancelled')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    processed_by VARCHAR(255),
    transaction_fee DECIMAL(8,2) DEFAULT 0,
    currency VARCHAR(3) DEFAULT 'USD'
);

-- Create client_invoices table
CREATE TABLE IF NOT EXISTS public.client_invoices (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    client_id UUID NOT NULL REFERENCES public.clients(id) ON DELETE CASCADE,
    project_id VARCHAR(100),
    invoice_number VARCHAR(100) NOT NULL UNIQUE,
    issue_date DATE NOT NULL,
    due_date DATE NOT NULL,
    amount DECIMAL(12,2) NOT NULL,
    tax_amount DECIMAL(12,2) DEFAULT 0,
    discount_amount DECIMAL(12,2) DEFAULT 0,
    total_amount DECIMAL(12,2) NOT NULL,
    status VARCHAR(20) DEFAULT 'Draft' CHECK (status IN ('Draft', 'Sent', 'Paid', 'Overdue', 'Cancelled')),
    description TEXT,
    line_items JSONB,
    payment_terms INTEGER DEFAULT 30,
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    sent_date TIMESTAMP WITH TIME ZONE,
    paid_date TIMESTAMP WITH TIME ZONE,
    currency VARCHAR(3) DEFAULT 'USD'
);

-- Create client_contacts table
CREATE TABLE IF NOT EXISTS public.client_contacts (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    client_id UUID NOT NULL REFERENCES public.clients(id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    title VARCHAR(100),
    email VARCHAR(255),
    phone VARCHAR(50),
    is_primary BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create client_documents table
CREATE TABLE IF NOT EXISTS public.client_documents (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    client_id UUID NOT NULL REFERENCES public.clients(id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    type VARCHAR(50) NOT NULL,
    file_path VARCHAR(500),
    file_size INTEGER,
    mime_type VARCHAR(100),
    uploaded_by VARCHAR(255),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create client_activities table
CREATE TABLE IF NOT EXISTS public.client_activities (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    client_id UUID NOT NULL REFERENCES public.clients(id) ON DELETE CASCADE,
    activity_type VARCHAR(50) NOT NULL,
    description TEXT NOT NULL,
    performed_by VARCHAR(255),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_clients_email ON public.clients(email);
CREATE INDEX IF NOT EXISTS idx_clients_status ON public.clients(status);
CREATE INDEX IF NOT EXISTS idx_clients_client_type ON public.clients(client_type);
CREATE INDEX IF NOT EXISTS idx_clients_industry ON public.clients(industry);
CREATE INDEX IF NOT EXISTS idx_client_payments_client_id ON public.client_payments(client_id);
CREATE INDEX IF NOT EXISTS idx_client_payments_payment_date ON public.client_payments(payment_date);
CREATE INDEX IF NOT EXISTS idx_client_invoices_client_id ON public.client_invoices(client_id);
CREATE INDEX IF NOT EXISTS idx_client_invoices_status ON public.client_invoices(status);
CREATE INDEX IF NOT EXISTS idx_client_invoices_due_date ON public.client_invoices(due_date);

-- Enable Row Level Security (RLS)
ALTER TABLE public.clients ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.client_payments ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.client_invoices ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.client_contacts ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.client_documents ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.client_activities ENABLE ROW LEVEL SECURITY;

-- Create policies (allow all for now - adjust based on your auth requirements)
CREATE POLICY "Allow all operations on clients" ON public.clients FOR ALL USING (true);
CREATE POLICY "Allow all operations on client_payments" ON public.client_payments FOR ALL USING (true);
CREATE POLICY "Allow all operations on client_invoices" ON public.client_invoices FOR ALL USING (true);
CREATE POLICY "Allow all operations on client_contacts" ON public.client_contacts FOR ALL USING (true);
CREATE POLICY "Allow all operations on client_documents" ON public.client_documents FOR ALL USING (true);
CREATE POLICY "Allow all operations on client_activities" ON public.client_activities FOR ALL USING (true);

-- Insert sample data
INSERT INTO public.clients (
    name, company_name, email, phone, address, city, state, zip_code, 
    contact_person, payment_terms, credit_limit, status, client_type, 
    industry, website, notes, preferred_communication
) VALUES 
(
    'ABC Construction Corp',
    'ABC Construction Corp',
    '<EMAIL>',
    '+****************',
    '123 Business Ave',
    'New York',
    'NY',
    '10001',
    'John Smith',
    30,
    100000.00,
    'Active',
    'Company',
    'Construction',
    'https://abcconstruction.com',
    'Long-term client with excellent payment history',
    'Email'
),
(
    'Metro Development LLC',
    'Metro Development LLC',
    '<EMAIL>',
    '+****************',
    '456 Development Blvd',
    'Los Angeles',
    'CA',
    '90210',
    'Sarah Johnson',
    45,
    150000.00,
    'Active',
    'Company',
    'Real Estate',
    'https://metrodev.com',
    'High-value client, prefers detailed invoicing',
    'Phone'
),
(
    'City Infrastructure Dept',
    'City Infrastructure Department',
    '<EMAIL>',
    '+****************',
    '789 Government Plaza',
    'Chicago',
    'IL',
    '60601',
    'Michael Brown',
    60,
    500000.00,
    'Active',
    'Government',
    'Government',
    'https://cityinfra.gov',
    'Government contract work, requires detailed documentation',
    'Email'
);

-- Insert sample payments
INSERT INTO public.client_payments (
    client_id, invoice_id, project_id, amount, payment_date, payment_method,
    reference_number, description, status, processed_by, transaction_fee
) VALUES 
(
    (SELECT id FROM public.clients WHERE email = '<EMAIL>'),
    'INV-2024-001',
    '1',
    25000.00,
    '2024-01-15',
    'Bank Transfer',
    'TXN-123456',
    'Payment for Foundation Work - Phase 1',
    'Completed',
    'System',
    25.00
),
(
    (SELECT id FROM public.clients WHERE email = '<EMAIL>'),
    'INV-2024-002',
    '2',
    18500.00,
    '2024-01-14',
    'Check',
    'CHK-789012',
    'Payment for Electrical Installation',
    'Completed',
    'System',
    0.00
);

-- Insert sample invoices
INSERT INTO public.client_invoices (
    client_id, project_id, invoice_number, issue_date, due_date, amount,
    tax_amount, total_amount, status, description, payment_terms, notes
) VALUES 
(
    (SELECT id FROM public.clients WHERE email = '<EMAIL>'),
    '1',
    'INV-2024-001',
    '2024-01-01',
    '2024-01-31',
    25000.00,
    2500.00,
    27500.00,
    'Paid',
    'Foundation excavation and concrete work',
    30,
    'Payment due within 30 days'
),
(
    (SELECT id FROM public.clients WHERE email = '<EMAIL>'),
    '2',
    'INV-2024-002',
    '2024-01-05',
    '2024-02-19',
    32000.00,
    3200.00,
    35200.00,
    'Sent',
    'Electrical installation and wiring',
    45,
    'Payment due within 45 days'
);

-- Create function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers to automatically update updated_at
CREATE TRIGGER update_clients_updated_at BEFORE UPDATE ON public.clients FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_client_payments_updated_at BEFORE UPDATE ON public.client_payments FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_client_invoices_updated_at BEFORE UPDATE ON public.client_invoices FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_client_contacts_updated_at BEFORE UPDATE ON public.client_contacts FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_client_documents_updated_at BEFORE UPDATE ON public.client_documents FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
