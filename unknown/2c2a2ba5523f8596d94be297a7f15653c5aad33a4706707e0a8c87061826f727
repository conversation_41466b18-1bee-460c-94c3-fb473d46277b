import React, { useState, useCallback, useRef, useMemo, useEffect } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { <PERSON><PERSON>, DialogContent, <PERSON><PERSON>Header, <PERSON><PERSON>Title, DialogTrigger } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { useToast } from '@/hooks/use-toast';
import { 
  Plus, 
  ZoomIn, 
  ZoomOut, 
  RotateCcw, 
  Calendar, 
  Clock, 
  Users, 
  Filter,
  Eye,
  EyeOff,
  Edit,
  Trash2
} from 'lucide-react';

interface Project {
  id: string;
  name: string;
  status: string;
  start_date: string;
  end_date: string;
  budget: number;
  description?: string;
}

interface Task {
  id: string;
  name: string;
  description?: string;
  status: string;
  priority: string;
  start_date: string;
  end_date: string;
  progress: number;
  assignee?: string;
  project_id: string;
  estimated_hours?: number;
  actual_hours?: number;
  dependencies?: string[];
  tags?: string[];
  parent_task_id?: string;
}

interface GanttChartProps {
  projects: Project[];
  tasks: Task[];
  loading?: boolean;
  onTaskUpdate: (taskId: string, updates: Partial<Task>) => Promise<void>;
  onTaskCreate: (task: Omit<Task, 'id'>) => Promise<void>;
  onTaskDelete: (taskId: string) => Promise<void>;
}

export const GanttChart: React.FC<GanttChartProps> = ({
  projects,
  tasks,
  loading = false,
  onTaskUpdate,
  onTaskCreate,
  onTaskDelete
}) => {
  const { toast } = useToast();
  const ganttRef = useRef<HTMLDivElement>(null);

  // State management
  const [zoomLevel, setZoomLevel] = useState(1);
  const [viewMode, setViewMode] = useState<'days' | 'weeks' | 'months'>('days');
  const [filterProject, setFilterProject] = useState('all');
  const [filterStatus, setFilterStatus] = useState('all');
  const [showDependencies, setShowDependencies] = useState(true);
  const [isTaskDialogOpen, setIsTaskDialogOpen] = useState(false);
  const [editingTask, setEditingTask] = useState<Task | null>(null);
  const [isInitialRender, setIsInitialRender] = useState(true);

  // New task form state
  const [newTaskForm, setNewTaskForm] = useState({
    name: '',
    start_date: '',
    end_date: '',
    assignee: '',
    priority: 'Medium',
    status: 'Not Started',
    project_id: '',
    dependencies: [] as string[],
    description: '',
    estimated_hours: 0
  });

  // Calculate timeline bounds
  const { startDate, endDate } = useMemo(() => {
    if (tasks.length === 0) {
      const today = new Date();
      const start = new Date(today);
      start.setDate(today.getDate() - 30);
      const end = new Date(today);
      end.setDate(today.getDate() + 60);
      return { startDate: start, endDate: end };
    }

    const dates = tasks.flatMap(task => [new Date(task.start_date), new Date(task.end_date)]);
    const minDate = new Date(Math.min(...dates.map(d => d.getTime())));
    const maxDate = new Date(Math.max(...dates.map(d => d.getTime())));
    
    // Add padding
    minDate.setDate(minDate.getDate() - 7);
    maxDate.setDate(maxDate.getDate() + 7);
    
    return { startDate: minDate, endDate: maxDate };
  }, [tasks]);

  // Timeline calculation functions
  const dateToPixel = useCallback((date: Date, containerWidth: number) => {
    const totalDuration = endDate.getTime() - startDate.getTime();
    const dateOffset = date.getTime() - startDate.getTime();
    return (dateOffset / totalDuration) * containerWidth * zoomLevel;
  }, [startDate, endDate, zoomLevel]);

  // Container width state to avoid repeated getBoundingClientRect calls
  const [containerWidth, setContainerWidth] = useState(800);

  // Update container width on resize
  useEffect(() => {
    const updateContainerWidth = () => {
      if (ganttRef.current) {
        setContainerWidth(ganttRef.current.getBoundingClientRect().width - 300);
      }
    };

    updateContainerWidth();
    window.addEventListener('resize', updateContainerWidth);
    return () => window.removeEventListener('resize', updateContainerWidth);
  }, []);

  // Handle initial render optimization
  useEffect(() => {
    if (tasks.length > 0 && isInitialRender) {
      // Small delay to allow UI to render before heavy calculations
      const timer = setTimeout(() => {
        setIsInitialRender(false);
      }, 100);
      return () => clearTimeout(timer);
    }
  }, [tasks, isInitialRender]);

  // Get task position and dimensions (memoized)
  const getTaskPosition = useCallback((task: Task) => {
    const taskStart = new Date(task.start_date);
    const taskEnd = new Date(task.end_date);

    const left = dateToPixel(taskStart, containerWidth);
    const right = dateToPixel(taskEnd, containerWidth);
    const width = Math.max(10, right - left);

    return { left, width };
  }, [dateToPixel, containerWidth]);

  // Status and priority color functions
  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'completed':
        return 'bg-green-500';
      case 'in progress':
        return 'bg-blue-500';
      case 'on hold':
        return 'bg-yellow-500';
      case 'cancelled':
        return 'bg-red-500';
      default:
        return 'bg-gray-500';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority.toLowerCase()) {
      case 'critical':
        return 'border-red-500';
      case 'high':
        return 'border-orange-500';
      case 'medium':
        return 'border-yellow-500';
      case 'low':
        return 'border-green-500';
      default:
        return 'border-gray-500';
    }
  };

  // Format date for display (memoized)
  const formatDate = useCallback((date: Date) => {
    switch (viewMode) {
      case 'days':
        return date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
      case 'weeks':
        return `Week ${Math.ceil(date.getDate() / 7)}`;
      case 'months':
        return date.toLocaleDateString('en-US', { month: 'short', year: 'numeric' });
      default:
        return date.toLocaleDateString();
    }
  }, [viewMode]);

  // Zoom functions
  const handleZoomIn = useCallback(() => {
    setZoomLevel(prev => Math.min(prev * 1.5, 5));
  }, []);

  const handleZoomOut = useCallback(() => {
    setZoomLevel(prev => Math.max(prev / 1.5, 0.5));
  }, []);

  const handleResetZoom = useCallback(() => {
    setZoomLevel(1);
  }, []);

  // Filter tasks based on current filters (memoized for performance)
  const filteredTasks = useMemo(() => {
    return tasks.filter(task => {
      const projectMatch = filterProject === 'all' || task.project_id === filterProject;
      const statusMatch = filterStatus === 'all' || task.status === filterStatus;
      return projectMatch && statusMatch;
    });
  }, [tasks, filterProject, filterStatus]);

  // Memoize task positions to avoid recalculating on every render
  const taskPositions = useMemo(() => {
    const positions = new Map();
    filteredTasks.forEach(task => {
      positions.set(task.id, getTaskPosition(task));
    });
    return positions;
  }, [filteredTasks, getTaskPosition]);

  // Limit tasks for initial render performance
  const displayTasks = useMemo(() => {
    if (isInitialRender && filteredTasks.length > 20) {
      return filteredTasks.slice(0, 20);
    }
    return filteredTasks;
  }, [filteredTasks, isInitialRender]);

  // Generate timeline header (memoized for performance)
  const timelineHeaders = useMemo(() => {
    const headers = [];
    const current = new Date(startDate);

    while (current <= endDate) {
      const position = dateToPixel(current, containerWidth);
      headers.push(
        <div
          key={current.toISOString()}
          className="absolute top-0 h-full border-l border-gray-200 text-xs text-gray-600 pl-1"
          style={{ left: `${position}px` }}
        >
          {formatDate(current)}
        </div>
      );

      // Increment based on view mode
      switch (viewMode) {
        case 'days':
          current.setDate(current.getDate() + 1);
          break;
        case 'weeks':
          current.setDate(current.getDate() + 7);
          break;
        case 'months':
          current.setMonth(current.getMonth() + 1);
          break;
      }
    }

    return headers;
  }, [startDate, endDate, viewMode, containerWidth, dateToPixel, formatDate]);

  // Task creation and editing
  const handleCreateTask = async () => {
    if (!onTaskCreate) return;

    try {
      await onTaskCreate({
        ...newTaskForm,
        progress: 0,
        dependencies: newTaskForm.dependencies
      });

      setNewTaskForm({
        name: '',
        start_date: '',
        end_date: '',
        assignee: '',
        priority: 'Medium',
        status: 'Not Started',
        project_id: '',
        dependencies: [],
        description: '',
        estimated_hours: 0
      });
      setIsTaskDialogOpen(false);

      toast({
        title: "Task Created",
        description: `${newTaskForm.name} has been created successfully.`,
      });
    } catch (error) {
      toast({
        title: "Creation Failed",
        description: "Failed to create task. Please try again.",
        variant: "destructive",
      });
    }
  };

  const handleEditTask = (task: Task) => {
    setEditingTask(task);
    setNewTaskForm({
      name: task.name,
      start_date: task.start_date,
      end_date: task.end_date,
      assignee: task.assignee || '',
      priority: task.priority,
      status: task.status,
      project_id: task.project_id,
      dependencies: task.dependencies || [],
      description: task.description || '',
      estimated_hours: task.estimated_hours || 0
    });
    setIsTaskDialogOpen(true);
  };

  const handleUpdateTask = async () => {
    if (!onTaskUpdate || !editingTask) return;

    try {
      await onTaskUpdate(editingTask.id, newTaskForm);
      setEditingTask(null);
      setIsTaskDialogOpen(false);

      toast({
        title: "Task Updated",
        description: `${newTaskForm.name} has been updated successfully.`,
      });
    } catch (error) {
      toast({
        title: "Update Failed",
        description: "Failed to update task. Please try again.",
        variant: "destructive",
      });
    }
  };

  const handleDeleteTask = async (taskId: string) => {
    if (!onTaskDelete) return;

    try {
      await onTaskDelete(taskId);
      toast({
        title: "Task Deleted",
        description: "Task has been deleted successfully.",
      });
    } catch (error) {
      toast({
        title: "Delete Failed",
        description: "Failed to delete task. Please try again.",
        variant: "destructive",
      });
    }
  };

  // Show loading state
  if (loading) {
    return (
      <div className="space-y-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Calendar className="h-5 w-5" />
              🚀 Real Interactive Gantt Chart
            </CardTitle>
            <p className="text-sm text-gray-600">Loading tasks and projects...</p>
          </CardHeader>
        </Card>
        <div className="flex justify-center items-center h-64">
          <div className="text-center">
            <div className="w-8 h-8 border-4 border-blue-600 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
            <p className="text-gray-600">Preparing your Gantt chart...</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Gantt Chart Header */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              <Calendar className="h-5 w-5" />
              🚀 Real Interactive Gantt Chart
            </CardTitle>
            <div className="flex items-center gap-2">
              <Badge variant="secondary" className="bg-green-100 text-green-800">
                ✅ Fully Functional
              </Badge>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          {/* Controls */}
          <div className="flex flex-wrap items-center gap-4 mb-6">
            {/* Add Task Button */}
            <Dialog open={isTaskDialogOpen} onOpenChange={setIsTaskDialogOpen}>
              <DialogTrigger asChild>
                <Button onClick={() => setEditingTask(null)}>
                  <Plus className="w-4 h-4 mr-2" />
                  Add Task
                </Button>
              </DialogTrigger>
              <DialogContent className="max-w-md">
                <DialogHeader>
                  <DialogTitle>
                    {editingTask ? 'Edit Task' : 'Create New Task'}
                  </DialogTitle>
                </DialogHeader>
                <div className="space-y-4">
                  <div>
                    <Label htmlFor="task-name">Task Name</Label>
                    <Input
                      id="task-name"
                      value={newTaskForm.name}
                      onChange={(e) => setNewTaskForm(prev => ({ ...prev, name: e.target.value }))}
                      placeholder="Enter task name"
                    />
                  </div>
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="start-date">Start Date</Label>
                      <Input
                        id="start-date"
                        type="date"
                        value={newTaskForm.start_date}
                        onChange={(e) => setNewTaskForm(prev => ({ ...prev, start_date: e.target.value }))}
                      />
                    </div>
                    <div>
                      <Label htmlFor="end-date">End Date</Label>
                      <Input
                        id="end-date"
                        type="date"
                        value={newTaskForm.end_date}
                        onChange={(e) => setNewTaskForm(prev => ({ ...prev, end_date: e.target.value }))}
                      />
                    </div>
                  </div>
                  <div>
                    <Label htmlFor="project">Project</Label>
                    <Select value={newTaskForm.project_id} onValueChange={(value) => setNewTaskForm(prev => ({ ...prev, project_id: value }))}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select project" />
                      </SelectTrigger>
                      <SelectContent>
                        {projects.map(project => (
                          <SelectItem key={project.id} value={project.id}>
                            {project.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="flex justify-end gap-2">
                    <Button variant="outline" onClick={() => setIsTaskDialogOpen(false)}>
                      Cancel
                    </Button>
                    <Button onClick={editingTask ? handleUpdateTask : handleCreateTask}>
                      {editingTask ? 'Update Task' : 'Create Task'}
                    </Button>
                  </div>
                </div>
              </DialogContent>
            </Dialog>

            {/* View Mode Selector */}
            <div className="flex items-center gap-2">
              <Label className="text-sm font-medium">View:</Label>
              <Select value={viewMode} onValueChange={(value: 'days' | 'weeks' | 'months') => setViewMode(value)}>
                <SelectTrigger className="w-24">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="days">Days</SelectItem>
                  <SelectItem value="weeks">Weeks</SelectItem>
                  <SelectItem value="months">Months</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Zoom Controls */}
            <div className="flex items-center gap-2">
              <Button variant="outline" size="sm" onClick={handleZoomOut}>
                <ZoomOut className="w-4 h-4" />
              </Button>
              <span className="text-sm font-medium min-w-[60px] text-center">
                {Math.round(zoomLevel * 100)}%
              </span>
              <Button variant="outline" size="sm" onClick={handleZoomIn}>
                <ZoomIn className="w-4 h-4" />
              </Button>
              <Button variant="outline" size="sm" onClick={handleResetZoom}>
                <RotateCcw className="w-4 h-4" />
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Gantt Chart Visualization */}
      <Card>
        <CardContent className="p-0">
          <div ref={ganttRef} className="relative overflow-x-auto">
            {/* Timeline Header */}
            <div className="sticky top-0 bg-white border-b border-gray-200 z-10">
              <div className="flex">
                <div className="w-80 p-4 border-r border-gray-200 bg-gray-50">
                  <h3 className="font-medium text-gray-900">Tasks</h3>
                </div>
                <div className="flex-1 relative h-12 bg-gray-50">
                  {timelineHeaders}
                </div>
              </div>
            </div>

            {/* Tasks and Timeline */}
            <div className="min-h-96">
              {displayTasks.length === 0 ? (
                <div className="flex items-center justify-center h-64 text-gray-500">
                  <div className="text-center">
                    <Calendar className="h-12 w-12 mx-auto mb-4 text-gray-400" />
                    <h3 className="text-lg font-medium mb-2">No Tasks Found</h3>
                    <p className="text-sm">Create your first task to see it on the Gantt chart</p>
                    <Button 
                      className="mt-4" 
                      onClick={() => setIsTaskDialogOpen(true)}
                    >
                      <Plus className="w-4 h-4 mr-2" />
                      Add First Task
                    </Button>
                  </div>
                </div>
              ) : (
                <div className="relative">
                  {displayTasks.map((task, index) => {
                    const position = taskPositions.get(task.id) || { left: 0, width: 0 };
                    const project = projects.find(p => p.id === task.project_id);
                    
                    return (
                      <div key={task.id} className="flex border-b border-gray-100 hover:bg-gray-50">
                        {/* Task Info Sidebar */}
                        <div className="w-80 p-4 border-r border-gray-200">
                          <div className="space-y-2">
                            <div className="flex items-center justify-between">
                              <h4 className="font-medium text-gray-900 truncate">{task.name}</h4>
                              <div className="flex items-center gap-1">
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => handleEditTask(task)}
                                  className="h-6 w-6 p-0"
                                >
                                  <Edit className="h-3 w-3" />
                                </Button>
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => handleDeleteTask(task.id)}
                                  className="h-6 w-6 p-0 text-red-600 hover:text-red-700"
                                >
                                  <Trash2 className="h-3 w-3" />
                                </Button>
                              </div>
                            </div>
                            <div className="flex items-center gap-2 text-xs text-gray-600">
                              <Badge variant="outline" className={getPriorityColor(task.priority)}>
                                {task.priority}
                              </Badge>
                              <Badge variant="secondary" className={getStatusColor(task.status)}>
                                {task.status}
                              </Badge>
                            </div>
                            {project && (
                              <p className="text-xs text-gray-500 truncate">{project.name}</p>
                            )}
                            {task.assignee && (
                              <div className="flex items-center gap-1 text-xs text-gray-600">
                                <Users className="h-3 w-3" />
                                {task.assignee}
                              </div>
                            )}
                            <div className="flex items-center gap-1 text-xs text-gray-600">
                              <Clock className="h-3 w-3" />
                              {task.start_date} - {task.end_date}
                            </div>
                          </div>
                        </div>

                        {/* Task Timeline Bar */}
                        <div className="flex-1 relative h-20 p-4">
                          <div
                            className={`absolute top-6 h-8 rounded cursor-move transition-all duration-200 hover:shadow-md ${getStatusColor(task.status)} ${getPriorityColor(task.priority)} border-2`}
                            style={{
                              left: `${position.left}px`,
                              width: `${position.width}px`,
                            }}
                            title={`${task.name} (${task.start_date} - ${task.end_date})`}
                          >
                            {/* Task Progress Bar */}
                            {task.progress > 0 && (
                              <div 
                                className="absolute top-0 left-0 h-full bg-white bg-opacity-30 rounded"
                                style={{ width: `${task.progress}%` }}
                              />
                            )}
                            
                            {/* Task Name (if width allows) */}
                            {position.width > 100 && (
                              <div className="absolute inset-0 flex items-center px-2">
                                <span className="text-xs font-medium text-white truncate">
                                  {task.name}
                                </span>
                              </div>
                            )}
                          </div>
                        </div>
                      </div>
                    );
                  })}

                  {/* Show More Button */}
                  {isInitialRender && filteredTasks.length > 20 && (
                    <div className="flex justify-center py-4">
                      <Button
                        variant="outline"
                        onClick={() => setIsInitialRender(false)}
                        className="text-sm"
                      >
                        Show All {filteredTasks.length} Tasks
                      </Button>
                    </div>
                  )}
                </div>
              )}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Chart Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
              <span className="text-sm font-medium">Total Tasks</span>
            </div>
            <p className="text-2xl font-bold mt-1">{filteredTasks.length}</p>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <div className="w-2 h-2 bg-green-500 rounded-full"></div>
              <span className="text-sm font-medium">Completed</span>
            </div>
            <p className="text-2xl font-bold mt-1">
              {filteredTasks.filter(t => t.status === 'Completed').length}
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <div className="w-2 h-2 bg-yellow-500 rounded-full"></div>
              <span className="text-sm font-medium">In Progress</span>
            </div>
            <p className="text-2xl font-bold mt-1">
              {filteredTasks.filter(t => t.status === 'In Progress').length}
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <div className="w-2 h-2 bg-red-500 rounded-full"></div>
              <span className="text-sm font-medium">Overdue</span>
            </div>
            <p className="text-2xl font-bold mt-1">
              {filteredTasks.filter(t => new Date(t.end_date) < new Date() && t.status !== 'Completed').length}
            </p>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};
