import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  BookOpen,
  MessageCircle,
  Phone,
  Mail,
  FileText,
  Video,
  ExternalLink,
  Search,
  ChevronRight,
  Clock,
  Users,
  Star,
  Download,
  PlayCircle,
  HelpCircle,
  Zap,
  Shield,
  Settings,
  CreditCard,
  Building2
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { useToast } from '@/components/ui/use-toast';

interface HelpCategory {
  id: string;
  title: string;
  description: string;
  icon: React.ReactNode;
  articles: number;
  popular?: boolean;
}

interface QuickAction {
  id: string;
  title: string;
  description: string;
  icon: React.ReactNode;
  action: () => void;
  external?: boolean;
}

const HelpSupport: React.FC = () => {
  const navigate = useNavigate();
  const { toast } = useToast();
  const [searchQuery, setSearchQuery] = useState('');

  const helpCategories: HelpCategory[] = [
    {
      id: 'getting-started',
      title: 'Getting Started',
      description: 'Learn the basics of using Martcosy Construction Management',
      icon: <PlayCircle className="w-6 h-6" />,
      articles: 12,
      popular: true
    },
    {
      id: 'project-management',
      title: 'Project Management',
      description: 'Manage projects, tasks, and timelines effectively',
      icon: <Building2 className="w-6 h-6" />,
      articles: 18
    },
    {
      id: 'financial-management',
      title: 'Financial Management',
      description: 'Handle invoices, payments, and financial reporting',
      icon: <CreditCard className="w-6 h-6" />,
      articles: 15
    },
    {
      id: 'user-management',
      title: 'User Management',
      description: 'Manage team members, roles, and permissions',
      icon: <Users className="w-6 h-6" />,
      articles: 10
    },
    {
      id: 'security',
      title: 'Security & Privacy',
      description: 'Keep your data safe and secure',
      icon: <Shield className="w-6 h-6" />,
      articles: 8
    },
    {
      id: 'settings',
      title: 'Settings & Configuration',
      description: 'Customize your workspace and preferences',
      icon: <Settings className="w-6 h-6" />,
      articles: 14
    }
  ];

  const quickActions: QuickAction[] = [
    {
      id: 'live-chat',
      title: 'Live Chat Support',
      description: 'Get instant help from our support team',
      icon: <MessageCircle className="w-5 h-5" />,
      action: () => navigate('/contact-support')
    },
    {
      id: 'email-support',
      title: 'Email Support',
      description: 'Send us a detailed message',
      icon: <Mail className="w-5 h-5" />,
      action: () => navigate('/contact-support')
    },
    {
      id: 'phone-support',
      title: 'Phone Support',
      description: 'Call us for immediate assistance',
      icon: <Phone className="w-5 h-5" />,
      action: () => window.location.href = 'tel:+263-4-123-4567'
    },
    {
      id: 'schedule-demo',
      title: 'Schedule a Demo',
      description: 'Book a personalized walkthrough',
      icon: <Clock className="w-5 h-5" />,
      action: () => window.open('https://calendly.com/martcosy-demo', '_blank'),
      external: true
    }
  ];

  const popularArticles = [
    {
      title: 'How to Create Your First Project',
      category: 'Getting Started',
      readTime: '5 min read',
      rating: 4.8
    },
    {
      title: 'Setting Up User Roles and Permissions',
      category: 'User Management',
      readTime: '8 min read',
      rating: 4.9
    },
    {
      title: 'Creating and Managing Invoices',
      category: 'Financial Management',
      readTime: '6 min read',
      rating: 4.7
    },
    {
      title: 'Project Timeline and Gantt Charts',
      category: 'Project Management',
      readTime: '10 min read',
      rating: 4.8
    }
  ];

  const handleSearch = () => {
    if (searchQuery.trim()) {
      window.open(`https://help.martcosy.com/search?q=${encodeURIComponent(searchQuery)}`, '_blank');
    } else {
      toast({
        title: "Search Query Required",
        description: "Please enter a search term to find help articles.",
        variant: "destructive"
      });
    }
  };

  const handleCategoryClick = (categoryId: string) => {
    // Navigate to documentation with category filter
    navigate(`/documentation?category=${categoryId}`);
  };

  return (
    <div className="space-y-8 p-6 max-w-7xl mx-auto">
      {/* Header Section */}
      <div className="text-center space-y-4">
        <div className="flex items-center justify-center space-x-3">
          <div className="w-12 h-12 rounded-xl bg-blue-50 dark:bg-blue-900/20 flex items-center justify-center">
            <HelpCircle className="w-6 h-6 text-blue-600 dark:text-blue-400" />
          </div>
          <div>
            <h1 className="text-3xl font-bold text-gray-900 dark:text-white">Help & Support</h1>
            <p className="text-gray-600 dark:text-gray-400">Find answers and get the help you need</p>
          </div>
        </div>

        {/* Search Bar */}
        <div className="max-w-2xl mx-auto">
          <div className="relative">
            <Search className="absolute left-3 top-3 h-5 w-5 text-gray-400" />
            <Input
              placeholder="Search for help articles, tutorials, or guides..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
              className="pl-10 pr-20 py-3 text-lg"
            />
            <Button
              onClick={handleSearch}
              className="absolute right-2 top-2"
              size="sm"
            >
              Search
            </Button>
          </div>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {quickActions.map((action) => (
          <Card key={action.id} className="cursor-pointer hover:shadow-lg transition-shadow" onClick={action.action}>
            <CardContent className="p-6 text-center space-y-3">
              <div className="w-12 h-12 rounded-xl bg-orange-50 dark:bg-orange-900/20 flex items-center justify-center mx-auto">
                {action.icon}
              </div>
              <div>
                <h3 className="font-semibold text-gray-900 dark:text-white flex items-center justify-center gap-2">
                  {action.title}
                  {action.external && <ExternalLink className="w-3 h-3" />}
                </h3>
                <p className="text-sm text-gray-600 dark:text-gray-400">{action.description}</p>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Help Categories */}
        <div className="lg:col-span-2 space-y-6">
          <div>
            <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">Browse by Category</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {helpCategories.map((category) => (
                <Card 
                  key={category.id} 
                  className="cursor-pointer hover:shadow-lg transition-shadow"
                  onClick={() => handleCategoryClick(category.id)}
                >
                  <CardContent className="p-6">
                    <div className="flex items-start space-x-4">
                      <div className="w-12 h-12 rounded-xl bg-sky-50 dark:bg-sky-900/20 flex items-center justify-center flex-shrink-0">
                        {category.icon}
                      </div>
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center gap-2 mb-2">
                          <h3 className="font-semibold text-gray-900 dark:text-white">{category.title}</h3>
                          {category.popular && (
                            <Badge variant="secondary" className="text-xs">Popular</Badge>
                          )}
                        </div>
                        <p className="text-sm text-gray-600 dark:text-gray-400 mb-3">{category.description}</p>
                        <div className="flex items-center justify-between">
                          <span className="text-xs text-gray-500">{category.articles} articles</span>
                          <ChevronRight className="w-4 h-4 text-gray-400" />
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Popular Articles */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Star className="w-5 h-5 text-yellow-500" />
                Popular Articles
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {popularArticles.map((article, index) => (
                <div key={index} className="space-y-2">
                  <h4 className="font-medium text-sm text-gray-900 dark:text-white hover:text-blue-600 cursor-pointer">
                    {article.title}
                  </h4>
                  <div className="flex items-center justify-between text-xs text-gray-500">
                    <span>{article.category}</span>
                    <span>{article.readTime}</span>
                  </div>
                  <div className="flex items-center gap-1">
                    <Star className="w-3 h-3 fill-yellow-400 text-yellow-400" />
                    <span className="text-xs text-gray-600">{article.rating}</span>
                  </div>
                  {index < popularArticles.length - 1 && <Separator />}
                </div>
              ))}
            </CardContent>
          </Card>

          {/* Quick Links */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Zap className="w-5 h-5 text-orange-500" />
                Quick Links
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <Button
                variant="outline"
                className="w-full justify-start"
                onClick={() => navigate('/documentation')}
              >
                <BookOpen className="w-4 h-4 mr-2" />
                Full Documentation
              </Button>
              <Button
                variant="outline"
                className="w-full justify-start"
                onClick={() => navigate('/video-tutorials')}
              >
                <Video className="w-4 h-4 mr-2" />
                Video Tutorials
              </Button>
              <Button
                variant="outline"
                className="w-full justify-start"
                onClick={() => navigate('/faq')}
              >
                <FileText className="w-4 h-4 mr-2" />
                FAQ
              </Button>
            </CardContent>
          </Card>

          {/* Contact Info */}
          <Card>
            <CardHeader>
              <CardTitle>Contact Information</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="flex items-center gap-3">
                <Mail className="w-4 h-4 text-gray-500" />
                <span className="text-sm"><EMAIL></span>
              </div>
              <div className="flex items-center gap-3">
                <Phone className="w-4 h-4 text-gray-500" />
                <span className="text-sm">+263 4 123 4567</span>
              </div>
              <div className="flex items-center gap-3">
                <Clock className="w-4 h-4 text-gray-500" />
                <span className="text-sm">Mon-Fri, 8AM-6PM CAT</span>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default HelpSupport;
