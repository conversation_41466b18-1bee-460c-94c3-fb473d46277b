# User Management & Authentication System Setup Guide

## Quick Setup Instructions

### Step 1: Create User Management Database Schema
```sql
-- Copy and paste the content from: database/user_management_schema.sql
-- Run in Supabase SQL Editor
```

### Step 2: Configure Supabase Authentication
1. Go to your Supabase project dashboard
2. Navigate to Authentication → Settings
3. Enable email confirmations (optional)
4. Configure email templates (optional)

### Step 3: Test the System
1. Navigate to `/register` to create accounts
2. Navigate to `/login` to sign in
3. Navigate to `/profile` to manage profiles

## What Gets Created

### Database Tables:
1. **`user_roles`** - Defines available roles and permissions
2. **`user_profiles`** - Extended user profile information
3. **`user_sessions`** - Track active user sessions
4. **`audit_logs`** - Audit trail of user actions
5. **`password_reset_tokens`** - Password reset management

### Default Roles Created:
- **Administrator** - Full system access with all privileges
- **Quantity Surveyor** - Cost management and quantity surveying
- **Accountant** - Financial management and accounting
- **Management** - Project and team management oversight
- **Client** - Limited project visibility and communication

### Role Permissions:
Each role has specific permissions for different system areas:
- **Users**: create, read, update, delete user accounts
- **Projects**: manage construction projects
- **Financials**: handle financial data and invoicing
- **Assets**: manage company assets and equipment
- **Messages**: team communication access
- **Reports**: generate and view reports

## Features Implemented

### Authentication System:
- **User Registration** - Create accounts with role selection
- **User Login** - Sign in with email/password and role verification
- **Password Security** - Secure password hashing and validation
- **Email Verification** - Optional email confirmation
- **Password Reset** - Secure password reset functionality

### Role-Based Access Control:
- **5 Predefined Roles** - Admin, QS, Accountant, Management, Client
- **Granular Permissions** - Specific permissions per system area
- **Permission Checking** - Verify user permissions before actions
- **Role Management** - Admins can manage user roles

### Profile Management:
- **Complete Profiles** - Personal, professional, and contact info
- **Profile Editing** - Users can update their information
- **Role Display** - Visual role indicators and descriptions
- **Activity Tracking** - Track user actions and login history

### Admin Features:
- **User Management** - Create, update, delete user accounts
- **Role Assignment** - Assign and change user roles
- **Account Activation** - Enable/disable user accounts
- **Audit Logs** - View all user actions and changes

### Security Features:
- **Row Level Security** - Database-level access control
- **Session Management** - Track and manage user sessions
- **Audit Logging** - Complete audit trail of actions
- **Permission Validation** - Server-side permission checking

## User Interface

### Login Page (`/login`):
- **Professional Design** - Clean, modern login interface
- **Role Selection** - Optional role-based login
- **Password Visibility** - Toggle password visibility
- **Role Cards** - Visual role selection with descriptions
- **Responsive Design** - Works on all devices

### Registration Page (`/register`):
- **Comprehensive Form** - All required user information
- **Role Selection** - Choose role during registration
- **Password Confirmation** - Ensure password accuracy
- **Professional Info** - Job title and department fields
- **Validation** - Client-side and server-side validation

### Profile Page (`/profile`):
- **Tabbed Interface** - Profile, Security, Activity, Preferences
- **Edit Mode** - In-place editing with save/cancel
- **Role Display** - Current role with permissions
- **Activity History** - Recent user actions
- **Security Settings** - Password and 2FA management

## Role Descriptions & Permissions

### 👑 Administrator
- **Full System Access** - All features and data
- **User Management** - Create, edit, delete accounts
- **System Settings** - Configure system preferences
- **Audit Access** - View all system logs
- **Override Permissions** - Access any data or feature

### 📊 Quantity Surveyor (QS)
- **Cost Management** - Estimates, budgets, cost analysis
- **Project Access** - Create and manage projects
- **Financial Data** - View and edit financial information
- **Reports** - Generate cost and project reports
- **Asset Management** - View and update asset information

### 💰 Accountant
- **Financial Management** - Complete financial control
- **Invoicing** - Create, edit, delete invoices
- **Payment Tracking** - Manage payments and receipts
- **Financial Reports** - Generate financial statements
- **Asset Values** - Update asset financial information

### 👥 Management
- **Team Oversight** - Manage team members and assignments
- **Project Management** - Full project control and oversight
- **Reports Access** - View all project and team reports
- **Communication** - Manage team communications
- **Asset Management** - Assign and track company assets

### 🤝 Client
- **Project Visibility** - View assigned project information
- **Communication** - Participate in project discussions
- **Invoice Access** - View invoices and payment status
- **Report Viewing** - Access project progress reports
- **Limited Access** - Read-only access to most features

## Security Implementation

### Database Security:
- **Row Level Security** - Users can only access their data
- **Role-Based Policies** - Different access levels per role
- **Audit Triggers** - Automatic logging of data changes
- **Secure Functions** - Server-side permission validation

### Application Security:
- **Permission Checking** - Verify permissions before actions
- **Session Validation** - Validate user sessions
- **Input Sanitization** - Prevent injection attacks
- **Error Handling** - Secure error messages

### Authentication Security:
- **Password Hashing** - Secure password storage
- **Session Tokens** - Secure session management
- **Email Verification** - Optional email confirmation
- **Password Reset** - Secure reset process

## Usage Instructions

### For Administrators:
1. **Create Admin Account** - Register with admin role
2. **Set Up Users** - Create accounts for team members
3. **Assign Roles** - Set appropriate roles for each user
4. **Monitor Activity** - Review audit logs regularly

### For Users:
1. **Register Account** - Create account with appropriate role
2. **Complete Profile** - Fill in all profile information
3. **Set Preferences** - Configure notification and display settings
4. **Use System** - Access features based on your role

### For Clients:
1. **Receive Invitation** - Admin creates client account
2. **Set Password** - Complete account setup
3. **View Projects** - Access assigned project information
4. **Communicate** - Participate in project discussions

## Troubleshooting

### If registration fails:
1. Check database schema is created
2. Verify Supabase authentication is enabled
3. Check email format and password requirements

### If login fails:
1. Verify email and password are correct
2. Check if account is activated
3. Ensure role permissions are set correctly

### If permissions are denied:
1. Check user role assignment
2. Verify role permissions in database
3. Contact administrator for role updates

## Next Steps

After setup:
1. **Create Admin Account** - Register first admin user
2. **Test All Roles** - Create test accounts for each role
3. **Configure Permissions** - Adjust role permissions as needed
4. **Train Users** - Provide training on role-specific features
5. **Monitor Usage** - Review audit logs and user activity
