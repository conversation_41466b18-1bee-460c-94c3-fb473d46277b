-- Diagnose Supabase Configuration Issues
-- This checks for deeper configuration problems causing "Database error querying schema"

-- 1. Check auth schema permissions and ownership
DO $$
DECLARE
    schema_info RECORD;
    table_info RECORD;
BEGIN
    RAISE NOTICE '';
    RAISE NOTICE '=== SCHEMA PERMISSIONS DIAGNOSTIC ===';
    
    -- Check auth schema ownership
    SELECT schema_owner 
    INTO schema_info
    FROM information_schema.schemata 
    WHERE schema_name = 'auth';
    
    IF schema_info.schema_owner IS NOT NULL THEN
        RAISE NOTICE '✅ Auth schema owner: %', schema_info.schema_owner;
    ELSE
        RAISE NOTICE '❌ Auth schema not found or no owner';
    END IF;
    
    -- Check auth.users table permissions
    FOR table_info IN
        SELECT grantee, privilege_type, is_grantable
        FROM information_schema.table_privileges
        WHERE table_schema = 'auth' AND table_name = 'users'
        ORDER BY grantee, privilege_type
    LOOP
        RAISE NOTICE 'Auth.users permission: % has % (grantable: %)', 
            table_info.grantee, table_info.privilege_type, table_info.is_grantable;
    END LOOP;
    
    RAISE NOTICE '';
END $$;

-- 2. Check RLS policies on auth tables
DO $$
DECLARE
    policy_info RECORD;
    policy_count INTEGER := 0;
BEGIN
    RAISE NOTICE '=== RLS POLICIES ON AUTH TABLES ===';
    
    -- Check if RLS is enabled on auth.users
    SELECT relrowsecurity INTO policy_info
    FROM pg_class c
    JOIN pg_namespace n ON c.relnamespace = n.oid
    WHERE n.nspname = 'auth' AND c.relname = 'users';
    
    IF policy_info.relrowsecurity THEN
        RAISE NOTICE '⚠️ RLS is ENABLED on auth.users table';
        
        -- List RLS policies
        FOR policy_info IN
            SELECT schemaname, tablename, policyname, permissive, roles, cmd, qual, with_check
            FROM pg_policies
            WHERE schemaname = 'auth' AND tablename = 'users'
        LOOP
            policy_count := policy_count + 1;
            RAISE NOTICE 'Policy %: % (%) for roles: % | Command: %', 
                policy_count, policy_info.policyname, 
                CASE WHEN policy_info.permissive = 'PERMISSIVE' THEN 'ALLOW' ELSE 'DENY' END,
                policy_info.roles, policy_info.cmd;
        END LOOP;
        
        IF policy_count = 0 THEN
            RAISE NOTICE '❌ RLS enabled but NO policies found - this will block all access!';
        END IF;
    ELSE
        RAISE NOTICE '✅ RLS is DISABLED on auth.users table';
    END IF;
    
    RAISE NOTICE '';
END $$;

-- 3. Test direct auth table access
DO $$
DECLARE
    user_count INTEGER;
    access_error TEXT;
BEGIN
    RAISE NOTICE '=== TESTING DIRECT AUTH TABLE ACCESS ===';
    
    BEGIN
        SELECT COUNT(*) INTO user_count FROM auth.users;
        RAISE NOTICE '✅ Can access auth.users table - found % users', user_count;
    EXCEPTION
        WHEN OTHERS THEN
            access_error := SQLERRM;
            RAISE NOTICE '❌ Cannot access auth.users table: %', access_error;
    END;
    
    -- Test specific user access
    BEGIN
        PERFORM 1 FROM auth.users WHERE email = '<EMAIL>';
        RAISE NOTICE '✅ Can query specific user in auth.users';
    EXCEPTION
        WHEN OTHERS THEN
            access_error := SQLERRM;
            RAISE NOTICE '❌ Cannot query specific user: %', access_error;
    END;
    
    RAISE NOTICE '';
END $$;

-- 4. Check auth configuration tables
DO $$
DECLARE
    config_info RECORD;
    config_count INTEGER := 0;
BEGIN
    RAISE NOTICE '=== AUTH CONFIGURATION TABLES ===';
    
    -- Check if auth.config exists
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_schema = 'auth' AND table_name = 'config') THEN
        RAISE NOTICE '✅ auth.config table exists';
        
        BEGIN
            FOR config_info IN
                SELECT * FROM auth.config LIMIT 5
            LOOP
                config_count := config_count + 1;
                RAISE NOTICE 'Config entry %: %', config_count, config_info;
            END LOOP;
            
            IF config_count = 0 THEN
                RAISE NOTICE '⚠️ auth.config table is empty';
            END IF;
        EXCEPTION
            WHEN OTHERS THEN
                RAISE NOTICE '❌ Cannot read auth.config: %', SQLERRM;
        END;
    ELSE
        RAISE NOTICE '⚠️ auth.config table does not exist';
    END IF;
    
    RAISE NOTICE '';
END $$;

-- 5. Check current database user and roles
DO $$
DECLARE
    current_user_info RECORD;
    role_info RECORD;
BEGIN
    RAISE NOTICE '=== CURRENT DATABASE USER INFO ===';
    
    SELECT 
        current_user as current_user,
        session_user as session_user,
        current_database() as current_database,
        current_schema() as current_schema
    INTO current_user_info;
    
    RAISE NOTICE 'Current user: %', current_user_info.current_user;
    RAISE NOTICE 'Session user: %', current_user_info.session_user;
    RAISE NOTICE 'Current database: %', current_user_info.current_database;
    RAISE NOTICE 'Current schema: %', current_user_info.current_schema;
    
    -- Check user roles
    RAISE NOTICE '';
    RAISE NOTICE 'User roles:';
    FOR role_info IN
        SELECT rolname, rolsuper, rolcreaterole, rolcreatedb, rolcanlogin
        FROM pg_roles
        WHERE rolname = current_user
    LOOP
        RAISE NOTICE '  Role: % | Super: % | CreateRole: % | CreateDB: % | CanLogin: %',
            role_info.rolname, role_info.rolsuper, role_info.rolcreaterole,
            role_info.rolcreatedb, role_info.rolcanlogin;
    END LOOP;
    
    RAISE NOTICE '';
END $$;

-- 6. Final recommendations
DO $$
BEGIN
    RAISE NOTICE '=== RECOMMENDATIONS ===';
    RAISE NOTICE '';
    RAISE NOTICE 'If "Database error querying schema" persists after running this diagnostic:';
    RAISE NOTICE '';
    RAISE NOTICE '1. CHECK SUPABASE DASHBOARD:';
    RAISE NOTICE '   - Go to Authentication > Settings';
    RAISE NOTICE '   - Verify Auth is enabled';
    RAISE NOTICE '   - Check for any error messages';
    RAISE NOTICE '';
    RAISE NOTICE '2. CHECK PROJECT STATUS:';
    RAISE NOTICE '   - Ensure project is not paused';
    RAISE NOTICE '   - Verify database is healthy';
    RAISE NOTICE '   - Check for any ongoing maintenance';
    RAISE NOTICE '';
    RAISE NOTICE '3. RESET AUTH (LAST RESORT):';
    RAISE NOTICE '   - In Supabase Dashboard > Authentication > Settings';
    RAISE NOTICE '   - Consider resetting Auth configuration';
    RAISE NOTICE '   - This will require recreating all users';
    RAISE NOTICE '';
    RAISE NOTICE '4. CHECK ENVIRONMENT:';
    RAISE NOTICE '   - Verify VITE_SUPABASE_URL is correct';
    RAISE NOTICE '   - Verify VITE_SUPABASE_ANON_KEY is correct';
    RAISE NOTICE '   - Try creating a fresh Supabase client';
    RAISE NOTICE '';
END $$;
