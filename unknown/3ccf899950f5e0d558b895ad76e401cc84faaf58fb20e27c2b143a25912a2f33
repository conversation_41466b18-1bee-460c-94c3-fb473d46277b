# Supabase Setup Guide for Employee Salary Register

## 🚀 Quick Setup (5 minutes)

### Step 1: Create Supabase Account
1. Go to [supabase.com](https://supabase.com)
2. Click "Start your project"
3. Sign up with GitHub, Google, or email

### Step 2: Create New Project
1. Click "New Project"
2. Choose your organization
3. Enter project details:
   - **Name**: `construction-management`
   - **Database Password**: Generate a strong password
   - **Region**: Choose closest to your location
4. Click "Create new project"
5. Wait 2-3 minutes for setup to complete

### Step 3: Get Project Credentials
1. Go to **Settings** → **API**
2. Copy these values:
   - **Project URL**: `https://your-project-id.supabase.co`
   - **Anon public key**: `eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...`

### Step 4: Configure Environment Variables
1. In your project root, copy `.env.local.example` to `.env.local`
2. Replace the values:
```env
VITE_SUPABASE_URL=https://your-project-id.supabase.co
VITE_SUPABASE_ANON_KEY=your-anon-key-here
```

### Step 5: Create Database Table
1. Go to **SQL Editor** in Supabase dashboard
2. Click "New query"
3. Paste this SQL and click "Run":

```sql
-- Create employees table
CREATE TABLE employees (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  name VARCHAR(255) NOT NULL,
  position VARCHAR(100) NOT NULL,
  month VARCHAR(50) NOT NULL,
  days INTEGER NOT NULL CHECK (days > 0 AND days <= 31),
  rate_per_day DECIMAL(10,2) NOT NULL CHECK (rate_per_day > 0),
  credit DECIMAL(10,2) DEFAULT 0 CHECK (credit >= 0),
  total_salary DECIMAL(10,2) GENERATED ALWAYS AS (days * rate_per_day - credit) STORED,
  date_added TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  last_modified TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX idx_employees_position ON employees(position);
CREATE INDEX idx_employees_month ON employees(month);
CREATE INDEX idx_employees_name ON employees(name);
CREATE INDEX idx_employees_date_added ON employees(date_added);

-- Enable Row Level Security
ALTER TABLE employees ENABLE ROW LEVEL SECURITY;

-- Create policies for public access (adjust as needed)
CREATE POLICY "Enable read access for all users" ON employees
  FOR SELECT USING (true);

CREATE POLICY "Enable insert access for all users" ON employees
  FOR INSERT WITH CHECK (true);

CREATE POLICY "Enable update access for all users" ON employees
  FOR UPDATE USING (true);

CREATE POLICY "Enable delete access for all users" ON employees
  FOR DELETE USING (true);

-- Create trigger to update last_modified timestamp
CREATE OR REPLACE FUNCTION update_last_modified_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.last_modified = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_employees_last_modified 
    BEFORE UPDATE ON employees 
    FOR EACH ROW 
    EXECUTE FUNCTION update_last_modified_column();
```

### Step 6: Test the Connection
1. Restart your development server: `npm run dev`
2. Go to the Register page
3. Try adding a new employee
4. Check the Supabase dashboard → **Table Editor** → **employees** to see your data

## 🔧 Advanced Configuration

### Authentication (Optional)
If you want to add user authentication:

```sql
-- Update policies to require authentication
DROP POLICY "Enable read access for all users" ON employees;
DROP POLICY "Enable insert access for all users" ON employees;
DROP POLICY "Enable update access for all users" ON employees;
DROP POLICY "Enable delete access for all users" ON employees;

-- Create authenticated user policies
CREATE POLICY "Users can view all employees" ON employees
  FOR SELECT USING (auth.role() = 'authenticated');

CREATE POLICY "Users can insert employees" ON employees
  FOR INSERT WITH CHECK (auth.role() = 'authenticated');

CREATE POLICY "Users can update employees" ON employees
  FOR UPDATE USING (auth.role() = 'authenticated');

CREATE POLICY "Users can delete employees" ON employees
  FOR DELETE USING (auth.role() = 'authenticated');
```

### Real-time Subscriptions
Real-time updates are automatically enabled. Multiple users will see changes instantly.

### Backup and Recovery
Supabase automatically backs up your database. You can also:
1. Go to **Settings** → **Database**
2. Click "Download backup" for manual backups

## 🔍 Troubleshooting

### Common Issues:

**1. "Failed to fetch employees" error:**
- Check your `.env.local` file has correct values
- Verify your Supabase project is running
- Check browser console for detailed errors

**2. "Row Level Security" errors:**
- Make sure you ran the policy creation SQL
- Check if authentication is required

**3. Data not syncing:**
- Check your internet connection
- Verify Supabase project status
- Look for errors in browser console

**4. Migration not working:**
- Clear localStorage: `localStorage.clear()`
- Refresh the page
- Check if table exists in Supabase

### Getting Help:
- Check Supabase documentation: [supabase.com/docs](https://supabase.com/docs)
- Join Supabase Discord: [discord.supabase.com](https://discord.supabase.com)
- Check browser console for error messages

## 🎯 What You Get

✅ **Real-time Updates** - Multiple users see changes instantly
✅ **Offline Support** - Works without internet, syncs when back online  
✅ **Data Persistence** - Never lose your employee data
✅ **Automatic Backups** - Supabase handles backups automatically
✅ **Scalability** - Handles thousands of employees
✅ **Security** - Row-level security and data validation
✅ **Export/Import** - CSV export functionality
✅ **Search & Filter** - Advanced filtering capabilities

Your Employee Salary Register is now production-ready with enterprise-grade database integration!
