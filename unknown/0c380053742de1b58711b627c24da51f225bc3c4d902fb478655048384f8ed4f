import React, { useState, useEffect } from 'react';
import Layout from '@/components/layout/Layout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useToast } from '@/components/ui/use-toast';
import { useUserCreation } from '@/hooks/useUserCreation';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  UserPlus, 
  Mail, 
  User, 
  Phone, 
  Briefcase, 
  Building, 
  Key,
  Copy,
  Send,
  Loader2,
  CheckCircle,
  XCircle,
  Crown,
  Calculator,
  Users,
  UserCheck
} from 'lucide-react';

const AdminCreateUser = () => {
  const { toast } = useToast();
  const { loading, roles, loadRoles, createUser } = useUserCreation();
  const [result, setResult] = useState<any>(null);
  const [formData, setFormData] = useState({
    email: '',
    firstName: '',
    lastName: '',
    role: 'client',
    phone: '',
    department: '',
    jobTitle: '',
    password: 'TempPass123!'
  });

  // Load roles on component mount
  useEffect(() => {
    loadRoles();
  }, []);

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const generatePassword = () => {
    const password = 'Temp' + Math.floor(Math.random() * 9000 + 1000) + '!';
    setFormData(prev => ({ ...prev, password }));
  };

  const validateForm = () => {
    if (!formData.email || !formData.firstName || !formData.lastName || !formData.role) {
      toast({
        title: "Validation Error",
        description: "Please fill in all required fields.",
        variant: "destructive",
      });
      return false;
    }

    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(formData.email)) {
      toast({
        title: "Validation Error",
        description: "Please enter a valid email address.",
        variant: "destructive",
      });
      return false;
    }

    return true;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) return;

    setResult(null);
    
    // Use the working user creation logic from standalone register
    const createResult = await createUser({
      email: formData.email,
      firstName: formData.firstName,
      lastName: formData.lastName,
      role: formData.role,
      password: formData.password,
      phone: formData.phone,
      department: formData.department,
      jobTitle: formData.jobTitle
    });

    setResult(createResult);

    if (createResult.success) {
      toast({
        title: "User Created Successfully",
        description: `Account created for ${formData.firstName} ${formData.lastName}`,
      });

      // Reset form
      setFormData({
        email: '',
        firstName: '',
        lastName: '',
        role: 'client',
        phone: '',
        department: '',
        jobTitle: '',
        password: 'TempPass123!'
      });
    } else {
      toast({
        title: "Failed to Create User",
        description: createResult.error || "An error occurred while creating the user.",
        variant: "destructive",
      });
    }
  };

  const copyCredentials = () => {
    if (!result?.credentials) return;
    
    const credentials = `Login Credentials:
Email: ${result.credentials.email}
Password: ${result.credentials.password}
Role: ${formData.role}

Please save these credentials securely.`;
    
    navigator.clipboard.writeText(credentials);
    toast({
      title: "Credentials Copied",
      description: "Login credentials have been copied to clipboard.",
    });
  };

  const getRoleIcon = (roleName: string) => {
    switch (roleName) {
      case 'admin': return <Crown className="w-4 h-4" />;
      case 'qs': return <Calculator className="w-4 h-4" />;
      case 'accountant': return <Building className="w-4 h-4" />;
      case 'management': return <Users className="w-4 h-4" />;
      case 'client': return <UserCheck className="w-4 h-4" />;
      default: return <User className="w-4 h-4" />;
    }
  };

  return (
    <Layout>
      <div className="container mx-auto p-6 space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold">Create New User</h1>
            <p className="text-muted-foreground">
              Add a new user to the system using the proven working method
            </p>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* User Creation Form */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <UserPlus className="h-5 w-5" />
                User Information
              </CardTitle>
              <CardDescription>
                Enter the user's details to create their account (uses working standalone register logic)
              </CardDescription>
            </CardHeader>
            <CardContent>
              <form onSubmit={handleSubmit} className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="firstName">First Name *</Label>
                    <div className="relative">
                      <User className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                      <Input
                        id="firstName"
                        type="text"
                        placeholder="Enter first name"
                        value={formData.firstName}
                        onChange={(e) => handleInputChange('firstName', e.target.value)}
                        className="pl-10"
                        required
                      />
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="lastName">Last Name *</Label>
                    <div className="relative">
                      <User className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                      <Input
                        id="lastName"
                        type="text"
                        placeholder="Enter last name"
                        value={formData.lastName}
                        onChange={(e) => handleInputChange('lastName', e.target.value)}
                        className="pl-10"
                        required
                      />
                    </div>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="email">Email Address *</Label>
                  <div className="relative">
                    <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                    <Input
                      id="email"
                      type="email"
                      placeholder="Enter email address"
                      value={formData.email}
                      onChange={(e) => handleInputChange('email', e.target.value)}
                      className="pl-10"
                      required
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="role">Role *</Label>
                  <Select value={formData.role} onValueChange={(value) => handleInputChange('role', value)}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select a role" />
                    </SelectTrigger>
                    <SelectContent>
                      {roles.map((role) => (
                        <SelectItem key={role.id} value={role.role_name}>
                          <div className="flex items-center gap-2">
                            {getRoleIcon(role.role_name)}
                            <span>{role.role_name.charAt(0).toUpperCase() + role.role_name.slice(1)}</span>
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="password">Temporary Password</Label>
                  <div className="flex gap-2">
                    <div className="relative flex-1">
                      <Key className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                      <Input
                        id="password"
                        type="text"
                        placeholder="Temporary password"
                        value={formData.password}
                        onChange={(e) => handleInputChange('password', e.target.value)}
                        className="pl-10"
                      />
                    </div>
                    <Button type="button" variant="outline" onClick={generatePassword}>
                      Generate
                    </Button>
                  </div>
                  <p className="text-xs text-gray-500">
                    User will be able to change this password after first login
                  </p>
                </div>

                <Button type="submit" className="w-full" disabled={loading}>
                  {loading ? (
                    <>
                      <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                      Creating User...
                    </>
                  ) : (
                    <>
                      <UserPlus className="w-4 h-4 mr-2" />
                      Create User Account
                    </>
                  )}
                </Button>
              </form>
            </CardContent>
          </Card>

          {/* Result Display */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Send className="h-5 w-5" />
                Account Status
              </CardTitle>
              <CardDescription>
                User creation result and login credentials
              </CardDescription>
            </CardHeader>
            <CardContent>
              {result ? (
                <Alert className={result.success ? "border-green-200 bg-green-50" : "border-red-200 bg-red-50"}>
                  {result.success ? <CheckCircle className="h-4 w-4" /> : <XCircle className="h-4 w-4" />}
                  <AlertDescription>
                    <div className="space-y-3">
                      <div><strong>Result:</strong> {result.success ? 'SUCCESS' : 'FAILED'}</div>
                      
                      {result.step && <div><strong>Step:</strong> {result.step}</div>}
                      {result.method && <div><strong>Method:</strong> {result.method}</div>}
                      {result.error && <div><strong>Error:</strong> {result.error}</div>}
                      
                      {result.success && result.credentials && (
                        <div className="mt-4 p-4 bg-blue-50 border border-blue-200 rounded">
                          <div className="flex items-center justify-between mb-2">
                            <strong>🎉 User Created Successfully!</strong>
                            <Button size="sm" variant="outline" onClick={copyCredentials}>
                              <Copy className="w-3 h-3 mr-1" />
                              Copy
                            </Button>
                          </div>
                          <div className="space-y-1 text-sm">
                            <div><strong>Email:</strong> {result.credentials.email}</div>
                            <div><strong>Password:</strong> {result.credentials.password}</div>
                            <div><strong>Role:</strong> {formData.role}</div>
                          </div>
                          <div className="mt-2 text-xs text-blue-600">
                            Please share these credentials securely with the user.
                          </div>
                        </div>
                      )}
                    </div>
                  </AlertDescription>
                </Alert>
              ) : (
                <div className="text-center text-gray-500 py-8">
                  <UserPlus className="w-12 h-12 mx-auto mb-4 opacity-50" />
                  <p>Fill out the form and click "Create User Account" to get started.</p>
                  <p className="text-xs mt-2">Uses the proven working method from standalone register</p>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>
    </Layout>
  );
};

export default AdminCreateUser;
