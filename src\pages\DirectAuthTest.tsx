import React, { useState } from 'react';
import { createClient } from '@supabase/supabase-js';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { TestTube, AlertTriangle, CheckCircle, XCircle } from 'lucide-react';

const DirectAuthTest = () => {
  const [email, setEmail] = useState('<EMAIL>');
  const [password, setPassword] = useState('TempPass123!');
  const [loading, setLoading] = useState(false);
  const [result, setResult] = useState<any>(null);

  // Create a completely fresh Supabase client
  const supabaseUrl = import.meta.env.VITE_SUPABASE_URL;
  const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY;
  
  const testClient = createClient(supabaseUrl, supabaseAnonKey, {
    auth: {
      autoRefreshToken: false,
      persistSession: false,
      detectSessionInUrl: false
    }
  });

  const testDirectAuth = async () => {
    setLoading(true);
    setResult(null);

    try {
      console.log('🔍 Testing direct Supabase auth...');
      console.log('URL:', supabaseUrl);
      console.log('Key length:', supabaseAnonKey?.length);

      // Test 1: Basic connection
      console.log('Testing basic connection...');
      const { data: connectionTest, error: connectionError } = await testClient
        .from('user_profiles')
        .select('count', { count: 'exact', head: true });

      if (connectionError) {
        setResult({
          success: false,
          step: 'Connection Test',
          error: connectionError.message,
          details: connectionError
        });
        return;
      }

      console.log('✅ Basic connection successful');

      // Test 2: Direct auth attempt
      console.log('Testing direct auth...');
      const { data: authData, error: authError } = await testClient.auth.signInWithPassword({
        email,
        password
      });

      if (authError) {
        console.error('❌ Direct auth failed:', authError);
        setResult({
          success: false,
          step: 'Direct Auth Test',
          error: authError.message,
          details: {
            name: authError.name,
            status: authError.status,
            stack: authError.stack
          }
        });
        return;
      }

      console.log('✅ Direct auth successful!');
      
      // Clean up session
      await testClient.auth.signOut();

      setResult({
        success: true,
        step: 'Direct Auth Test',
        user: authData.user,
        session: !!authData.session
      });

    } catch (error: any) {
      console.error('❌ Test failed:', error);
      setResult({
        success: false,
        step: 'Test Execution',
        error: error.message,
        details: error
      });
    } finally {
      setLoading(false);
    }
  };

  const testEnvironment = () => {
    console.log('🔍 Environment Variables:');
    console.log('VITE_SUPABASE_URL:', import.meta.env.VITE_SUPABASE_URL);
    console.log('VITE_SUPABASE_ANON_KEY length:', import.meta.env.VITE_SUPABASE_ANON_KEY?.length);
    console.log('VITE_SUPABASE_ANON_KEY preview:', import.meta.env.VITE_SUPABASE_ANON_KEY?.substring(0, 20) + '...');
    
    setResult({
      success: true,
      step: 'Environment Check',
      environment: {
        url: import.meta.env.VITE_SUPABASE_URL,
        keyLength: import.meta.env.VITE_SUPABASE_ANON_KEY?.length,
        keyPreview: import.meta.env.VITE_SUPABASE_ANON_KEY?.substring(0, 20) + '...'
      }
    });
  };

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Direct Supabase Auth Test</h1>
          <p className="text-muted-foreground">
            Test Supabase Auth directly, bypassing our app logic
          </p>
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <TestTube className="h-5 w-5" />
            Direct Auth Test
          </CardTitle>
          <CardDescription>
            This creates a fresh Supabase client and tests auth directly
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="email">Email</Label>
              <Input
                id="email"
                type="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
              />
            </div>
            <div>
              <Label htmlFor="password">Password</Label>
              <Input
                id="password"
                type="password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
              />
            </div>
          </div>

          <div className="flex gap-2">
            <Button onClick={testDirectAuth} disabled={loading}>
              {loading ? 'Testing...' : 'Test Direct Auth'}
            </Button>
            <Button variant="outline" onClick={testEnvironment}>
              Check Environment
            </Button>
          </div>

          {result && (
            <Alert className={result.success ? "border-green-200 bg-green-50" : "border-red-200 bg-red-50"}>
              {result.success ? <CheckCircle className="h-4 w-4" /> : <XCircle className="h-4 w-4" />}
              <AlertDescription>
                <div className="space-y-2">
                  <div><strong>Step:</strong> {result.step}</div>
                  <div><strong>Result:</strong> {result.success ? 'SUCCESS' : 'FAILED'}</div>
                  
                  {result.error && (
                    <div><strong>Error:</strong> {result.error}</div>
                  )}
                  
                  {result.user && (
                    <div><strong>User:</strong> {result.user.email}</div>
                  )}
                  
                  {result.environment && (
                    <div>
                      <strong>Environment:</strong>
                      <ul className="mt-1 space-y-1 text-sm">
                        <li>URL: {result.environment.url}</li>
                        <li>Key Length: {result.environment.keyLength}</li>
                        <li>Key Preview: {result.environment.keyPreview}</li>
                      </ul>
                    </div>
                  )}
                  
                  {result.details && (
                    <details className="mt-2">
                      <summary className="cursor-pointer text-sm font-medium">Error Details</summary>
                      <pre className="mt-1 text-xs bg-gray-100 p-2 rounded overflow-auto">
                        {JSON.stringify(result.details, null, 2)}
                      </pre>
                    </details>
                  )}
                </div>
              </AlertDescription>
            </Alert>
          )}
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <AlertTriangle className="h-5 w-5" />
            Troubleshooting Info
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2 text-sm">
            <p><strong>Current Issue:</strong> "Database error querying schema" for all users</p>
            <p><strong>What this test does:</strong></p>
            <ul className="list-disc list-inside space-y-1 ml-4">
              <li>Creates a completely fresh Supabase client</li>
              <li>Tests basic database connection</li>
              <li>Tests auth service directly</li>
              <li>Bypasses all our app logic</li>
            </ul>
            <p><strong>If this test fails:</strong> The issue is with Supabase project configuration</p>
            <p><strong>If this test succeeds:</strong> The issue is with our app's auth implementation</p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default DirectAuthTest;
