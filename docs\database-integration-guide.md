# Database Integration Guide for Employee Salary Register

## Option 1: Supabase Integration (Recommended)

### Setup Steps:

1. **Install Supabase Client**
```bash
npm install @supabase/supabase-js
```

2. **Create Database Schema**
```sql
-- Create employees table
CREATE TABLE employees (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  name VARCHAR(255) NOT NULL,
  position VARCHAR(100) NOT NULL,
  month VARCHAR(50) NOT NULL,
  days INTEGER NOT NULL CHECK (days > 0 AND days <= 31),
  rate_per_day DECIMAL(10,2) NOT NULL CHECK (rate_per_day > 0),
  credit DECIMAL(10,2) DEFAULT 0 CHECK (credit >= 0),
  total_salary DECIMAL(10,2) GENERATED ALWAYS AS (days * rate_per_day - credit) STORED,
  date_added TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  last_modified TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create index for better performance
CREATE INDEX idx_employees_position ON employees(position);
CREATE INDEX idx_employees_month ON employees(month);
CREATE INDEX idx_employees_name ON employees(name);

-- Enable Row Level Security
ALTER TABLE employees ENABLE ROW LEVEL SECURITY;

-- Create policy for authenticated users
CREATE POLICY "Users can view all employees" ON employees
  FOR SELECT USING (auth.role() = 'authenticated');

CREATE POLICY "Users can insert employees" ON employees
  FOR INSERT WITH CHECK (auth.role() = 'authenticated');

CREATE POLICY "Users can update employees" ON employees
  FOR UPDATE USING (auth.role() = 'authenticated');

CREATE POLICY "Users can delete employees" ON employees
  FOR DELETE USING (auth.role() = 'authenticated');
```

3. **Environment Configuration**
```env
# .env.local
VITE_SUPABASE_URL=your_supabase_url
VITE_SUPABASE_ANON_KEY=your_supabase_anon_key
```

4. **Supabase Client Setup**
```typescript
// src/lib/supabase.ts
import { createClient } from '@supabase/supabase-js'

const supabaseUrl = import.meta.env.VITE_SUPABASE_URL
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY

export const supabase = createClient(supabaseUrl, supabaseAnonKey)

export type Employee = {
  id: string
  name: string
  position: string
  month: string
  days: number
  rate_per_day: number
  credit: number
  total_salary: number
  date_added: string
  last_modified: string
  created_at: string
}
```

## Option 2: Firebase Firestore

### Setup Steps:

1. **Install Firebase**
```bash
npm install firebase
```

2. **Firebase Configuration**
```typescript
// src/lib/firebase.ts
import { initializeApp } from 'firebase/app'
import { getFirestore } from 'firebase/firestore'
import { getAuth } from 'firebase/auth'

const firebaseConfig = {
  // Your Firebase config
}

const app = initializeApp(firebaseConfig)
export const db = getFirestore(app)
export const auth = getAuth(app)
```

3. **Firestore Rules**
```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    match /employees/{document} {
      allow read, write: if request.auth != null;
    }
  }
}
```

## Option 3: Traditional Backend (Node.js + PostgreSQL)

### Backend Setup:

1. **Express Server with PostgreSQL**
```bash
npm install express pg cors helmet bcryptjs jsonwebtoken
npm install -D @types/node @types/express @types/pg
```

2. **Database Schema (PostgreSQL)**
```sql
CREATE DATABASE construction_management;

CREATE TABLE employees (
  id SERIAL PRIMARY KEY,
  name VARCHAR(255) NOT NULL,
  position VARCHAR(100) NOT NULL,
  month VARCHAR(50) NOT NULL,
  days INTEGER NOT NULL CHECK (days > 0 AND days <= 31),
  rate_per_day DECIMAL(10,2) NOT NULL CHECK (rate_per_day > 0),
  credit DECIMAL(10,2) DEFAULT 0 CHECK (credit >= 0),
  total_salary DECIMAL(10,2) GENERATED ALWAYS AS (days * rate_per_day - credit) STORED,
  date_added TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  last_modified TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

3. **API Endpoints**
```typescript
// server/routes/employees.ts
import express from 'express'
import { Pool } from 'pg'

const router = express.Router()
const pool = new Pool({
  connectionString: process.env.DATABASE_URL
})

// GET /api/employees
router.get('/', async (req, res) => {
  try {
    const result = await pool.query('SELECT * FROM employees ORDER BY date_added DESC')
    res.json(result.rows)
  } catch (error) {
    res.status(500).json({ error: 'Failed to fetch employees' })
  }
})

// POST /api/employees
router.post('/', async (req, res) => {
  const { name, position, month, days, rate_per_day, credit } = req.body
  try {
    const result = await pool.query(
      'INSERT INTO employees (name, position, month, days, rate_per_day, credit) VALUES ($1, $2, $3, $4, $5, $6) RETURNING *',
      [name, position, month, days, rate_per_day, credit]
    )
    res.status(201).json(result.rows[0])
  } catch (error) {
    res.status(500).json({ error: 'Failed to create employee' })
  }
})

// PUT /api/employees/:id
router.put('/:id', async (req, res) => {
  const { id } = req.params
  const { name, position, month, days, rate_per_day, credit } = req.body
  try {
    const result = await pool.query(
      'UPDATE employees SET name = $1, position = $2, month = $3, days = $4, rate_per_day = $5, credit = $6, last_modified = CURRENT_TIMESTAMP WHERE id = $7 RETURNING *',
      [name, position, month, days, rate_per_day, credit, id]
    )
    res.json(result.rows[0])
  } catch (error) {
    res.status(500).json({ error: 'Failed to update employee' })
  }
})

// DELETE /api/employees/:id
router.delete('/:id', async (req, res) => {
  const { id } = req.params
  try {
    await pool.query('DELETE FROM employees WHERE id = $1', [id])
    res.status(204).send()
  } catch (error) {
    res.status(500).json({ error: 'Failed to delete employee' })
  }
})

export default router
```

## Option 4: Prisma ORM (Modern & Type-Safe)

### Setup Steps:

1. **Install Prisma**
```bash
npm install prisma @prisma/client
npx prisma init
```

2. **Schema Definition**
```prisma
// prisma/schema.prisma
generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model Employee {
  id           String   @id @default(cuid())
  name         String
  position     String
  month        String
  days         Int
  ratePerDay   Decimal  @map("rate_per_day")
  credit       Decimal  @default(0)
  totalSalary  Decimal? @map("total_salary")
  dateAdded    DateTime @default(now()) @map("date_added")
  lastModified DateTime @updatedAt @map("last_modified")

  @@map("employees")
}
```

3. **Generate Client**
```bash
npx prisma generate
npx prisma db push
```

## Frontend Integration Patterns

### 1. Custom Hooks for Data Management
```typescript
// src/hooks/useEmployees.ts
import { useState, useEffect } from 'react'
import { supabase } from '@/lib/supabase'

export const useEmployees = () => {
  const [employees, setEmployees] = useState([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)

  const fetchEmployees = async () => {
    try {
      setLoading(true)
      const { data, error } = await supabase
        .from('employees')
        .select('*')
        .order('date_added', { ascending: false })
      
      if (error) throw error
      setEmployees(data || [])
    } catch (err) {
      setError(err.message)
    } finally {
      setLoading(false)
    }
  }

  const addEmployee = async (employee) => {
    try {
      const { data, error } = await supabase
        .from('employees')
        .insert([employee])
        .select()
      
      if (error) throw error
      setEmployees(prev => [data[0], ...prev])
      return data[0]
    } catch (err) {
      throw new Error(err.message)
    }
  }

  const updateEmployee = async (id, updates) => {
    try {
      const { data, error } = await supabase
        .from('employees')
        .update(updates)
        .eq('id', id)
        .select()
      
      if (error) throw error
      setEmployees(prev => prev.map(emp => emp.id === id ? data[0] : emp))
      return data[0]
    } catch (err) {
      throw new Error(err.message)
    }
  }

  const deleteEmployee = async (id) => {
    try {
      const { error } = await supabase
        .from('employees')
        .delete()
        .eq('id', id)
      
      if (error) throw error
      setEmployees(prev => prev.filter(emp => emp.id !== id))
    } catch (err) {
      throw new Error(err.message)
    }
  }

  useEffect(() => {
    fetchEmployees()
  }, [])

  return {
    employees,
    loading,
    error,
    addEmployee,
    updateEmployee,
    deleteEmployee,
    refetch: fetchEmployees
  }
}
```

### 2. React Query for Advanced Caching
```typescript
// src/hooks/useEmployeesQuery.ts
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { supabase } from '@/lib/supabase'

export const useEmployeesQuery = () => {
  return useQuery({
    queryKey: ['employees'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('employees')
        .select('*')
        .order('date_added', { ascending: false })
      
      if (error) throw error
      return data
    }
  })
}

export const useAddEmployeeMutation = () => {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: async (employee) => {
      const { data, error } = await supabase
        .from('employees')
        .insert([employee])
        .select()
      
      if (error) throw error
      return data[0]
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['employees'] })
    }
  })
}
```

## Migration Strategy

### Step 1: Gradual Migration
1. Keep localStorage as fallback
2. Add database operations alongside
3. Sync data between both
4. Remove localStorage once stable

### Step 2: Data Migration Script
```typescript
// src/utils/migrateData.ts
export const migrateLocalStorageToDatabase = async () => {
  const localData = localStorage.getItem('employees')
  if (!localData) return

  const employees = JSON.parse(localData)
  
  for (const employee of employees) {
    try {
      await supabase.from('employees').insert({
        name: employee.name,
        position: employee.position,
        month: employee.month,
        days: employee.days,
        rate_per_day: employee.ratePerDay,
        credit: employee.credit
      })
    } catch (error) {
      console.error('Migration error:', error)
    }
  }
  
  // Clear localStorage after successful migration
  localStorage.removeItem('employees')
}
```

## Recommended Approach

**For your construction management system, I recommend Supabase because:**

1. ✅ **Quick Setup** - Database ready in minutes
2. ✅ **Real-time Updates** - Multiple users can see changes instantly
3. ✅ **Built-in Authentication** - User management included
4. ✅ **Automatic API** - No backend coding needed
5. ✅ **TypeScript Support** - Full type safety
6. ✅ **Scalable** - Handles growth automatically
7. ✅ **Free Tier** - Great for development and small teams

Would you like me to implement the Supabase integration for your Employee Register system?
