# Client Access Control Implementation

## Overview

This implementation provides role-based access control for the construction management system, ensuring that client users can only see projects associated with their specific client organization, while admin, management, QS, and accountant users maintain full access to all projects.

## 🏗️ Architecture

### Database Schema Changes

1. **Projects Table Enhancement**
   - Added `client_id` column with foreign key to `clients` table
   - Maintains backward compatibility with existing `client_name` field

2. **New Client-User Junction Table**
   - `client_users` table links user accounts to client organizations
   - Supports multiple users per client and multiple clients per user
   - Includes role and access level granularity

3. **Row Level Security (RLS)**
   - Policies ensure users can only see their own client relationships
   - Admin users can manage all relationships

### Backend Functions

1. **`get_user_accessible_projects(user_id)`**
   - Returns projects based on user role
   - Clients see only their linked projects
   - Other roles see all projects

2. **`can_user_access_project(project_id, user_id)`**
   - Checks if a specific user can access a specific project
   - Used for individual project access validation

3. **`link_user_to_client(user_id, client_id, role, access_level, is_primary)`**
   - Admin-only function to create client-user relationships
   - Supports different roles and access levels

## 📁 Files Modified/Created

### Database Scripts
- `database/client_project_access_control.sql` - Main schema and functions
- `database/client_access_setup_guide.sql` - Setup validation and helper functions

### Frontend Components
- `src/lib/projects.ts` - Updated ProjectService with role-based filtering
- `src/hooks/useProjects.ts` - Modified to pass user ID for filtering
- `src/components/admin/ClientUserManagement.tsx` - Admin interface for managing relationships
- `src/pages/Settings.tsx` - Added client-user management to admin panel
- `src/utils/testClientAccess.ts` - Testing utilities for validation

### Type Definitions
- Updated `Project` interface to include `client_id` field

## 🚀 Setup Instructions

### 1. Database Setup

```sql
-- Run in Supabase SQL Editor
-- Execute the main schema script
\i database/client_project_access_control.sql

-- Run the setup guide for validation
\i database/client_access_setup_guide.sql
```

### 2. Link Client Users to Clients

**Option A: Using Admin Interface**
1. Login as an admin user
2. Go to Settings → Admin tab
3. Use the "Client Access Control" section to link users to clients

**Option B: Using SQL Functions**
```sql
-- Link a client user to their client
SELECT link_user_to_client(
  'user-uuid-here'::UUID,     -- User ID from user_profiles
  'client-uuid-here'::UUID,   -- Client ID from clients table
  'client',                   -- Role (client, contact, admin)
  'read',                     -- Access level (read, write, admin)
  true                        -- Is primary contact
);
```

**Option C: Auto-link by Email Domain**
```sql
-- Automatically link users to clients based on email domain matching
SELECT auto_link_users_by_email_domain();
```

### 3. Validation

**Using SQL Functions:**
```sql
-- Check setup status
SELECT * FROM validate_access_control_setup();

-- View current relationships
SELECT * FROM show_client_user_relationships();

-- Test user access
SELECT * FROM show_user_project_access('<EMAIL>');
```

**Using Frontend Testing:**
```javascript
// In browser console
await testClientAccess.logAccessReport();
```

## 🔐 How It Works

### For Client Users
1. User logs in with client role
2. `useProjects` hook calls `ProjectService.getProjects(userId)`
3. Service calls database function `get_user_accessible_projects(userId)`
4. Function checks user role and returns only linked projects
5. UI displays filtered project list

### For Non-Client Users
1. User logs in with admin/management/qs/accountant role
2. Same flow as above, but function returns all projects
3. No filtering applied for these roles

### Access Control Flow
```
User Login → Role Check → Project Fetch → Role-Based Filter → Display Results
```

## 🧪 Testing

### Test Scenarios

1. **Client User Access**
   - Login as client user
   - Verify only linked projects are visible
   - Verify cannot access other clients' projects

2. **Admin User Access**
   - Login as admin user
   - Verify all projects are visible
   - Verify can manage client-user relationships

3. **Management/QS/Accountant Access**
   - Login with these roles
   - Verify all projects are visible

### Testing Tools

1. **SQL Validation Functions**
   ```sql
   SELECT * FROM validate_access_control_setup();
   SELECT * FROM show_user_project_access();
   ```

2. **Frontend Testing Utility**
   ```javascript
   import { ClientAccessTester } from '@/utils/testClientAccess';
   
   // Test specific user
   const result = await ClientAccessTester.testUserAccess('user-id');
   
   // Test all users
   const allResults = await ClientAccessTester.testAllUsersAccess();
   
   // Generate comprehensive report
   await ClientAccessTester.logAccessReport();
   ```

## 🔧 Configuration Options

### Client-User Relationship Types

- **Role**: `client`, `contact`, `admin`
- **Access Level**: `read`, `write`, `admin`
- **Primary Contact**: Boolean flag for main contact

### Customization Points

1. **Role Permissions**: Modify `get_user_accessible_projects()` function
2. **Access Levels**: Extend `client_users` table with additional permissions
3. **Project Filtering**: Add additional filters in the database function

## 🚨 Troubleshooting

### Common Issues

1. **Client sees all projects**
   - Check if user is linked to a client in `client_users` table
   - Verify `client_id` is set on projects
   - Run validation functions

2. **Database function errors**
   - Ensure all SQL scripts have been executed
   - Check for missing foreign key relationships
   - Verify RLS policies are enabled

3. **Frontend not filtering**
   - Check if user ID is being passed to `ProjectService.getProjects()`
   - Verify `useProjects` hook includes user in dependency array
   - Check browser console for errors

### Debug Commands

```sql
-- Check user-client relationships
SELECT * FROM client_users WHERE user_id = 'user-id-here';

-- Check project-client relationships
SELECT id, name, client_name, client_id FROM projects;

-- Test access function directly
SELECT * FROM get_user_accessible_projects('user-id-here');
```

## 📈 Performance Considerations

1. **Database Indexes**: Created on `client_id`, `user_id`, and `role` columns
2. **Query Optimization**: Functions use efficient joins and filters
3. **Caching**: Frontend can cache user-specific project lists
4. **RLS Performance**: Policies are optimized for common access patterns

## 🔮 Future Enhancements

1. **Project-Level Permissions**: Fine-grained access control per project
2. **Time-Based Access**: Temporary access grants with expiration
3. **Audit Logging**: Track access attempts and permission changes
4. **API Rate Limiting**: Prevent abuse of access control functions
5. **Multi-Tenant Support**: Extend for multiple organizations

## 📝 Maintenance

### Regular Tasks

1. **Monitor Access Patterns**: Review who accesses what projects
2. **Update Relationships**: Keep client-user links current
3. **Performance Monitoring**: Watch for slow queries
4. **Security Audits**: Regular review of access permissions

### Backup Considerations

- Include `client_users` table in backups
- Document client-user relationships for disaster recovery
- Test restore procedures with access control intact
