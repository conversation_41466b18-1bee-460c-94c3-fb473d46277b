import { supabase } from './supabase';
import { NotificationService, EmailQueueItem } from './notificationService';

export interface EmailTemplate {
  subject: string;
  html: string;
  text: string;
}

export interface EmailDigest {
  user_id: string;
  notifications: any[];
  frequency: 'hourly' | 'daily' | 'weekly';
  scheduled_for: string;
}

export class EmailNotificationService {
  // Enhanced email sending with better error handling and retry logic
  static async sendEmail(
    to_email: string,
    subject: string,
    html_content: string,
    text_content: string
  ): Promise<{ success: boolean; message: string }> {
    try {
      console.log(`Attempting to send email to: ${to_email}`);

      // Method 1: Try using Supabase Auth reset password (if <PERSON><PERSON> is configured)
      try {
        const { error: authError } = await supabase.auth.resetPasswordForEmail(
          to_email,
          {
            redirectTo: `${window.location.origin}/notifications?email_notification=true`
          }
        );

        if (!authError) {
          console.log('✅ Email sent successfully via Supabase Auth SMTP');
          return {
            success: true,
            message: 'Email sent successfully via Supabase Auth'
          };
        }
      } catch (authError) {
        console.log('Supabase Auth email failed, trying alternative methods...');
      }

      // Method 2: Try using Supabase Edge Function (if configured)
      try {
        const { data, error: edgeError } = await supabase.functions.invoke('send-email', {
          body: {
            to: to_email,
            subject,
            html: html_content,
            text: text_content
          }
        });

        if (!edgeError && data?.success) {
          console.log('✅ Email sent successfully via Edge Function');
          return {
            success: true,
            message: 'Email sent successfully via Edge Function'
          };
        }
      } catch (edgeError) {
        console.log('Edge Function email failed, using fallback...');
      }

      // Method 3: Fallback - Display email content for manual sending
      console.log('📧 Email Content for Manual Sending:');
      console.log('To:', to_email);
      console.log('Subject:', subject);
      console.log('HTML Content:', html_content);
      console.log('Text Content:', text_content);

      // Store email content in localStorage for admin review
      const emailLog = JSON.parse(localStorage.getItem('email_log') || '[]');
      emailLog.push({
        timestamp: new Date().toISOString(),
        to: to_email,
        subject,
        html_content,
        text_content,
        status: 'manual_required'
      });
      localStorage.setItem('email_log', JSON.stringify(emailLog.slice(-50))); // Keep last 50 emails

      return {
        success: false,
        message: 'Email content generated for manual sending. Check console and localStorage for details.'
      };

    } catch (error) {
      console.error('Error in email sending process:', error);
      return {
        success: false,
        message: `Email sending failed: ${error.message}`
      };
    }
  }

  // Process email queue with enhanced error handling
  static async processEmailQueue(limit: number = 10): Promise<{
    processed: number;
    sent: number;
    failed: number;
    errors: string[];
  }> {
    const result = {
      processed: 0,
      sent: 0,
      failed: 0,
      errors: []
    };

    try {
      // Get pending emails
      const { data: emails, error } = await supabase
        .from('email_queue')
        .select('*')
        .eq('status', 'pending')
        .lte('scheduled_for', new Date().toISOString())
        .lt('attempts', 3)
        .order('scheduled_for', { ascending: true })
        .limit(limit);

      if (error) {
        throw error;
      }

      if (!emails || emails.length === 0) {
        console.log('No pending emails in queue');
        return result;
      }

      console.log(`Processing ${emails.length} emails from queue`);

      for (const email of emails) {
        result.processed++;
        
        try {
          // Mark as sending
          await supabase
            .from('email_queue')
            .update({ 
              status: 'sending',
              attempts: email.attempts + 1,
              updated_at: new Date().toISOString()
            })
            .eq('id', email.id);

          // Send the email
          const sendResult = await this.sendEmail(
            email.to_email,
            email.subject,
            email.html_content,
            email.text_content
          );

          if (sendResult.success) {
            // Mark as sent
            await supabase
              .from('email_queue')
              .update({ 
                status: 'sent',
                sent_at: new Date().toISOString(),
                updated_at: new Date().toISOString()
              })
              .eq('id', email.id);

            // Update notification as email sent
            if (email.notification_id) {
              await supabase
                .from('notifications')
                .update({ 
                  email_sent: true,
                  email_sent_at: new Date().toISOString(),
                  updated_at: new Date().toISOString()
                })
                .eq('id', email.notification_id);
            }

            result.sent++;
            console.log(`✅ Email sent to ${email.to_email}`);
          } else {
            throw new Error(sendResult.message);
          }

        } catch (emailError) {
          result.failed++;
          result.errors.push(`${email.to_email}: ${emailError.message}`);
          
          // Mark as failed if max attempts reached
          const status = email.attempts + 1 >= email.max_attempts ? 'failed' : 'pending';
          
          await supabase
            .from('email_queue')
            .update({ 
              status,
              error_message: emailError.message,
              updated_at: new Date().toISOString()
            })
            .eq('id', email.id);

          console.error(`❌ Failed to send email to ${email.to_email}:`, emailError.message);
        }
      }

      console.log(`Email queue processing complete: ${result.sent} sent, ${result.failed} failed`);
      return result;

    } catch (error) {
      console.error('Error processing email queue:', error);
      result.errors.push(`Queue processing error: ${error.message}`);
      return result;
    }
  }

  // Create email digest for users who prefer batched emails
  static async createEmailDigests(): Promise<void> {
    try {
      console.log('Creating email digests...');

      // Get users who want email digests
      const { data: users, error } = await supabase
        .from('user_notification_preferences')
        .select('user_id, email_digest_frequency, timezone')
        .neq('email_digest_frequency', 'immediate')
        .neq('email_digest_frequency', 'never')
        .eq('email_notifications', true);

      if (error) {
        throw error;
      }

      if (!users || users.length === 0) {
        console.log('No users configured for email digests');
        return;
      }

      for (const user of users) {
        await this.createUserEmailDigest(user.user_id, user.email_digest_frequency);
      }

    } catch (error) {
      console.error('Error creating email digests:', error);
    }
  }

  // Create email digest for a specific user
  static async createUserEmailDigest(
    user_id: string,
    frequency: 'hourly' | 'daily' | 'weekly'
  ): Promise<void> {
    try {
      // Calculate time range based on frequency
      const now = new Date();
      let since: Date;

      switch (frequency) {
        case 'hourly':
          since = new Date(now.getTime() - 60 * 60 * 1000); // 1 hour ago
          break;
        case 'daily':
          since = new Date(now.getTime() - 24 * 60 * 60 * 1000); // 1 day ago
          break;
        case 'weekly':
          since = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000); // 1 week ago
          break;
        default:
          return;
      }

      // Get unread notifications for the user in the time range
      const { data: notifications, error } = await supabase
        .from('notifications')
        .select('*')
        .eq('user_id', user_id)
        .eq('read', false)
        .eq('email_sent', false)
        .gte('created_at', since.toISOString())
        .order('created_at', { ascending: false });

      if (error) {
        throw error;
      }

      if (!notifications || notifications.length === 0) {
        console.log(`No notifications for digest for user ${user_id}`);
        return;
      }

      // Get user email
      const { data: user, error: userError } = await supabase.auth.admin.getUserById(user_id);
      if (userError || !user?.user?.email) {
        console.error('Cannot create digest - user email not found');
        return;
      }

      // Create digest email
      const digestEmail = this.createDigestEmailTemplate(notifications, frequency);
      
      // Queue the digest email
      const { error: queueError } = await supabase
        .from('email_queue')
        .insert({
          user_id,
          to_email: user.user.email,
          subject: digestEmail.subject,
          html_content: digestEmail.html,
          text_content: digestEmail.text,
          scheduled_for: new Date().toISOString()
        });

      if (queueError) {
        throw queueError;
      }

      // Mark notifications as email sent (since they're included in digest)
      const notificationIds = notifications.map(n => n.id);
      await supabase
        .from('notifications')
        .update({ 
          email_sent: true,
          email_sent_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })
        .in('id', notificationIds);

      console.log(`Created ${frequency} digest for user ${user_id} with ${notifications.length} notifications`);

    } catch (error) {
      console.error(`Error creating ${frequency} digest for user ${user_id}:`, error);
    }
  }

  // Create email template for digest
  static createDigestEmailTemplate(
    notifications: any[],
    frequency: string
  ): EmailTemplate {
    const notificationsByCategory = notifications.reduce((acc, notification) => {
      if (!acc[notification.category]) {
        acc[notification.category] = [];
      }
      acc[notification.category].push(notification);
      return acc;
    }, {});

    const subject = `Your ${frequency} notification digest - ${notifications.length} updates`;

    const html = `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <title>Notification Digest</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: #2563eb; color: white; padding: 20px; border-radius: 8px 8px 0 0; }
          .content { background: #f8fafc; padding: 20px; }
          .category { margin-bottom: 30px; }
          .category-title { font-size: 18px; font-weight: bold; color: #1e40af; margin-bottom: 15px; }
          .notification { background: white; padding: 15px; margin-bottom: 10px; border-radius: 6px; border-left: 4px solid #2563eb; }
          .notification-title { font-weight: bold; margin-bottom: 5px; }
          .notification-message { color: #666; margin-bottom: 10px; }
          .notification-time { font-size: 12px; color: #999; }
          .footer { background: #e2e8f0; padding: 15px; text-align: center; border-radius: 0 0 8px 8px; }
          .button { display: inline-block; background: #2563eb; color: white; padding: 10px 20px; text-decoration: none; border-radius: 4px; margin: 10px 5px; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>Your ${frequency.charAt(0).toUpperCase() + frequency.slice(1)} Notification Digest</h1>
            <p>You have ${notifications.length} new notifications</p>
          </div>
          <div class="content">
            ${Object.entries(notificationsByCategory).map(([category, categoryNotifications]: [string, any[]]) => `
              <div class="category">
                <div class="category-title">${category.charAt(0).toUpperCase() + category.slice(1)} (${categoryNotifications.length})</div>
                ${categoryNotifications.map(notification => `
                  <div class="notification">
                    <div class="notification-title">${notification.title}</div>
                    <div class="notification-message">${notification.message}</div>
                    <div class="notification-time">${new Date(notification.created_at).toLocaleString()}</div>
                    ${notification.action_url ? `<a href="${notification.action_url}" class="button">${notification.action_label || 'View'}</a>` : ''}
                  </div>
                `).join('')}
              </div>
            `).join('')}
          </div>
          <div class="footer">
            <p>You can manage your notification preferences in your account settings.</p>
            <a href="/settings/notifications" class="button">Manage Preferences</a>
          </div>
        </div>
      </body>
      </html>
    `;

    const text = `
Your ${frequency} Notification Digest
=====================================

You have ${notifications.length} new notifications:

${Object.entries(notificationsByCategory).map(([category, categoryNotifications]: [string, any[]]) => `
${category.toUpperCase()} (${categoryNotifications.length}):
${categoryNotifications.map(notification => `
- ${notification.title}
  ${notification.message}
  ${new Date(notification.created_at).toLocaleString()}
  ${notification.action_url ? `  View: ${notification.action_url}` : ''}
`).join('')}
`).join('')}

Manage your notification preferences: /settings/notifications
    `;

    return { subject, html, text };
  }

  // Get email queue status
  static async getEmailQueueStatus(): Promise<{
    pending: number;
    sending: number;
    sent: number;
    failed: number;
    total: number;
  }> {
    try {
      const { data, error } = await supabase
        .from('email_queue')
        .select('status')
        .gte('created_at', new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString()); // Last 24 hours

      if (error) {
        throw error;
      }

      const status = {
        pending: 0,
        sending: 0,
        sent: 0,
        failed: 0,
        total: data?.length || 0
      };

      data?.forEach(item => {
        status[item.status]++;
      });

      return status;
    } catch (error) {
      console.error('Error getting email queue status:', error);
      return { pending: 0, sending: 0, sent: 0, failed: 0, total: 0 };
    }
  }

  // Retry failed emails
  static async retryFailedEmails(): Promise<void> {
    try {
      const { error } = await supabase
        .from('email_queue')
        .update({ 
          status: 'pending',
          attempts: 0,
          error_message: null,
          updated_at: new Date().toISOString()
        })
        .eq('status', 'failed')
        .lt('attempts', 3);

      if (error) {
        throw error;
      }

      console.log('Failed emails reset for retry');
    } catch (error) {
      console.error('Error retrying failed emails:', error);
    }
  }
}
