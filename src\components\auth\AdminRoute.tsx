import React from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Shield, Lock } from 'lucide-react';

interface AdminRouteProps {
  children: React.ReactNode;
}

const AdminRoute: React.FC<AdminRouteProps> = ({ children }) => {
  const { profile } = useAuth();
  
  // Get role name from profile - handle both direct role_name and nested role object
  const userRole = profile?.role_name || profile?.role?.role_name || 'client';
  
  // Check if user is admin
  if (userRole !== 'admin') {
    return (
      <div className="container mx-auto p-6">
        <Alert className="border-red-200 bg-red-50">
          <Shield className="h-4 w-4" />
          <AlertDescription>
            <div className="flex items-center gap-2">
              <Lock className="h-4 w-4" />
              <div>
                <div className="font-semibold">Access Denied</div>
                <div>This feature is only available to administrators. Your current role: {userRole}</div>
              </div>
            </div>
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  // User is admin, render children
  return <>{children}</>;
};

export default AdminRoute;
