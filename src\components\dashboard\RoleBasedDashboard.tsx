import React from 'react';
import { useRoleAccess } from '@/hooks/useRoleAccess';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  Crown, 
  Calculator, 
  Building, 
  Users, 
  UserCheck, 
  Shield,
  BarChart3,
  DollarSign,
  FolderOpen,
  MessageSquare
} from 'lucide-react';

const RoleBasedDashboard = () => {
  const { userRole, permissions, isAdmin, isManagement, isQS, isAccountant, isClient } = useRoleAccess();

  const getRoleIcon = () => {
    switch (userRole) {
      case 'admin': return <Crown className="h-8 w-8 text-yellow-500" />;
      case 'management': return <Users className="h-8 w-8 text-blue-500" />;
      case 'qs': return <Calculator className="h-8 w-8 text-green-500" />;
      case 'accountant': return <Building className="h-8 w-8 text-purple-500" />;
      case 'client': return <UserCheck className="h-8 w-8 text-gray-500" />;
      default: return <Shield className="h-8 w-8 text-gray-400" />;
    }
  };

  const getRoleDescription = () => {
    switch (userRole) {
      case 'admin': return 'Full system access and user management';
      case 'management': return 'Project oversight and strategic planning';
      case 'qs': return 'Cost estimation and quantity surveying';
      case 'accountant': return 'Financial management and reporting';
      case 'client': return 'Project viewing and communication';
      default: return 'Limited access';
    }
  };

  const getAvailableFeatures = () => {
    const features = [];
    
    if (permissions.canAccessProjects) {
      features.push({ name: 'Projects', icon: FolderOpen, description: 'Manage construction projects' });
    }
    if (permissions.canAccessFinancials) {
      features.push({ name: 'Financials', icon: DollarSign, description: 'Financial management and invoicing' });
    }
    if (permissions.canAccessReports) {
      features.push({ name: 'Reports', icon: BarChart3, description: 'Generate reports and analytics' });
    }
    if (permissions.canAccessUserManagement) {
      features.push({ name: 'User Management', icon: Users, description: 'Manage system users and roles' });
    }
    if (permissions.canAccessMessages) {
      features.push({ name: 'Messages', icon: MessageSquare, description: 'Team communication' });
    }
    
    return features;
  };

  const getRestrictedFeatures = () => {
    const restricted = [];
    
    if (!permissions.canAccessProjects) {
      restricted.push('Projects');
    }
    if (!permissions.canAccessFinancials) {
      restricted.push('Financials');
    }
    if (!permissions.canAccessReports) {
      restricted.push('Reports');
    }
    if (!permissions.canAccessUserManagement) {
      restricted.push('User Management');
    }
    if (!permissions.canAccessAssets) {
      restricted.push('Assets');
    }
    
    return restricted;
  };

  return (
    <div className="space-y-6">
      {/* Role Information Card */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-3">
            {getRoleIcon()}
            <div>
              <div className="text-xl font-bold">
                Welcome, {userRole.charAt(0).toUpperCase() + userRole.slice(1)}
              </div>
              <div className="text-sm text-muted-foreground font-normal">
                {getRoleDescription()}
              </div>
            </div>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <h4 className="font-semibold text-green-700 mb-2">Available Features</h4>
              <div className="space-y-2">
                {getAvailableFeatures().map((feature, index) => (
                  <div key={index} className="flex items-center gap-2 text-sm">
                    <feature.icon className="h-4 w-4 text-green-500" />
                    <span className="font-medium">{feature.name}</span>
                    <span className="text-gray-500">- {feature.description}</span>
                  </div>
                ))}
              </div>
            </div>
            
            {getRestrictedFeatures().length > 0 && (
              <div>
                <h4 className="font-semibold text-red-700 mb-2">Restricted Features</h4>
                <div className="space-y-1">
                  {getRestrictedFeatures().map((feature, index) => (
                    <div key={index} className="flex items-center gap-2 text-sm text-red-600">
                      <Shield className="h-4 w-4" />
                      <span>{feature}</span>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Role-Specific Quick Actions */}
      {isAdmin && (
        <Card>
          <CardHeader>
            <CardTitle className="text-yellow-700">Admin Quick Actions</CardTitle>
            <CardDescription>Administrative functions and system management</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="p-4 border rounded-lg hover:bg-gray-50 cursor-pointer">
                <Users className="h-6 w-6 text-blue-500 mb-2" />
                <h4 className="font-semibold">User Management</h4>
                <p className="text-sm text-gray-600">Create and manage user accounts</p>
              </div>
              <div className="p-4 border rounded-lg hover:bg-gray-50 cursor-pointer">
                <BarChart3 className="h-6 w-6 text-green-500 mb-2" />
                <h4 className="font-semibold">System Reports</h4>
                <p className="text-sm text-gray-600">View comprehensive system analytics</p>
              </div>
              <div className="p-4 border rounded-lg hover:bg-gray-50 cursor-pointer">
                <Shield className="h-6 w-6 text-purple-500 mb-2" />
                <h4 className="font-semibold">Security Settings</h4>
                <p className="text-sm text-gray-600">Manage system security and permissions</p>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {isAccountant && (
        <Card>
          <CardHeader>
            <CardTitle className="text-purple-700">Financial Quick Actions</CardTitle>
            <CardDescription>Financial management and accounting functions</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="p-4 border rounded-lg hover:bg-gray-50 cursor-pointer">
                <DollarSign className="h-6 w-6 text-green-500 mb-2" />
                <h4 className="font-semibold">Create Invoice</h4>
                <p className="text-sm text-gray-600">Generate new client invoices</p>
              </div>
              <div className="p-4 border rounded-lg hover:bg-gray-50 cursor-pointer">
                <BarChart3 className="h-6 w-6 text-blue-500 mb-2" />
                <h4 className="font-semibold">Financial Reports</h4>
                <p className="text-sm text-gray-600">View revenue and expense reports</p>
              </div>
              <div className="p-4 border rounded-lg hover:bg-gray-50 cursor-pointer">
                <Building className="h-6 w-6 text-purple-500 mb-2" />
                <h4 className="font-semibold">Client Billing</h4>
                <p className="text-sm text-gray-600">Manage client accounts and payments</p>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {isQS && (
        <Card>
          <CardHeader>
            <CardTitle className="text-green-700">QS Quick Actions</CardTitle>
            <CardDescription>Quantity surveying and cost estimation tools</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="p-4 border rounded-lg hover:bg-gray-50 cursor-pointer">
                <Calculator className="h-6 w-6 text-green-500 mb-2" />
                <h4 className="font-semibold">Cost Estimation</h4>
                <p className="text-sm text-gray-600">Create project cost estimates</p>
              </div>
              <div className="p-4 border rounded-lg hover:bg-gray-50 cursor-pointer">
                <FolderOpen className="h-6 w-6 text-blue-500 mb-2" />
                <h4 className="font-semibold">Project Quantities</h4>
                <p className="text-sm text-gray-600">Manage project measurements</p>
              </div>
              <div className="p-4 border rounded-lg hover:bg-gray-50 cursor-pointer">
                <BarChart3 className="h-6 w-6 text-purple-500 mb-2" />
                <h4 className="font-semibold">Progress Reports</h4>
                <p className="text-sm text-gray-600">Track project progress and costs</p>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {isClient && (
        <Alert>
          <UserCheck className="h-4 w-4" />
          <AlertDescription>
            <div className="space-y-2">
              <div className="font-semibold">Welcome to your project portal!</div>
              <div>As a client, you can view your projects, communicate with the team, and track progress. 
              For additional access or questions, please contact your project manager.</div>
            </div>
          </AlertDescription>
        </Alert>
      )}
    </div>
  );
};

export default RoleBasedDashboard;
