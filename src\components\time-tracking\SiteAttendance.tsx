import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { useToast } from '@/components/ui/use-toast';
import { TimeTrackingService, SiteAttendance as SiteAttendanceType, Site } from '@/lib/timeTrackingService';
import {
  Users,
  MapPin,
  Clock,
  Calendar,
  RefreshCw,
  Download,
  Filter,
  PlayCircle,
  StopCircle,
  PauseCircle,
  User,
  Building
} from 'lucide-react';

interface SiteAttendanceProps {
  sites: Site[];
}

const SiteAttendance: React.FC<SiteAttendanceProps> = ({ sites }) => {
  const { toast } = useToast();
  const [loading, setLoading] = useState(false);
  const [attendance, setAttendance] = useState<SiteAttendanceType[]>([]);
  const [selectedSite, setSelectedSite] = useState<string>('all');
  const [selectedDate, setSelectedDate] = useState<string>(
    new Date().toISOString().split('T')[0]
  );

  useEffect(() => {
    loadAttendance();
  }, [selectedSite, selectedDate]);

  const loadAttendance = async () => {
    setLoading(true);
    try {
      const siteId = selectedSite === 'all' ? undefined : selectedSite;
      const attendanceData = await TimeTrackingService.getSiteAttendance(siteId, selectedDate);
      setAttendance(attendanceData);
    } catch (error) {
      console.error('Error loading attendance:', error);
      toast({
        title: "Error",
        description: "Failed to load site attendance data",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'active':
        return (
          <Badge className="bg-green-100 text-green-800">
            <PlayCircle className="w-3 h-3 mr-1" />
            Active
          </Badge>
        );
      case 'break':
        return (
          <Badge className="bg-yellow-100 text-yellow-800">
            <PauseCircle className="w-3 h-3 mr-1" />
            On Break
          </Badge>
        );
      case 'completed':
        return (
          <Badge className="bg-gray-100 text-gray-800">
            <StopCircle className="w-3 h-3 mr-1" />
            Completed
          </Badge>
        );
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  const getRoleBadge = (role: string) => {
    const roleColors = {
      admin: 'bg-purple-100 text-purple-800',
      management: 'bg-blue-100 text-blue-800',
      qs: 'bg-green-100 text-green-800',
      accountant: 'bg-orange-100 text-orange-800',
      client: 'bg-gray-100 text-gray-800'
    };

    return (
      <Badge className={roleColors[role as keyof typeof roleColors] || 'bg-gray-100 text-gray-800'}>
        {role.charAt(0).toUpperCase() + role.slice(1)}
      </Badge>
    );
  };

  const formatTime = (timeString: string) => {
    return new Date(timeString).toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const formatDuration = (hours: number) => {
    const h = Math.floor(hours);
    const m = Math.round((hours - h) * 60);
    return `${h}h ${m}m`;
  };

  const exportAttendance = () => {
    const csvContent = [
      ['Name', 'Role', 'Site', 'Clock In', 'Clock Out', 'Total Hours', 'Status', 'Description'].join(','),
      ...attendance.map(entry => [
        entry.user_name,
        entry.user_role,
        entry.site_name,
        formatTime(entry.clock_in_time),
        entry.clock_out_time ? formatTime(entry.clock_out_time) : 'Still working',
        entry.total_hours ? formatDuration(entry.total_hours) : 'N/A',
        entry.status,
        entry.work_description || ''
      ].join(','))
    ].join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `attendance-${selectedDate}.csv`;
    a.click();
    window.URL.revokeObjectURL(url);
  };

  // Group attendance by site
  const attendanceBySite = attendance.reduce((acc, entry) => {
    if (!acc[entry.site_name]) {
      acc[entry.site_name] = [];
    }
    acc[entry.site_name].push(entry);
    return acc;
  }, {} as Record<string, SiteAttendanceType[]>);

  const totalActiveWorkers = attendance.filter(entry => entry.status === 'active').length;
  const totalHours = attendance.reduce((sum, entry) => sum + (entry.total_hours || 0), 0);

  return (
    <div className="space-y-6">
      {/* Header and Filters */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Users className="h-5 w-5" />
            Site Attendance
          </CardTitle>
          <CardDescription>
            Monitor worker attendance across all sites
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col sm:flex-row gap-4 items-end">
            <div className="flex-1">
              <Label htmlFor="site-filter">Filter by Site</Label>
              <Select value={selectedSite} onValueChange={setSelectedSite}>
                <SelectTrigger>
                  <SelectValue placeholder="Select site" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Sites</SelectItem>
                  {sites.map((site) => (
                    <SelectItem key={site.id} value={site.id}>
                      {site.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            
            <div className="flex-1">
              <Label htmlFor="date-filter">Date</Label>
              <Input
                id="date-filter"
                type="date"
                value={selectedDate}
                onChange={(e) => setSelectedDate(e.target.value)}
              />
            </div>

            <div className="flex gap-2">
              <Button onClick={loadAttendance} disabled={loading}>
                {loading ? (
                  <RefreshCw className="h-4 w-4 animate-spin" />
                ) : (
                  <RefreshCw className="h-4 w-4" />
                )}
              </Button>
              <Button variant="outline" onClick={exportAttendance}>
                <Download className="h-4 w-4 mr-2" />
                Export
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Summary Stats */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center gap-2">
              <Users className="h-5 w-5 text-blue-500" />
              <div>
                <p className="text-sm text-gray-600">Active Workers</p>
                <p className="text-2xl font-bold">{totalActiveWorkers}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center gap-2">
              <Clock className="h-5 w-5 text-green-500" />
              <div>
                <p className="text-sm text-gray-600">Total Hours</p>
                <p className="text-2xl font-bold">{formatDuration(totalHours)}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center gap-2">
              <MapPin className="h-5 w-5 text-purple-500" />
              <div>
                <p className="text-sm text-gray-600">Active Sites</p>
                <p className="text-2xl font-bold">{Object.keys(attendanceBySite).length}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Attendance by Site */}
      <div className="space-y-4">
        {Object.keys(attendanceBySite).length === 0 ? (
          <Card>
            <CardContent className="p-12 text-center">
              <Users className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-gray-600 mb-2">No Attendance Data</h3>
              <p className="text-gray-500">
                No workers have clocked in for the selected date and site.
              </p>
            </CardContent>
          </Card>
        ) : (
          Object.entries(attendanceBySite).map(([siteName, siteAttendance]) => (
            <Card key={siteName}>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Building className="h-5 w-5" />
                  {siteName}
                  <Badge variant="outline">{siteAttendance.length} workers</Badge>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {siteAttendance.map((entry, index) => (
                    <div
                      key={index}
                      className="flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50"
                    >
                      <div className="flex items-center gap-4">
                        <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                          <User className="h-5 w-5 text-blue-600" />
                        </div>
                        <div>
                          <div className="font-medium">{entry.user_name}</div>
                          <div className="flex items-center gap-2 text-sm text-gray-500">
                            {getRoleBadge(entry.user_role)}
                            {entry.work_description && (
                              <span>• {entry.work_description}</span>
                            )}
                          </div>
                        </div>
                      </div>
                      
                      <div className="flex items-center gap-4 text-sm">
                        <div className="text-center">
                          <div className="text-gray-500">Clock In</div>
                          <div className="font-medium">{formatTime(entry.clock_in_time)}</div>
                        </div>
                        
                        <div className="text-center">
                          <div className="text-gray-500">Clock Out</div>
                          <div className="font-medium">
                            {entry.clock_out_time ? formatTime(entry.clock_out_time) : '-'}
                          </div>
                        </div>
                        
                        <div className="text-center">
                          <div className="text-gray-500">Hours</div>
                          <div className="font-medium">
                            {entry.total_hours ? formatDuration(entry.total_hours) : '-'}
                          </div>
                        </div>
                        
                        <div>
                          {getStatusBadge(entry.status)}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          ))
        )}
      </div>
    </div>
  );
};

export default SiteAttendance;
