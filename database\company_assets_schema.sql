-- Company Assets Management System Database Schema
-- Run this in your Supabase SQL Editor

-- IMPORTANT: Run this entire script in order to ensure proper table dependencies

-- 1. First, create the asset categories table
CREATE TABLE IF NOT EXISTS public.asset_categories (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    name VARCHAR(100) NOT NULL UNIQUE,
    description TEXT,
    icon VARCHAR(50), -- Icon name for UI
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Insert default categories first (before creating company_assets table)
INSERT INTO public.asset_categories (name, description, icon) VALUES
('Heavy Equipment', 'Excavators, bulldozers, cranes, and other heavy machinery', 'Truck'),
('Vehicles', 'Company cars, trucks, vans, and transportation vehicles', 'Car'),
('Tools & Equipment', 'Hand tools, power tools, and small equipment', 'Wrench'),
('Technology', 'Computers, tablets, software licenses, and IT equipment', 'Laptop'),
('Property', 'Buildings, land, warehouses, and real estate', 'Building'),
('Safety Equipment', 'Safety gear, protective equipment, and safety systems', 'Shield'),
('Office Equipment', 'Furniture, printers, office supplies, and fixtures', 'Printer'),
('Machinery', 'Manufacturing equipment, generators, and industrial machines', 'Cog')
ON CONFLICT (name) DO NOTHING;

-- 2. Now create the company assets table with proper foreign key reference
CREATE TABLE IF NOT EXISTS public.company_assets (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    asset_number VARCHAR(50) UNIQUE NOT NULL, -- Asset tracking number
    name VARCHAR(255) NOT NULL,
    description TEXT,
    category_id UUID REFERENCES public.asset_categories(id) ON DELETE SET NULL,
    asset_type VARCHAR(50) NOT NULL CHECK (asset_type IN ('Equipment', 'Vehicle', 'Property', 'Tool', 'Machinery', 'Technology')),
    
    -- Financial Information
    purchase_price DECIMAL(15,2) NOT NULL DEFAULT 0,
    current_value DECIMAL(15,2) NOT NULL DEFAULT 0,
    depreciation_rate DECIMAL(5,2) DEFAULT 0, -- Annual depreciation percentage
    purchase_date DATE NOT NULL,
    warranty_expiry DATE,
    
    -- Asset Details
    brand VARCHAR(100),
    model VARCHAR(100),
    serial_number VARCHAR(100),
    condition VARCHAR(20) DEFAULT 'Good' CHECK (condition IN ('Excellent', 'Good', 'Fair', 'Poor', 'Needs Repair')),
    status VARCHAR(20) DEFAULT 'Active' CHECK (status IN ('Active', 'Inactive', 'Under Maintenance', 'Disposed', 'Lost', 'Sold')),
    
    -- Location and Assignment
    location VARCHAR(255),
    assigned_to VARCHAR(255), -- Employee or department
    project_id UUID, -- Reference to projects table if assigned to specific project
    
    -- Maintenance Information
    last_maintenance_date DATE,
    next_maintenance_date DATE,
    maintenance_interval_months INTEGER DEFAULT 12,
    maintenance_cost_ytd DECIMAL(12,2) DEFAULT 0, -- Year to date maintenance costs
    
    -- Insurance and Compliance
    insurance_policy VARCHAR(100),
    insurance_expiry DATE,
    compliance_certificates TEXT[], -- Array of compliance certificates
    
    -- Additional Information
    notes TEXT,
    attachments JSONB, -- Store file references, photos, documents
    tags TEXT[], -- Array of tags for categorization
    
    -- Audit Trail
    created_by VARCHAR(255),
    updated_by VARCHAR(255),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 3. Asset Maintenance Records Table
CREATE TABLE IF NOT EXISTS public.asset_maintenance (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    asset_id UUID NOT NULL REFERENCES public.company_assets(id) ON DELETE CASCADE,
    maintenance_type VARCHAR(50) NOT NULL CHECK (maintenance_type IN ('Preventive', 'Corrective', 'Emergency', 'Inspection', 'Upgrade')),
    description TEXT NOT NULL,
    
    -- Scheduling
    scheduled_date DATE,
    completed_date DATE,
    next_due_date DATE,
    
    -- Cost Information
    cost DECIMAL(12,2) DEFAULT 0,
    labor_hours DECIMAL(8,2) DEFAULT 0,
    parts_cost DECIMAL(12,2) DEFAULT 0,
    external_service_cost DECIMAL(12,2) DEFAULT 0,
    
    -- Service Provider
    service_provider VARCHAR(255),
    technician VARCHAR(255),
    
    -- Status and Priority
    status VARCHAR(20) DEFAULT 'Scheduled' CHECK (status IN ('Scheduled', 'In Progress', 'Completed', 'Cancelled', 'Overdue')),
    priority VARCHAR(20) DEFAULT 'Medium' CHECK (priority IN ('Low', 'Medium', 'High', 'Critical')),
    
    -- Documentation
    work_performed TEXT,
    parts_replaced TEXT[],
    recommendations TEXT,
    attachments JSONB,
    
    -- Audit Trail
    created_by VARCHAR(255),
    updated_by VARCHAR(255),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 4. Asset Depreciation Records Table
CREATE TABLE IF NOT EXISTS public.asset_depreciation (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    asset_id UUID NOT NULL REFERENCES public.company_assets(id) ON DELETE CASCADE,
    year INTEGER NOT NULL,
    opening_value DECIMAL(15,2) NOT NULL,
    depreciation_amount DECIMAL(15,2) NOT NULL,
    closing_value DECIMAL(15,2) NOT NULL,
    depreciation_method VARCHAR(50) DEFAULT 'Straight Line',
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 5. Asset Transfers/Assignments Table
CREATE TABLE IF NOT EXISTS public.asset_assignments (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    asset_id UUID NOT NULL REFERENCES public.company_assets(id) ON DELETE CASCADE,
    assigned_from VARCHAR(255),
    assigned_to VARCHAR(255) NOT NULL,
    project_id UUID, -- Reference to projects table
    location_from VARCHAR(255),
    location_to VARCHAR(255) NOT NULL,
    assignment_date DATE NOT NULL DEFAULT CURRENT_DATE,
    return_date DATE,
    purpose TEXT,
    condition_at_assignment VARCHAR(20),
    condition_at_return VARCHAR(20),
    notes TEXT,
    created_by VARCHAR(255),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create Indexes for Performance
CREATE INDEX IF NOT EXISTS idx_company_assets_category ON public.company_assets(category_id);
CREATE INDEX IF NOT EXISTS idx_company_assets_type ON public.company_assets(asset_type);
CREATE INDEX IF NOT EXISTS idx_company_assets_status ON public.company_assets(status);
CREATE INDEX IF NOT EXISTS idx_company_assets_assigned_to ON public.company_assets(assigned_to);
CREATE INDEX IF NOT EXISTS idx_company_assets_project ON public.company_assets(project_id);
CREATE INDEX IF NOT EXISTS idx_asset_maintenance_asset ON public.asset_maintenance(asset_id);
CREATE INDEX IF NOT EXISTS idx_asset_maintenance_status ON public.asset_maintenance(status);
CREATE INDEX IF NOT EXISTS idx_asset_maintenance_due_date ON public.asset_maintenance(next_due_date);
CREATE INDEX IF NOT EXISTS idx_asset_depreciation_asset ON public.asset_depreciation(asset_id);
CREATE INDEX IF NOT EXISTS idx_asset_assignments_asset ON public.asset_assignments(asset_id);

-- Enable Row Level Security (RLS)
ALTER TABLE public.asset_categories ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.company_assets ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.asset_maintenance ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.asset_depreciation ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.asset_assignments ENABLE ROW LEVEL SECURITY;

-- Create RLS Policies (Allow all operations for now - customize based on your auth requirements)
CREATE POLICY "Allow all operations on asset_categories" ON public.asset_categories FOR ALL USING (true);
CREATE POLICY "Allow all operations on company_assets" ON public.company_assets FOR ALL USING (true);
CREATE POLICY "Allow all operations on asset_maintenance" ON public.asset_maintenance FOR ALL USING (true);
CREATE POLICY "Allow all operations on asset_depreciation" ON public.asset_depreciation FOR ALL USING (true);
CREATE POLICY "Allow all operations on asset_assignments" ON public.asset_assignments FOR ALL USING (true);



-- Create function to automatically update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers for automatic timestamp updates
CREATE TRIGGER update_asset_categories_updated_at BEFORE UPDATE ON public.asset_categories FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_company_assets_updated_at BEFORE UPDATE ON public.company_assets FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_asset_maintenance_updated_at BEFORE UPDATE ON public.asset_maintenance FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Create function to calculate asset depreciation
CREATE OR REPLACE FUNCTION calculate_asset_depreciation(
    asset_id_param UUID,
    calculation_date DATE DEFAULT CURRENT_DATE
)
RETURNS TABLE (
    current_value DECIMAL(15,2),
    total_depreciation DECIMAL(15,2),
    annual_depreciation DECIMAL(15,2)
) AS $$
DECLARE
    asset_record RECORD;
    years_owned DECIMAL(10,4);
    calculated_depreciation DECIMAL(15,2);
    calculated_current_value DECIMAL(15,2);
    annual_dep DECIMAL(15,2);
BEGIN
    -- Get asset details
    SELECT purchase_price, purchase_date, depreciation_rate
    INTO asset_record
    FROM public.company_assets
    WHERE id = asset_id_param;
    
    IF NOT FOUND THEN
        RAISE EXCEPTION 'Asset not found';
    END IF;
    
    -- Calculate years owned (with decimal precision)
    years_owned := EXTRACT(EPOCH FROM (calculation_date - asset_record.purchase_date)) / (365.25 * 24 * 60 * 60);
    
    -- Calculate annual depreciation
    annual_dep := asset_record.purchase_price * (asset_record.depreciation_rate / 100);
    
    -- Calculate total depreciation
    calculated_depreciation := annual_dep * years_owned;
    
    -- Ensure depreciation doesn't exceed purchase price
    IF calculated_depreciation > asset_record.purchase_price THEN
        calculated_depreciation := asset_record.purchase_price;
    END IF;
    
    -- Calculate current value
    calculated_current_value := asset_record.purchase_price - calculated_depreciation;
    
    -- Ensure current value is not negative
    IF calculated_current_value < 0 THEN
        calculated_current_value := 0;
    END IF;
    
    RETURN QUERY SELECT calculated_current_value, calculated_depreciation, annual_dep;
END;
$$ LANGUAGE plpgsql;

-- Add comments for documentation
COMMENT ON TABLE public.asset_categories IS 'Categories for organizing company assets';
COMMENT ON TABLE public.company_assets IS 'Main table for tracking all company assets including equipment, vehicles, and property';
COMMENT ON TABLE public.asset_maintenance IS 'Maintenance records and scheduling for company assets';
COMMENT ON TABLE public.asset_depreciation IS 'Annual depreciation records for financial reporting';
COMMENT ON TABLE public.asset_assignments IS 'Track asset assignments to employees, projects, and locations';
COMMENT ON FUNCTION calculate_asset_depreciation IS 'Calculate current depreciation and value for an asset';
