// Role-based access control hook
import { useAuth } from '@/contexts/AuthContext';

export interface RolePermissions {
  // Navigation access
  canAccessDashboard: boolean;
  canAccessProjects: boolean;
  canAccessFinancials: boolean;
  canAccessReports: boolean;
  canAccessMessages: boolean;
  canAccessAssets: boolean;
  canAccessClients: boolean;
  canAccessUserManagement: boolean;
  
  // Project permissions
  canCreateProjects: boolean;
  canEditProjects: boolean;
  canDeleteProjects: boolean;
  canViewAllProjects: boolean;
  canViewOwnProjects: boolean;
  
  // Financial permissions
  canCreateInvoices: boolean;
  canEditInvoices: boolean;
  canDeleteInvoices: boolean;
  canViewFinancialReports: boolean;
  canManagePayments: boolean;
  canViewRevenue: boolean;
  
  // User management permissions
  canCreateUsers: boolean;
  canEditUsers: boolean;
  canDeleteUsers: boolean;
  canViewAllUsers: boolean;
  canManageRoles: boolean;
  
  // Asset permissions
  canCreateAssets: boolean;
  canEditAssets: boolean;
  canDeleteAssets: boolean;
  canViewAssets: boolean;
  
  // Client permissions
  canCreateClients: boolean;
  canEditClients: boolean;
  canDeleteClients: boolean;
  canViewAllClients: boolean;
  
  // Report permissions
  canGenerateReports: boolean;
  canViewAnalytics: boolean;
  canExportData: boolean;
  
  // System permissions
  canAccessSettings: boolean;
  canManageSystem: boolean;
}

const rolePermissions: Record<string, RolePermissions> = {
  admin: {
    // Navigation access - Admin can access everything
    canAccessDashboard: true,
    canAccessProjects: true,
    canAccessFinancials: true,
    canAccessReports: true,
    canAccessMessages: true,
    canAccessAssets: true,
    canAccessClients: true,
    canAccessUserManagement: true,
    
    // Project permissions - Full access
    canCreateProjects: true,
    canEditProjects: true,
    canDeleteProjects: true,
    canViewAllProjects: true,
    canViewOwnProjects: true,
    
    // Financial permissions - Full access
    canCreateInvoices: true,
    canEditInvoices: true,
    canDeleteInvoices: true,
    canViewFinancialReports: true,
    canManagePayments: true,
    canViewRevenue: true,
    
    // User management permissions - Full access
    canCreateUsers: true,
    canEditUsers: true,
    canDeleteUsers: true,
    canViewAllUsers: true,
    canManageRoles: true,
    
    // Asset permissions - Full access
    canCreateAssets: true,
    canEditAssets: true,
    canDeleteAssets: true,
    canViewAssets: true,
    
    // Client permissions - Full access
    canCreateClients: true,
    canEditClients: true,
    canDeleteClients: true,
    canViewAllClients: true,
    
    // Report permissions - Full access
    canGenerateReports: true,
    canViewAnalytics: true,
    canExportData: true,
    
    // System permissions - Full access
    canAccessSettings: true,
    canManageSystem: true,
  },
  
  management: {
    // Navigation access - Management level access
    canAccessDashboard: true,
    canAccessProjects: true,
    canAccessFinancials: true,
    canAccessReports: true,
    canAccessMessages: true,
    canAccessAssets: true,
    canAccessClients: true,
    canAccessUserManagement: false, // No user management
    
    // Project permissions - Can manage projects
    canCreateProjects: true,
    canEditProjects: true,
    canDeleteProjects: false, // Can't delete
    canViewAllProjects: true,
    canViewOwnProjects: true,
    
    // Financial permissions - View only
    canCreateInvoices: false,
    canEditInvoices: false,
    canDeleteInvoices: false,
    canViewFinancialReports: true,
    canManagePayments: false,
    canViewRevenue: true,
    
    // User management permissions - No access
    canCreateUsers: false,
    canEditUsers: false,
    canDeleteUsers: false,
    canViewAllUsers: false,
    canManageRoles: false,
    
    // Asset permissions - View and edit
    canCreateAssets: true,
    canEditAssets: true,
    canDeleteAssets: false,
    canViewAssets: true,
    
    // Client permissions - View and edit
    canCreateClients: true,
    canEditClients: true,
    canDeleteClients: false,
    canViewAllClients: true,
    
    // Report permissions - Full access
    canGenerateReports: true,
    canViewAnalytics: true,
    canExportData: true,
    
    // System permissions - No access
    canAccessSettings: false,
    canManageSystem: false,
  },
  
  qs: {
    // Navigation access - QS specific access
    canAccessDashboard: true,
    canAccessProjects: true,
    canAccessFinancials: false, // No financial access
    canAccessReports: true,
    canAccessMessages: true,
    canAccessAssets: false, // No asset access
    canAccessClients: true,
    canAccessUserManagement: false,
    
    // Project permissions - Can view and edit projects
    canCreateProjects: true,
    canEditProjects: true,
    canDeleteProjects: false,
    canViewAllProjects: true,
    canViewOwnProjects: true,
    
    // Financial permissions - No access
    canCreateInvoices: false,
    canEditInvoices: false,
    canDeleteInvoices: false,
    canViewFinancialReports: false,
    canManagePayments: false,
    canViewRevenue: false,
    
    // User management permissions - No access
    canCreateUsers: false,
    canEditUsers: false,
    canDeleteUsers: false,
    canViewAllUsers: false,
    canManageRoles: false,
    
    // Asset permissions - No access
    canCreateAssets: false,
    canEditAssets: false,
    canDeleteAssets: false,
    canViewAssets: false,
    
    // Client permissions - View only
    canCreateClients: false,
    canEditClients: false,
    canDeleteClients: false,
    canViewAllClients: true,
    
    // Report permissions - Project reports only
    canGenerateReports: true,
    canViewAnalytics: false,
    canExportData: false,
    
    // System permissions - No access
    canAccessSettings: false,
    canManageSystem: false,
  },
  
  accountant: {
    // Navigation access - Financial focus
    canAccessDashboard: true,
    canAccessProjects: false, // No project access
    canAccessFinancials: true,
    canAccessReports: true,
    canAccessMessages: true,
    canAccessAssets: false,
    canAccessClients: true,
    canAccessUserManagement: false,
    
    // Project permissions - View only
    canCreateProjects: false,
    canEditProjects: false,
    canDeleteProjects: false,
    canViewAllProjects: true,
    canViewOwnProjects: true,
    
    // Financial permissions - Full financial access
    canCreateInvoices: true,
    canEditInvoices: true,
    canDeleteInvoices: false,
    canViewFinancialReports: true,
    canManagePayments: true,
    canViewRevenue: true,
    
    // User management permissions - No access
    canCreateUsers: false,
    canEditUsers: false,
    canDeleteUsers: false,
    canViewAllUsers: false,
    canManageRoles: false,
    
    // Asset permissions - No access
    canCreateAssets: false,
    canEditAssets: false,
    canDeleteAssets: false,
    canViewAssets: false,
    
    // Client permissions - View and edit for billing
    canCreateClients: true,
    canEditClients: true,
    canDeleteClients: false,
    canViewAllClients: true,
    
    // Report permissions - Financial reports
    canGenerateReports: true,
    canViewAnalytics: true,
    canExportData: true,
    
    // System permissions - No access
    canAccessSettings: false,
    canManageSystem: false,
  },
  
  client: {
    // Navigation access - Very limited
    canAccessDashboard: true,
    canAccessProjects: true, // Only their own projects
    canAccessFinancials: false,
    canAccessReports: false,
    canAccessMessages: true,
    canAccessAssets: false,
    canAccessClients: false,
    canAccessUserManagement: false,
    
    // Project permissions - View only their projects
    canCreateProjects: false,
    canEditProjects: false,
    canDeleteProjects: false,
    canViewAllProjects: false,
    canViewOwnProjects: true,
    
    // Financial permissions - No access
    canCreateInvoices: false,
    canEditInvoices: false,
    canDeleteInvoices: false,
    canViewFinancialReports: false,
    canManagePayments: false,
    canViewRevenue: false,
    
    // User management permissions - No access
    canCreateUsers: false,
    canEditUsers: false,
    canDeleteUsers: false,
    canViewAllUsers: false,
    canManageRoles: false,
    
    // Asset permissions - No access
    canCreateAssets: false,
    canEditAssets: false,
    canDeleteAssets: false,
    canViewAssets: false,
    
    // Client permissions - No access
    canCreateClients: false,
    canEditClients: false,
    canDeleteClients: false,
    canViewAllClients: false,
    
    // Report permissions - No access
    canGenerateReports: false,
    canViewAnalytics: false,
    canExportData: false,
    
    // System permissions - No access
    canAccessSettings: false,
    canManageSystem: false,
  },
};

export const useRoleAccess = () => {
  const { profile } = useAuth();

  // Get role name from profile - handle both direct role_name and nested role object
  const userRole = profile?.role_name || profile?.role?.role_name || 'client';
  const permissions = rolePermissions[userRole] || rolePermissions.client;
  
  const hasPermission = (permission: keyof RolePermissions): boolean => {
    return permissions[permission];
  };
  
  const canAccess = (resource: string): boolean => {
    switch (resource) {
      case 'dashboard': return permissions.canAccessDashboard;
      case 'projects': return permissions.canAccessProjects;
      case 'financials': return permissions.canAccessFinancials;
      case 'reports': return permissions.canAccessReports;
      case 'messages': return permissions.canAccessMessages;
      case 'assets': return permissions.canAccessAssets;
      case 'clients': return permissions.canAccessClients;
      case 'users': return permissions.canAccessUserManagement;
      default: return false;
    }
  };
  
  return {
    userRole,
    permissions,
    hasPermission,
    canAccess,
    isAdmin: userRole === 'admin',
    isManagement: userRole === 'management',
    isQS: userRole === 'qs',
    isAccountant: userRole === 'accountant',
    isClient: userRole === 'client',
  };
};
