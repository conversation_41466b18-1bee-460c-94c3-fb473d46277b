import { useState, useEffect, useCallback } from 'react'
import { EmployeeService, Employee, useLocalStorageFallback } from '@/lib/supabase'
import { useToast } from '@/components/ui/use-toast'

interface UseEmployeesReturn {
  employees: Employee[]
  loading: boolean
  error: string | null
  addEmployee: (employee: Omit<Employee, 'id' | 'total_salary' | 'date_added' | 'last_modified' | 'created_at'>) => Promise<Employee>
  updateEmployee: (id: string, updates: Partial<Omit<Employee, 'id' | 'total_salary' | 'date_added' | 'created_at'>>) => Promise<Employee>
  deleteEmployee: (id: string) => Promise<void>
  refreshEmployees: () => Promise<void>
  isOnline: boolean
  syncStatus: 'synced' | 'syncing' | 'offline' | 'error'
}

// Local storage fallback functions
const loadFromLocalStorage = (): Employee[] => {
  try {
    const saved = localStorage.getItem('employees')
    if (saved) {
      return JSON.parse(saved)
    }
  } catch (error) {
    console.error('Error loading from localStorage:', error)
  }
  
  // No sample data - use only Supabase data
  return [];
}

const saveToLocalStorage = (employees: Employee[]) => {
  try {
    localStorage.setItem('employees', JSON.stringify(employees))
  } catch (error) {
    console.error('Error saving to localStorage:', error)
  }
}

const calculateTotalSalary = (days: number, ratePerDay: number, credit: number): number => {
  return (days * ratePerDay) - credit
}

export const useEmployees = (): UseEmployeesReturn => {
  const { toast } = useToast()
  const [employees, setEmployees] = useState<Employee[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [isOnline, setIsOnline] = useState(navigator.onLine)
  const [syncStatus, setSyncStatus] = useState<'synced' | 'syncing' | 'offline' | 'error'>('synced')
  
  const useFallback = useLocalStorageFallback()

  // Monitor online status
  useEffect(() => {
    const handleOnline = () => {
      setIsOnline(true)
      if (!useFallback) {
        refreshEmployees()
      }
    }
    
    const handleOffline = () => {
      setIsOnline(false)
      setSyncStatus('offline')
    }

    window.addEventListener('online', handleOnline)
    window.addEventListener('offline', handleOffline)

    return () => {
      window.removeEventListener('online', handleOnline)
      window.removeEventListener('offline', handleOffline)
    }
  }, [useFallback])

  // Load employees
  const refreshEmployees = useCallback(async () => {
    try {
      setLoading(true)
      setError(null)
      setSyncStatus('syncing')

      if (useFallback || !isOnline) {
        // Use localStorage fallback
        const localEmployees = loadFromLocalStorage()
        setEmployees(localEmployees)
        setSyncStatus(useFallback ? 'synced' : 'offline')
      } else {
        // Use Supabase
        const data = await EmployeeService.getEmployees()
        setEmployees(data)
        setSyncStatus('synced')
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to load employees'
      setError(errorMessage)
      setSyncStatus('error')
      
      // Fallback to localStorage on error
      if (!useFallback) {
        const localEmployees = loadFromLocalStorage()
        setEmployees(localEmployees)
        toast({
          title: "Connection Error",
          description: "Using offline data. Changes will sync when connection is restored.",
          variant: "destructive",
        })
      }
    } finally {
      setLoading(false)
    }
  }, [useFallback, isOnline, toast])

  // Add employee
  const addEmployee = useCallback(async (employeeData: Omit<Employee, 'id' | 'total_salary' | 'date_added' | 'last_modified' | 'created_at'>): Promise<Employee> => {
    try {
      setSyncStatus('syncing')
      
      if (useFallback || !isOnline) {
        // Use localStorage fallback
        const newEmployee: Employee = {
          id: Date.now().toString(),
          ...employeeData,
          total_salary: calculateTotalSalary(employeeData.days, employeeData.rate_per_day, employeeData.credit),
          date_added: new Date().toISOString(),
          last_modified: new Date().toISOString(),
          created_at: new Date().toISOString()
        }
        
        const updatedEmployees = [newEmployee, ...employees]
        setEmployees(updatedEmployees)
        saveToLocalStorage(updatedEmployees)
        setSyncStatus(useFallback ? 'synced' : 'offline')
        
        return newEmployee
      } else {
        // Use Supabase
        const newEmployee = await EmployeeService.addEmployee(employeeData)
        setEmployees(prev => [newEmployee, ...prev])
        setSyncStatus('synced')
        
        return newEmployee
      }
    } catch (err) {
      setSyncStatus('error')
      const errorMessage = err instanceof Error ? err.message : 'Failed to add employee'
      throw new Error(errorMessage)
    }
  }, [employees, useFallback, isOnline])

  // Update employee
  const updateEmployee = useCallback(async (id: string, updates: Partial<Omit<Employee, 'id' | 'total_salary' | 'date_added' | 'created_at'>>): Promise<Employee> => {
    try {
      setSyncStatus('syncing')
      
      if (useFallback || !isOnline) {
        // Use localStorage fallback
        const updatedEmployees = employees.map(emp => {
          if (emp.id === id) {
            const updated = {
              ...emp,
              ...updates,
              last_modified: new Date().toISOString()
            }
            
            // Recalculate total salary if relevant fields changed
            if (updates.days !== undefined || updates.rate_per_day !== undefined || updates.credit !== undefined) {
              updated.total_salary = calculateTotalSalary(
                updates.days ?? emp.days,
                updates.rate_per_day ?? emp.rate_per_day,
                updates.credit ?? emp.credit
              )
            }
            
            return updated
          }
          return emp
        })
        
        setEmployees(updatedEmployees)
        saveToLocalStorage(updatedEmployees)
        setSyncStatus(useFallback ? 'synced' : 'offline')
        
        const updatedEmployee = updatedEmployees.find(emp => emp.id === id)!
        return updatedEmployee
      } else {
        // Use Supabase
        const updatedEmployee = await EmployeeService.updateEmployee(id, updates)
        setEmployees(prev => prev.map(emp => emp.id === id ? updatedEmployee : emp))
        setSyncStatus('synced')
        
        return updatedEmployee
      }
    } catch (err) {
      setSyncStatus('error')
      const errorMessage = err instanceof Error ? err.message : 'Failed to update employee'
      throw new Error(errorMessage)
    }
  }, [employees, useFallback, isOnline])

  // Delete employee
  const deleteEmployee = useCallback(async (id: string): Promise<void> => {
    try {
      setSyncStatus('syncing')
      
      if (useFallback || !isOnline) {
        // Use localStorage fallback
        const updatedEmployees = employees.filter(emp => emp.id !== id)
        setEmployees(updatedEmployees)
        saveToLocalStorage(updatedEmployees)
        setSyncStatus(useFallback ? 'synced' : 'offline')
      } else {
        // Use Supabase
        await EmployeeService.deleteEmployee(id)
        setEmployees(prev => prev.filter(emp => emp.id !== id))
        setSyncStatus('synced')
      }
    } catch (err) {
      setSyncStatus('error')
      const errorMessage = err instanceof Error ? err.message : 'Failed to delete employee'
      throw new Error(errorMessage)
    }
  }, [employees, useFallback, isOnline])

  // Initial load
  useEffect(() => {
    refreshEmployees()
  }, [refreshEmployees])

  // Set up real-time subscription (only if using Supabase)
  useEffect(() => {
    if (useFallback || !isOnline) return

    try {
      const subscription = EmployeeService.subscribeToChanges((payload) => {
        console.log('Real-time update:', payload)

        switch (payload.eventType) {
          case 'INSERT':
            setEmployees(prev => [payload.new, ...prev])
            break
          case 'UPDATE':
            setEmployees(prev => prev.map(emp => emp.id === payload.new.id ? payload.new : emp))
            break
          case 'DELETE':
            setEmployees(prev => prev.filter(emp => emp.id !== payload.old.id))
            break
        }
      })

      return () => {
        try {
          subscription.unsubscribe()
        } catch (error) {
          console.warn('Error unsubscribing from real-time updates:', error)
        }
      }
    } catch (error) {
      console.warn('Real-time subscription failed (this is normal with Cloudflare protection):', error)
      // Continue without real-time updates
    }
  }, [useFallback, isOnline])

  // Auto-migration on first load
  useEffect(() => {
    if (!useFallback && isOnline && employees.length === 0 && !loading) {
      EmployeeService.migrateFromLocalStorage().catch(console.error)
    }
  }, [useFallback, isOnline, employees.length, loading])

  return {
    employees,
    loading,
    error,
    addEmployee,
    updateEmployee,
    deleteEmployee,
    refreshEmployees,
    isOnline,
    syncStatus
  }
}
