-- Client Access Control Setup Guide
-- Complete setup script for client-project access control
-- Run this after the main client_project_access_control.sql script

-- 1. Create some test client users (if they don't exist)
-- This is just for demonstration - in production, users would be created through the UI

-- First, let's check what clients and users we have
DO $$
DECLARE
  client_count INTEGER;
  user_count INTEGER;
BEGIN
  SELECT COUNT(*) INTO client_count FROM public.clients;
  SELECT COUNT(*) INTO user_count FROM public.user_profiles WHERE role_name = 'client';
  
  RAISE NOTICE '';
  RAISE NOTICE '📊 Current Database Status:';
  RAISE NOTICE '  - Clients: %', client_count;
  RAISE NOTICE '  - Client Users: %', user_count;
  RAISE NOTICE '';
END $$;

-- 2. Example: Link existing client users to their clients
-- This is a template - you'll need to adjust the actual user_ids and client_ids

-- Example linking script (commented out - uncomment and modify with real IDs):
/*
-- Link user to client example:
SELECT link_user_to_client(
  'user-uuid-here'::UUID,     -- target_user_id (from auth.users or user_profiles)
  'client-uuid-here'::UUID,   -- target_client_id (from clients table)
  'client',                   -- user_role
  'read',                     -- access_level
  true                        -- is_primary
);
*/

-- 3. Create a helper function to show current relationships
CREATE OR REPLACE FUNCTION show_client_user_relationships()
RETURNS TABLE (
  client_name TEXT,
  user_email TEXT,
  user_role TEXT,
  access_level TEXT,
  is_primary BOOLEAN,
  relationship_created TIMESTAMP WITH TIME ZONE
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    c.name as client_name,
    up.email as user_email,
    cu.role as user_role,
    cu.access_level,
    cu.is_primary,
    cu.created_at as relationship_created
  FROM public.client_users cu
  JOIN public.clients c ON cu.client_id = c.id
  JOIN public.user_profiles up ON cu.user_id = up.user_id
  ORDER BY c.name, cu.is_primary DESC, cu.created_at;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 4. Create a helper function to show project access by user
CREATE OR REPLACE FUNCTION show_user_project_access(target_email TEXT DEFAULT NULL)
RETURNS TABLE (
  user_email VARCHAR(255),
  user_role VARCHAR(50),
  project_name VARCHAR(255),
  client_name VARCHAR(255),
  can_access BOOLEAN
) AS $$
DECLARE
  user_record RECORD;
BEGIN
  -- If specific email provided, check just that user
  IF target_email IS NOT NULL THEN
    SELECT user_id, email, role_name INTO user_record
    FROM public.user_profiles 
    WHERE email = target_email;
    
    IF user_record.user_id IS NOT NULL THEN
      RETURN QUERY
      SELECT 
        user_record.email,
        user_record.role_name,
        p.name as project_name,
        p.client_name,
        can_user_access_project(p.id, user_record.user_id) as can_access
      FROM public.projects p
      ORDER BY p.name;
    END IF;
  ELSE
    -- Show access for all client users
    FOR user_record IN 
      SELECT user_id, email, role_name 
      FROM public.user_profiles 
      WHERE role_name = 'client'
    LOOP
      RETURN QUERY
      SELECT 
        user_record.email,
        user_record.role_name,
        p.name as project_name,
        p.client_name,
        can_user_access_project(p.id, user_record.user_id) as can_access
      FROM public.projects p
      ORDER BY p.name;
    END LOOP;
  END IF;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 5. Create a function to automatically link users to clients based on email domain
CREATE OR REPLACE FUNCTION auto_link_users_by_email_domain()
RETURNS INTEGER AS $$
DECLARE
  user_record RECORD;
  client_record RECORD;
  linked_count INTEGER := 0;
  user_domain TEXT;
  client_domain TEXT;
BEGIN
  -- Loop through client users who aren't linked yet
  FOR user_record IN 
    SELECT up.user_id, up.email, up.full_name
    FROM public.user_profiles up
    WHERE up.role_name = 'client'
    AND NOT EXISTS (
      SELECT 1 FROM public.client_users cu WHERE cu.user_id = up.user_id
    )
  LOOP
    -- Extract domain from user email
    user_domain := LOWER(SPLIT_PART(user_record.email, '@', 2));
    
    -- Try to find a client with matching domain in email or website
    FOR client_record IN
      SELECT id, name, email, website
      FROM public.clients
      WHERE LOWER(SPLIT_PART(COALESCE(email, ''), '@', 2)) = user_domain
         OR LOWER(REPLACE(REPLACE(COALESCE(website, ''), 'https://', ''), 'http://', '')) LIKE '%' || user_domain || '%'
      LIMIT 1
    LOOP
      -- Link the user to the client
      INSERT INTO public.client_users (
        client_id, user_id, role, access_level, is_primary, created_by
      ) VALUES (
        client_record.id, 
        user_record.user_id, 
        'client', 
        'read', 
        false,
        (SELECT user_id FROM public.user_profiles WHERE role_name = 'admin' LIMIT 1)
      ) ON CONFLICT (client_id, user_id) DO NOTHING;
      
      linked_count := linked_count + 1;
      
      RAISE NOTICE 'Linked % (%) to % based on domain %', 
        user_record.full_name, user_record.email, client_record.name, user_domain;
    END LOOP;
  END LOOP;
  
  RETURN linked_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 6. Create a function to validate the access control setup
CREATE OR REPLACE FUNCTION validate_access_control_setup()
RETURNS TABLE (
  check_name TEXT,
  status TEXT,
  details TEXT
) AS $$
DECLARE
  client_count INTEGER;
  user_count INTEGER;
  relationship_count INTEGER;
  project_count INTEGER;
  projects_with_client_id INTEGER;
BEGIN
  -- Check 1: Basic table existence and data
  SELECT COUNT(*) INTO client_count FROM public.clients;
  SELECT COUNT(*) INTO user_count FROM public.user_profiles WHERE role_name = 'client';
  SELECT COUNT(*) INTO relationship_count FROM public.client_users;
  SELECT COUNT(*) INTO project_count FROM public.projects;
  SELECT COUNT(*) INTO projects_with_client_id FROM public.projects WHERE client_id IS NOT NULL;
  
  RETURN QUERY VALUES 
    ('Clients in database', 'INFO', client_count::TEXT),
    ('Client users in database', 'INFO', user_count::TEXT),
    ('Client-user relationships', 'INFO', relationship_count::TEXT),
    ('Total projects', 'INFO', project_count::TEXT),
    ('Projects with client_id', 'INFO', projects_with_client_id::TEXT);
  
  -- Check 2: Validate relationships
  IF relationship_count = 0 AND user_count > 0 THEN
    RETURN QUERY VALUES ('Client-user linking', 'WARNING', 'No client users are linked to clients');
  ELSIF relationship_count > 0 THEN
    RETURN QUERY VALUES ('Client-user linking', 'SUCCESS', 'Client users are properly linked');
  END IF;
  
  -- Check 3: Validate project relationships
  IF projects_with_client_id < project_count THEN
    RETURN QUERY VALUES ('Project-client linking', 'WARNING', 
      (project_count - projects_with_client_id)::TEXT || ' projects missing client_id');
  ELSE
    RETURN QUERY VALUES ('Project-client linking', 'SUCCESS', 'All projects have client relationships');
  END IF;
  
  -- Check 4: Test function availability
  BEGIN
    PERFORM get_user_accessible_projects();
    RETURN QUERY VALUES ('Access control functions', 'SUCCESS', 'All functions are available');
  EXCEPTION WHEN OTHERS THEN
    RETURN QUERY VALUES ('Access control functions', 'ERROR', 'Functions not working: ' || SQLERRM);
  END;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 7. Run validation
SELECT * FROM validate_access_control_setup();

-- 8. Show current relationships (if any)
DO $$
DECLARE
  rel_count INTEGER;
BEGIN
  SELECT COUNT(*) INTO rel_count FROM public.client_users;
  
  IF rel_count > 0 THEN
    RAISE NOTICE '';
    RAISE NOTICE '👥 Current Client-User Relationships:';
    RAISE NOTICE '=====================================';
  ELSE
    RAISE NOTICE '';
    RAISE NOTICE '⚠️  No client-user relationships found!';
    RAISE NOTICE '';
    RAISE NOTICE '📋 Next Steps:';
    RAISE NOTICE '1. Use the ClientUserManagement component in the admin panel';
    RAISE NOTICE '2. Or run: SELECT auto_link_users_by_email_domain();';
    RAISE NOTICE '3. Or manually link users with: SELECT link_user_to_client(...);';
  END IF;
END $$;

-- Show relationships if they exist
SELECT * FROM show_client_user_relationships();

-- Final success message
DO $$
BEGIN
  RAISE NOTICE '';
  RAISE NOTICE '✅ Client Access Control Setup Complete!';
  RAISE NOTICE '';
  RAISE NOTICE '🔧 Available Functions:';
  RAISE NOTICE '  - get_user_accessible_projects(user_id)';
  RAISE NOTICE '  - can_user_access_project(project_id, user_id)';
  RAISE NOTICE '  - link_user_to_client(user_id, client_id, role, access, primary)';
  RAISE NOTICE '  - show_client_user_relationships()';
  RAISE NOTICE '  - show_user_project_access(email)';
  RAISE NOTICE '  - auto_link_users_by_email_domain()';
  RAISE NOTICE '  - validate_access_control_setup()';
  RAISE NOTICE '';
  RAISE NOTICE '🚀 Frontend Changes Applied:';
  RAISE NOTICE '  - ProjectService.getProjects() now filters by user role';
  RAISE NOTICE '  - useProjects hook passes user ID for filtering';
  RAISE NOTICE '  - ClientUserManagement component available for admin';
  RAISE NOTICE '';
  RAISE NOTICE '📝 To test:';
  RAISE NOTICE '1. Link client users to clients';
  RAISE NOTICE '2. Login as a client user';
  RAISE NOTICE '3. Verify they only see their projects';
END $$;
