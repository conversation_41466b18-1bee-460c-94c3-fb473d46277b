-- Use Supabase's Built-in Password Reset Email (Guaranteed to Work with <PERSON><PERSON>)
-- This leverages Supabase's native email system that uses your SMTP settings

-- 1. Reset email limits
UPDATE public.user_profiles 
SET 
    setup_email_count = 0,
    email_status = 'pending'
WHERE setup_email_count > 0;

-- 2. Create function that triggers <PERSON><PERSON><PERSON>'s password reset email
CREATE OR REPLACE FUNCTION public.send_password_reset_email(
    user_profile_id UUID
)
RETURNS TABLE(success BOOLEAN, message TEXT, email_data JSONB) AS $$
DECLARE
    user_record RECORD;
    current_count INTEGER;
    max_emails INTEGER := 10;
BEGIN
    -- Get user information including auth user ID
    SELECT up.*, ur.role_name, up.user_id as auth_user_id
    INTO user_record
    FROM public.user_profiles up
    JOIN public.user_roles ur ON up.role_id = ur.id
    WHERE up.id = user_profile_id;
    
    IF NOT FOUND THEN
        RETURN QUERY SELECT false, 'User not found', '{}'::jsonb;
        RETURN;
    END IF;
    
    -- Check if user has auth account
    IF user_record.auth_user_id IS NULL THEN
        RETURN QUERY SELECT false, 'User has no auth account', '{}'::jsonb;
        RETURN;
    END IF;
    
    -- Check email limits
    current_count := COALESCE(user_record.setup_email_count, 0);
    IF current_count >= max_emails THEN
        RETURN QUERY SELECT false, 'Maximum email limit reached (' || max_emails || ')', '{}'::jsonb;
        RETURN;
    END IF;
    
    -- Update email count
    UPDATE public.user_profiles 
    SET 
        setup_email_sent_at = NOW(),
        setup_email_count = current_count + 1,
        email_status = 'password_reset_sent',
        updated_at = NOW()
    WHERE id = user_profile_id;
    
    -- Return success - the actual email sending will be handled by frontend
    RETURN QUERY SELECT 
        true, 
        'Ready to send password reset email via Supabase Auth',
        jsonb_build_object(
            'to_email', user_record.email,
            'user_name', user_record.first_name || ' ' || user_record.last_name,
            'auth_user_id', user_record.auth_user_id,
            'method', 'supabase_password_reset'
        );
        
EXCEPTION
    WHEN OTHERS THEN
        RETURN QUERY SELECT false, 'Function failed: ' || SQLERRM, '{}'::jsonb;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 3. Update RPC function
CREATE OR REPLACE FUNCTION public.rpc_resend_setup_email(user_profile_id UUID)
RETURNS jsonb AS $$
DECLARE
    v_requesting_user_id UUID;
    email_result RECORD;
BEGIN
    v_requesting_user_id := auth.uid();
    
    IF v_requesting_user_id IS NULL THEN
        RETURN jsonb_build_object('success', false, 'error', 'Not authenticated');
    END IF;
    
    SELECT * INTO email_result 
    FROM public.send_password_reset_email(user_profile_id);
    
    IF email_result.success THEN
        RETURN jsonb_build_object(
            'success', true, 
            'message', email_result.message,
            'email_data', email_result.email_data
        );
    ELSE
        RETURN jsonb_build_object('success', false, 'error', email_result.message);
    END IF;
    
EXCEPTION
    WHEN OTHERS THEN
        RETURN jsonb_build_object('success', false, 'error', SQLERRM);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 4. Grant permissions
GRANT EXECUTE ON FUNCTION public.send_password_reset_email(UUID) TO authenticated;

-- 5. Test the function
DO $$
DECLARE
    test_user_id UUID;
    email_result RECORD;
BEGIN
    RAISE NOTICE '';
    RAISE NOTICE '=== TESTING PASSWORD RESET EMAIL FUNCTION ===';
    
    SELECT id INTO test_user_id 
    FROM public.user_profiles 
    WHERE email = '<EMAIL>'
    LIMIT 1;
    
    IF test_user_id IS NOT NULL THEN
        SELECT * INTO email_result 
        FROM public.send_password_reset_email(test_user_id);
        
        RAISE NOTICE 'Function result:';
        RAISE NOTICE '  Success: %', email_result.success;
        RAISE NOTICE '  Message: %', email_result.message;
        
        IF email_result.success THEN
            RAISE NOTICE '✓ Function ready - frontend will handle actual email sending';
        END IF;
    END IF;
    
    RAISE NOTICE '';
    RAISE NOTICE 'NEXT: Update frontend to use supabase.auth.resetPasswordForEmail()';
END $$;
